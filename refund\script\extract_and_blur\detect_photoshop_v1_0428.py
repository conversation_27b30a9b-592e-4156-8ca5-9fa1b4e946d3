#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image, ImageChops, ImageEnhance
import piexif
from pathlib import Path
import requests
import io

def perform_ela(image_data, quality=90, scale=10):
    """
    执行错误等级分析(Error Level Analysis)
    
    参数:
        image_data: 图片数据（bytes）
        quality: JPEG压缩质量
        scale: 差异放大倍数
    
    返回:
        ela_image: ELA结果图像
        score: ELA分数（差异程度）
    """
    # 从bytes创建PIL图像
    original = Image.open(io.BytesIO(image_data)).convert('RGB')
    
    # 创建内存中的JPEG
    temp_buffer = io.BytesIO()
    original.save(temp_buffer, 'JPEG', quality=quality)
    temp_buffer.seek(0)
    
    # 读取重新保存的图像
    resaved = Image.open(temp_buffer).convert('RGB')
    
    # 计算原始图像和重新保存的图像之间的差异
    ela_image = ImageChops.difference(original, resaved)
    
    # 放大差异以便更容易看到
    extrema = ela_image.getextrema()
    max_diff = max([ex[1] for ex in extrema])
    if max_diff == 0:
        max_diff = 1
    
    # 计算ELA分数 - 差异越大，可能被PS的概率越高
    score = sum([ex[1] for ex in extrema]) / (3 * 255)  # 归一化分数
    
    # 放大差异
    ela_image = ImageEnhance.Brightness(ela_image).enhance(scale / max_diff)
    
    return ela_image, score

def noise_analysis(image_data):
    """
    执行噪声分析，检测图像中噪声的不一致性
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        noise_image: 噪声图像
        noise_score: 噪声不一致性分数
    """
    # 将bytes转换为numpy数组
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("无法解码图像数据")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 计算噪声
    noise = cv2.absdiff(gray, blurred)
    
    # 计算噪声的标准差作为分数
    noise_score = np.std(noise) / 255.0  # 归一化
    
    # 创建热图以显示噪声分布
    noise_image = cv2.applyColorMap(noise, cv2.COLORMAP_JET)
    
    return noise_image, noise_score

def check_metadata(image_data):
    """
    检查图像元数据中的PS痕迹
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 从bytes创建PIL图像
        img = Image.open(io.BytesIO(image_data))
        metadata_info = {}
        has_ps_metadata = False
        
        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])
            
            # 检查软件信息
            if piexif.ImageIFD.Software in exif_dict["0th"]:
                software = exif_dict["0th"][piexif.ImageIFD.Software].decode('utf-8', errors='ignore')
                metadata_info["软件"] = software
                if "photoshop" in software.lower() or "adobe" in software.lower():
                    has_ps_metadata = True
            
            # 检查修改历史
            if piexif.ExifIFD.MakerNote in exif_dict["Exif"]:
                maker_note = exif_dict["Exif"][piexif.ExifIFD.MakerNote].decode('utf-8', errors='ignore')
                metadata_info["制造商备注"] = maker_note
                if "photoshop" in maker_note.lower() or "adobe" in maker_note.lower():
                    has_ps_metadata = True
        
        # 检查XMP数据
        if "photoshop" in str(img.info).lower() or "adobe" in str(img.info).lower():
            has_ps_metadata = True
            metadata_info["其他元数据"] = "包含Adobe/Photoshop相关信息"
        
        return has_ps_metadata, metadata_info
    
    except Exception as e:
        print(f"检查元数据时出错: {e}")
        return False, {"错误": str(e)}

def detect_frequency_artifacts(image_data):
    """
    检测频域人工痕迹，AI生成图像常有特定频率特征
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        freq_score: 频域人工痕迹分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 转为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 应用FFT
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift) + 1)
        
        # 分析频谱特征
        # 检查规则网格模式，常见于GAN生成图像
        max_val = np.max(magnitude_spectrum)
        high_freq_ratio = np.sum(magnitude_spectrum > max_val * 0.5) / magnitude_spectrum.size
        
        # 根据经验，真实图像高频比例通常更平衡
        freq_score = 0
        explanation = ""
        
        if high_freq_ratio < 0.01:
            freq_score = 0.7
            explanation = f"检测到可疑的频域模式，可能是AI合成图像 (高频比例: {high_freq_ratio:.4f})"
        elif high_freq_ratio < 0.03:
            freq_score = 0.4
            explanation = f"频域特征有轻微异常，可能经过处理 (高频比例: {high_freq_ratio:.4f})"
        else:
            explanation = f"频域特征基本正常 (高频比例: {high_freq_ratio:.4f})"
        
        return freq_score, explanation
    
    except Exception as e:
        print(f"检测频域痕迹时出错: {e}")
        return 0, f"检测频域痕迹时出错: {e}"

def analyze_texture_consistency(image_data):
    """
    分析纹理一致性，AI生成图像常有纹理异常
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        texture_score: 纹理不一致性分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 提取纹理特征
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用LBP (Local Binary Pattern) 提取纹理
        def calculate_lbp(image, P=8, R=1):
            rows, cols = image.shape
            result = np.zeros((rows, cols), dtype=np.uint8)
            for i in range(R, rows - R):
                for j in range(R, cols - R):
                    center = image[i, j]
                    binary = []
                    for k in range(P):
                        angle = 2 * np.pi * k / P
                        x = i + R * np.cos(angle)
                        y = j + R * np.sin(angle)
                        x1, y1 = int(np.floor(x)), int(np.floor(y))
                        x2, y2 = min(x1 + 1, rows - 1), min(y1 + 1, cols - 1)
                        tx, ty = x - x1, y - y1
                        val = (1 - tx) * (1 - ty) * image[x1, y1] + \
                              tx * (1 - ty) * image[x2, y1] + \
                              (1 - tx) * ty * image[x1, y2] + \
                              tx * ty * image[x2, y2]
                        binary.append(1 if val >= center else 0)
                    decimal = sum([binary[k] * (2 ** k) for k in range(P)])
                    result[i, j] = decimal
            return result
        
        lbp = calculate_lbp(gray)
        
        # 计算局部区域LBP直方图标准差，检测不一致性
        block_size = 32
        rows, cols = gray.shape
        stds = []
        
        for i in range(0, rows - block_size + 1, block_size // 2):
            for j in range(0, cols - block_size + 1, block_size // 2):
                block = lbp[i:i+block_size, j:j+block_size]
                hist, _ = np.histogram(block, bins=256, range=(0, 256))
                hist = hist / np.sum(hist)  # 归一化
                stds.append(np.std(hist))
        
        # 分析标准差的变异
        texture_variation = np.var(stds) if stds else 0
        
        # 设置阈值判断
        texture_score = 0
        explanation = ""
        
        if texture_variation < 0.0001:  # 过于一致
            texture_score = 0.6
            explanation = f"检测到异常的纹理一致性，可能是AI合成图像 (纹理变异: {texture_variation:.6f})"
        elif texture_variation > 0.01:  # 过于不一致
            texture_score = 0.7
            explanation = f"检测到异常的纹理不一致性，可能是拼接或AI合成图像 (纹理变异: {texture_variation:.6f})"
        else:
            explanation = f"纹理一致性正常 (纹理变异: {texture_variation:.6f})"
        
        return texture_score, explanation
    
    except Exception as e:
        print(f"分析纹理一致性时出错: {e}")
        return 0, f"分析纹理一致性时出错: {e}"

def check_facial_anomalies(image_data):
    """
    检查面部异常，AI生成的人脸常有细节问题
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        face_score: 面部异常分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 使用人脸检测器
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)
        
        if len(faces) == 0:
            return 0, "未检测到人脸"
        
        face_score = 0
        anomalies = []
        
        for (x, y, w, h) in faces:
            face_region = gray[y:y+h, x:x+w]
            
            # 检测眼睛
            eyes = eye_cascade.detectMultiScale(face_region)
            
            # 检查眼睛对称性和位置
            if len(eyes) == 2:
                eye1, eye2 = eyes[0], eyes[1]
                eye_distance = abs((eye1[0] + eye1[2]//2) - (eye2[0] + eye2[2]//2))
                eye_height_diff = abs((eye1[1] + eye1[3]//2) - (eye2[1] + eye2[3]//2))
                
                if eye_height_diff > 0.15 * h:
                    anomalies.append("眼睛高度不对称")
                    face_score = max(face_score, 0.6)
            
            # 检查面部纹理
            face_texture = cv2.calcHist([face_region], [0], None, [256], [0, 256])
            face_texture = cv2.normalize(face_texture, face_texture).flatten()
            texture_entropy = -np.sum(face_texture * np.log2(face_texture + 1e-10))
            
            if texture_entropy < 3.0:  # 低熵可能表示AI生成的过于平滑的皮肤
                anomalies.append("面部纹理异常平滑")
                face_score = max(face_score, 0.7)
        
        if anomalies:
            explanation = f"检测到面部异常: {', '.join(anomalies)}"
        else:
            explanation = "未检测到明显的面部异常"
        
        return face_score, explanation
    
    except Exception as e:
        print(f"检查面部异常时出错: {e}")
        return 0, f"检查面部异常时出错: {e}"

def detect_photoshop_and_ai(image_data, ela_quality=90, ela_scale=10):
    """
    综合检测图像是否被PS过或AI合成
    
    参数:
        image_data: 图片数据（bytes）
        ela_quality: ELA分析的JPEG质量
        ela_scale: ELA差异放大倍数
    
    返回:
        result: 检测结果字典
    """
    result = {
        "ps_probability": 0.0,
        "ai_probability": 0.0,
        "analysis_results": {}
    }
    
    # 执行ELA分析
    try:
        ela_image, ela_score = perform_ela(image_data, quality=ela_quality, scale=ela_scale)
        result["analysis_results"]["ela_score"] = ela_score
        
        # ELA分数解释
        if ela_score > 0.05:
            ps_probability = min(1.0, ela_score * 5)  # 将分数映射到0-1范围
            result["analysis_results"]["ela_explanation"] = f"检测到较高的错误等级差异，可能被PS过 (分数: {ela_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
        else:
            result["analysis_results"]["ela_explanation"] = f"错误等级差异较低，可能未被PS过 (分数: {ela_score:.4f})"
    
    except Exception as e:
        result["analysis_results"]["ela_error"] = str(e)
    
    # 执行噪声分析
    try:
        noise_image, noise_score = noise_analysis(image_data)
        result["analysis_results"]["noise_score"] = noise_score
        
        # 噪声分数解释
        if noise_score < 0.01:
            ps_probability = 0.7  # 非常低的噪声可能表示过度平滑
            ai_probability = 0.6  # AI生成图像常有噪声异常
            result["analysis_results"]["noise_explanation"] = f"检测到异常低的噪声水平，可能被PS过或AI生成 (分数: {noise_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
            result["ai_probability"] = max(result["ai_probability"], ai_probability)
        elif noise_score > 0.1:
            ps_probability = 0.6  # 非常高的噪声可能表示添加了人工噪声
            result["analysis_results"]["noise_explanation"] = f"检测到异常高的噪声水平，可能被PS过 (分数: {noise_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
        else:
            result["analysis_results"]["noise_explanation"] = f"噪声水平正常 (分数: {noise_score:.4f})"
    
    except Exception as e:
        result["analysis_results"]["noise_analysis_error"] = str(e)
    
    # 检查元数据
    try:
        has_ps_metadata, metadata_info = check_metadata(image_data)
        result["analysis_results"]["metadata"] = metadata_info
        
        if has_ps_metadata:
            result["analysis_results"]["metadata_explanation"] = "检测到Photoshop/Adobe相关元数据"
            result["ps_probability"] = max(result["ps_probability"], 0.9)  # 元数据是很强的证据
        else:
            result["analysis_results"]["metadata_explanation"] = "未检测到Photoshop/Adobe相关元数据"
    
    except Exception as e:
        result["analysis_results"]["metadata_check_error"] = str(e)
    
    # AI检测 - 频域分析
    try:
        freq_score, freq_explanation = detect_frequency_artifacts(image_data)
        result["analysis_results"]["frequency_score"] = freq_score
        result["analysis_results"]["frequency_explanation"] = freq_explanation
        result["ai_probability"] = max(result["ai_probability"], freq_score)
    except Exception as e:
        result["analysis_results"]["frequency_analysis_error"] = str(e)
    
    # AI检测 - 纹理一致性
    try:
        texture_score, texture_explanation = analyze_texture_consistency(image_data)
        result["analysis_results"]["texture_score"] = texture_score
        result["analysis_results"]["texture_explanation"] = texture_explanation
        result["ai_probability"] = max(result["ai_probability"], texture_score)
    except Exception as e:
        result["analysis_results"]["texture_analysis_error"] = str(e)
    
    # AI检测 - 面部异常
    try:
        face_score, face_explanation = check_facial_anomalies(image_data)
        result["analysis_results"]["face_score"] = face_score
        result["analysis_results"]["face_explanation"] = face_explanation
        result["ai_probability"] = max(result["ai_probability"], face_score)
    except Exception as e:
        result["analysis_results"]["face_analysis_error"] = str(e)
    
    # 综合评估
    if result["ps_probability"] > 0.7:
        result["ps_conclusion"] = f"图像很可能被PS过 (可能性: {result['ps_probability']:.2f})"
    elif result["ps_probability"] > 0.4:
        result["ps_conclusion"] = f"图像可能被PS过 (可能性: {result['ps_probability']:.2f})"
    else:
        result["ps_conclusion"] = f"图像可能未被PS过 (可能性: {result['ps_probability']:.2f})"
    
    if result["ai_probability"] > 0.7:
        result["ai_conclusion"] = f"图像很可能是AI合成的 (可能性: {result['ai_probability']:.2f})"
    elif result["ai_probability"] > 0.4:
        result["ai_conclusion"] = f"图像可能是AI合成的 (可能性: {result['ai_probability']:.2f})"
    else:
        result["ai_conclusion"] = f"图像可能不是AI合成的 (可能性: {result['ai_probability']:.2f})"
    
    # 最终结论
    result["final_conclusion"] = "真实图像"
    if result["ps_probability"] > 0.7 or result["ai_probability"] > 0.7:
        result["final_conclusion"] = "人工处理或AI合成图像"
    elif result["ps_probability"] > 0.4 or result["ai_probability"] > 0.4:
        result["final_conclusion"] = "可疑图像，可能经过处理"
    
    return result

def main(image_url):
    """
    主函数，处理图片URL并返回PS和AI合成检测结果
    
    参数:
        image_url: 图片URL
    
    返回:
        dict: 检测结果
    """
    try:
        # 下载图片数据
        response = requests.get(image_url, stream=True)
        response.raise_for_status()
        image_data = response.content
        
        # 执行PS和AI合成检测
        result = detect_photoshop_and_ai(image_data, 90, 10)
        return result
    
    except Exception as e:
        print(f"处理图片时出错: {e}")
        return {"error": f"处理图片时出错: {e}"}

if __name__ == "__main__":
    #ps
    test_url1 = "http://fuwu.qunar.com/qbcp/file/download?fileName=7.jpeg&uniqueName=attach20250423133437943405"
    #md5加密test_url1
    import hashlib
    md5_hash = hashlib.md5(test_url1.encode('utf-8')).hexdigest()
    print(md5_hash)
    #真实
    #test_url1 = "https://fuwu.qunar.com/orderview/upload/queryFile/mJYfb-bIdc7rXvUwcFn9KAlAqSsi-pm6fdg6TBD3rynV_J_3c1f7cZdmaWIgbA5U5pqG_t4y6BPOo9pM-_iEqkf-Xff3uzBq8MiCnevDO_d5m4rCJQI1UCHDXGSJF0-Yi3r9MfWxhQwCEmegPdawYaWa49XVEIaDZu4_lxAGkXNdMGt7zhOGeoOVYzHqLidVXkW2JDE4b0aqsOv120BNMSuCp07s2BIE-hYr9whivs24ENb8P9rbUN0DVQwLvfe7.jpeg"
    #result = main(test_url1)
    #print(json.dumps(result, ensure_ascii=False, indent=2))
