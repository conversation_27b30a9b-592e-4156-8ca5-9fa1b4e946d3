import traceback
from typing import Any, Optional, Union


def validate_parameters(params):
    """
    校验入参参数
    Args:
        params: 入参字典
    Returns:
        tuple: (是否校验通过(bool), 校验失败原因(str))
    """
    # Step 1: 校验外层参数是否为空
    if not params:
        return False, "入参为空"

    required_outer_fields = ["liabilityReport"]
    for field in required_outer_fields:
        if field not in params:
            return False, f"缺少必要参数: {field}"

    if "refundInfo" not in params:
        params["refundInfo"] = {}
    return True, ""


def main(param: dict) -> dict:
    try:
        is_valid, message = validate_parameters(param)
        return {"is_valid": is_valid, "errorMsg": message}
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg}


if __name__ == "__main__":
    param = {
        "liabilityReport": "## 工单【NIMF20250330070902321627】质检报告（终审版）  ---  ### 1. 工单基本信息核实  - **工单ID**：NIMF20250330070902321627 - **问题分类**：不认可退票费用 / 质疑手续费高 - **乘机人数量**：2人（票号299-2329247470 贾小平，299-2329247471 李子楠） - **订单金额**：进单票面650元 × 2 = **1300元** - **平台原定手续费**：694元/人 × 2 = **1388元** - **航司核实手续费**：315元/人 × 2 = **630元**（基于350×90%×2人） - **系统退款金额**：180元（原路退回） - **平台线下赔付**：494元（线下协商赔付款，覆盖剩余部分差额） - **赔付总额**：180 + 494 = **674元**（用户总计返还金额） - **用户承担损失**：1300 - 674 = **626元**  ---  ### 2. 用户诉求及问题场景分析  - 用户买错票，知晓个人原因退票应承担部分费用，但不认可平台收“全损”手续费 - 用户明确质疑手续费过高，称“提前订的没给你们造成损失，要按比例扣，不能全额扣” - 主张手续费“最多100元/人”，否则不接受，拒付高额手续费 - 本质问题：代理商收费超出核实航司标准，用户质疑平台差价不透明，要求减免 - 问题类型明确对应 **“不认可退票费用”**，应按【不认可退票费用SOP】定责  ---  ### 3. 费用核算  | 项目                 | 平台应收（元）        | 航司实收（元）        | 差额/差价（元）       | 说明                                   | 赔付金额减平台应收（元）     | |---------------------|---------------------|---------------------|---------------------|----------------------------------------|-----------------------------| | 退票手续费 (单人)    | 694                 | 315                 | 379                 | 650元票面 *100%  vs. 航司认定350*90%      | 494/2 - 694 ≈ -447         | | 退票手续费 (2人共)   | 1388                | 630                 | **758**             | 平台较航司多收的手续费合计               | 494 - 1388 = -894          | | 实际退款金额         | 180                 | 不适用              | 不适用              | 系统原路退金额                         | N/A                         | | **合计**             | **1388**            | **630**             | **758**             | 赔付金额低于差价                      | -894                        |  **核算复核说明**：  - 用户最终获得共674元，承担（本金）损失1300-674=626元，与乘客实际应承担的航司手续费630元十分接近 - 差价=1388-630=**758元**，平台手续费较航司核实多收约758元 - 平台此次赔付494元，未覆盖全部差价，金额合理低于可赔付上限  ---  ### 4. 赔付方案评估  - **赔付金额**：494元（线下已赔付，不含系统自动退款180元） - **可赔付差价范围**：最高为758元 - **A转B判定**：   - 用户主诉“退票手续费过高、不按比例” → 素属买错票个人原因，不符合非自愿全退   - 平台原因：因代理未执行航司标准，收费高于核实标准   - 属于典型 **A（用户质疑代理霸王条款或差价）转B（平台差价问题）场景**，符合差价返还逻辑 - **是否合理**：   - 赔付494元 < 758元差价   - 符合 **“差价范围内赔偿，原则上线内”** 要求 - **结论**：   - **合规且合理**，未超差额上限，未造成超赔风险，处置恰当  ---  ### 5. 客服处理合规性评估  - **流程合规**：   - 正确核实了航司真实手续费（350元票面×90%）   - 识别平台代理收取手续费明显高于航司，存在收费异常   - 多轮沟通后采取差价范围协商赔付方案，及时止损 - **金额处置**：   - 面对用户“最多100元手续费/人”诉求，平台赔付494元，部分缓解用户不满，未超赔 - **操作规范**：   - 赔付流程未见升级审批记录，但考虑金额规模较小，且赔付未超差价，符合客诉快速调解机制   - 责任归因标记“代理责任”，合理 - **结论**：   - **客服合规，责任识别正确，避免升级，适当赔付**  ---  ### 6. 责任判定  #### 6.1 定责逻辑推导  - **确定SOP路径**：   - 用户诉求为退票手续费过高，不接受平台手续费   - 根据工单信息，“个人原因买错票”，但争议核心为代理收费高   - 明确匹配 【不认可退票费用SOP】  - **依据【不认可退票费用SOP】**：    - **是否线上二次退款？**     - 无信息表明有“线上二次退款” → 走步骤2      - **判断赔付金额是否 ≤ 差价（494元 < 758元）**     - 是     - → 根据步骤2.1 —— **赔付金额 ≤ 差价，赔付金额应追款代理商**  - **无需进入特殊报备、费率一致核查及细分责任判断**  - **最终结论**：  | 责任方                     | 金额（元）          | 责任说明                                                 | |----------------------------|-------------------|--------------------------------------------------------| | **代理商**                 | 494               | 高于航司收费标准过度收费，导致差价构成赔偿责任， 应全额承担                    | | 平台内部                   | 0                  | 无超赔行为，流程无违规，平台责任无                     | | 呼叫中心 / 产品 / 运营等其他 | 0                  | 不涉责任                                                |  ---  ### 7. 处理建议  - **追款方案**   - 全额向代理商追偿**494元**赔付   - 依据：代理差价责任明显，超航司标准过度收费 - **内部优化建议**   - 强化平台对代理釆集航司手续费标准的实时同步，确保展示价格与核实费用一致，杜绝超收   - 建议完善高额差价预警与审核机制 - **用户沟通建议**   - 针对此类差价争议应提前透明告知用户航司与代理差异，减少投诉 - **知识库与培训**   - 将此案例整理为【代理费用差价投诉处理SOP】模板范例   - 培训客服识别和拆解责任，提升快速判断和止损能力  ---  # **总结裁决依据及理由**  - 两份LLM质检报告均**认定问题本质为不认可手续费超航司标准，匹配“不认可退票费用SOP”，责任指向代理商，裁决方向正确。** - **LLM1**在费用核算细节（单人、多人数合计、赔付金额与差价多维比较）方面更细致，尤其清晰展示代理“超收758元”、实际赔付不超差额的合理性，且责任拆分更完整。 - **LLM2**尽管责任判断及流程基本正确，但在费用表述细节、百元手续费的理解及赔付金额逻辑链不够严密，略显笼统，部分数据（如赔付计算）表述欠明晰。 - 赔付金额核算、A转B场景识别、SOP步骤应用三方面，两者均未出现明显失误，符合工单数据与业务逻辑。 - 终审基于**事实核算**重算差额和赔付金额，确认代理责任区间；依**SOP条款**明确流程节点责任判定；整体遵守Prompt中的逐项输出格式，确保内容的**准确性、逻辑性及完整性**。 - 综上，整合两者优点，形成此份终裁复核版质检报告，内容准确完整，责任认定清晰明确。  ---  # **最终结论**  - 本工单赔付方案 **合理、合规** - **全额赔付494元应由代理商承担** - 无平台内部责任拆分 - 本次质检为依据数据、SOP、逻辑核验后的**唯一权威终审结果**。  ---"
    }
    print(main(param))
