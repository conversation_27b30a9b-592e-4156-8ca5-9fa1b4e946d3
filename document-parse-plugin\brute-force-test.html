<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>DOC暴力解析测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .input-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
    textarea {
      width: 100%;
      height: 150px;
      padding: 15px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      resize: vertical;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 8px;
      cursor: pointer;
      margin: 5px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    button:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }
    .brute-force-btn {
      background: #dc3545;
      font-weight: bold;
    }
    .brute-force-btn:hover {
      background: #c82333;
    }
    .sample-btn {
      background: #28a745;
    }
    .sample-btn:hover {
      background: #1e7e34;
    }
    .clear-btn {
      background: #6c757d;
    }
    .clear-btn:hover {
      background: #545b62;
    }
    .result-section {
      margin-top: 30px;
    }
    .best-result {
      background: #d4edda;
      border: 2px solid #28a745;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .best-result h3 {
      color: #155724;
      margin-top: 0;
    }
    .method-result {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      border-left: 4px solid #007bff;
    }
    .method-title {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .score {
      background: #007bff;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
    }
    .method-content {
      background: white;
      padding: 15px;
      border-radius: 5px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
    }
    .report-section {
      background: #e9ecef;
      padding: 20px;
      border-radius: 10px;
      margin-top: 20px;
    }
    .report-content {
      background: white;
      padding: 15px;
      border-radius: 5px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 400px;
      overflow-y: auto;
    }
    .status {
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔨 DOC暴力解析器</h1>
    <div class="warning">
      <strong>⚠️ 注意:</strong> 这是最后的解决方案，专门处理严重乱码的.doc文件。
      使用多种暴力方法强制提取可读内容。
    </div>
    
    <div class="input-section">
      <h3>📝 输入乱码文本</h3>
      <textarea id="inputText" placeholder="粘贴您的乱码文本..."></textarea>
    </div>

    <div class="button-group">
      <button class="brute-force-btn" onclick="bruteForceExtract()">🔨 暴力解析</button>
      <button class="sample-btn" onclick="loadSampleText()">📄 加载示例</button>
      <button class="clear-btn" onclick="clearAll()">🗑️ 清空</button>
    </div>

    <div class="result-section" id="resultSection" style="display: none;">
      <div id="bestResult"></div>
      <div id="allResults"></div>
      <div class="report-section">
        <h3>📊 详细报告</h3>
        <div class="report-content" id="reportContent"></div>
      </div>
    </div>
  </div>

  <!-- 加载暴力解析器 -->
  <script src="lib/doc-brute-force-parser.js"></script>

  <script>
    // 初始化暴力解析器
    let bruteForceParser = null;
    try {
      bruteForceParser = new DocBruteForceParser();
      console.log('暴力解析器初始化成功');
      showStatus('success', '暴力解析器加载成功，准备处理乱码文本');
    } catch (error) {
      console.error('暴力解析器初始化失败:', error);
      showStatus('error', '暴力解析器加载失败: ' + error.message);
    }

    function bruteForceExtract() {
      const inputText = document.getElementById('inputText').value.trim();
      
      if (!inputText) {
        showStatus('error', '请输入要解析的乱码文本');
        return;
      }

      if (!bruteForceParser) {
        showStatus('error', '暴力解析器未初始化');
        return;
      }

      showStatus('info', '开始暴力解析，这可能需要几秒钟...');

      try {
        // 生成完整报告
        const result = bruteForceParser.generateReport(inputText);
        
        displayResults(result);
        showStatus('success', '暴力解析完成！');
        
        // 显示结果区域
        document.getElementById('resultSection').style.display = 'block';
        
      } catch (error) {
        showStatus('error', '暴力解析失败: ' + error.message);
        console.error('解析错误:', error);
      }
    }

    function loadSampleText() {
      const sampleText = `Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_\`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡\` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\\ WPS Office 专业版王如根@鞓D鞓耀 (\\耀dlKSOProductBuildVer2052-9.1.0.39140澐C`;
      
      document.getElementById('inputText').value = sampleText;
      showStatus('info', '示例乱码文本已加载，点击"暴力解析"开始处理');
    }

    function displayResults(result) {
      // 显示最佳结果
      const bestResultDiv = document.getElementById('bestResult');
      bestResultDiv.innerHTML = `
        <div class="best-result">
          <h3>🏆 最佳解析结果</h3>
          <div class="method-content">${result.bestText || '无法提取有效内容'}</div>
        </div>
      `;

      // 显示所有方法结果
      const allResultsDiv = document.getElementById('allResults');
      let allResultsHtml = '<h3>🔍 所有解析方法对比</h3>';
      
      result.allResults.forEach((methodResult, index) => {
        allResultsHtml += `
          <div class="method-result">
            <div class="method-title">
              ${index + 1}. ${methodResult.method}
              <span class="score">得分: ${methodResult.score}</span>
            </div>
            <div class="method-content">${methodResult.text || '无内容'}</div>
          </div>
        `;
      });
      
      allResultsDiv.innerHTML = allResultsHtml;

      // 显示详细报告
      const reportDiv = document.getElementById('reportContent');
      reportDiv.textContent = result.report;
    }

    function showStatus(type, message) {
      const container = document.querySelector('.container');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      
      container.insertBefore(statusDiv, container.firstChild);
      
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function clearAll() {
      document.getElementById('inputText').value = '';
      document.getElementById('resultSection').style.display = 'none';
      showStatus('info', '已清空所有内容');
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof DocBruteForceParser !== 'undefined') {
        showStatus('success', '页面加载完成，暴力解析器可用');
        // 自动加载示例文本
        loadSampleText();
      } else {
        showStatus('error', '暴力解析器未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
