def getBusinessLineByFOrderNo(orderNo: str) -> str:
    """
    根据订单号后三位匹配业务线

    Args:
        orderNo: 订单号

    Returns:
        str: 业务线名称
    """
    # 默认返回国内OTA
    DEFAULT_BUSINESS_LINE = "国内OTA"

    if not orderNo or len(orderNo) < 3:
        return DEFAULT_BUSINESS_LINE

    # 获取订单号后三位
    suffix = orderNo[-3:].lower()

    # 创建业务线映射map（只包含非默认值的映射）
    business_line_map = {
        # 国内自营
        "xuy": "国内自营",
        "xep": "国内自营",
        "xdr": "国内自营",
        "cxf": "国内自营",
        "pzf": "国内自营",
        "lah": "国内自营",
        "qnt": "国内自营",
        "yau": "国内自营",
        "xae": "国内自营",
        "xyg": "国内自营",
        "yro": "国内自营",
        "taq": "国内自营",
        "nyl": "国内自营",
        "nss": "国内自营",
        "qnk": "国内自营",
        "qnf": "国内自营",
        "abc": "国内自营",
        "dmo": "国内自营",
        "dhz": "国内自营",
        "qcx": "国内自营",
        "hul": "国内自营",
        "qnd": "国内自营",
        "qnc": "国内自营",
        "tbl": "国内自营",
        "xcx": "国内自营",
        "yxa": "国内自营",
        "xyj": "国内自营",
        "jya": "国内自营",
        "xac": "国内自营",
        "xoa": "国内自营",
        "xam": "国内自营",
        "xrp": "国内自营",
        "qnu": "国内自营",
        "fzg": "国内自营",
        "jbb": "国内自营",
        "qzj": "国内自营",
        "qnn": "国内自营",
        "okm": "国内自营",
        "mia": "国内自营",
        "gqd": "国内自营",
        "cha": "国内自营",
        "ope": "国内自营",
        "fli": "国内自营",
        "exp": "国内自营",
        "wux": "国内自营",
        "ckg": "国内自营",
        "dfu": "国内自营",
        "tyx": "国内自营",
        "hzz": "国内自营",
        "hzw": "国内自营",
        "ltl": "国内自营",
        "btb": "国内自营",
        "sba": "国内自营",
        "grf": "国内自营",
        "ree": "国内自营",
        "yps": "国内自营",
        "ypk": "国内自营",
        "ybt": "国内自营",
        "zoy": "国内自营",
        # 旗舰店
        "cea": "旗舰店",
        "xma": "旗舰店",
        "xia": "旗舰店",
        "qda": "旗舰店",
        "don": "旗舰店",
        "chi": "旗舰店",
        "rui": "旗舰店",
        "kna": "旗舰店",
        "tib": "旗舰店",
        "cgz": "旗舰店",
        "hga": "旗舰店",
        "joy": "旗舰店",
        "loo": "旗舰店",
        "cej": "旗舰店",
        "yin": "旗舰店",
        "uru": "旗舰店",
        "cho": "旗舰店",
        "fuz": "旗舰店",
        "xbh": "旗舰店",
        "szd": "旗舰店",
        "cap": "旗舰店",
        "szf": "旗舰店",
        "szg": "旗舰店",
        "gxa": "旗舰店",
        "qlz": "旗舰店",
        "hhj": "旗舰店",
        "dhp": "旗舰店",
        "qde": "旗舰店",
        "xfu": "旗舰店",
        "jun": "旗舰店",
        "ghq": "旗舰店",
        "sub": "旗舰店",
        "xer": "旗舰店",
        "sdc": "旗舰店",
        "tjf": "旗舰店",
        "scv": "旗舰店",
        "xxx": "旗舰店",
        "ghc": "旗舰店",
        "srs": "旗舰店",
        "hiy": "旗舰店",
        "cqh": "旗舰店",
        "fhr": "旗舰店",
        "kmb": "旗舰店",
        "hzq": "旗舰店",
        "bbw": "旗舰店",
        "sme": "旗舰店",
        "www": "旗舰店",
        "mcj": "旗舰店",
        "nhq": "旗舰店",
        "qdh": "旗舰店",
        "xpb": "旗舰店",
        "qdu": "旗舰店",
        "dhe": "旗舰店",
        "wla": "旗舰店",
        "zbr": "旗舰店",
        "dhy": "旗舰店",
        "gld": "旗舰店",
        "jbk": "旗舰店",
        "xgo": "旗舰店",
        "dhv": "旗舰店",
        "fod": "旗舰店",
        "xzk": "旗舰店",
        "hkh": "旗舰店",
        "dce": "旗舰店",
        "trs": "旗舰店",
        # 分销
        "jlx": "分销",
        "czh": "分销",
        "zbv": "分销",
        "jbx": "分销",
        "ffm": "分销",
        "xnp": "分销",
        "sec": "分销",
        "mwb": "分销",
        "xwc": "分销",
        "qor": "分销",
        "abz": "分销",
        "xna": "分销",
        "kas": "分销",
        "jbw": "分销",
        "rnb": "分销",
        "gek": "分销",
        "pme": "分销",
        "xkv": "分销",
        "tdw": "分销",
        "teb": "分销",
        "zbw": "分销",
        "hpv": "分销",
        "bba": "分销",
        "mxu": "分销",
        "xiv": "分销",
        "kul": "分销",
        "lex": "分销",
        "zgy": "分销",
        "kcz": "分销",
        "fzv": "分销",
        "abw": "分销",
        "quo": "分销",
        "zqw": "分销",
        "zrv": "分销",
        "wtd": "分销",
        "xvv": "分销",
        "xug": "分销",
        "ted": "分销",
        "xnv": "分销",
        "hwu": "分销",
        "nth": "分销",
        "cpm": "分销",
        "aby": "分销",
        "qtl": "分销",
        "wxn": "分销",
        "lkh": "分销",
        "llp": "分销",
        "xng": "分销",
        "zbx": "分销",
        "lqm": "分销",
        "kql": "分销",
        "tgz": "分销",
        "jkd": "分销",
        "pgk": "分销",
        "vmi": "分销",
        "amb": "分销",
        "sfj": "分销",
        "hwm": "分销",
        "xux": "分销",
        "jfb": "分销",
        "oev": "分销",
        "sre": "分销",
        "jhe": "分销",
        "dah": "分销",
        "uah": "分销",
        "acf": "分销",
        "jgb": "分销",
        "rpk": "分销",
        "drz": "分销",
        "tec": "分销",
        "knx": "分销",
        "cay": "分销",
        "jnv": "分销",
        "lfx": "分销",
        "hrk": "分销",
        "dxi": "分销",
        "jbs": "分销",
        "teg": "分销",
        "xiz": "分销",
        "bkb": "分销",
        "cjd": "分销",
        "tee": "分销",
        "teh": "分销",
        "xoi": "分销",
        "ach": "分销",
        "xom": "分销",
        "blq": "分销",
        "acl": "分销",
        "xpp": "分销",
        "sio": "分销",
        "tem": "分销",
        "xok": "分销",
        "aco": "分销",
        "tek": "分销",
        "ter": "分销",
        "jbz": "分销",
        "acr": "分销",
        "kpa": "分销",
        "ssx": "分销",
        "sei": "分销",
        "xol": "分销",
        "jby": "分销",
        "wdm": "分销",
        "jfc": "分销",
        "zcj": "分销",
        "tlf": "分销",
        "wwq": "分销",
        "sqj": "分销",
        "fzj": "分销",
        "acn": "分销",
        "zck": "分销",
        "zkn": "分销",
        "tpx": "分销",
        "kpb": "分销",
        "sej": "分销",
        "fns": "分销",
        "acq": "分销",
        "nbo": "分销",
        "adn": "分销",
        "zfb": "分销",
        "xor": "分销",
        "sen": "分销",
        "qzw": "分销",
        "ezs": "分销",
        "com": "分销",
        "xpu": "分销",
        "pak": "分销",
        "rsc": "分销",
        "dzy": "分销",
        "ppu": "分销",
        "aws": "分销",
        "str": "分销",
        "sqw": "分销",
        "bkc": "分销",
        "tiz": "分销",
        "lqa": "分销",
        "lbz": "分销",
        "acv": "分销",
        "bmw": "分销",
        "tep": "分销",
        "bgb": "分销",
        "lhc": "分销",
        "nfv": "分销",
        "iod": "分销",
        "adi": "分销",
        "caz": "分销",
        "juq": "分销",
        "sel": "分销",
        "glg": "分销",
        "kat": "分销",
        "cpz": "分销",
        "sto": "分销",
        "stv": "分销",
        "acy": "分销",
        "zsx": "分销",
        "ofk": "分销",
        "xwf": "分销",
        "pxh": "分销",
        "teq": "分销",
        "adj": "分销",
        "dao": "分销",
        "ifv": "分销",
        "pai": "分销",
        "zpo": "分销",
        "zcu": "分销",
        "wse": "分销",
        "xuf": "分销",
        "nyq": "分销",
        "uyb": "分销",
        "nxh": "分销",
        "cxu": "分销",
        "cbe": "分销",
        "tes": "分销",
        "fio": "分销",
        "kcy": "分销",
        "acz": "分销",
        "swm": "分销",
        "tev": "分销",
        "tjx": "分销",
        "dak": "分销",
        "xoo": "分销",
        "ffv": "分销",
        "zfs": "分销",
        "rpn": "分销",
        "tzx": "分销",
        "wxx": "分销",
        "bbf": "分销",
        "lcq": "分销",
        "zwq": "分销",
        "suf": "分销",
        "ndk": "分销",
        "qfh": "分销",
        "spm": "分销",
        "zkg": "分销",
        "aza": "分销",
        "fbf": "分销",
        "jlz": "分销",
        "sis": "分销",
        "fig": "分销",
        "bsf": "分销",
        "aky": "分销",
        "jif": "分销",
        "mho": "分销",
        "amw": "分销",
        "tgn": "分销",
        "aoc": "分销",
        "sil": "分销",
        "siu": "分销",
        "aph": "分销",
        "msd": "分销",
        "sgx": "分销",
        "lra": "分销",
        "jgg": "分销",
        "ana": "分销",
        "qbb": "分销",
        "aqu": "分销",
        "apa": "分销",
        "ctd": "分销",
        "aqq": "分销",
        "apn": "分销",
        "aqx": "分销",
        "bbt": "分销",
        "aol": "分销",
        "are": "分销",
        "aqm": "分销",
        "ari": "分销",
        "siv": "分销",
        "aok": "分销",
        "ajm": "分销",
        "gau": "分销",
        "skz": "分销",
        "tgt": "分销",
        "fcf": "分销",
        "bby": "分销",
        "bea": "分销",
        "fcg": "分销",
        "auo": "分销",
        "atw": "分销",
        "jgx": "分销",
        "jju": "分销",
        "qtf": "分销",
        "feh": "分销",
        "zqe": "分销",
    }

    # 查找匹配的业务线，如果找不到则返回默认值（国内OTA）
    return business_line_map.get(suffix, DEFAULT_BUSINESS_LINE)
