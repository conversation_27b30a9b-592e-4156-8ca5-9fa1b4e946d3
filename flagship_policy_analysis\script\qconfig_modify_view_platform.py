# 导入需要的包(本案例不需要导入以下两个python包，如果有不支持的包，可以找平台管理员安装)
# import json
#
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple, Union
import random
import string
import json
import requests
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> Dict[str, Any]:
    """统一处理请求异常，返回统一的错误格式"""
    if isinstance(e, requests.exceptions.HTTPError):
        return {"status": 1, "message": f"http错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.ConnectionError):
        return {"status": 1, "message": f"连接错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.Timeout):
        return {"status": 1, "message": f"超时错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.RequestException):
        return {"status": 1, "message": f"请求错误: {str(e)}"}
    else:
        # 其他异常转换为统一格式
        return {"status": 1, "message": f"{e.__class__.__name__}: {str(e)}"}

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 校验入参
    required_params = ['targetgroupid', 'dataid', 'env', 'content', 'processId', 'oaId', 'qunar_account']
    for param_name in required_params:
        if param_name not in param or not param[param_name]:
            return {
                "status": 1,
                "message": f"参数 {param_name} 不能为空"
            }
    
    # 校验processId和oaId格式并转换为int
    try:
        process_id = int(param['processId'])
        oa_id = int(param['oaId'])
    except ValueError:
        return {
            "status": 1,
            "message": "参数 processId 或 oaId 格式不正确，必须为整数"
        }
    
    # 获取并验证环境变量
    env = param.get('env', '')
    # 定义允许的环境变量值，这里是qconfig的配置环境，不是真实的机器部署环境
    ALLOWED_ENVS = {"resources", "prod", "beta"}
    # 如果环境变量为空或不在允许的范围内，返回默认值beta
    if not env or env not in ALLOWED_ENVS:
        env = "beta"# 获取环境变量，默认为beta


    # 步骤一和二：GET请求获取当前配置
    try:
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': param['dataid'],
            'env': env,
            "subenv": param.get('subenv', ''),
            'targetgroupid': param['targetgroupid']
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        return handle_request_exception(e)

    # 步骤三：处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": get_result.get('status', 1),
            "message": get_result.get('message', '获取配置失败')
        }

    config_data = get_result['data']
    current_data = config_data.get('data', '')

    # 步骤四：处理数据
    new_content = process_content(param['targetgroupid'], param['dataid'], current_data, param['content'], param)
    if not new_content:
        return {"status": 1, "message": "入参配置解析失败"}


    #步骤五：POST请求更新配置
    return submit_config_by_env(env, process_id, oa_id, param['dataid'], param['qunar_account'], new_content)

def generate_random_row(existing_rows: set, length: int = 4) -> str:
    """生成不重复的随机行号

    参数:
    existing_rows: set - 已存在的row值集合
    length: int - 随机字符串长度，默认为4位

    返回:
    str - 生成的随机行号
    """
    characters = string.ascii_uppercase + string.digits  # 只包含大写字母和数字
    while True:
        # 生成指定长度的随机字符串
        random_row = ''.join(random.choices(characters, k=length))
        # 确保生成的row值不重复
        if random_row not in existing_rows:
            return random_row

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result

def process_properties_content(current_data: str, new_content: str) -> List[Dict]:
    """处理.properties格式的配置内容（修复版）"""
    current_dict = {}

    # 解析当前数据
    if current_data:
        for line in current_data.split('\n'):
            line = line.strip()
            if line and '=' in line:
                key, value = line.split('=', 1)
                current_dict[key.strip()] = value.strip()

    # 解析新内容（假设 parse_urlencoded_structured_data 返回字段在 columns 中）
    content_items, parse_status = parse_urlencoded_structured_data(new_content)
    if parse_status["status"] != "success":
        return None
    
    merged_list = []
    merged_dict = current_dict.copy()  # 保留现有数据

    # 任意更改key编辑标识，联动可视化平台ALL_FIELD类型标识，同一个配置中只会存在一个ALL_FIELD类型标识，不会有其他类型参数
    all_fields_sign = ""

    for item in content_items:
        field_name = str(item.get("field_name", "")).strip()
        field_value = str(item.get("field_value", "")).strip()
        split_char = str(item.get("split_char", "")).strip().upper()
        # 解析配置是否为任意key value编辑模式
        all_fields_sign = str(item.get("all_fields_sign", "")).strip()

        if not field_name:
            continue  # 跳过无效字段

        # 确定分隔符（默认为逗号）
        delimiter = "," if split_char == "COMMA" else ""

        # 合并逻辑
        if field_name in merged_dict:
            if delimiter:
                # 去重合并
                existing_values = merged_dict[field_name].split(delimiter)
                if field_value not in existing_values:
                    merged_dict[field_name] += f"{delimiter}{field_value}"
                    merged_list.append({field_name:merged_dict[field_name]})
            else:
                # 覆盖模式
                merged_dict[field_name] = field_value
                merged_list.append({field_name:merged_dict[field_name]})
        else:
            # 新增字段
            merged_dict[field_name] = field_value
            merged_list.append({field_name:merged_dict[field_name]})

    # 任意key value编辑模式，修改参数格式（特殊逻辑，可能看起来稍微有点复杂）
    if all_fields_sign:
        # 1. 提取本次有变更的 key-value
        changed_dict = {}
        for d in merged_list:
            for k, v in d.items():
                changed_dict[k] = v
        return merge_properties_with_comments(current_data, changed_dict, all_fields_sign)

    # 返回标准参数格式，仅修改新增允许编辑的key
    return merged_list

def merge_properties_with_comments(current_data: str, changed_dict: dict, all_fields_sign: str) -> list:
    """
    合并properties内容并保留注释和原始顺序，仅对changed_dict中的key做替换或追加。
    返回格式为：[{all_fields_sign: 拼接字符串}]
    """
    lines = []
    processed_keys = set()
    if current_data:
        for line in current_data.split('\n'):
            striped_line = line.strip()
            if not striped_line or striped_line.startswith('#'):
                lines.append(line.rstrip('\r'))
                continue
            if '=' in striped_line:
                key = striped_line.split('=', 1)[0].strip()
                if key in changed_dict:
                    lines.append(f"{key}={changed_dict[key]}")
                    processed_keys.add(key)
                else:
                    lines.append(line.rstrip('\r'))
            else:
                lines.append(line.rstrip('\r'))
    # 追加本次有变更但原数据没有的 key
    for key, value in changed_dict.items():
        if key not in processed_keys:
            lines.append(f"{key}={value}")
    all_fields_str = "\r\n".join(lines)
    return [{all_fields_sign: all_fields_str}]

def _generate_row_value(new_item: Dict[str, Any], existing_rows: set, row_key_format: str = None, param: Dict[str, Any] = None, max_numeric_row: int = 1) -> Tuple[str, int]:
    """生成row值

    参数:
    new_item: Dict[str, Any] - 新项目数据
    existing_rows: set - 已存在的row值集合
    row_key_format: str - rowKeyFormat设置格式
    param: Dict[str, Any] - 参数字典
    max_numeric_row: int - 当前最大数字row值

    返回:
    Tuple[str, int] - 生成的row值和更新后的max_numeric_row
    """
    # 先检查columns中的rowKey字段
    columns = new_item.get('columns', {})
    row_key = columns.get('rowKey', '')

    # 检查rowKey是否符合rowKeyFormat的要求且不重复
    use_row_key = False
    if row_key:
        # 检查rowKey是否不重复
        if row_key not in existing_rows:
            # 如果rowKeyFormat为numeric，则还需要检查rowKey是否为数字
            if row_key_format == 'numeric':
                if row_key.isdigit():
                    use_row_key = True
            else:
                # 随机生成模式，只要不重复即可
                use_row_key = True

    if use_row_key:
        # 使用columns中的rowKey作为row值
        return row_key, max_numeric_row
    else:
        # 根据rowKeyFormat设置格式生成row值
        if row_key_format == 'numeric':
            # 数字递增模式
            max_numeric_row += 1
            new_row = str(max_numeric_row)

            # 检查生成的数字row值是否已存在，如果存在则继续递增
            while new_row in existing_rows:
                max_numeric_row += 1
                new_row = str(max_numeric_row)
        else:
            # 默认随机生成模式
            # 获取随机字符串长度，默认为4位
            random_length = 4
            if param and 'randomLength' in param:
                try:
                    random_length = int(param['randomLength'])
                except (ValueError, TypeError):
                    pass
            # 生成不重复的随机row值
            new_row = generate_random_row(existing_rows, random_length)

        return new_row, max_numeric_row

def process_t_content(current_data: str, new_content: str, param: Dict[str, Any] = None) -> Optional[List[Dict]]:
    """处理.t格式的配置内容

    参数:
    current_data: str - 当前数据
    new_content: str - 新内容
    param: Dict[str, Any] - 参数字典，包含rowKeyFormat设置格式

    返回:
    Optional[List[Dict]] - 处理后的内容
    """
    try:
        result_list = []
        current_list = json.loads(current_data) if current_data else []
        content_items, parse_status = parse_urlencoded_structured_data(new_content)
        if parse_status["status"] != "success":
            return None

        # 调用特殊id分配逻辑
        build_content_items_by_special_dataid(current_list, content_items, param)
        new_items = [{"columns": item} for item in content_items]

        # 获取现有的所有row值
        existing_rows = {item.get('row') for item in current_list if item.get('row')}

        # 创建行号到索引的映射
        row_map = {item.get('row'): i for i, item in enumerate(current_list)}

        # 获取rowKeyFormat设置格式
        row_key_format = None
        if param and 'rowKeyFormat' in param:
            row_key_format = param['rowKeyFormat']

        # 如果是数字递增模式，找出当前最大数字row值
        max_numeric_row = 1
        if row_key_format == 'numeric':
            for item in current_list:
                row = item.get('row')
                if row and row.isdigit():
                    try:
                        row_value = int(row)
                        max_numeric_row = max(max_numeric_row, row_value)
                    except (ValueError, TypeError):
                        # 忽略非数字的row值
                        pass

        for new_item in new_items:
            # 生成row值
            new_row, max_numeric_row = _generate_row_value(
                new_item, existing_rows, row_key_format, param, max_numeric_row
            )
            new_item['row'] = new_row
            existing_rows.add(new_row)
            # 添加到列表末尾
            result_list.append(new_item)
        return result_list

    except json.JSONDecodeError:
        return None


def build_content_items_by_special_dataid(current_list, content_items, param: Dict[str, Any] = None):
    """
    针对特定系统名和文件名，自动为content_items分配自增id。
    :param current_list: 已有数据的列表
    :param content_items: 需要分配id的新数据列表
    :param param: 参数字典，需包含targetgroupid和dataid
    :return: None，直接修改content_items
    """
    if not param:
        return
    targetgroupid = param.get('targetgroupid', '')
    dataid = param.get('dataid', '')
    # 只针对指定的系统名和文件名处理id自增
    if (
        (targetgroupid == 'f_tts_product_config' and dataid == 'product_type.t') or
        (targetgroupid == 'f_twell_domestic' and dataid == 'booking_research_price.t')
    ):
        get_next_id(current_list, content_items, id_key="id")

def get_next_id(current_list, content_items, id_key="id"):
    """
    从current_list中提取id最大值，并为content_items中的每个item分配自增id（如果已存在则递增）。
    :param current_list: 已有数据的列表
    :param content_items: 需要分配id的新数据列表
    :param id_key: id字段名，默认为"id"
    :return: None，直接修改content_items
    """
    # 提取所有已有id的最大值
    max_id = 0
    existing_ids = set()
    for item in current_list:
        columns = item.get('columns', {})
        id_val = columns.get('id')
        if id_val is not None and str(id_val).isdigit():
            id_int = int(id_val)
            max_id = max(max_id, id_int)
            existing_ids.add(id_int)
    # 为content_items分配id
    for item in content_items:
        # 如果已有id且不冲突，跳过
        id_val = item.get(id_key)
        if id_val is not None and str(id_val).isdigit():
            id_int = int(id_val)
            if id_int not in existing_ids:
                existing_ids.add(id_int)
                continue  # 已有且不冲突
        # 否则分配新id
        while True:
            max_id += 1
            if max_id not in existing_ids:
                item[id_key] = str(max_id)
                existing_ids.add(max_id)
                break

def process_content(targetgroupid: str, dataid: str, current_data: str, new_content: str, param: Dict[str, Any] = None) -> Optional[List[Dict]]:
    """根据文件类型处理配置内容

    参数:
    dataid: str - 数据ID
    current_data: str - 当前数据
    new_content: str - 新内容
    param: Dict[str, Any] - 参数字典，包含rowKeyFormat设置格式

    返回:
    Optional[List[Dict]] - 处理后的内容
    """
    
    # 特殊场景，针对交易运价直连恶意配置解析
    if targetgroupid == "f_tts_trade_core" and dataid == "common.properties":
        special_scene = param.get('special_scene', '')
        if special_scene == "afareMeanOrderDomainProductBlack":
            return build_afare_mean_order_domain_product_black_content(current_data, new_content)

    # 特殊场景，针对会员弹框配置解析，支持复用场景
    if targetgroupid == "f_athena_domestic_tts" and dataid == "order_popup_config_info_v3.t":
        return build_order_popup_config_info_v3_content(current_data, new_content, param)

    # 通用配置解析处理逻辑
    if dataid.endswith('.properties'):
        return process_properties_content(current_data,new_content)
    elif dataid.endswith('.t'):
        content = process_t_content(current_data, new_content, param)
        return transform_data(content) if content is not None else None
    return None

def transform_data(original_data: Optional[List[Dict]]) -> List[Dict]:
    transformed = []
    if not original_data:
        return transformed
    for item in original_data:
        new_item = {
            "_xconfig_id_": item["row"],
            "@data_row_id@": item["row"],
            **item["columns"]
        }
        transformed.append(new_item)
    return transformed

def generate_config(dataid: str, config_data: list) -> dict:
    """
    将配置数据按 dataid 封装为指定格式

    :param dataid: 配置文件名（如 "product_code.t"）
    :param config_data: 配置数据列表，格式为 List[Dict]
    :return: 结构化配置字典
    """
    return {dataid: config_data}

def submit_config_by_env(env: str, process_id: int, oa_id: int, dataid: str, qunar_account: str, new_content: Any) -> Dict[str, Any]:
    """根据环境标识提交配置到不同的URL
    
    参数:
    env: str - 环境标识，如'beta', 'prod', 'resources'
    process_id: int - 流程ID
    oa_id: int - OA ID
    dataid: str - 数据ID
    new_content: Any - 新的配置内容
    
    返回:
    Dict[str, Any] - 提交结果
    """
    # 根据环境设置不同的URL
    if env == "prod" or env == "resources":
        post_url = "http://quan.corp.qunar.com/activityConfig/visualizationPublish/submitQConfigByAi"
        user_id = qunar_account
    else:
        # 默认使用beta环境
        post_url = "http://f-tts-activity-config.fd-329539-tros.inner3.beta.qunar.com/activityConfig/visualizationPublish/submitQConfigByAi"
        user_id = "pengyy.tan"
    try:
        headers = {
            "Content-Type": "application/json",
            "Cookie": "_xconfig_userId=" + user_id
        }

        post_data = {
            "processId": process_id,
            "oaId": oa_id,
            "configData": generate_config(dataid, new_content)
        }
        post_response = requests.post(
            post_url,
            headers=headers,
            json=post_data,
            timeout=30
        )
        post_response.raise_for_status()
        post_result = post_response.json()

        # 处理post返回结果
        if post_result.get('status') != 0:
            return {
                "status": post_result.get('status', 1),
                "message": post_result.get('msg', '上传配置失败')
            }
        
        # 成功时返回统一格式的字典
        return {
            "status": 0,  # 成功状态
            "message": "操作成功",
            "data": post_result.get('data')
        }

    except Exception as e:
        return handle_request_exception(e)

def build_afare_mean_order_domain_product_black_content(current_data: str, new_content: str) -> Optional[List[Dict]]:
    current_dict = {}

    # 解析当前数据
    if current_data:
        for line in current_data.split('\n'):
            line = line.strip()
            if line and '=' in line:
                key, value = line.split('=', 1)
                current_dict[key.strip()] = value.strip()

    # 解析新内容
    content_items, parse_status = parse_urlencoded_structured_data(new_content)
    if parse_status["status"] != "success":
        return None
    
    merged_list = []
    merged_dict = current_dict.copy()  # 保留现有数据

    for item in content_items:
        field_name = str(item.get("field_name", "")).strip()
        domain = str(item.get("domain", "")).strip()
        product_mark = str(item.get("productMark", "")).strip()

        if not field_name:
            continue  # 跳过无效字段

        afareConfig = merged_dict.get(field_name, "")
        domain_configs = afareConfig.split(';') if afareConfig else []
        domain_exists = False

        for i, domain_config in enumerate(domain_configs):
            if ":" in domain_config:
                config_domain, product_marks = domain_config.split(':', 1)
                if config_domain == domain:
                    domain_exists = True
                    # 检查productMark是否已经存在
                    product_marks_list = product_marks.split(',') if product_marks else []
                    if product_mark in product_marks_list:
                        break  # 已存在，跳出循环
                    # 追加productMark
                    if product_marks:
                        product_marks += f",{product_mark}"
                    else:
                        product_marks = product_mark
                    domain_configs[i] = f"{config_domain}:{product_marks}"
                    break  # 已处理，跳出循环

        # 如果domain不存在，追加domain和productMark
        if not domain_exists:
            domain_configs.append(f"{domain}:{product_mark}")

        merged_dict[field_name] = ";".join(domain_configs)
        merged_list.append({field_name: merged_dict[field_name]})

    return merged_list

def build_order_popup_config_info_v3_content(current_data: str, new_content: str, param: Dict[str, Any] = None) -> Optional[List[Dict]]:
    try:
        result_list = []
        current_list = json.loads(current_data) if current_data else []
        content_items, parse_status = parse_urlencoded_structured_data(new_content)
        if parse_status["status"] != "success":
            return None

        # 获取rowKeyFormat设置格式
        row_key_format = None
        if param and 'rowKeyFormat' in param:
            row_key_format = param['rowKeyFormat']

        # 如果是数字递增模式，找出当前最大数字row值
        max_numeric_row = 1
        if row_key_format == 'numeric':
            for item in current_list:
                row = item.get('row')
                if row and row.isdigit():
                    try:
                        row_value = int(row)
                        max_numeric_row = max(max_numeric_row, row_value)
                    except (ValueError, TypeError):
                        pass

        existing_rows = {item.get('row') for item in current_list if item.get('row')}

        for item in content_items:
            if item.get("reuse_record") == "true" and item.get("rowKey"):
                row_key = item["rowKey"]
                matched = next((x for x in current_list if x.get("row") == row_key), None)
                if matched:
                    old_mark = matched.get("columns", {}).get("mark", "")
                    new_mark = item.get("mark", "")
                    old_mark_list = [m.strip() for m in old_mark.split(",") if m.strip()]
                    if new_mark in old_mark_list:
                        continue  # 已存在，忽略
                    # 复用除mark外所有字段
                    new_columns = {k: v for k, v in matched.get("columns", {}).items() if k != "mark"}
                    # 拼接mark
                    if old_mark:
                        new_columns["mark"] = old_mark + "," + new_mark
                    else:
                        new_columns["mark"] = new_mark

                    # 设置修改标识
                    new_columns["_xconfig_row_flag"] = "change"
                    # 组装新对象
                    result_list.append({
                        "row": row_key,
                        "columns": new_columns
                    })
                else:
                    # 没找到rowKey，直接忽略
                    continue
            else:
                # 原逻辑：生成row值
                new_item = {"columns": item}
                new_row, max_numeric_row = _generate_row_value(
                    new_item, existing_rows, row_key_format, param, max_numeric_row
                )
                new_item['row'] = new_row
                existing_rows.add(new_row)
                result_list.append(new_item)

        # 格式化处理进行返回
        return transform_data(result_list) if result_list is not None else None        

    except json.JSONDecodeError:
        return None

