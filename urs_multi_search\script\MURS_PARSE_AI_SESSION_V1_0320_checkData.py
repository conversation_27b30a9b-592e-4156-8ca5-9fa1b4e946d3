import numbers
import re
import json
from datetime import datetime, date
from typing import List, Dict, Any, Set, Tuple, Union, Optional


def try_parse_json(content: str) -> Optional[Union[dict, list]]:
    """尝试解析可能包含转义字符的JSON字符串"""
    try:
        # 1. 直接尝试解析
        return json.loads(content)
    except json.JSONDecodeError:
        try:
            # 2. 处理可能的转义字符
            # 处理双重转义的情况
            if "\\\\" in content:
                content = content.replace("\\\\", "\\")
            # 处理转义的引号
            if '\\"' in content:
                content = content.replace('\\"', '"')
            # 处理转义的斜杠
            if "\\/" in content:
                content = content.replace("\\/", "/")
            return json.loads(content)
        except json.JSONDecodeError:
            return None


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON
    6. 带有转义字符的JSON字符串

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    if not isinstance(text, str):
        return None

    try:
        # 1. 首先尝试直接解析整个文本
        result = try_parse_json(text)
        if result is not None:
            return result

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            result = try_parse_json(content)
            if result is not None:
                return result

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            result = try_parse_json(match.group(0))
            if result is not None:
                return result

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        result = try_parse_json(cleaned_text)
        if result is not None:
            return result

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj.strip() == ""
    return False  # 非容器类型且非 None 的视为非空


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None


def validate_param(param: Dict[str, Any]) -> Dict[str, str]:
    """
    验证输入参数是否有效

    Args:
        param (Dict[str, Any]): 输入参数字典

    Returns:
        Dict[str, str]: 包含验证结果的字典，格式为 {"isValid": "true/false", "errorMsg": "错误信息"}
    """
    result = {"isValid": "false", "errorMsg": ""}

    # 检查param是否为空
    if not param:
        result["errorMsg"] = "输入参数为空"
        return result

    # 检查intentSlots
    if "intentSlots" not in param:
        result["errorMsg"] = "缺少intentSlots参数"
        return result

    intent_slots = safe_json_parse(param["intentSlots"])
    if intent_slots is None:
        result["errorMsg"] = "intentSlots解析失败"
        return result

    if is_deep_empty(intent_slots):
        result["errorMsg"] = "intentSlots内容为空"
        return result

    # 检查conversationList
    if "conversationList" not in param or not param["conversationList"]:
        result["errorMsg"] = "conversationList为空"
        return result

    result["isValid"] = "true"
    return result


def main(param):
    result = validate_param(param)
    return result


if __name__ == "__main__":

    param = {
        "conversationId": "H73tE7Capy80BIvGFnHnIfNo7rERB1M1",
        "uid": "198d329ce4df4fdd",
        "username": "sjninhg4391",
        "intent": "多次搜索时",
        "intentSlots": '{"出发地": "南宁", "目的地": "泉州", "价格差距": "500", "出发时间": "2025-03-26 22:00"}',
        "conversationList": '["请问您反馈的价格不稳定场景是2025-03-26 22:00, 从南宁到泉州的航班吗?", "是的", "请问您在多次搜索中，价格变动了多少呢？例如，第一次搜索是620元，第二次搜索是600元，那么价格差距是20元。", "第一次搜索600第二次搜索1100", "感谢您的反馈！我们将认真记录并尽快完善。当前页面将在 5 秒后自动关闭，再次感谢您的支持与信任！"]',
        "createTime": "2025-03-18T15:39:34.278+00:00",
    }
    print(
        "请求参数Json:"
        + json.dumps(
            param,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    )
    reuslt = main(param)
    if reuslt.get("error"):
        print("获取记录失败，错误信息:" + reuslt.get("error"))
    else:
        print(
            "检验结果:"
            + json.dumps(
                reuslt,
                ensure_ascii=False,
                separators=(",", ":"),  # 移除多余空格
                check_circular=True,
            )
        )
        # print("获取记录:" + json.dumps(
        #     reuslt.get("results"),
        #     ensure_ascii=False,
        #     separators=(",", ":"),  # 移除多余空格
        #     check_circular=True,
        # ))
