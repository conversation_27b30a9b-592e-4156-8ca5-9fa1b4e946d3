import hashlib
import uuid
import requests
import json
from typing import Dict, List, Any
import urllib, urllib3
import base64
import re
import time


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }
    
def validate_invoice(fpdm: str, fphm: str, kprq: str, jym: str, je: str) -> Dict[str, Any]:
    """
    调用发票验证接口验证发票真伪
    Args:
        fpdm: 发票代码
        fphm: 发票号码
        kprq: 开票日期
        jym: 校验码
        je: 金额
    Returns:
        Dict: 验证结果
    """
    try:
        url = 'https://fpzwcx.com/v1/check_czpj/info'

        appcode = '1b71a0e927d3461182218ce9a1a6c10a'
        #appcode = 'a090e1ebe87c46f2b1334632a327112a'
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {appcode}'
        }
        
        # 构建请求体
        payload = {
            "fpdm": fpdm,
            "fphm": fphm,
            "kprq": kprq,
            "je": je,
            "jym": jym
        }
        
        # 发送POST请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        
        # 解析响应内容
        content = response.text
        return content
        
    except requests.exceptions.RequestException as e:
        return {
            "error": f"发票验证失败: {str(e)}",
            "code": 500,
            "success": False
        }
    except Exception as e:
        return {
            "error": f"发票验证失败: {str(e)}",
            "code": 500,
            "success": False
        }
    

def mask_sensitive_info(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    对敏感信息进行遮挡处理,并增加对应字段的MD5加密值
    Args:
        data: 包含敏感信息的字典
    Returns:
        Dict: 遮挡后的字典，并包含MD5加密的字段
    """
    if not data or not isinstance(data, dict):
        return data
    
    # 复制原始数据，避免修改原始数据
    masked_data = data.copy()
    
    # 敏感字段及其遮挡规则
    mask_rules = {
        # 姓名: 保留第一个字符，其余用*代替
        "姓名": lambda x: x[0] + '*' * (len(x) - 1) if len(x) > 1 else x,
        "患者姓名": lambda x: x[0] + '*' * (len(x) - 1) if len(x) > 1 else x,
        "联系人": lambda x: x[0] + '*' * (len(x) - 1) if len(x) > 1 else x,
        "交款人": lambda x: x[0] + '*' * (len(x) - 1) if len(x) > 1 else x,
        
        # 身份证号: 保留前3位和后3位，中间用*代替
        "身份证号": lambda x: x[:3] + '*' * (len(x) - 6) + x[-3:] if len(x) >= 6 else x,
        "证件号码": lambda x: x[:3] + '*' * (len(x) - 6) + x[-3:] if len(x) >= 6 else x,
        "证件号": lambda x: x[:3] + '*' * (len(x) - 6) + x[-3:] if len(x) >= 6 else x,
        "护照号": lambda x: x[:3] + '*' * (len(x) - 6) + x[-3:] if len(x) >= 6 else x,
        
        # 地址: 保留前6个字符，其余用*代替
        "住址": lambda x: x[:6] + '*' * (len(x) - 6) if len(x) > 6 else x,
        "地址": lambda x: x[:6] + '*' * (len(x) - 6) if len(x) > 6 else x,
        "详细地址": lambda x: x[:6] + '*' * (len(x) - 6) if len(x) > 6 else x,
        
        # 发票代码/号码: 保留前1位和后2位，中间用*代替
        "票据代码": lambda x: x[:1] + '*' * (len(x) - 3) + x[-2:] if len(x) >= 3 else x,
        "发票代码": lambda x: x[:1] + '*' * (len(x) - 3) + x[-2:] if len(x) >= 3 else x,
        "发票号码": lambda x: x[:1] + '*' * (len(x) - 3) + x[-2:] if len(x) >= 3 else x,
        "票据号码": lambda x: x[:1] + '*' * (len(x) - 3) + x[-2:] if len(x) >= 3 else x,
        
        # 校验码: 保留前2位，其余用*代替
        "校验码": lambda x: x[:2] + '*' * (len(x) - 2) if len(x) > 2 else x,
        
        # 手机号/电话: 保留前3位和后4位，中间用*代替
        "手机号": lambda x: x[:3] + '*' * (len(x) - 7) + x[-4:] if len(x) >= 7 else x,
        "电话": lambda x: x[:3] + '*' * (len(x) - 7) + x[-4:] if len(x) >= 7 else x,
        "联系电话": lambda x: x[:3] + '*' * (len(x) - 7) + x[-4:] if len(x) >= 7 else x,
        
        # 就诊号: 保留前1位，其余用*代替
        "就诊号": lambda x: x[:1] + '*' * (len(x) - 1) if len(x) >= 1 else x,
        "门诊号": lambda x: x[:1] + '*' * (len(x) - 1) if len(x) >= 1 else x,
        "病历号": lambda x: x[:1] + '*' * (len(x) - 1) if len(x) >= 1 else x,
        "住院号": lambda x: x[:1] + '*' * (len(x) - 1) if len(x) >= 1 else x,
    }
    
    # 遍历字典，对敏感信息进行遮挡，并添加对应的MD5加密字段
    for key, value in data.items():
        # 完全匹配的情况
        if key in mask_rules and isinstance(value, str) and value:
            # 应用遮挡规则
            masked_data[key] = mask_rules[key](value)
            # 添加对应的MD5加密字段
            masked_data[f"{key}_md5"] = hashlib.md5(value.encode('utf-8')).hexdigest()
        # 部分匹配关键字的情况
        elif isinstance(value, str) and value:
            # 处理包含"姓名"关键字的字段
            if "姓名" in key:
                # 使用姓名的遮挡规则
                masked_data[key] = value[0] + '*' * (len(value) - 1) if len(value) > 1 else value
                masked_data[f"{key}_md5"] = hashlib.md5(value.encode('utf-8')).hexdigest()
            # 处理包含"身份证"关键字的字段
            elif "身份证" in key or "证件号" in key or "护照号" in key:
                # 使用身份证号的遮挡规则
                masked_data[key] = value[:3] + '*' * (len(value) - 6) + value[-3:] if len(value) >= 6 else value
                masked_data[f"{key}_md5"] = hashlib.md5(value.encode('utf-8')).hexdigest()
    
    return masked_data


def mask_invoice_validation_data(validation_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    对发票验证返回的JSON数据中的敏感字段进行脱敏处理，并增加对应字段的MD5加密值
    Args:
        validation_json: 发票验证返回的JSON数据
    Returns:
        Dict: 脱敏后的JSON数据，并包含MD5加密的字段
    """
    if not validation_json or not isinstance(validation_json, dict) or not validation_json.get("data") or not isinstance(validation_json["data"], dict):
        return validation_json
        
    # 创建validate_result_json的副本用于脱敏处理
    masked_validation_result = validation_json.copy()
    
    # 提取需要脱敏的字段
    masked_fields = {}
    if "fpdm" in validation_json["data"]:
        masked_fields["发票代码"] = validation_json["data"]["fpdm"]
    if "fphm" in validation_json["data"]:
        masked_fields["发票号码"] = validation_json["data"]["fphm"]
    if "jym" in validation_json["data"]:
        masked_fields["校验码"] = validation_json["data"]["jym"]
    if "jkr" in validation_json["data"]:
        masked_fields["交款人"] = validation_json["data"]["jkr"]
    if "就诊号" in validation_json["data"]:
        masked_fields["就诊号"] = validation_json["data"]["就诊号"]
    if "门诊号" in validation_json["data"]:
        masked_fields["门诊号"] = validation_json["data"]["门诊号"]
    if "病历号" in validation_json["data"]:
        masked_fields["病历号"] = validation_json["data"]["病历号"]
    if "住院号" in validation_json["data"]:
        masked_fields["住院号"] = validation_json["data"]["住院号"]
    # 对提取的字段进行脱敏，并添加MD5加密字段
    masked_fields = mask_sensitive_info(masked_fields)
    
    # 将脱敏后的字段放回副本中，包括MD5字段
    if "发票代码" in masked_fields and "fpdm" in validation_json["data"]:
        masked_validation_result["data"]["fpdm"] = masked_fields["发票代码"]
        masked_validation_result["data"]["fpdm_md5"] = masked_fields["发票代码_md5"]
    if "发票号码" in masked_fields and "fphm" in validation_json["data"]:
        masked_validation_result["data"]["fphm"] = masked_fields["发票号码"]
        masked_validation_result["data"]["fphm_md5"] = masked_fields["发票号码_md5"]
    if "校验码" in masked_fields and "jym" in validation_json["data"]:
        masked_validation_result["data"]["jym"] = masked_fields["校验码"]
        masked_validation_result["data"]["jym_md5"] = masked_fields["校验码_md5"]
    if "交款人" in masked_fields and "jkr" in validation_json["data"]:
        masked_validation_result["data"]["jkr"] = masked_fields["交款人"]
        masked_validation_result["data"]["jkr_md5"] = masked_fields["交款人_md5"]
    if "就诊号" in masked_fields and "就诊号" in validation_json["data"]:
        masked_validation_result["data"]["就诊号"] = masked_fields["就诊号"]
        masked_validation_result["data"]["就诊号_md5"] = masked_fields["就诊号_md5"]
    if "门诊号" in masked_fields and "门诊号" in validation_json["data"]:
        masked_validation_result["data"]["门诊号"] = masked_fields["门诊号"]
        masked_validation_result["data"]["门诊号_md5"] = masked_fields["门诊号_md5"]
    if "病历号" in masked_fields and "病历号" in validation_json["data"]:
        masked_validation_result["data"]["病历号"] = masked_fields["病历号"]
        masked_validation_result["data"]["病历号_md5"] = masked_fields["病历号_md5"]
    if "住院号" in masked_fields and "住院号" in validation_json["data"]:
        masked_validation_result["data"]["住院号"] = masked_fields["住院号"]
        masked_validation_result["data"]["住院号_md5"] = masked_fields["住院号_md5"]
        
    
    return masked_validation_result 

def perform_ai(image_data: bytes, doc_type: int, trace_id: str) -> Dict[str, Any]:
    """
    调用OCR接口识别图片内容
    Args:
        image_data: 图片二进制数据
    Returns:
        Dict: OCR识别结果
    """
    try:
        # 将图片数据转换为base64编码
        img_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # 构建请求参数
        request_data = {
            "app_code": "f_pangu",
            "trace_id": trace_id,
            "doc_type": doc_type,
            "img_id": trace_id,
            "img_value": f"data:image/png;base64,{img_base64}"
        }
        
        # 调用OCR接口
        response = requests.post(
            url="http://aigc.application.j.corp.qunar.com/image/docReview",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        response.raise_for_status()
        result = response.json()
        return result
        
    except Exception as e:
        return {"error": f"OCR识别失败: {str(e)}", "status": 500, "data": {}}   


def detect_doc_type_from_fields(ai_data: Dict[str, Any]) -> int:
    """
    根据AI识别结果中的字段内容判断文档类型
    优先级匹配，匹配上后不再执行后面的逻辑
    
    Args:
        ai_data: AI识别的字段数据字典
    
    Returns:
        int: 文档类型 0-身份证明, 1-发票证明, 2-诊断证明
    """
    # 1、如果包含"姓名"+"身份证号"+出生日期    设置为身份证明
    if "姓名" in ai_data and "身份证号" in ai_data and "出生日期" in ai_data:
        return 0
    
    # 2、如果包含"有效期限"+"签发机关"  设置身份证明
    if "有效期限" in ai_data and "签发机关" in ai_data:
        return 0
    
    if "护照号码" in ai_data:
        return 0
    if "新生儿姓名" in ai_data or "母亲姓名" in ai_data or "父亲姓名" in ai_data:
        return 0
    if "姓名（男）" in ai_data and  "姓名（女）" in ai_data:
        return 0
    
    # 3、如果包含"发票代码" 或者"票据代码"   设置为发票证明
    if "发票代码" in ai_data or "票据代码" in ai_data:
        return 1
    
    if "发票号码" in ai_data or "票据号码" in ai_data:
        return 1
    
    if "票据抬头" in ai_data:
        return 1
    
    # 如果JSON字符串中包含以下医疗相关关键字，设置为诊断证明
    medical_keywords = ["病历", "诊断", "住院", "出院", "用药", "挂号", "检查", "报告"]
    json_data_str = json.dumps(ai_data, ensure_ascii=False)
    for keyword in medical_keywords:
        if keyword in json_data_str:
            return 2
    
    # 如果都匹配不上，则返回-1表示不修改
    return -1

def get_material_by_md5(orderNo: str, domain: str, appCode: str, appToken: str, uniqKey: str, imgPathMd5: str) -> Dict[str, Any]:
    """
    根据图片MD5获取单条材料记录并处理
    Args:
        orderNo: 订单号
        domain: 域名
        appCode: 应用代码
        appToken: 应用令牌
        uniqKey: 唯一标识
        imgPathMd5: 图片MD5值
    Returns:
        Dict[str, Any]: 处理后的单条材料记录
    """
    if not imgPathMd5:
        return {}

    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/refund/refundconfirm/proveimg/showRecordMaterialList?orderNo={orderNo}&recordId=&crowdsourcingAuditResult=3&agentAuditResult=3&domain={domain}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        if "error" in result:
            return {}

        response_data = json.loads(result.get("data", "{}"))

        if not response_data or response_data.get("ret") is False:
            return {}

        material_list = response_data.get("data", {}).get("aduitModel", [])

        # 查找匹配的材料
        for material in material_list:
            img_path = material.get("imgPath", "")
            current_img_path_md5 = ""
            if img_path:
                # 对图片地址进行MD5加密
                current_img_path_md5 = hashlib.md5(img_path.encode('utf-8')).hexdigest()
                
            # 如果没有匹配的MD5，继续下一个
            if current_img_path_md5 != imgPathMd5:
                continue
                
            # 找到匹配的材料，处理并返回
            processed_material = {
                "uniqKey": uniqKey,
                "orderNo": material.get("orderNo", ""),
                "materialType": material.get("materialType", ""),
                "materialStringType": material.get("materialStringType", ""),
                "imgName": material.get("imgName", ""),
                "imgPathMd5": current_img_path_md5,
                "uploadTime": material.get("uploadTime", ""),
                "refundApplyTime": material.get("refundApplyTime", ""),
                "agentAuditResult": material.get("agentAuditResult", ""),
                "agentAuditResultChosen": material.get("agentAuditResultChosen", ""),
                "crowdsourcingAuditResult": material.get("crowdsourcingAuditResult", ""),
                "crowdsourcingAuditResultChosen": material.get("crowdsourcingAuditResultChosen", ""),
                "traceId": str(uuid.uuid4())
            }

            # 如果存在图片路径，进行图片识别
            if img_path:
                try:
                    # 下载图片数据
                    response = requests.get(img_path, stream=True)
                    response.raise_for_status()
                    image_data = response.content
                    
                    processed_material["invoice_validation_content"] = {}
                    # 调用接口获取图片信息
                    #0 — 身份证明 1 — 发票证明 2 — 诊断证明
                    
                    # 定义关键词映射表 
                    doc_type_mapping = {
                        0: ["身份", "证件"],
                        1: ["发票", "票据", "收据", "账单","交款人"],
                        2: ["诊", "医", "病", "检查", "处方","小结"]
                    }
                    
                    material_type = processed_material["materialStringType"].lower()
                    
                    # 默认为诊断证明
                    doc_type = 2
                    
                    # 按照优先级 0, 1, 2 进行匹配，一旦匹配到就不再继续匹配
                    # 先尝试匹配身份证明(0)
                    for keyword in doc_type_mapping[0]:
                        if keyword in material_type:
                            doc_type = 0
                            break
                    
                    # 如果没有匹配到0，尝试匹配发票证明(1)
                    if doc_type != 0:
                        for keyword in doc_type_mapping[1]:
                            if keyword in material_type:
                                doc_type = 1
                                break
                    
                    # 如果既没有匹配到0也没有匹配到1，使用默认的诊断证明(2)
                    # (默认值已设为2，所以不需要额外处理)
                    
                    # 转为中文 0 — 身份证明 1 — 发票证明 2 — 诊断证明    
                    doc_type_mapping_cn = {
                        0: "身份证明",
                        1: "发票证明",
                        2: "诊断证明"
                    }
                    processed_material["docType"] = doc_type_mapping_cn[doc_type]

                    ai_result = perform_ai(image_data, doc_type, processed_material["traceId"])
                    ai_data ={}
                    if ai_result.get("status") == 0:
                        ai_data = ai_result.get("data", {})
                    else:
                        ai_data = {}
        

                    # 如果ai_data为空，则ocr识别失败
                    if not ai_data:
                        processed_material["imgContent"] = {}
                        processed_material["invoice_validation_content"] = {}
                    else:

                        # 如果ai_data不为空则按照字段内容重新映射doc_type
                        detected_doc_type = detect_doc_type_from_fields(ai_data)
                        if detected_doc_type != -1:
                            doc_type = detected_doc_type
                            processed_material["docType"] = doc_type_mapping_cn[doc_type]

                        # 优先获取不为空的值
                        # 发票代码 - 从"发票代码"和"票据代码"中获取不为空的值
                        fpdm = ""
                        if "发票代码" in ai_data and ai_data["发票代码"]:
                            fpdm = ai_data["发票代码"]
                        elif "票据代码" in ai_data and ai_data["票据代码"]:
                            fpdm = ai_data["票据代码"]
                        
                        # 发票号码 - 从"发票号码"和"票据号码"中获取不为空的值
                        fphm = ""
                        if "发票号码" in ai_data and ai_data["发票号码"]:
                            fphm = ai_data["发票号码"]
                        elif "票据号码" in ai_data and ai_data["票据号码"]:
                            fphm = ai_data["票据号码"]
                        
                        kprq = ai_data.get("开票日期", "") if "开票日期" in ai_data and ai_data["开票日期"] else ""
                        jym = ai_data.get("校验码", "") if "校验码" in ai_data and ai_data["校验码"] else ""
                        je = ai_data.get("金额（小写）", "") if "金额（小写）" in ai_data and ai_data["金额（小写）"] else ""
                        if je:
                            # 只保留数字和小数点，去除¥、汉字、括号等所有其他字符
                            je = re.sub(r'[^\d.]', '', je)
                        
                        # 执行发票校验,如果包含发票所需的关键字段，则执行发票校验
                        # 检查是否为发票，并且包含发票所需的字段且字段不为空
                        has_invoice_fields = (fpdm and fphm and kprq and jym and je)

                        #ai_data中打码
                        ai_data = mask_sensitive_info(ai_data)
                        # 添加识别的文本内容
                        processed_material["imgContent"] = ai_data
                        validate_result = ""
                        if has_invoice_fields:
                            # 使用原始数据进行验证，而不是遮挡后的数据
                            validate_result = validate_invoice(fpdm, fphm, kprq, jym, je)
                            processed_material["fpdm"] = ai_data["票据代码_md5"]
                            processed_material["fphm"] = ai_data["票据号码_md5"]
                            processed_material["kprq"] = kprq
                            processed_material["jym"] = ai_data["校验码_md5"]
                            processed_material["je"] = je
                            try:    
                               # 解析validate_result
                               if validate_result and isinstance(validate_result, str):
                                validate_result_json = json.loads(validate_result)
                                
                                # 检查是否需要重试 (code=3004表示税局网络异常)
                                if validate_result_json.get("success") == False and validate_result_json.get("code") == 3004:
                                    # Sleep 1秒后重试
                                    time.sleep(1)
                                    # 重新调用发票验证接口
                                    validate_result = validate_invoice(fpdm, fphm, kprq, jym, je)
                                    if validate_result and isinstance(validate_result, str):
                                        validate_result_json = json.loads(validate_result)
                                
                                # 调用脱敏处理函数
                                masked_validation_json = mask_invoice_validation_data(validate_result_json)
                                processed_material["invoice_validation_content"] = masked_validation_json
 
                                if validate_result_json.get("success") == False:
                                    # 如果验证失败，code=3003表示当前发票调用次数超限
                                    if validate_result_json.get("code") == 3003:
                                        processed_material["invoice_validation_result"] = "今日次数超限"
                                        processed_material["invoice_validation_msg"] = validate_result_json.get("msg", "")
                                    else:
                                        processed_material["invoice_validation_result"] = "失败"
                                        processed_material["invoice_validation_msg"] = validate_result_json.get("msg", "")
                                else:
                                    #提取返回结果的内容
                                    
                                    # 从发票验证结果中提取jkr_md5字段值
                                    jkr_name = ""
                                    if validate_result_json.get("data", {}).get("jkr_md5"):
                                        jkr_name = validate_result_json.get("data", {}).get("jkr_md5")
                                        
                                    # 检查jkr_md5值是否存在于ai_data中的任何字段
                                    jkr_match_found = False
                                    if jkr_name:
                                        # 优先检查常见的姓名相关字段
                                        name_fields = ["姓名_md5", "交款人_md5"]
                                        
                                        # 先检查常见姓名字段
                                        for field in name_fields:
                                            if field in ai_data and isinstance(ai_data[field], str):
                                                if jkr_name in ai_data[field] or ai_data[field] in jkr_name:
                                                    jkr_match_found = True
                                                    break
                                        
                                        # 如果在常见字段中没找到，检查所有字段
                                        if not jkr_match_found:
                                            for key, value in ai_data.items():
                                                if isinstance(value, str) and jkr_name in value:
                                                    jkr_match_found = True
                                                    break
                                    if jkr_match_found or not jkr_name:
                                        processed_material["invoice_validation_result"] = "成功"
                                        if jkr_match_found:
                                            processed_material["invoice_validation_msg"] = f"发票识别成功，交款人信息匹配"
                                        else:
                                            processed_material["invoice_validation_msg"] = "发票识别成功，交款人信息未在图片中找到"
                                    else:
                                        processed_material["invoice_validation_result"] = "失败"
                                        processed_material["invoice_validation_msg"] = f"发票识别成功，交款人信息不匹配"
                               else:
                                processed_material["invoice_validation_result"] = "失败"
                                processed_material["invoice_validation_msg"] = "发票识别失败，返回结果异常"
                            except Exception as e:
                               processed_material["invoice_validation_result"] = "失败"
                               processed_material["invoice_validation_msg"] = f"发票识别失败，返回结果解析失败: {str(e)}"
                        else:
                            if doc_type == 1:
                                processed_material["invoice_validation_result"] = "失败"
                                processed_material["invoice_validation_msg"] = "发票识别失败,可能为纸质发票，待确认"
                            else:
                                processed_material["invoice_validation_result"] = ""
                                processed_material["invoice_validation_msg"] = ""
                        
                            
                except Exception as e:
                    processed_material["imgContent"] = f"图片内容识别失败: {str(e)}"
                    processed_material["invoice_validation"] = f"发票验证失败: {str(e)}"
            
            # 匹配到了材料并处理完成，返回结果
            return processed_material

        # 没有找到匹配的材料
        return {}

    except Exception as e:
        return {}

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数 - 返回单个对象结果而不是列表
    Args:
        param: 包含orderNo、invokeAppCode、invokeToken和imgPathMd5的参数字典
    Returns:
        Dict: 处理结果 - 单个对象而不是列表
    """
    orderNo = param.get("orderNo", "")
    uniqKey = param.get("uniqKey", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    imgPathMd5 = param.get("imgPathMd5", "")
    domain = param.get("domain", {})
    
    if not orderNo:
        return {"error": "订单号不能为空", "result": {}}
    
    if not imgPathMd5:
        return {"error": "图片MD5不能为空", "result": {}}

    try:
        # 获取单个材料
        material = get_material_by_md5(orderNo, domain, appCode, appToken, uniqKey, imgPathMd5)
        
        if not material:
            return {"error": f"未找到匹配的图片记录: {imgPathMd5}", "result": {}}
            
        # 返回处理后的单个材料
        return {
            "error": "",
            "result": material
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "result": {}}

def test():
    param = {
        "orderNo": "xau250508110550462",
        "uniqKey": "1234567890",
        "imgPathMd5":"b2f83af6f8a7f5e9b1e319f218b08bb0",
        "domain": "xau.trade.qunar.com",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    test() 
