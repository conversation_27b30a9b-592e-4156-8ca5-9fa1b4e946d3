import json
from ParseAndValiatePriceChangeParam import getPriceByPriceClassify, checkIsPriceMatch

# 测试数据
test_json = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [{"parseType": "精准", "price": "275"}],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"}
            ],
            "surPrecisePriceList": [{"parseType": "精准", "price": "405"}],
            "surDurationPriceList": [
                {"leftPrice": 400, "rightPrice": 409, "parseType": "模糊的区间价格"}
            ],
            "changePrecisePriceList": [{"parseType": "精准", "price": "130"}],
            "changeDurationPriceList": [
                {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"}
            ],
        }
    ]
}


def test_price_match():
    print("===== 测试价格匹配功能 =====")

    # 获取前序价格列表
    pre_prices = getPriceByPriceClassify(test_json, "preSurPriceGroups", "prePrice")
    print(f"前序价格列表有 {len(pre_prices)} 项:")
    for price in pre_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    # 测试精准匹配
    print("\n1. 测试精准价格匹配:")
    # 精准匹配价格275
    is_match, matched_item = checkIsPriceMatch(275, pre_prices)
    print(f"价格275匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 精准不匹配价格300
    is_match, matched_item = checkIsPriceMatch(300, pre_prices)
    print(f"价格300匹配结果: {is_match}")

    # 测试区间匹配
    print("\n2. 测试区间价格匹配:")
    # 区间内价格275
    is_match, matched_item = checkIsPriceMatch(275, pre_prices)
    print(f"价格275匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 边界值匹配270
    is_match, matched_item = checkIsPriceMatch(270, pre_prices)
    print(f"价格270匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 边界值匹配279
    is_match, matched_item = checkIsPriceMatch(279, pre_prices)
    print(f"价格279匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 区间外价格280
    is_match, matched_item = checkIsPriceMatch(280, pre_prices)
    print(f"价格280匹配结果: {is_match}")

    # 获取所有价格列表
    all_prices = getPriceByPriceClassify(
        test_json, "preSurPriceGroups", ["prePrice", "surPrice", "changePrice"]
    )

    print("\n3. 测试匹配所有价格类型:")
    # 匹配后序价格405
    is_match, matched_item = checkIsPriceMatch(405, all_prices)
    print(f"价格405匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 匹配变化价格130
    is_match, matched_item = checkIsPriceMatch(130, all_prices)
    print(f"价格130匹配结果: {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 测试无效价格
    print("\n4. 测试无效价格:")
    is_match, matched_item = checkIsPriceMatch("abc", all_prices)
    print(f"价格'abc'匹配结果: {is_match}")

    is_match, matched_item = checkIsPriceMatch(None, all_prices)
    print(f"价格None匹配结果: {is_match}")


if __name__ == "__main__":
    test_price_match()
