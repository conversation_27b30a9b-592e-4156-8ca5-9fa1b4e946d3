import json
from ParseAndValiatePriceChangeParam import parse_and_validate_price_change_param

# Example test data
test_json = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [{"parseType": "精准", "price": "275"}],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"}
            ],
            "surPrecisePriceList": [{"parseType": "精准", "price": "405"}],
            "surDurationPriceList": [
                {"leftPrice": 400, "rightPrice": 409, "parseType": "模糊的区间价格"}
            ],
            "changePrecisePriceList": [{"parseType": "精准", "price": "130"}],
            "changeDurationPriceList": [
                {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"}
            ],
        }
    ]
}

# Test with invalid price entries
test_json_with_invalid = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [
                {"parseType": "精准", "price": "275"},
                {"parseType": "精准", "price": "abc"},  # Invalid price
            ],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"},
                {
                    "leftPrice": "abc",  # Invalid left price
                    "rightPrice": 279,
                    "parseType": "模糊的区间价格",
                },
            ],
        }
    ]
}


# Run tests
def run_tests():
    print("Testing with valid data:")
    results = parse_and_validate_price_change_param(test_json)
    print(f"Found {len(results)} valid price entries:")
    for price in results:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    print("\nTesting with some invalid data:")
    invalid_results = parse_and_validate_price_change_param(test_json_with_invalid)
    print(
        f"Found {len(invalid_results)} valid price entries (invalid ones should be filtered out):"
    )
    for price in invalid_results:
        print(json.dumps(price, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    run_tests()
