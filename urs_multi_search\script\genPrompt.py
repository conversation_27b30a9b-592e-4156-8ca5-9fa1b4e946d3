from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串
    
    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(obj, 
                            ensure_ascii=False, 
                            separators=(',', ':'),  # 移除多余空格
                            check_circular=True)
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe='', encoding='utf-8', errors='strict')
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    
    参数：
    data_str : str - 输入的原生字符串数据
    
    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []
    
    # 第一阶段：分割并清洗原始数据
    raw_entries = [entry.strip(', ') for entry in data_str.split('~~*~~') if entry.strip()]
    
    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split('#*#') if f.strip()]
        
        for field in fields:
            # 第三阶段：键值对解析
            if ':' not in field:
                continue  # 跳过无效字段
                
            key, value = field.split(':', 1)
            key = key.strip()
            value = value.strip()
            
            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == 'null':
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识
                
            record[key] = value
        
        if record:  # 跳过空记录
            result.append(record)
    
    return result

def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}

def split_price_changes(groups: dict, group_type: str) -> tuple:
    """拆分价格变化的分组（增强版）"""
    price_change = {}
    no_change = {}
    
    for key, items in groups.items():
        # 处理航司/航班号分组
        if group_type in ('airline', 'flight'):
            # 获取所有唯一价格（字符串形式）
            prices = {str(item['price']) for item in items}
            
            if len(prices) > 1:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': list({item['flightNo'] for item in items}),  # 去重后的航班号列表
                    'prices': list(prices)  # 保证单值
                }
        
        # 处理OTA关联数据
        elif group_type == 'list2ota':
            ota_price = items['otaPrice']['price']
            list_price = items['listPrice']['price']
            
            if ota_price != list_price:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': [items['otaPrice']['flightNo']],
                    'prices': [ota_price]
                }
    
    return price_change, no_change

def generate_no_change_report(no_change_groups: dict) -> str:
    """生成无价格变动报告字符串"""
    if not no_change_groups:
        return ""
    
    report = []
    for key, group in no_change_groups.items():
        report.append(
            f"结论：无价格变动 | "
            f"分组类型: {group['group_type']} | "
            f"分组值: {group['group_value']}\n"
            f"  涉及航班: {', '.join(group['flight_nos'])}\n"
            f"  稳定价格: {group['prices'][0]}\n"
            f"{'-'*40}"
        )
    
    return "\n".join(report) + "\n"

def get_airline(flight_no: str) -> str:
    if '/' in flight_no:
        parts = flight_no.split('/')
        return '/'.join([p[:2] for p in parts])
    return flight_no[:2]

def group_by_airline(data: List[dict]) -> List[dict]:
    # 第一层分组：按航司
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}
    
    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")
        
        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        airline = get_airline(flight_no=flight_no)
        # 初始化航班分组
        if airline not in flight_groups:
            flight_groups[airline] = {}
        
        # 初始化追踪ID分组
        if tracer_id not in flight_groups[airline]:
            flight_groups[airline][tracer_id] = []
        
        # 添加原始数据（保留完整字段）
        flight_groups[airline][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append({
                "groupKey": tracer_key,
                "groupType": "tradeId",
                "groupValue": items.copy()  # 包含完整的原始数据
            })
        
        result.append({
            "groupKey": flight_key,
            "groupType": "airline",
            "groupValue": tracer_list
        })
    
    return result

def group_by_flightno(data: List[dict]) -> List[dict]:
    # 第一层分组：按航班号
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}
    
    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")
        
        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        
        # 初始化航班分组
        if flight_no not in flight_groups:
            flight_groups[flight_no] = {}
        
        # 初始化追踪ID分组
        if tracer_id not in flight_groups[flight_no]:
            flight_groups[flight_no][tracer_id] = []
        
        # 添加原始数据（保留完整字段）
        flight_groups[flight_no][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append({
                "groupKey": tracer_key,
                "groupType": "tradeId",
                "groupValue": items.copy()  # 包含完整的原始数据
            })
        
        result.append({
            "groupKey": flight_key,
            "groupType": "flightNo",
            "groupValue": tracer_list
        })
    
    return result

# 方法二：判断是否统一航班号
def is_unified_flight(data: list) -> bool:
    flights = {item["flightNo"] for item in data}
    return len(flights) == 1



# 方法四：处理OTA数据关联
def process_ota_data(data: list) -> dict:
    # 建立list数据索引
    list_data = {item["tradeId"]: item for item in data if item["tSource"] == "list"}
    
    # 处理OTA数据
    result = {}
    for item in data:
        listTradeId = item.get("listTradeId")
        if item["tSource"] == "ota" and listTradeId is not None :
            list_item = list_data.get(item["listTradeId"])
            result[item["tradeId"]] = {
                "otaPrice": item,
                "listPrice": list_item
            }
    return flatten_grouped_data(result, "list2ota")

def flatten_grouped_data(
    grouped_data: Dict[str, any],
    group_type: str
) -> List[dict]:
    result = []
    
    for group_key, groupData in grouped_data.items():
        # 合并所有trace分组下的数据
        merged_data = [] 
        result.append({
            "groupKey": group_key,
            "groupType": group_type,
            "groupValue": groupData
        })
    
    return result

def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False

def format_json_placeholder(template: str, obj1, obj2) -> str:
    """
    将两个对象JSON序列化后填充到字符串模板的占位符
    
    :param template: 含两个{}占位符的字符串模板
    :param obj1: 第一个可序列化对象
    :param obj2: 第二个可序列化对象
    :return: 格式化后的字符串
    
    示例：
    >>> data = {"name": "张三", "age": 25}
    >>> lst = [1, 2, 3]
    >>> print(format_json_placeholder("用户数据：{}，订单列表：{}", data, lst))
    用户数据：{"name": "张三", "age": 25}，订单列表：[1, 2, 3]
    """
    # 序列化时保持中文可读性（ensure_ascii=False）
    json_str2 = json.dumps(obj2, ensure_ascii=False, indent=None)  # 紧凑格式
    
    
    
    try:
        return template.format(obj1, json_str2)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e
    

def genPrompt(obj1, obj2) -> str:
    template = '''你是一名资深的机票行业运营，现在用户反馈机票搜索有变价，请结合用户反馈问题描述，搜索数据明细，搜索业务规则，变价场景和变价分析规则进行搜索变价分析
# 变价分析目标
搜索变价分析目标：根据用户的反馈question，搜索数据明细，搜索业务规则，变价场景和变价分析规则分析是否发生用户反馈的变价，并且进行变价归因和变价定责

#入参
##：入参说明
question参数： 用户反馈的信息
param 参数：结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
##：入参明细
question: {}
param: {}

#机票搜索业务说明
## 搜索流程
1、搜索分为list搜索（简称L页）和ota搜索（简称D页），用户搜索先进list，list展示用户选择的航线，起飞日期展示不同航班号的价格（一个航班号在list也只有一个航班号，通常是用户能看到的最低价）
2、用户在list页点击具体的航班会进入ota搜索，ota展示同航班，不同tag的报价，ota的tag分为(tag, oritag)，ota正常情况下有一个tag跟list tag(ota.tag = list.tag || ota.oriTag = list.tag)和价格(price)都一致的报价；从list进ota的血缘关系可以 ota.listTradeId = list.tradeId 关联
3、用户可能进行多次list搜索，也可以从同一个list点同一航班或不同航班多次进入ota搜索
4、每一次搜索事件都对应有一个唯一的tradeId

#搜索变价业务说明
## 用户视角能感知搜索变价业务场景
以下是用户视角能感知到的分维度变价场景，几个维度可以组合
### 用户视角能感知搜索流程维度变价场景
1、用户多次list搜索变价场景
2、单次list -> 单次ota， ota没有list的价格（list -> ota 具有血缘关系）
3、单次list触发多次ota，部分ota/全部ota 没有list的价格（list -> ota 具有血缘关系）

### 用户视角能感知产品维度变价场景
1、同航班变价
2、不同航班同航司变价
3、不同航班不同航变变价
4、不同航线变价

### 用户视角能感知的时间维度变价场景
1、秒级/分钟级/小时级/天级

### 用户视角变价场景定责说明
#### 平台无责场景（通常我们分析变价可以直接忽略此类场景）
1、不同航线变价
2、不同航班变价
3、不同航司变价
4、风控用户：如黄牛用户（用户标签包含 HNMX,SBHN,ZFHN,DJH,HSRY），抓取用户已经被平台识别投毒（poison=true）
5、用户搜索筛选项变化：如filters内容不一致
6、用户搜索时身份变化：用户标签（ZSJ6,PTGP,ZSJ6HEI有一个不同就算不同），乘机人不一致
7、同航班搜索时间跨度大（目前天级维度的就可判断平台无责，小时级维度的需要进一步分析原因定责

#### 同航班时间跨度范围不大的定责
1、多次搜索舱位变化，一般是航班基础数据(cabin)变化，用户平台均无责
2、票面价变化，一般是航班基础数据变化（viewPrice），用户平台均无责
3、供应（wrapperId）变化且票面没变，用户平台均无责


## 数据字段说明
### param结构说明
结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
### param明细字段
searchDateTime:搜索时间
departureDate:航班起飞日期
depTime:航班起飞时间
tradeId：每次搜索事件唯一id
tSource:list/ota搜索枚举
cabinType:舱等信息，目前只有ota能拿到舱等，list可以默认经济舱价格
price:展示价格
coupon:代金券，影响展示价格
cut:营销金额，影响展示价格
xCut:辅营营销金额，影响展示价格
tag:tag
oriTag:实tag，ota搜索会有根据一个实oriTag包装出多个tag的场景，这类case理论上他们之间的viewPrice和basePrice是一样的
expVendor:膨胀金金额，跟expansionType组合判断本价格是否用了膨胀金
expansionType:膨胀金计算类型，跟expVendor组合使用，当expVendor > 0时，根据下面枚举决定是否使用膨胀金，DURING说明当前搜索使用了膨胀金
   LIMIT(0, "不允许参与"),
   ALLOW(1, "可参与"),
   DURING(2, "参与中")
poison:投毒标识,=true说明平台识别的非法用户，进行了投毒
basicLabels:用户标签，识别用户身份使用（用户身份影响价格包装）
filters:搜索筛选项，如舱等选择，乘机人类型选择都可能影响价格
passengers:乘机人信息，影响价格（如特殊产品低价只有特定乘机人能买,会员产品,不同乘机人绑定不同代金券和营销）
wrapperId:供应id，供应不一致会导致价格变化
cabin:舱位，舱位变化大概率价格变化
packagePrice:包装价
basePrice:政策价
viewPrice:票面价
policyId:政策id，政策id不一致价格计算方式可能不一样
autoPriceDecreaseAmount:追价金额，平台低价策略，追价产品可根据次低价调整追价金额，达到低价优势,追价金额影响政策价计算
secondPrice:次低价，影响追价金额取值
CPT:报价生成时间，生成时间不一致可能是底层航班基础数据变化，结合viewPrice,basePrice,wrapperId,cabin看
allGoodItemPrice:加价商品金额，价格不一致可能是加价商品金额不一致

### param里价格计算依赖顺序
viewPrice -> basePrice -> packagePrice -> price
viewPrice一致，basePrice不一致影响因素：(secondPrice，autoPriceDecreaseAmount，policyId，wrapperId）
basePrice一致，packagePrice不一致影响因素(allGoodItemPrice,policyId,wrapperId)
packagePrice一致，price不一致影响因素(coupon,cut,xCut,[expVendor,expansionType],tag)

### 变价原因归因规则
1、展示价showPrice不同，判断投毒，如果结果不同，则归因为投毒
2、展示价showPrice不同，投毒相同，判断用户身份标签，身份标签含（HNMX,SBHN,ZFHN,DJH,HSRY），则归因为异常用户，身份标签不同（ZSJ6,PTGP,ZSJ6HEI有一个不同就算不同），则归因为用户身份发生变化
3、展示价showPrice不同，投毒相同，用户身份标签相同，判断乘机人是否相同，如果乘机人不同，则归因为乘机人填写不同
4、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，判断筛选项是否相同，如果结果为不同，则归因为筛选项
5、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，判断tag是否相同，如果不同则归因为无外露同tag
6、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，判断营销，如果包装价不变或变低，则归因为营销变化
7、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，判断膨胀金计算类型，如果相同
8、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位变化，则归因为航班基础数据变化-舱位变化
9、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位不变，则归因为航班基础数据变化-运价变化
10、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变，票面价未变，不同代理，则归因为供应变化-代理商调整价格
11、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变，票面价未变，同代理，同政策，追价金额变化，tag内次低价变化，则归因为供应变化-tag内次低价变化
12、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-同政策-追价金额变化，则归因为平台策略-追价变化
13、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-同政策-追价金额不变，则归因为其他
14、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-非同政策-报价生成时间变化，则归因为供应变化-报价生成时间变化
15、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-同tag包装价变价-政策价变-票面价未变-同代理-非同政策-低价被过滤 ，则归因为平台策略-低价被过滤
16、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价不变-加价变化（SPE_ADD）、限价变化、其余商品变化，则归因为平台策略-加价等商品变化

## 基于用户问题进行搜索变价分析说明

### 分析分类
1、 同航班不同list搜索变价分析，即分析的时候只取同行班且tSource=list的数据(L页 -> L页变价)
2、 同航班具有血缘关系的list->ota搜索分析(L页：tSource=list -> D页：tSource=ota 变价) - 如果用户明确指定了是L页变价，可不执行L页->D页的变价分析

### param分析数据剪枝说明
1、 结合用户的问题，如果报价航班，航司，搜索日期，航班起飞日期，时间，价格跟 param里的报价项不匹配，可提前剪枝，忽略此类数据
2、 如果某类分析分组仅有一个搜索事件（如某个航班只有一次list搜索事件），可提前剪枝，忽略此类数据（记录剪枝原因）

### 分析前置数据处理
1、list搜索事件请按照搜索时间升序排序，如果没有搜索时间可从tradeId提取，如ops_slugger_250226.181810.10.90.5.84.1867071.8637025621_1 时间是(250226.181810)

### 价格比较说明
1、都是比展示价格，即price
2、同航班不同list价格比较，比价无需区分tag，直接比同航班的price，如果有变价则进行归因
3、list -> ota 的比价说明，优先选同航班，同tag进行对比(ota.tag = list.tag || ota.oriTag = list.tag，如果同航班同一个list 搜索有多条ota数据 && 有tag也匹配的ota数据，则可忽略其他同list但tag不一致的ota数据（优先取ota.tag = list.tag的数据），如果有变价则进行归因



### 分析步骤（以上两类分析都是执行下面步骤）
#### step1: 组合分类数据(两类：同航班list搜索的数据（L页-），有血缘关系的ota和它对一个list之间的数据)
#### step2：遍历分类数据 -> 执行剪枝规则-> 数据前置处理-> 执行比价分析 -> 记录是否变价结果(这组比价的流水号flightNo，tradeId, price, tag, oriTag, searchDateTime，searchTimeDiff(搜索时间差，时间差用人类阅读友好的方式展示，如xx秒，xx分钟xx秒) tSource, isPriceChage（是否变价），priceChageDesc(变价判断依据))
#### step3: 如step2存在变价数据，对这组数据进行变价归因，输出归因信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）

# 输出
1、结合用户是问题给出是否有变价，字段名:hasPriceChange = true|false， 是否有变价的依据，字段名：hasPriceChangeDesc
2、如果有变价，按照markdow格式输出变价信息，如果同航班有变价且有多个list搜索，尽量这些list分析都输出
        flightNo，tradeId, price, tag, oriTag, searchDateTime，searchTimeDiff,tSource, isPriceChage（是否变价），priceChageDesc(变价判断依据),信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）
输出特别说明：tradeId不能截断和省略，searchDateTime需要有，如果是变价的case，attributeDesc，attributeReason，attributeResp不能省略'''
    return format_json_placeholder(template, obj1, obj2)


def main(param: dict) -> dict:
    
    data, parseStatus = parse_urlencoded_structured_data(param, 'param')
    oriQe = param.get('question')
    
    if parseStatus['status'] != 'success':
        return {"status":404, "errMsg": parseStatus['message']}
    
    # print(json.dumps(data, indent=2, ensure_ascii=False))

    #final_report = ""
    # 方法二调用
    unified = is_unified_flight(data)
    # print(f"\n是否统一航班号：{unified}")
    airline_groups: Dict[str, Dict[str, List[dict]]] = {}
    if not unified:
         airline_groups = group_by_airline(data)
         # print("航司分组结果：")
         # print(json.dumps(airline_groups, indent=2, ensure_ascii=False))
        
         # 拆分价格变动组
         #airline_pc, airline_nc = split_price_changes(airline_groups, 'airline')
         #final_report += generate_no_change_report(airline_nc)
         

    # 方法三调用
    flight_groups = group_by_flightno(data)
    # print("\n航班号分组结果：")
    # print(json.dumps(flight_groups, indent=2, ensure_ascii=False))
    # 拆分价格变动组
    #flight_pc, flight_nc = split_price_changes(flight_groups, 'flight')
    #final_report += generate_no_change_report(flight_nc)

    # 方法四调用
    ota_relations = process_ota_data(data)
    # print("\nOTA数据关联结果：")
    # print(json.dumps(ota_relations, indent=2, ensure_ascii=False))

    
    # 拆分价格变动组
    #ota_pc, ota_nc = split_price_changes(ota_relations, 'ota')
    
    # 生成报告
    # 生成综合报告
    #final_report += generate_no_change_report(ota_nc)

    finalData = {
        "status": 200,
        "errMsg": "",
        "airLinePriceGroup": airline_groups,
        "airLinePriceGroupEmpty":is_deep_empty(airline_groups),
        "flightPriceGroup": flight_groups,
        "flightPriceGroupEmpty":is_deep_empty(flight_groups),
        "list2otaPriceGroup": ota_relations,
        "list2otaPriceGroupEmpty":is_deep_empty(ota_relations)
    }

    prompt = genPrompt(oriQe, flight_groups)
    return {"prompt": prompt}

# 使用示例
if __name__ == "__main__":


    
    param = {"question": "【多次搜索变价】用户反馈（本机）于L页搜索同一航班：东航承运的3月14约14：00出发18：45抵达的哈尔滨-深圳航班价格连续上涨，5-6天前开始关注此航班，价格分别为490左右-520左右-550左右（昨日价格）-597（今日价格）。两次询问其他平台波动，用户未明确告知，但告知同程旅行价格更低，用户未购买机票。", "param": "tradeId%3Aops_slugger_250228.094152.10.95.133.41.2889129.3473346813_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3Anull%23%2A%23tag%3AZYJ1%23%2A%23searchDateTime%3A2025-02-28%2009%3A41%3A52%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU4890%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22549%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ALIMIT%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CTIME_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3A%23%2A%23productMark%3A%23%2A%23cabin%3A%23%2A%23packagePrice%3Anull%23%2A%23basePrice%3Anull%23%2A%23viewPrice%3Anull%23%2A%23policyId%3Anull%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A%23%2A%23allGoodItemPrice%3Anull%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.202915.10.95.140.67.3864637.4126929833_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3Anull%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2020%3A29%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22578%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22586.9%22%23%2A%23CPT%3A1740657592%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194255.10.90.5.148.3109568.3741842986_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3Anull%23%2A%23tag%3AYCP1%23%2A%23searchDateTime%3A2025-02-27%2019%3A42%3A55%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22582%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A582%23%2A%23basePrice%3A576.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194115.10.90.5.105.2299869.4600522426_1%23%2A%23oriTag%3AGPC1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A563%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194115.10.90.5.105.2299869.4600522426_1%23%2A%23oriTag%3ATYL1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A588%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A0%23%2A%23cabin%3AT%23%2A%23packagePrice%3A598%23%2A%23basePrice%3A598%23%2A%23viewPrice%3A598%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740654721%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194115.10.90.5.105.2299869.4600522426_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A568%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194115.10.90.5.105.2299869.4600522426_1%23%2A%23oriTag%3AGPC1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC8%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A553%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A11%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22568%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192949.10.95.133.39.1265679.1851414182_1%23%2A%23oriTag%3AGPC1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A29%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A563%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192942.10.95.133.39.1265679.6076443467_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192949.10.95.133.39.1265679.1851414182_1%23%2A%23oriTag%3ATYL1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-02-27%2019%3A29%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A588%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A0%23%2A%23cabin%3AT%23%2A%23packagePrice%3A598%23%2A%23basePrice%3A598%23%2A%23viewPrice%3A598%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740654721%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192942.10.95.133.39.1265679.6076443467_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192949.10.95.133.39.1265679.1851414182_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3ATTL1%23%2A%23searchDateTime%3A2025-02-27%2019%3A29%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A589%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd00111%23%2A%23productMark%3A266%23%2A%23cabin%3AT%23%2A%23packagePrice%3A599%23%2A%23basePrice%3A592.8%23%2A%23viewPrice%3A600%23%2A%23policyId%3A19754764119%23%2A%23autoPriceDecreaseAmount%3A1.8%23%2A%23secondPrice%3A%220.0%22%23%2A%23CPT%3A1740655782%23%2A%23allGoodItemPrice%3A6.2%23%2A%23listTradeId%3Aops_slugger_250227.192942.10.95.133.39.1265679.6076443467_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192942.10.95.133.39.1265679.6076443467_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A29%3A42%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22568%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192703.10.95.136.63.467953.3492658013_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A27%3A03%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22568%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E4%BB%BB%E9%87%91%E6%9C%8B%26ageType%3D0%26cardNo%3D231025200204231810%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192703.10.95.136.63.467953.1906620881_1%23%2A%23oriTag%3AGPC1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A27%3A03%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A563%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192703.10.95.136.63.467953.1906620881_1%23%2A%23oriTag%3ATYL1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-02-27%2019%3A27%3A03%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A588%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A0%23%2A%23cabin%3AT%23%2A%23packagePrice%3A598%23%2A%23basePrice%3A598%23%2A%23viewPrice%3A598%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740654721%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192703.10.95.136.63.467953.1906620881_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A27%3A03%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A568%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192629.10.95.136.63.467953.7670082872_1%23%2A%23oriTag%3ATYL1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-02-27%2019%3A26%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A588%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A0%23%2A%23cabin%3AT%23%2A%23packagePrice%3A598%23%2A%23basePrice%3A598%23%2A%23viewPrice%3A598%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740654721%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192629.10.95.136.63.467953.7670082872_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A26%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AHRB%23%2A%23price%3A570%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3A%23%2A%23poison%3AFalse%23%2A%23basicLabels%3A%23%2A%23filters%3A%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A585%23%2A%23basePrice%3A579.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A15.3%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A5%23%2A%23listTradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250227.192544.10.95.133.35.948010.9063895278_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AYCP1%23%2A%23searchDateTime%3A2025-02-27%2019%3A25%3A44%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22575%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A585%23%2A%23basePrice%3A579.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A15.3%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E"}
   
    result = main(
        param
        
    )
    print(json.dumps(result, indent=2, ensure_ascii=False))
