import traceback


def validate_parameters(params):
    """
    校验入参参数
    Args:
        params: 入参字典
    Returns:
        tuple: (是否校验通过(bool), 校验失败原因(str))
    """
    # Step 1: 校验外层参数是否为空
    if not params:
        return False, "入参为空"

    required_outer_fields = ["prompt"]
    for field in required_outer_fields:
        if field not in params:
            return False, f"缺少必要参数: {field}"
    return True, ""


def main(param: dict) -> dict:
    try:
        is_valid, message = validate_parameters(param)
        return {"is_valid": is_valid, "errorMsg": message}
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg}


def test_validate_parameters():
    """
    测试参数校验函数
    """
    # 测试用例1：正常数据
    test_data_1 = {
        "data": {"basePriceEnum": "", "tgqSourceType": "", "oriSaleTgqInfo": "test"},
        "prompt": "",
    }

    is_valid, message = validate_parameters(test_data_1)
    print("Test case 1 - Valid data:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")

    # 测试用例2：缺少必要参数
    test_data_2 = {"baseInfo": {}, "tgEventInfo": {}}

    is_valid, message = validate_parameters(test_data_2)
    print("\nTest case 2 - Missing required fields:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")


if __name__ == "__main__":
    test_validate_parameters()
