<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>WPS vs Word 乱码对比测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .comparison-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 30px;
    }
    .input-panel {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      border: 2px solid #dee2e6;
    }
    .input-panel.wps {
      border-color: #28a745;
    }
    .input-panel.word {
      border-color: #007bff;
    }
    .panel-title {
      font-weight: bold;
      margin-bottom: 15px;
      padding: 8px 15px;
      border-radius: 5px;
      color: white;
    }
    .panel-title.wps {
      background: #28a745;
    }
    .panel-title.word {
      background: #007bff;
    }
    textarea {
      width: 100%;
      height: 150px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: monospace;
      font-size: 11px;
      resize: vertical;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 8px;
      cursor: pointer;
      margin: 5px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    button:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }
    .detect-btn {
      background: #dc3545;
      font-weight: bold;
    }
    .detect-btn:hover {
      background: #c82333;
    }
    .sample-btn {
      background: #28a745;
    }
    .sample-btn:hover {
      background: #1e7e34;
    }
    .clear-btn {
      background: #6c757d;
    }
    .clear-btn:hover {
      background: #545b62;
    }
    .results-section {
      margin-top: 30px;
      display: none;
    }
    .detection-result {
      background: #e9ecef;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
    .software-badge {
      display: inline-block;
      padding: 5px 15px;
      border-radius: 15px;
      color: white;
      font-weight: bold;
      margin-right: 10px;
    }
    .software-badge.wps {
      background: #28a745;
    }
    .software-badge.word {
      background: #007bff;
    }
    .software-badge.unknown {
      background: #6c757d;
    }
    .confidence-bar {
      width: 100%;
      height: 20px;
      background: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
      margin: 10px 0;
    }
    .confidence-fill {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      transition: width 0.3s ease;
    }
    .comparison-results {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }
    .result-panel {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
    }
    .result-content {
      background: white;
      padding: 15px;
      border-radius: 5px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      font-size: 12px;
    }
    .features-list {
      background: white;
      padding: 15px;
      border-radius: 5px;
      margin-top: 10px;
    }
    .features-list ul {
      margin: 0;
      padding-left: 20px;
    }
    .features-list li {
      margin: 5px 0;
    }
    .status {
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔍 WPS vs Word 乱码检测对比</h1>
    
    <div class="comparison-section">
      <div class="input-panel wps">
        <div class="panel-title wps">📄 WPS Office 乱码文本</div>
        <textarea id="wpsText" placeholder="粘贴WPS创建的.doc文件乱码..."></textarea>
      </div>
      
      <div class="input-panel word">
        <div class="panel-title word">📄 Microsoft Word 乱码文本</div>
        <textarea id="wordText" placeholder="粘贴Word创建的.doc文件乱码..."></textarea>
      </div>
    </div>

    <div class="button-group">
      <button class="detect-btn" onclick="detectAndCompare()">🔍 智能检测对比</button>
      <button class="sample-btn" onclick="loadSampleTexts()">📄 加载示例</button>
      <button onclick="testSingleText()">🧪 测试单个文本</button>
      <button class="clear-btn" onclick="clearAll()">🗑️ 清空</button>
    </div>

    <div class="results-section" id="resultsSection">
      <div class="detection-result" id="detectionResult"></div>
      <div class="comparison-results" id="comparisonResults"></div>
    </div>
  </div>

  <!-- 加载必要的库 -->
  <script src="lib/doc-software-detector.js"></script>
  <script src="lib/doc-smart-parser.js"></script>

  <script>
    // 初始化检测器和解析器
    let softwareDetector = null;
    let smartParser = null;

    try {
      softwareDetector = new DocSoftwareDetector();
      smartParser = new DocSmartParser();
      console.log('软件检测器和智能解析器初始化成功');
      showStatus('success', '检测器加载成功，可以开始对比测试');
    } catch (error) {
      console.error('初始化失败:', error);
      showStatus('error', '检测器加载失败: ' + error.message);
    }

    function detectAndCompare() {
      const wpsText = document.getElementById('wpsText').value.trim();
      const wordText = document.getElementById('wordText').value.trim();
      
      if (!wpsText && !wordText) {
        showStatus('error', '请至少输入一个文本进行检测');
        return;
      }

      if (!softwareDetector || !smartParser) {
        showStatus('error', '检测器未初始化');
        return;
      }

      showStatus('info', '开始智能检测和对比...');

      try {
        const results = [];
        
        // 检测WPS文本
        if (wpsText) {
          const wpsBuffer = new TextEncoder().encode(wpsText).buffer;
          const wpsDetection = softwareDetector.detectSoftware(wpsBuffer, wpsText);
          const wpsStrategy = softwareDetector.getParsingStrategy(wpsDetection);
          results.push({
            type: 'WPS输入',
            text: wpsText,
            detection: wpsDetection,
            strategy: wpsStrategy
          });
        }
        
        // 检测Word文本
        if (wordText) {
          const wordBuffer = new TextEncoder().encode(wordText).buffer;
          const wordDetection = softwareDetector.detectSoftware(wordBuffer, wordText);
          const wordStrategy = softwareDetector.getParsingStrategy(wordDetection);
          results.push({
            type: 'Word输入',
            text: wordText,
            detection: wordDetection,
            strategy: wordStrategy
          });
        }
        
        displayComparisonResults(results);
        showStatus('success', '检测对比完成！');
        
        // 显示结果区域
        document.getElementById('resultsSection').style.display = 'block';
        
      } catch (error) {
        showStatus('error', '检测失败: ' + error.message);
        console.error('检测错误:', error);
      }
    }

    function loadSampleTexts() {
      // WPS样本（基于您提供的乱码）
      const wpsSample = `Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_\`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡\` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\\ WPS Office 专业版王如根@鞓D鞓耀 (\\耀dlKSOProductBuildVer2052-9.1.0.39140澐C`;
      
      // Word样本（模拟Word创建的乱码）
      const wordSample = `Root Entry WordDocument Data 1Table 0Table SummaryInformation DocumentSummaryInformation CompObj Microsoft Word Document Times New Roman Arial Symbol Courier New Normal.dot 正文 标题1 标题2 页眉 页脚 默认段落字体 Microsoft Office Word 2019 Document1 Administrator Normal.dotm 16.0.4266.1001 Microsoft Corporation`;
      
      document.getElementById('wpsText').value = wpsSample;
      document.getElementById('wordText').value = wordSample;
      
      showStatus('info', '示例文本已加载，点击"智能检测对比"开始分析');
    }

    function testSingleText() {
      const wpsText = document.getElementById('wpsText').value.trim();
      const wordText = document.getElementById('wordText').value.trim();
      const testText = wpsText || wordText;
      
      if (!testText) {
        showStatus('error', '请输入要测试的文本');
        return;
      }

      if (!smartParser) {
        showStatus('error', '智能解析器未初始化');
        return;
      }

      showStatus('info', '开始智能解析测试...');

      try {
        const buffer = new TextEncoder().encode(testText).buffer;
        smartParser.smartParse(buffer, 'test.doc').then(result => {
          displaySingleTestResult(result, testText);
          showStatus('success', '智能解析测试完成！');
          document.getElementById('resultsSection').style.display = 'block';
        }).catch(error => {
          showStatus('error', '智能解析失败: ' + error.message);
        });
        
      } catch (error) {
        showStatus('error', '测试失败: ' + error.message);
        console.error('测试错误:', error);
      }
    }

    function displayComparisonResults(results) {
      const detectionDiv = document.getElementById('detectionResult');
      const comparisonDiv = document.getElementById('comparisonResults');
      
      // 显示总体检测结果
      let detectionHtml = '<h3>🔍 检测结果总览</h3>';
      
      results.forEach((result, index) => {
        const software = result.detection.software;
        const confidence = result.detection.confidence;
        
        detectionHtml += `
          <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 8px;">
            <h4>${result.type}</h4>
            <div>
              <span class="software-badge ${software}">${software.toUpperCase()}</span>
              <span>置信度: ${(confidence * 100).toFixed(1)}%</span>
            </div>
            <div class="confidence-bar">
              <div class="confidence-fill" style="width: ${confidence * 100}%"></div>
            </div>
            <div><strong>推荐策略:</strong> ${result.strategy.name}</div>
          </div>
        `;
      });
      
      detectionDiv.innerHTML = detectionHtml;
      
      // 显示详细对比
      let comparisonHtml = '';
      
      results.forEach((result, index) => {
        comparisonHtml += `
          <div class="result-panel">
            <h4>${result.type} - ${result.detection.software.toUpperCase()}</h4>
            
            <div><strong>检测特征:</strong></div>
            <div class="features-list">
              <ul>
                ${result.detection.features.map(feature => `<li>${feature}</li>`).join('')}
              </ul>
            </div>
            
            <div style="margin-top: 15px;"><strong>解析策略:</strong></div>
            <div class="result-content">编码顺序: ${result.strategy.encodings.join(', ')}
字节偏移: ${result.strategy.byteOffsets.map(o => '0x' + o.toString(16)).join(', ')}
特殊处理: ${result.strategy.specialHandling}</div>
            
            <div style="margin-top: 15px;"><strong>文本预览:</strong></div>
            <div class="result-content">${result.text.substring(0, 200)}${result.text.length > 200 ? '...' : ''}</div>
          </div>
        `;
      });
      
      comparisonDiv.innerHTML = comparisonHtml;
    }

    function displaySingleTestResult(result, originalText) {
      const detectionDiv = document.getElementById('detectionResult');
      const comparisonDiv = document.getElementById('comparisonResults');
      
      detectionDiv.innerHTML = `
        <h3>🧪 智能解析测试结果</h3>
        <div style="padding: 15px; background: white; border-radius: 8px;">
          <div><strong>检测软件:</strong> <span class="software-badge ${result.detection ? result.detection.software : 'unknown'}">${result.detection ? result.detection.software.toUpperCase() : 'UNKNOWN'}</span></div>
          <div><strong>解析方法:</strong> ${result.method}</div>
          <div><strong>解析策略:</strong> ${result.strategy || '未知'}</div>
          <div><strong>成功状态:</strong> ${result.success ? '✅ 成功' : '❌ 失败'}</div>
        </div>
      `;
      
      comparisonDiv.innerHTML = `
        <div class="result-panel">
          <h4>📄 解析结果</h4>
          <div class="result-content">${result.text}</div>
        </div>
        
        <div class="result-panel">
          <h4>📊 详细报告</h4>
          <div class="result-content">${result.report || '无详细报告'}</div>
        </div>
      `;
    }

    function showStatus(type, message) {
      const container = document.querySelector('.container');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      
      container.insertBefore(statusDiv, container.firstChild);
      
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function clearAll() {
      document.getElementById('wpsText').value = '';
      document.getElementById('wordText').value = '';
      document.getElementById('resultsSection').style.display = 'none';
      showStatus('info', '已清空所有内容');
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof DocSoftwareDetector !== 'undefined' && typeof DocSmartParser !== 'undefined') {
        showStatus('success', '页面加载完成，可以开始对比测试');
        // 自动加载示例文本
        loadSampleTexts();
      } else {
        showStatus('error', '检测器未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
