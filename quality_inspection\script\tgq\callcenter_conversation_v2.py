import re
import traceback
import requests
from typing import Dict, Any, Optional, Union
import json


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def fillMessageDetail(conversations: list, appCode: str, appToken: str) -> None:
    """
    遍历对话列表，为每个对话获取详细消息并填充到messages字段

    Args:
        conversations (list): 对话列表
        appCode (str): 应用代码
        appToken (str): 应用令牌
    """
    if not conversations:
        return

    for conversation in conversations:
        taskNo = conversation.get("taskNo")
        if not taskNo:
            continue

        # 查询消息明细
        result = queryMessagesByTaskNo(appCode, appToken, taskNo)
        if result["success"]:
            conversation["messages"] = result["data"]
        else:
            conversation["messages"] = []
            print(f"获取对话 {taskNo} 的消息明细失败: {result['error']}")


def getConversation(
    appCode: str, appToken: str, flowNo: str, orderNo: str, targetType: str
) -> Dict[str, Any]:
    """
    获取客服对话记录

    Args:
        flowNo (str): 流程编号
        fromSID (str): 来源会话ID
        orderNo (str): 订单号
        bizNo (str): 业务编号

    Returns:
        Dict[str, Any]: 包含以下字段的字典:
            - success (bool): 是否成功
            - error (str): 错误信息，成功时为None
            - data (Dict): 解析后的JSON数据，失败时为None
    """
    proxyData = {
        "method": "get",
        "url": f"http://fuwu.qunar.com/callcenter/sopmsg/msg/list?domain=callcenter.qunar.com&flowNo={flowNo}&orderNo={orderNo}&intl=false&targetType={targetType}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": []}

        conversations = inner_data.get("conversations")
        if conversations:
            fillMessageDetail(conversations, appCode, appToken)

        return {"success": True, "error": "", "data": conversations}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def queryMessagesByTaskNo(appCode: str, appToken: str, taskNo: str) -> Dict[str, Any]:
    """
    根据任务号查询消息列表

    Args:
        appCode (str): 应用代码
        appToken (str): 应用令牌
        taskNo (str): 任务编号

    Returns:
        Dict[str, Any]: 包含以下字段的字典:
            - success (bool): 是否成功
            - error (str): 错误信息，成功时为None
            - data (Dict): 解析后的JSON数据，失败时为None
    """
    proxyData = {
        "method": "get",
        "url": f"http://fuwu.qunar.com/qbcp/conversation/view/list?domain=callcenter.qunar.com&taskNo={taskNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": []}
        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def main(param: dict) -> dict:
    try:
        flowNo = param.get("flowNo")
        orderNo = param.get("orderNo")
        appCode = param.get("invokeAppCode")
        invokeToken = param.get("invokeToken")

        agentResult = getConversation(appCode, invokeToken, flowNo, orderNo, "代理商")
        airLineResult = getConversation(appCode, invokeToken, flowNo, orderNo, "航司")

        # 收集错误信息
        error_messages = []
        if not agentResult["success"]:
            error_messages.append(f"代理对话获取失败: {agentResult['error']}")
        if not airLineResult["success"]:
            error_messages.append(f"航司对话获取失败: {airLineResult['error']}")

        # 如果有任何错误，拼接错误信息
        error_msg = "\n".join(error_messages) if error_messages else ""
        isSuccess = agentResult.get("success") or airLineResult.get("success")
        result = {
            "isSuccess": "true" if isSuccess else "false",
            "errorMsg": error_msg,
            "agentConversation": agentResult.get("data"),
            "airLineConversation": airLineResult.get("data"),
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"获取客服对话记录异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {
            "isSuccess": "false",
            "errorMsg": error_msg,
            "agentConversation": [],
            "airLineConversation": [],
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# Example usage
if __name__ == "__main__":
    input = {
        "flowNo": "JF250207095432726175828",
        "orderNo": "acj250207095432726",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg=",
    }
    result = main(input)
    print(result)
    write_json_to_file(result, "quality_inspection/data/get_conversation_result.json")
