from string import Formatter
import requests
import time  # 添加time模块用于等待
import json
import base64
from datetime import datetime, timed<PERSON>ta
from csv import Dict<PERSON><PERSON>er, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>
from io import StringIO
import uuid
from typing import List, Dict, Any


def airport_to_city(key):
    return key


TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)


def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())


def count_distinct_users(items: List[Dict[str, Any]]) -> int:
    """
    统计过滤后数据中不同user_name的数量

    :param items: 经过过滤的字典列表
    :return: 去重后的用户数量（包含空值检测）

    >>> test_data = [
    ...    {"user_name": "Alice", "urs_flightNos": "AB123"},
    ...    {"user_name": "<PERSON>", "urs_flightNos": "CD456"},
    ...    {"user_name": "Alice", "urs_flightNos": "EF789"},
    ...    {"user_name": ""},  # 空用户名
    ...    {"user_name": None},  # None值
    ...    {"no_name_field": "data"}  # 缺失字段
    ... ]
    >>> count_distinct_users(test_data)
    3
    """
    if not isinstance(items, list):
        return 0

    seen_users = set()
    missing_count = 0
    empty_count = 0

    for idx, item in enumerate(items, 1):
        if not isinstance(item, dict):
            continue

        # 安全获取并标准化用户名
        username = item.get("user_name")

        # 处理空值情况
        if username is None:
            missing_count += 1
            continue

        # 转换为字符串并去除首尾空格
        cleaned_name = str(username).strip()

        if not cleaned_name:
            empty_count += 1
            continue

        seen_users.add(cleaned_name)

    return seen_users


def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        return
    except KeyError as e:
        return


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )

    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None


# http://************:8080/adhoc/executor/download/data?taskId=4710824
# http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId=4710824


def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"GenrecordId": generateId()})
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


URS_DATA_SYNC_SQL = """
SELECT
  p.uid,
  f.user_name,
  f.ext_json,
  f.page_json,
  f.carrier,
  f.dep_time,
  f.arr_time,
  f.dep_city,
  f.arr_city,
  f.create_time,
  p.orig_flight_type,
  is_bianjia,
  count(*) as compareCount
FROM
  flight.dwd_flow_dom_usertouch_ups_new_detail_di f
  LEFT JOIN flight.dwd_urs_price_changes_detail_di p ON f.user_name = p.qunar_username
WHERE
  f.dt = '{ursDate}'
  AND f.questionnaire_id in ('877')
  AND f.is_stable in ('不稳定')
  AND f.flag = 'UPS一致率'
  AND p.dt = '{ursDate}'
group by
  f.user_name,
  f.ext_json,
  f.page_json,
  f.carrier,
  f.dep_time,
  f.arr_time,
  f.dep_city,
  f.arr_city,
  f.create_time,
  p.orig_flight_type,
  is_bianjia
"""

URS_DATA_SYNC_SQL_WITH_USERNAME = """
SELECT
  p.uid,
  f.user_name,
  f.ext_json,
  f.page_json,
  f.carrier,
  f.dep_time,
  f.arr_time,
  f.dep_city,
  f.arr_city,
  f.create_time,
  p.orig_flight_type,
  is_bianjia,
  count(*) as compareCount
FROM
  flight.dwd_flow_dom_usertouch_ups_new_detail_di f
  LEFT JOIN flight.dwd_urs_price_changes_detail_di p ON f.user_name = p.qunar_username
WHERE
  f.dt = '{ursDate}'
  AND f.questionnaire_id in ('877')
  AND f.is_stable in ('不稳定')
  AND f.flag = 'UPS一致率'
  AND p.dt = '{ursDate}'
  AND f.user_name = '{username}'
group by
  f.user_name,
  f.ext_json,
  f.page_json,
  f.carrier,
  f.dep_time,
  f.arr_time,
  f.dep_city,
  f.arr_city,
  f.create_time,
  p.orig_flight_type,
  is_bianjia
"""


def buildSqlByQuery(ursData, username, extParam):

    #####
    sql = URS_DATA_SYNC_SQL

    try:
        params = {}
        params["ursDate"] = ursData

        if username:
            params["username"] = username
            sql = URS_DATA_SYNC_SQL_WITH_USERNAME
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def calculate_urs_statistics(
    ori_datas: List[Dict[str, Any]], ursDate: str
) -> List[Dict[str, Any]]:
    """
    Calculate statistics for URS data grouped by specific fields.

    Args:
        ori_datas: List of original data dictionaries

    Returns:
        List of dictionaries containing grouped statistics
    """
    # Initialize the grouping dictionary
    urs_statistic_info = {}

    for item in ori_datas:
        # Create grouping key from f table fields and parsed fields
        group_key = (
            item.get("user_name", ""),
            item.get("carrier", ""),
            item.get("dep_city", ""),
            item.get("arr_city", ""),
            item.get("dep_time", ""),
            item.get("arr_time", ""),
            item.get("urs_page", ""),
            item.get("urs_routeType", ""),
            item.get("urs_depAndArrTime", ""),
            item.get("create_time", ""),
            item.get("urs_flightNos", ""),
        )

        # Get or create group statistics
        if group_key not in urs_statistic_info:
            urs_statistic_info[group_key] = {
                "uuid": generateId(),
                "ursDate": ursDate,
                "user_name": item.get("user_name", ""),
                "uid": item.get("uid", ""),
                "carrier": item.get("carrier", ""),
                "dep_city": item.get("dep_city", ""),
                "arr_city": item.get("arr_city", ""),
                "dep_time": item.get("dep_time", ""),
                "arr_time": item.get("arr_time", ""),
                "urs_page": item.get("urs_page", ""),
                "urs_routeType": item.get("urs_routeType", ""),
                "urs_depAndArrTime": item.get("urs_depAndArrTime", ""),
                "create_time": item.get("create_time", ""),
                "urs_flightNos": item.get("urs_flightNos", ""),
                # Statistics for direct flights
                "directCount": 0,
                "directPriceChangeCount": 0,
                "hasDirectPriceChange": "否",
                # Statistics for round trips
                "roundTripCount": 0,
                "roundTripPriceChangeCount": 0,
                "hasRoundTripPriceChange": "否",
                # Total records in group
                "totalCompareCount": 0,
                "processStage": "URS-hive数据同步",
            }

        flight_type = item.get("orig_flight_type", "")
        is_bianjia = item.get("is_bianjia", "")
        compare_count = int(item.get("compareCount", 0))

        # Update statistics based on flight type
        if flight_type == "直飞":
            urs_statistic_info[group_key]["directCount"] += compare_count
            if is_bianjia == "是":
                urs_statistic_info[group_key]["directPriceChangeCount"] += compare_count
                urs_statistic_info[group_key]["hasDirectPriceChange"] = "是"
        elif flight_type == "往返":
            urs_statistic_info[group_key]["roundTripCount"] += compare_count
            if is_bianjia == "是":
                urs_statistic_info[group_key][
                    "roundTripPriceChangeCount"
                ] += compare_count
                urs_statistic_info[group_key]["hasRoundTripPriceChange"] = "是"

        # Update total compare count
        urs_statistic_info[group_key]["totalCompareCount"] += compare_count

    # Convert dictionary to list
    urs_statistics = list(urs_statistic_info.values())
    fillNeedPriceChangeAlField(urs_statistics)
    return urs_statistics


def fillNeedPriceChangeAlField(urs_statistics: List[Dict[str, Any]]) -> None:
    """
    Fill needExecAl and notExecAlReason fields based on specific conditions.

    Args:
        urs_statistics: List of dictionaries containing URS statistics

    The function modifies the dictionaries in place with the following rules:
    1. Default: needExecAl = "是", notExecAlReason = ""
    2. If totalCompareCount = 0: needExecAl = "否", notExecAlReason = "未查到需要对比价格的搜索记录"
    3. If totalCompareCount != 0 and directCount = 0:
       - needExecAl = "否"
       - notExecAlReason = "仅有往返记录，无单程搜索记录"
       - If hasRoundTripPriceChange = "是", append "有往返变价记录，目前不分析往返"
    4. If directCount != 0 and hasDirectPriceChange = "否":
       needExecAl = "否", notExecAlReason = "有单程搜索记录，但均未变价"
    """
    if not urs_statistics:
        return
    if not isinstance(urs_statistics, list):
        return

    for stat in urs_statistics:
        # Set default values
        stat["needExecAl"] = "是"
        stat["notExecAlReason"] = ""

        try:
            total_count = int(stat.get("totalCompareCount", 0))
            direct_count = int(stat.get("directCount", 0))
            has_direct_price_change = stat.get("hasDirectPriceChange", "否")
            has_round_trip_price_change = stat.get("hasRoundTripPriceChange", "否")

            # Case 1: No compare records
            if total_count == 0:
                stat["needExecAl"] = "否"
                stat["notExecAlReason"] = "未查到需要对比价格的搜索记录"
                continue

            # Case 2: Only round trip records
            if total_count != 0 and direct_count == 0:
                stat["needExecAl"] = "否"
                reason = "仅有往返记录，无单程搜索记录"
                if has_round_trip_price_change == "是":
                    reason += "；有往返变价记录，目前不分析往返"
                stat["notExecAlReason"] = reason
                continue

            # Case 3: Has direct flights but no price changes
            if direct_count != 0 and has_direct_price_change == "否":
                stat["needExecAl"] = "否"
                stat["notExecAlReason"] = "有单程搜索记录，但均未变价"
                continue

        except (ValueError, TypeError) as e:
            # Keep default values in case of error
            continue


def main(param):
    # query = json.loads(param.get("query"))
    sql = buildSqlByQuery(param.get("ursDate"), param.get("username"), None)
    # print(sql)
    oriDataResult = queryDataFromTamias(param.get("cookie"), sql)
    if oriDataResult.get("error"):
        oriDataResult["results"] = []
        return oriDataResult

    oriDatas = oriDataResult.get("results")

    # Calculate statistics for the data with error handling
    try:
        if not oriDatas or oriDatas == "当前条件未检索到数据":
            return {"error": "当前条件未检索到数据", "results": []}
        else:
            urs_statistics = calculate_urs_statistics(oriDatas, param.get("ursDate"))
            return {"error": "成功！", "results": urs_statistics}
    except Exception as e:
        return {"error": "统计用户URS信息计算失败", "results": []}


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象安全地序列化为 JSON 并写入文件

    :param obj: 要序列化的对象，支持基础类型/列表/字典/自定义对象
    :param file_path: 目标文件路径（支持自动创建目录）
    :param encoding: 文件编码格式，默认utf-8
    :param ensure_ascii: 是否转义非ASCII字符，默认False保留Unicode
    :param indent: JSON缩进空格数，默认2（设为None可压缩输出）
    :param default: 自定义对象序列化函数，默认使用__dict__转换
    :param json_kwargs: 其他json.dump参数（如sort_keys等）
    :return: 是否成功写入文件

    :raises IOError: 当遇到文件系统级错误时会抛出（非返回False的情况）

    异常处理策略：
    - 类型错误：打印建议信息并返回False
    - 权限错误：打印错误路径并返回False
    - 其他错误：打印错误信息并返回False
    """
    try:
        target_path = Path(file_path)

        # 自动创建父目录（exist_ok防止竞态条件）
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的序列化
        serialize_default = default or (
            lambda o: o.__dict__ if hasattr(o, "__dict__") else repr(o)
        )

        # 使用上下文管理器确保文件正确关闭
        with target_path.open("w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(
            f"[序列化失败] 对象类型错误: {str(e)}，建议：1.检查数据类型 2.提供自定义序列化函数"
        )
    except PermissionError:
        print(f"[权限拒绝] 无法写入文件: {file_path}，请检查文件权限")
    except json.JSONEncodeError as e:
        print(f"[编码错误] 非法数据: {str(e)}，请检查特殊字符")
    except Exception as e:
        print(f"[系统错误] 操作异常: {str(e)}")

    return False


from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
import datetime


def write_dicts_to_excel(
    data: list[dict],
    filename: str = "output.xlsx",
    sheet_name: str = "Data",
    autofit: bool = True,
) -> None:
    """
    将字典列表写入Excel文件

    :param data: 字典数据列表，要求所有字典的键一致
    :param filename: 输出文件名（默认：output.xlsx）
    :param sheet_name: 工作表名称（默认：Data）
    :param autofit: 是否自动调整列宽（默认：True）
    """
    if not data:
        raise ValueError("输入数据不能为空列表")

    # 验证数据结构
    keys = data[0].keys()
    if any(d.keys() != keys for d in data[1:]):
        raise ValueError("字典字段名不一致")

    # 创建Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = sheet_name

    # 写入表头（带格式）
    header_font = Font(bold=True)
    for col_num, key in enumerate(keys, 1):
        cell = ws.cell(row=1, column=col_num, value=str(key))
        cell.font = header_font

    # 写入数据
    for row_num, record in enumerate(data, 2):  # 从第2行开始
        for col_num, key in enumerate(keys, 1):
            value = record.get(key, "")

            # 特殊类型处理
            if isinstance(value, datetime.datetime):
                value = value.replace(tzinfo=None)  # 移除时区信息
            elif isinstance(value, (list, dict)):
                value = str(value)  # 复杂对象转为字符串

            ws.cell(row=row_num, column=col_num, value=value)

    # 自动调整列宽
    if autofit:
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    cell_length = len(str(cell.value))
                    # 加粗表头需要额外长度补偿
                    if cell.row == 1 and cell.font.bold:
                        cell_length += 2
                    max_length = max(max_length, cell_length)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

    # 保存文件
    try:
        wb.save(filename)
        print(f"文件已成功保存至：{filename}")
    except PermissionError:
        raise RuntimeError("文件被其他程序占用，请关闭Excel后重试")
    except Exception as e:
        raise RuntimeError(f"保存文件时发生错误：{str(e)}")


if __name__ == "__main__":
    # 假设输入数据存储在input_data变量中
    param = {
        "cookie": "QN1=00014b0014346c74ca982920; cookie=wanzhou.zheng&744122&8A2EE47906AB79B2BEA42F3838EEA290; JSESSIONID=82B0396E4DA55F340BEC8276A78DFB93",
        "ursDate": "2025-04-16",
        # "username": "qunar_lbs_2103708873",
    }
    reuslt = main(param)
    allUsers = count_distinct_users(reuslt.get("results"))
    print(
        "获取记录条数:"
        + str(len(reuslt.get("results")))
        + "用户数量："
        + str(len(allUsers))
    )

    write_dicts_to_excel(
        reuslt.get("results"),
        filename="urs_multi_search/data/URS_ZF_20250317_URS_INFO.xlsx",
        sheet_name="URS用户数据",
    )
