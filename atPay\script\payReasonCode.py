from dataclasses import dataclass
from typing import List, Optional
from enum import Enum


class AttributionLevel(Enum):
    FIRST = 1
    SECOND = 2
    THIRD = 3


@dataclass
class AttributionNode:
    description: str
    priority: int
    children: List["AttributionNode"] = None
    parent: Optional["AttributionNode"] = None

    def __post_init__(self):
        if self.children is None:
            self.children = []


class AttributionSystem:
    def __init__(self):
        self.root = AttributionNode("Root", 0)
        self._initialize_attribution_tree()

    def _initialize_attribution_tree(self):
        # First level attributions
        platform_responsible = AttributionNode("点击支付时-平台有责", 0)
        platform_not_responsible = AttributionNode("点击支付时-平台无责", 1)
        unmatched_scenario = AttributionNode("未匹配用户场景", 2)

        # Second level attributions for platform_responsible
        pre_payment_change = AttributionNode("支付前校验变价", 0)
        order_product_change = AttributionNode("生单商品变化", 1)
        platform_responsible.children = [pre_payment_change, order_product_change]

        # Second level attributions for platform_not_responsible
        second_purchase = AttributionNode("挽留二次加购", 0)
        multi_quote_change = AttributionNode("多报价生单变价", 1)
        platform_not_responsible.children = [second_purchase, multi_quote_change]

        # Second level attributions for unmatched_scenario
        no_price_change = AttributionNode("用户下单未变价", 0)
        no_order = AttributionNode("用户未下单", 1)
        unmatched_scenario.children = [no_order, no_price_change]

        # Third level attributions for multi_quote_change
        insufficient_seats = AttributionNode("余位不足", 0)
        passenger_limit = AttributionNode("乘机人数限制", 1)
        age_limit = AttributionNode("年龄限制", 2)
        other_reasons = AttributionNode("其它", 3)
        multi_quote_change.children = [
            insufficient_seats,
            passenger_limit,
            age_limit,
            other_reasons,
        ]

        # Third level attributions for order_product_change
        marketing_loss = AttributionNode("营销丢失", 0)
        ancillary_loss = AttributionNode("辅营商品丢失", 1)
        order_product_change.children = [marketing_loss, ancillary_loss]

        # Set up the tree structure
        self.root.children = [
            platform_responsible,
            platform_not_responsible,
            unmatched_scenario,
        ]

    def get_priority(self, level: AttributionLevel, description: str) -> int:
        """Get the priority of a specific attribution description at a given level"""
        if level == AttributionLevel.FIRST:
            for node in self.root.children:
                if node.description == description:
                    return node.priority
        elif level == AttributionLevel.SECOND:
            for parent in self.root.children:
                for node in parent.children:
                    if node.description == description:
                        return node.priority
        elif level == AttributionLevel.THIRD:
            for parent in self.root.children:
                for second_level in parent.children:
                    for node in second_level.children:
                        if node.description == description:
                            return node.priority
        return 999  # Default priority for unmatched descriptions

    def get_highest_priority_attribution(
        self, level: AttributionLevel, descriptions: List[str]
    ) -> str:
        """Get the highest priority attribution description from a list of descriptions at a given level"""
        if not descriptions:
            return None

        min_priority = float("inf")
        result_description = None

        for description in descriptions:
            priority = self.get_priority(level, description)
            if priority < min_priority:
                min_priority = priority
                result_description = description

        return result_description
