from datetime import datetime, date, timedelta
import json
import re
import time
from typing import Any, Dict, Optional, Union
import requests


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def safe_parse_datetime(
    datetime_str: str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime.datetime类型
    """
    if not datetime_str:
        return None

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except ValueError:
            continue

    return None


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def queryQualityCheckInfo(
    appCode: str, appToken: str, orderNo: str, createDate: str, creatorName: str
) -> Dict[str, Any]:
    """
    查询质检信息
    Args:
        orderNo: 订单号
        createTimeStart: 创建时间开始
        createTimeEnd: 创建时间结束
    Returns:
        Dict: 包含质检信息的字典
    """
    results = []
    page_index = 1
    start = 0
    last_index = 1

    while True:
        # 构建请求URL
        url = (
            f"https://fuwu.qunar.com/qualityCheck/taskManage/QCInfoQuery?"
            f"domain=callcenter.qunar.com&businessType=1&orderNo={orderNo}&"
            f"qualityCheckNo=&curtStep=-1&createTimeStart={createDate}&"
            f"createTimeEnd={createDate}&agent=&problemTypeLevel2=&"
            f"endTimeStart=&endTimeEnd=&operatorName=&orderSource=-1&"
            f"responsible=-1&compensateFlag=-1&confirmPenalty=-1&"
            f"paymentTimeStart=&paymentTimeEnd=&paymentStatus=-1&"
            f"paymentType=-1&paymentNo=&creatorName={creatorName}&curtStatus=0&"
            f"forcePenalty=-1&fenxiao=-1&_v=1744618148900&limit=20&"
            f"pageIndex={page_index}&start={start}&lastIndex={last_index}"
        )

        proxyData = {
            "method": "get",
            "url": url,
            "data": "",
            "dataType": "form-data",
            "authType": "qsso",
            "qssAuthParam": {
                "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
                "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
            },
        }

        proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

        try:
            result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
            # 检查是否有错误
            if "error" in result:
                return {"error": result["error"], "results": []}

            # 尝试解析data字段为JSON
            response_data = safe_json_parse(result.get("data"))

            # 检查ret字段和data字段
            if not response_data or response_data.get("ret") is False:
                error_msg = (
                    response_data.get("errmsg")
                    if response_data
                    else "Response data is empty or ret is false"
                )
                return {"error": error_msg, "results": []}

            # 获取内层data
            inner_data = response_data.get("data")
            if not inner_data:
                return {"error": "Inner data is empty", "results": []}

            total_count = inner_data.get("totalCount", 0)
            items = inner_data.get("list", [])
            # Filter items with problemTypeStr='不认可退改规则'
            filtered_items = [
                {
                    "orderNo": item.get("orderNo"),
                    "qualityCheckNo": item.get("qualityCheckNo"),
                    "complaintNo": item.get("complaintNo"),
                    "createTime": item.get("createTime"),
                    "problemTypeStr": item.get("problemTypeStr"),
                    "creatorName": item.get("creatorName")
                }
                for item in items
                if item.get("problemTypeStr") == "不认可退改规则"
            ]
            results.extend(filtered_items)    

            # Check if we've processed all items
            if start + 20 >= total_count:
                break

            # Update pagination parameters
            page_index += 1
            start += 20
            last_index = page_index - 1

            # Add 1 second delay between requests
            time.sleep(1)

        except Exception as e:
            return {"error": str(e), "results": []}

    return {"error": "", "results": results}


def main(param: Dict[str, str]) -> Dict[str, Any]:
    """
    主入口函数
    """
    # 从参数中提取必要的字段
    orderNo = param.get("orderNo", "")
    createDate = param.get("createDate", "")
    appCode = param.get("invokeAppCode")
    invokeToken = param.get("invokeToken")
    creatorName = param.get("creatorName")

    calDate = None
    # 检查参数是否完整
    if not createDate:
        calDate = datetime.now()
    else:
        calDate = safe_parse_datetime(createDate)

    # 将createDate减1天并格式化为YYYY-MM-DD
    actDate = (calDate - timedelta(days=1)).strftime("%Y-%m-%d")

    # 发起请求并获取数据
    return queryQualityCheckInfo(appCode, invokeToken, orderNo, actDate, creatorName)


def test():
    param = {
        "orderNo": "",
        "createDate": "2025/04/16 11:19",
        "invokeAppCode": "f_pangu",
        "invokeToken": "oABPAGnqeGY7y8OtsKjLiHQ2K1SzBuOLT8zYWyHCnviQX+KqugKDLwDcGpD8qRXSGTz2BFiDyWrwmI1otiXHGjlAIndwbOQh3LdaHBI7NR6s6G04LJB3fUFQHpVpjC4ISGT7gjw8IgiGR+b+ln/t0BsdLbkp1ZeHRbCZcgLw2Z0=",
        "creatorName": "SYSTEM",
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    test()
