import re
import time
import traceback
from urllib.parse import unquote_to_bytes
import requests
from typing import Dict, Any, List, Optional, Union
import json




def getHandleInfoList(appCode: str, appToken: str, qualityCheckNo: str) -> Dict[str, Any]:
    """
    获取质检单处理信息列表
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        qualityCheckNo: 质检单号
        
    Returns:
        Dict: 包含接口返回结果的字典
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/qualityCheck/handle/handleInfoList?isFlatAccount=&domain=callcenter.qunar.com&businessType=-1&orderNo=&qualityCheckNo={qualityCheckNo}&curtStatus=-1&createTimeStart=&createTimeEnd=&endTimeStart=&endTimeEnd=&problemTypeLevel2=-1&agent=&confirmPenalty=-1&forcePenalty=-1",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": None}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": None}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": None}

        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": None}

def getConfirmList(appCode: str, appToken: str, confirmTaskId: str) -> Dict[str, Any]:
    """
    获取确认任务列表
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        confirmTaskId: 确认任务ID
        
    Returns:
        Dict: 包含接口返回结果的字典
    """
    proxyData = {
        "method": "post",
        "url": "https://fuwu.qunar.com/gongdan/responsibilityConfirm/confirmList",
        "data": {
            "confirmTaskId": confirmTaskId,
            "isOffset": 0,
            "domain": "callcenter.qunar.com"
        },
        "dataType": "json",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": None}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": None}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": None}

        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": None}

def queryQualityCheckInfo(orderNo: str, createDate: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    查询质检信息
    Args:
        orderNo: 订单号
        createTimeStart: 创建时间开始
        createTimeEnd: 创建时间结束
    Returns:
        Dict: 包含质检信息的字典
    """
    results = []
    page_index = 1
    start = 0
    last_index = 1

    while True:
        # 构建请求URL
        url = (
            f"https://fuwu.qunar.com/qualityCheck/taskManage/QCInfoQuery?"
            f"domain=callcenter.qunar.com&businessType=1&orderNo={orderNo}&"
            f"qualityCheckNo=&curtStep=-1&createTimeStart={createDate}&"
            f"createTimeEnd={createDate}&agent=&problemTypeLevel2=-1&"
            f"endTimeStart=&endTimeEnd=&operatorName=&orderSource=-1&"
            f"responsible=-1&compensateFlag=-1&confirmPenalty=-1&"
            f"paymentTimeStart=&paymentTimeEnd=&paymentStatus=-1&"
            f"paymentType=-1&paymentNo=&creatorName=&curtStatus=-1&"
            f"forcePenalty=-1&fenxiao=-1&_v={int(time.time() * 1000)}&limit=20&"
            f"pageIndex={page_index}&start={start}&lastIndex={last_index}"
        )

        proxyData = {
            "method": "get",
            "url": url,
            "data": "",
            "dataType": "form-data",
            "authType": "qsso",
            "qssAuthParam": {
                "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
                "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
            },
        }

        proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

        try:
            result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
            # 检查是否有错误
            if "error" in result:
                return {"error": result["error"], "results": []}

            # 尝试解析data字段为JSON
            response_data = safe_json_parse(result.get("data"))

            # 检查ret字段和data字段
            if not response_data or response_data.get("ret") is False:
                error_msg = (
                    response_data.get("errmsg")
                    if response_data
                    else "Response data is empty or ret is false"
                )
                return {"error": error_msg, "results": []}

            # 获取内层data
            inner_data = response_data.get("data")
            if not inner_data:
                return {"error": "Inner data is empty", "results": []}

            total_count = inner_data.get("totalCount", 0)
            items = inner_data.get("list", [])
            # Filter items with problemTypeStr='不认可退改规则'
            results.extend(items)

            # Check if we've processed all items
            if start + 20 >= total_count:
                break

            # Update pagination parameters
            page_index += 1
            start += 20
            last_index = page_index - 1

            # Add 1 second delay between requests
            time.sleep(1)

        except Exception as e:
            return {"error": str(e), "results": []}

    return {"error": "", "results": results}


def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def needUnlockBeforeOpr(qualityCheckModel: Any) -> bool:
    operationTodo = qualityCheckModel.get("operationTodo")
    if not operationTodo:
        return False
    if (
        isinstance(operationTodo, list)
        and len(operationTodo) > 0
        and "UNLOCK" in operationTodo
    ):
        return True
    return False

def needProcessBeforeOpr(qualityCheckModel: Any) -> bool:
    operationTodo = qualityCheckModel.get("operationTodo")
    if not operationTodo:
        return False
    if (
        isinstance(operationTodo, list)
        and len(operationTodo) > 0
        and "PROCESSOR" in operationTodo
    ):
        return True
    return False


def queryQualityCheckByNo(
    appCode: str, appToken: str, qualityCheckNo: str
) -> Dict[str, Any]:
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/qualityCheck/handle/handleInfoQuery?domain=callcenter.qunar.com&qualityCheckNo={qualityCheckNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": []}

        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def unlockQualityCheckByNo(
    appCode: str, appToken: str, qualityCheckNo: str
) -> Dict[str, Any]:
    proxyData = {
        "method": "get",
        "url": f"http://fuwu.qunar.com/qualityCheck/QCprocessor/unlock?domain=callcenter.qunar.com&qualityCheckNo={qualityCheckNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        
        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        return {"success": True, "error": "", "data": []}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def createConfirmTask(appCode: str, appToken: str, dataJson: Any) -> Dict[str, Any]:
    """
    获取客服对话记录

    Args:
        flowNo (str): 流程编号
        fromSID (str): 来源会话ID
        orderNo (str): 订单号
        bizNo (str): 业务编号

    Returns:
        Dict[str, Any]: 包含以下字段的字典:
            - success (bool): 是否成功
            - error (str): 错误信息，成功时为None
            - data (Dict): 解析后的JSON数据，失败时为None
    """
    proxyData = {
        "method": "post",
        "url": f"https://fuwu.qunar.com/qualityCheck/responsibilityConfirm/createConfirmTask?domain=callcenter.qunar.com",
        "data": dataJson,
        "dataType": "json",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        return {"success": True, "error": "", "data": []}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def toAgent(appCode: str, appToken: str, toAgentParams: dict) -> Dict[str, Any]:
    """
    调用质检转客服接口
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        toAgentParams: 请求参数
        
    Returns:
        Dict: 包含接口返回结果的字典
    """
    # 构建URL参数
    params = [
        "domain=callcenter.qunar.com",
        f"agent={toAgentParams.get('agent', '')}",
        f"orderNo={toAgentParams.get('orderNo', '')}",
        f"compensateType={toAgentParams.get('compensateType', '')}",
        f"addSummary={toAgentParams.get('addSummary', '')}",
        f"isValid={toAgentParams.get('isValid', '')}",
        f"problemTypeLevel2={toAgentParams.get('problemTypeLevel2', '')}",
        f"responsibleExt={toAgentParams.get('responsibleExt', '')}",
        f"transferTo={toAgentParams.get('transferTo', '')}",
        f"points={toAgentParams.get('points', '')}",
        f"qualityCheckNo={toAgentParams.get('qualityCheckNo', '')}",
        f"compensateMoney={toAgentParams.get('compensateMoney', '')}",
        f"responsible={toAgentParams.get('responsible', '')}",
        f"name={toAgentParams.get('name', '')}",
        f"comment={toAgentParams.get('comment', '')}",
        f"penaltyMoney={toAgentParams.get('penaltyMoney', '')}",
        f"problemType={toAgentParams.get('problemType', '')}"
    ]
    url = f"https://fuwu.qunar.com/qualityCheck/QCprocessor/toAgent?{'&'.join(params)}"

    proxyData = {
        "method": "get",
        "url": url,
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))
        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        return {"success": True, "error": "", "data": []}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}


def processUnlockAndToAgent(
    appCode: str, 
    appToken: str, 
    qualityCheckNo: str, 
    qualityCheckModel: Dict[str, Any]
) -> Dict[str, Any]:
    """
    处理解锁和转客服逻辑
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        qualityCheckNo: 原质检单号
        qualityCheckModel: 质检单模型
        
    Returns:
        Dict: 包含处理结果的字典
    """
    # 1. 调用queryQualityCheckInfo，日期为空，订单号为qualityCheckModel中"orderNo"
    orderNo = qualityCheckModel.get("orderNo")
    if not orderNo:
        return {
            "isSuccess": "false",
            "errorMsg": "质检单中未找到订单号",
        }
    
    checkInfoResult = queryQualityCheckInfo(orderNo, "", appCode, appToken)
    if checkInfoResult.get("error"):
        return {
            "isSuccess": "false",
            "errorMsg": f"查询质检信息失败: {checkInfoResult.get('error')}",
        }

    # 2. 循环遍历结果，查找备注中包含原质检单号的记录
    results = checkInfoResult.get("results", [])
    if not results:
        return {
            "isSuccess": "false",
            "errorMsg": "未找到相关质检单",
        }

    # 记录是否找到匹配的记录
    foundMatch = False
    lastError = None

    for result in results:
        newQualityCheckNo = result.get("qualityCheckNo")
        if not newQualityCheckNo:
            continue

        # 调用queryQualityCheckByNo方法
        newQueryResult = queryQualityCheckByNo(appCode, appToken, newQualityCheckNo)
        if not newQueryResult["success"]:
            lastError = f"查询新质检单失败: {newQueryResult.get('error')}"
            continue

        # 检查remark是否包含原质检单号
        newQualityCheckModel = newQueryResult.get("data")
        remark = str(newQualityCheckModel.get("remark", ""))
        if qualityCheckNo not in remark:
            continue

        # 检查是否需要解锁
        if needUnlockBeforeOpr(newQualityCheckModel):
            unlockResult = unlockQualityCheckByNo(appCode, appToken, newQualityCheckNo)
            if not unlockResult["success"]:
                lastError = f"解锁新质检单失败: {unlockResult.get('error')}"
                continue
        elif not needProcessBeforeOpr(newQualityCheckModel):
            lastError = f"新质检单不需要处理:状态不是PROCESSOR"
            continue

        newQualityCheck = newQualityCheckModel.get("qualityCheck")
        if not newQualityCheck:
            lastError = f"新质检单中没有qualityCheck"
            continue
        agent = newQualityCheckModel.get("agent", "")
        compensateType = newQualityCheck.get("compensateType", "1")
        points = newQualityCheck.get("points", "0")
        compensateMoney = newQualityCheck.get("compensateMoney", "0")
        responsible = newQualityCheck.get("responsible", "2")
        penaltyMoney = newQualityCheck.get("penalty", "0")
        problemType = newQualityCheck.get("problemType", "")

        # 准备toAgent参数
        toAgentParams = {
            "agent": agent,
            "orderNo": orderNo,
            "compensateType": compensateType,
            "addSummary": "",
            "isValid": "1",
            "problemTypeLevel2": "p_2_13",
            "responsibleExt": "1",
            "transferTo": "",
            "points": points,
            "qualityCheckNo": newQualityCheckNo,
            "compensateMoney": compensateMoney,
            "domain": "callcenter.qunar.com",
            "responsible": responsible,
            "name": "1",
            "comment": "用户投诉退票手续费/改签手续费与航司不一致，协商赔付，损失需贵司承担",
            "penaltyMoney": penaltyMoney,
            "problemType": problemType
        }

        # 3. 调用toAgent接口
        toAgentResult = toAgent(appCode, appToken, toAgentParams)
        if not toAgentResult["success"]:
            lastError = f"转代理失败: {toAgentResult.get('error')}"
            continue

        # 标记找到匹配记录
        foundMatch = True
        break

    # 根据是否找到匹配记录返回结果
    if foundMatch:
        return {
            "isSuccess": "true",
            "errorMsg": "",
            "data": toAgentResult.get("data"),
        }
    else:
        return {
            "isSuccess": "false",
            "errorMsg": lastError or "分拆成功，转代理失败：未找到备注中包含原质检单号的新质检单",
        }


def init(appCode: str, appToken: str, qualityCheckNo: str, qualityCheckModel: Dict[str, Any]) -> Dict[str, Any]:
    """
    调用calculateFactor/init接口获取计算因子信息
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        qualityCheckNo: 质检单号
        qualityCheckModel: 质检单模型
        
    Returns:
        Dict: 包含接口返回结果的字典
    """
    orderNo = qualityCheckModel.get("orderNo")
    problemType = qualityCheckModel.get("qualityCheck",{}).get("problemType")
    
    if not orderNo or not problemType:
        return {
            "success": False,
            "error": "质检单中缺少orderNo或problemType",
            "data": None
        }
    
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/qualityCheck/calculateFactor/init?domain=callcenter.qunar.com&orderNo={orderNo}&qualityCheckNo={qualityCheckNo}&problemTypeLevel2={problemType}&businessType=1",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": None}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": None}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": None}

        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": None}


def checkAndFillSubmitData(responsibilityRows: List[Dict[str, Any]], initResult: Dict[str, Any],qualityCheckModel: Dict[str, Any], appCode: str, appToken: str) -> Dict[str, Any]:
    """
    校验并填充提交数据
    
    Args:
        submitData: 提交的数据列表
        initResult: 初始化结果
        qualityCheckModel: 质检单模型
        appCode: 应用代码
        appToken: 应用token
        
    Returns:
        Dict: 包含处理结果的字典
    """
    try:
        # 1. 调用handleInfoList接口获取confirmTaskId
        qualityCheckNo = qualityCheckModel.get("qualityCheck",{}).get("qualityCheckNo")
        if not qualityCheckNo:
            return {
                "status": "error",
                "message": "质检单号不能为空",
                "data": None
            }
            
        handleInfoResult = getHandleInfoList(appCode, appToken, qualityCheckNo)
        if not handleInfoResult["success"]:
            return {
                "status": "error",
                "message": f"获取质检单处理信息失败: {handleInfoResult.get('error')}",
                "data": None
            }
            
        # 获取第一条记录的confirmTaskId
        handleInfoList = handleInfoResult["data"].get("list", [])
        if not handleInfoList:
            return {
                "status": "error",
                "message": "未找到质检单处理信息",
                "data": None
            }
            
        confirmTaskId = handleInfoList[0].get("confirmTaskId")
        if not confirmTaskId:
            return {
                "status": "error",
                "message": "未找到confirmTaskId",
                "data": None
            }
            
        # 2. 调用confirmList接口获取详细信息
        confirmListResult = getConfirmList(appCode, appToken, confirmTaskId)
        if not confirmListResult["success"]:
            return {
                "status": "error",
                "message": f"获取确认任务列表失败: {confirmListResult.get('error')}",
                "data": None
            }
            
        # 获取第一条记录的详细信息
        confirmList = confirmListResult["data"].get("list", [])
        if not confirmList:
            return {
                "status": "error",
                "message": "未找到确认任务信息",
                "data": None
            }
            
        confirmInfo = confirmList[0]
        
        # 3. 校验responsibilityRows
        if not responsibilityRows or not isinstance(responsibilityRows, list):
            return {
                "status": "error",
                "message": "responsibilityRows必须是非空列表",
                "data": None
            }
            
        # 4. 计算totalPrice并校验
        total_compensation = 0
        confirmTaskCreateParamList = []
        
        for item in responsibilityRows:
            
            # 构建confirmTaskCreateParamList的每一项
            param_item = {
                "price": item.get("price", 0),
                "refundMarkMoney": item.get("refundMarkMoney", 0),
                "compensationMarkMoney": item.get("compensationMarkMoney", 0),
                "businessLine": item.get("businessLine", ""),
                "responsibleType": int(item.get("responsibleType")),
                "dutyPart": int(item.get("dutyPart")),
                "oneLevel": int(item.get("oneLevel")),
                "twoLevel": int(item.get("twoLevel")),
                "threeLevel": int(item.get("threeLevel")),
                "remark": qualityCheckNo,
            }
            confirmTaskCreateParamList.append(param_item)
            
        
        qualityCheck = qualityCheckModel.get("qualityCheck")
        if not qualityCheck:
            return {
                "status": "error",
                "message": "qualityCheck为空",
                "data": None
            }
        # 6. 构建返回数据
        result = {
            "confirmTaskCreateParamList": confirmTaskCreateParamList,
            # 2: 质检定责
            "createTaskType": 2,
            "totalPrice": qualityCheck.get("compensateMoney"),
            "orderNo": qualityCheckModel.get("orderNo"),
            "issueNo": qualityCheckModel.get("issueNo"),
            "qualityCheckNo": qualityCheck.get("qualityCheckNo"),
            "sourceQncNo": qualityCheck.get("qualityCheckNo"),
            "moneySplitCalculateInfo": json.dumps(initResult),
            "responsible": qualityCheck.get("responsible"),
            "isValid": 1,
            "penaltyMoney": qualityCheck.get("penalty"),
            "points": qualityCheck.get("points"),
            "compensateMoney": qualityCheck.get("compensateMoney"),
            "confirmTaskId": confirmTaskId,
            "compensationId": confirmInfo.get("compensationId"),
            "compensateRecordId": confirmInfo.get("compensateRecordId"),
            "sourceConfirmTaskId": confirmInfo.get("confirmTaskId"),
            "problemTypeLevel2": -1
        }
        return {
            "status": "success",
            "message": "",
            "data": result
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"处理提交数据时发生错误: {str(e)}",
            "data": {}
        }

def main(param: dict) -> dict:
    try:
        qualityCheckNo = param.get("qualityCheckNo")
        submitData = safe_json_parse(param.get("submitData"))
        appCode = param.get("invokeAppCode")
        invokeToken = param.get("invokeToken")

        queryResult = queryQualityCheckByNo(
            appCode, invokeToken, qualityCheckNo=qualityCheckNo
        )
        if not queryResult["success"]:
            result = {
                "isSuccess": "false",
                "errorMsg": f"查询质检单失败: {queryResult.get('error')}",
                "data": {}
            }
            return result
        

        qualityCheckModel = queryResult.get("data")
        if needUnlockBeforeOpr(qualityCheckModel):
            unlockQualityCheckByNo(appCode, invokeToken, qualityCheckNo=qualityCheckNo)

        # 调用init接口获取计算因子信息
        initResult = init(appCode, invokeToken, qualityCheckNo, qualityCheckModel)
        if not initResult["success"]:
            result = {
                "isSuccess": "false",
                "errorMsg": f"获取计算因子信息失败: {initResult.get('error')}",
                "data": {}
            }
            return result
        
        #检查、填充参数
        createConfirmTaskData = checkAndFillSubmitData(submitData.get("responsibilityRows"), initResult.get("data"),qualityCheckModel, appCode, invokeToken)

        if createConfirmTaskData["status"] != "success":
            result = {
                "isSuccess": "false",
                "errorMsg": f"质检结果录入数据非法: {createConfirmTaskData['message']}",
                "data": {}
            }   
            return result
        
        submitResult = createConfirmTask(appCode, invokeToken, createConfirmTaskData.get("data"))
        if not submitResult["success"]:
            result = {
                "isSuccess": "false",
                "errorMsg": f"质检结果录入: {submitResult.get('error')}",
                "data": {}
            }
            return result

        # 处理解锁和转客服逻辑
        unlockAndToAgentResult = processUnlockAndToAgent(
            appCode, 
            invokeToken, 
            qualityCheckNo, 
            qualityCheckModel
        )
        
        if unlockAndToAgentResult["isSuccess"] == "false":
            return unlockAndToAgentResult

        result = {
            "isSuccess": "true",
            "errorMsg": "成功",
            "data": {
                "submitData": submitResult.get("data"),
                "initData": initResult.get("data")
            }
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"质检结果录入: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {"isSuccess": "false", "errorMsg": error_msg}
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        return False
    except PermissionError:
        return False
    except Exception as e:
        return False