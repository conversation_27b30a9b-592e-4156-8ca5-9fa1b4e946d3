# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('markRecords'):
        return {
            "status": "error",
            "message": "缺少必要参数: markRecords",
            "data": None
        }
    
    # 校验其他必要参数
    required_fields = ['carrier', 'product_label', 'remark', 'process_key', 'scence']
    for field in required_fields:
        if not param.get(field):
            return {
                "status": "error",
                "message": f"缺少必要参数: {field}",
                "data": None
            }
    
    # 解析分析结果
    markRecords, parse_status = parse_urlencoded_structured_data(param['markRecords'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "markRecords 解析失败",
            "data": None
        }

    # 获取解析后的数据
    mark_data = markRecords[0] if markRecords else {}

    # 获取product_mark
    product_mark = mark_data.get("product_mark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "product_mark 不能为空",
            "data": None
        }
    
    # 使用increment_number函数对product_mark进行自增1操作
    try:
        new_mark = increment_number(product_mark)
    except ValueError as e:
        return {
            "status": "error",
            "message": f"product_mark自增失败: {str(e)}",
            "data": None
        }

    result_obj = {
        "product_mark": new_mark,
        "carrier": param.get('carrier'),
        "product_label": param.get('product_label'),
        "remark": param.get('remark'),
        "process_key": param.get('process_key'),
        "scence": param.get('scence')
    }
        
    # 构建结果对象
    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }


def increment_number(value, increment=1):
    """
    将输入的数字自增加指定值并返回整数结果
    
    参数:
        value: 数字或字符串类型的数字
        increment: 自增加的值，默认为1
        
    返回:
        自增加后的整数
        
    异常:
        ValueError: 当输入无法转换为数字时抛出
    """
    try:
        # 尝试将输入转换为浮点数
        num = float(value)
        # 自增加指定值
        result = num + increment
        # 始终返回整数结果
        return int(result)
    except (ValueError, TypeError):
        raise ValueError(f"无法将输入 '{value}' 转换为数字")



def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    
