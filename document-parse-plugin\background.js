// 存储窗口ID
let windowId = null;

// 初始化扩展
chrome.runtime.onInstalled.addListener(() => {
  console.log('文档解析助手已安装/更新');
});

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(() => {
  // 如果窗口已经打开，则聚焦到该窗口
  if (windowId !== null) {
    try {
      chrome.windows.get(windowId, { populate: true }, (window) => {
        if (chrome.runtime.lastError) {
          console.log('窗口不存在，创建新窗口:', chrome.runtime.lastError.message);
          windowId = null;
          createWindow();
        } else {
          console.log('窗口存在，聚焦到该窗口');
          chrome.windows.update(windowId, { focused: true });
        }
      });
    } catch (error) {
      console.error('获取窗口时出错:', error);
      windowId = null;
      createWindow();
    }
  } else {
    // 窗口未打开，创建新窗口
    console.log('创建新窗口');
    createWindow();
  }
});

// 创建新窗口
function createWindow() {
  const width = 800;  // 增加窗口宽度
  const height = 800; // 增加窗口高度

  try {
    // 创建窗口，让浏览器自动定位窗口位置
    chrome.windows.create({
      url: chrome.runtime.getURL('main.html'),
      type: 'popup',
      width: width,
      height: height
    }, (window) => {
      if (chrome.runtime.lastError) {
        console.error('创建窗口失败:', chrome.runtime.lastError.message);
        return;
      }

      if (window) {
        windowId = window.id;
        console.log('窗口已创建, ID:', windowId);
      } else {
        console.error('创建窗口失败: 未返回窗口对象');
      }
    });
  } catch (error) {
    console.error('创建窗口时出错:', error);
  }
}

// 监听窗口关闭事件
chrome.windows.onRemoved.addListener((removedWindowId) => {
  if (removedWindowId === windowId) {
    console.log('应用窗口已关闭');
    windowId = null;
  }
});

// 监听扩展卸载事件
chrome.runtime.onSuspend.addListener(() => {
  console.log('扩展即将被卸载');
  // 如果窗口还在打开状态，尝试关闭它
  if (windowId !== null) {
    try {
      chrome.windows.remove(windowId, () => {
        if (chrome.runtime.lastError) {
          console.error('关闭窗口失败:', chrome.runtime.lastError.message);
        }
      });
    } catch (error) {
      console.error('关闭窗口时出错:', error);
    }
  }
});
