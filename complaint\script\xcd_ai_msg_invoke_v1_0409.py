import json
from typing import Any, List, Tuple, Optional, Dict
from urllib.parse import quote


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj == ""
    return False


def parse_to_list(data: Any) -> Tuple[bool, Optional[List], str]:
    """
    将输入数据解析为列表格式
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否成功, 解析后的字典, 错误信息)
    """
    try:
        # 如果是字符串，尝试JSON解析
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                return False, None, "JSON解析失败"

        # 如果是字典，直接返回
        if isinstance(data, dict):
            return True, data, ""

        # 如果是列表，查找包含priceInfo的元素
        if isinstance(data, list):
            for item in data:
                if isinstance(item, list) and "priceInfo" in item:
                    return True, item, ""
            return False, None, "列表中未找到包含priceInfo的元素"

        return False, None, f"不支持的数据类型: {type(data)}"
    except Exception as e:
        return False, None, f"解析异常: {str(e)}"


def validate_list_structure(data: List) -> Tuple[bool, Optional[List], str]:
    """
    验证列表结构是否符合要求
    Args:
        data: 输入字典
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    try:
        if data is None:
            return False, None, "大模型返回数据为空"
        # 检查字典有效性
        if not isinstance(data, dict):
            return False, None, "大模型返回数据不是有效的字典"

        # 检查priceInfo字段
        if "priceInfo" not in data:
            return False, None, "大模型返回数据缺少priceInfo字段"

        priceInfo = data["priceInfo"]
        if not isinstance(priceInfo, list):
            return False, None, "大模型返回数据priceInfo不是有效的列表"

        # 检查priceInfo列表中的每个元素
        for item in priceInfo:
            if not isinstance(item, dict):
                return False, None, "priceInfo中的元素不是有效的字典"
            if "viewPrice" not in item or is_deep_empty(item.get("viewPrice")):
                return False, None, "大模型返回数据缺少viewPrice字段"
            # 设置默认值
            item.setdefault("name", "")
            item.setdefault("constructionFee", "")
            item.setdefault("fuelTaxFee", "")

        data.setdefault("compareAndAttributeDetails", [])

        return True, data, ""
    except Exception as e:
        return False, None, f"大模型返回数据格式/内容验证异常: {str(e)}"


def check_data_structure(data: Any) -> Tuple[bool, Optional[List], str]:
    """
    检查数据结构并进行解析验证
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    if data is None:
        return False, None, "大模型返回数据为空"
    # 第一步：解析成列表
    success, result_list, error_msg = parse_to_list(data)
    if not success:
        return False, None, error_msg

    # 第二步：验证列表结构
    return validate_list_structure(result_list)


def main(param: dict) -> dict:
    try:
        prompt = param.get("prompt")
        if not prompt or prompt == "":
            return {
                "aIResult": {
                    "priceInfo": [
                    {
                        "name": "",
                        "type": "",
                        "viewPrice": "",
                        "constructionFee": "",
                        "fuelTaxFee": ""
                    }
                    ],
                    "priceInfoStr": ""
                },
                "status": "成功",
                "errMsg": "默认值"
            }
        
        alResult = param.get("alResult")
        success, result_list, error_msg = check_data_structure(alResult)
        if not success:
            return {
                "aIResult": {
                    "priceInfo": [
                         {
                            "name": "",
                            "viewPrice": "",
                            "constructionFee": "",
                            "fuelTaxFee": "",
                        }
                    ],
                    "priceInfoStr": quote(json.dumps([{
                        "name": "",
                        "viewPrice": "",
                        "constructionFee": "",
                        "fuelTaxFee": "",
                    }], ensure_ascii=False))
                },
                "status": "失败",
                "errMsg": error_msg,
            }
        else:
            return {
                "aIResult": {
                    **result_list,
                    "priceInfoStr": quote(json.dumps(result_list.get("priceInfo", []), ensure_ascii=False))
                },
                "status": "成功",
                "errMsg": "大模型返回数据验证成功",
            }
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        result = {
            "aIResult": {
                "priceInfo": [
                    {
                        "name": "",
                        "viewPrice": "",
                        "constructionFee": "",
                        "fuelTaxFee": "",
                    }
                ],
                "priceInfoStr": quote(json.dumps([{
                    "name": "",
                    "viewPrice": "",
                    "constructionFee": "",
                    "fuelTaxFee": "",
                }], ensure_ascii=False))
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "aIResult": {
                "priceInfo": [
                    {
                        "name": "",
                        "viewPrice": "",
                        "constructionFee": "",
                        "fuelTaxFee": "",
                    }
                ],
                "priceInfoStr": quote(json.dumps([{
                    "name": "",
                    "viewPrice": "",
                    "constructionFee": "",
                    "fuelTaxFee": "",
                }], ensure_ascii=False))
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result


def test():
    param = {
        "prompt": "1",
        "alResult": {   
            "priceInfo": [
                {
                    "name": "张三",
                    "type": "成人",
                    "viewPrice": "0",
                    "constructionFee": "5",
                    "fuelTaxFee": "10",
                }
            ]
        },
        "orderNo": "jfx250309002848283",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 