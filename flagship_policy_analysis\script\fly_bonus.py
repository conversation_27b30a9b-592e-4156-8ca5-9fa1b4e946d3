# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }


    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }
    
    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }
    
    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    # 判断是否需要构建飞行券角标配置
    if not should_build_fly_bonus(analysis_data):
        return {
            "status": "ignore",
            "message": "无需构建飞行券角标配置",
            "data": []
        }

    # 航司
    carrier = mapping_data.get("carrier", "")
    # 构建角标文案
    fly_bonus_texts = build_fly_bonus_texts(analysis_data, mapping_data)
    if not fly_bonus_texts:
        # 构建默认文本信息
        product_label = analysis_data.get("product_label", "")
        display_limit = analysis_data.get("display_limit", "")
        fly_bonus_texts = {
            "labelShowName": product_label,
            "LayerTitle": product_label,
            "layerDescription": display_limit
        }

    # 构建结果对象
    result_obj = {
            "carrierCode": carrier,
            "productMark": product_mark,
            "labelShowName": fly_bonus_texts["labelShowName"],
            "LayerTitle": fly_bonus_texts["LayerTitle"],
            "layerDescription": fly_bonus_texts["layerDescription"]
    }
    
    return {
        "status": "success",
        "message": "操作成功",
        "data": result_obj
    }


def should_build_fly_bonus(analysis_data: Dict[str, Any]) -> bool:
    """
    判断是否需要构建飞行券角标配置
    
    参数:
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    bool - 是否需要构建飞行券角标配置
    """
    # 1. 次卡/权益卡限制时，不用配置飞行券角标
    if analysis_data.get("card_limit") == "true":
        return False
    
    # 2. 新会员，不用配置飞行券角标
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit and membership_limit in ["新会员"]:
        return False
    
    return True

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    

def build_fly_bonus_texts(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Optional[Dict[str, str]]:
    """
    构建飞行券角标文案，包括labelShowName、LayerTitle、layerDescription
    """
    # 解析年龄区间
    age_limit = analysis_data.get("age_limit", "")
    if age_limit:
        result_obj = _build_age_limit_fields(analysis_data, mapping_data)
        if result_obj:
            return result_obj
    # 包含多人限制
    capacity_limit = analysis_data.get("capacity_limit", "")
    if capacity_limit:
        result_obj = _build_capacity_limit_fields(analysis_data, mapping_data)
        if result_obj:
            return result_obj
    # 包含会员限制
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit:
        result_obj = _build_member_fields(analysis_data, mapping_data)
        if result_obj:
            return result_obj
    return None


def _build_age_limit_fields(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Optional[Dict[str, str]]:
    # 解析年龄区间
    age_limit = analysis_data.get("age_limit", "")
    if not age_limit:
        return None
    has_membership = False
    # 如果有会员限制，则叠加文案描述
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit == "新老会员":
        has_membership = True
    # 航司会员名称
    member_name = mapping_data.get("memberName", "航司会员")
    has_young = False
    has_old = False
    YOUNG_MIN = 12
    YOUNG_MAX = 28
    OLD_MIN = 50
    age_ranges = []
    for age_range in age_limit.split(","):
        age_range = age_range.strip()
        if not age_range:
            continue
        if "-" not in age_range:
            continue
        try:
            min_age, max_age = map(int, age_range.split("-"))
        except Exception:
            return None
        if min_age < 0 or max_age < 0 or max_age > 150 or min_age > max_age:
            continue
        if (min_age >= YOUNG_MIN and max_age <= YOUNG_MAX):
            has_young = True
        if min_age >= OLD_MIN:
            has_old = True
        age_ranges.append((min_age, max_age))
    if not age_ranges:
        return None
    if has_young and has_old:
        label = "家庭特惠"
        desc = "尊敬的用户，航司邀请您体验家庭出行优享低价权益，该权益限量邀请，感谢您的理解与支持。"
        if has_membership:
            desc = f"尊敬的用户，航司邀请您体验家庭出行优享低价权益，该权益限{member_name}优享，如非会员将自动注册为{member_name}，感谢您的理解与支持。"
        return {
            "labelShowName": label,
            "LayerTitle": label,
            "layerDescription": desc
        }
    if len(age_ranges) == 1:
        min_age, max_age = age_ranges[0]
        if has_young:
            label = "青年专享"
            desc = f"尊敬的用户，航司邀请{min_age}周岁（含）至{max_age}周岁（含）的青年旅客可享受优惠价格，感谢您的理解。"
            if has_membership:
                desc = f"尊敬的用户，航司邀请{min_age}周岁（含）至{max_age}周岁（含）的青年旅客可享受优惠价格，该权益限{member_name}优享，如非会员将自动注册为{member_name}，感谢您的理解与支持。"
            return {
                "labelShowName": label,
                "LayerTitle": label,
                "layerDescription": desc
            }
        elif has_old:
            label = "长者尊享"
            desc = f"尊敬的用户，航司邀请{min_age}周岁（含）以上用户可享受优惠价格，感谢您的理解。"
            if has_membership:
                desc = f"尊敬的用户，航司邀请{min_age}周岁（含）以上用户可享受优惠价格，该权益限{member_name}优享，如非会员将自动注册为{member_name}，感谢您的理解与支持。"
            return {
                "labelShowName": label,
                "LayerTitle": label,
                "layerDescription": desc
            }
    return None


def _build_capacity_limit_fields(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Optional[Dict[str, str]]:
    # 包含多人限制
    capacity_limit = analysis_data.get("capacity_limit", "")
    has_membership = False
    # 如果有会员限制，则叠加文案描述
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit == "新老会员":
        has_membership = True
    # 航司会员名称
    member_name = mapping_data.get("memberName", "航司会员")
    if capacity_limit and "-" in capacity_limit and capacity_limit != "1-1":
        try:
            min_count, max_count = map(int, capacity_limit.split("-"))
        except Exception:
            return None
        # 校验范围是否合法
        if min_count < 1 or max_count > 9 or min_count > max_count:
            return None
        label = "多人特惠"
        desc = "尊敬的用户，航司邀请您体验多人特惠权益，感谢您的理解。"
        # 如果有会员限制，则叠加文案描述
        if has_membership:
            desc = f"尊敬的用户，航司邀请您体验多人特惠权益，该权益限{member_name}优享，如非会员将自动注册为{member_name}，感谢您的理解与支持。"
        return {
            "labelShowName": label,
            "LayerTitle": label,
            "layerDescription": desc
        }
    return None

def _build_member_fields(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Optional[Dict[str, str]]:
    membership_limit = analysis_data.get("membership_limit", "")
    member_name = mapping_data.get("memberName", "航司会员")
    if membership_limit == "新老会员":
        if member_name == "金鹏会员":
            label = "金鹏会员专享"
            desc = "尊敬的用户，航司邀请您体验金鹏会员专享权益，如非金鹏会员将自动注册为会员，感谢您的理解与支持。"
            return {
                "labelShowName": label,
                "LayerTitle": label,
                "layerDescription": desc
            }
        else:
            label = "会员专享"
            desc = f"尊敬的用户，航司邀请您体验{member_name}优享权益，乘机人须为{member_name}，如非会员将自动为您注册为{member_name}，感谢您的理解。"
            return {
                "labelShowName": label,
                "LayerTitle": label,
                "layerDescription": desc
            }
    return None