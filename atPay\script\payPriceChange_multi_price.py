import requests
import json
from datetime import datetime
from typing import Dict, Any, <PERSON>
from requests.exceptions import RequestException


def validate_date_format(date_str: str) -> bool:
    """验证日期格式是否为YYYY-MM-DD"""
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def format_datetime_range(start_date: str, end_date: str) -> str:
    """
    将日期转换为带时间的格式
    示例输入：start_date="2025-03-11", end_date="2025-03-12"
    输出："2025-03-11 00:00:00,2025-03-12 23:59:59"
    """
    if not validate_date_format(start_date):
        raise ValueError(f"无效的开始日期格式: {start_date}")
    if not validate_date_format(end_date):
        raise ValueError(f"无效的结束日期格式: {end_date}")

    return f"{start_date} 00:00:00,{end_date} 23:59:59"


def query_trade_data(username: str, start_date: str, end_date: str) -> Dict[str, Any]:
    """
    查询交易数据接口
    :param username: 用户名
    :param start_date: 开始日期(YYYY-MM-DD)
    :param end_date: 结束日期(YYYY-MM-DD)
    """
    url = "http://tts.corp.qunar.com/tradeView/analysis/queryData"

    # 请求头配置（根据实际情况填写完整Cookie）
    headers = {"Cookie": "userId=daze.dong", "Content-Type": "application/json"}

    # 构建请求体
    payload = {
        "page": 1,
        "businessLine": "1",
        "timeRange": format_datetime_range(start_date, end_date),  # 动态生成时间范围
        "compareType": 0,
        "compareTimeRange": "",
        "stageId": 110,
        "conditionList": {
            "conjunction": "and",
            "children": [
                {
                    "left": {"type": "field", "field": "qunar_username"},
                    "op": "equal",
                    "right": username,
                },
                {
                    "left": {"type": "field", "field": "status"},
                    "op": "equal",
                    "right": "405",
                },
            ],
        },
        "groupByFieldList": {
            "id": "825a2e144d4d",
            "conjunction": "and",
            "children": [
                {
                    "id": "706daca6c33e",
                    "left": {"type": "field", "field": "block_reason"},
                }
            ],
        },
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=50)
        response.raise_for_status()
        return response.json().get("data", {})
    except ValueError as ve:
        return {"error": f"日期格式错误: {str(ve)}"}
    except RequestException as re:
        return {"error": f"请求失败: {str(re)}"}
    except Exception as e:
        return {"error": f"未知错误: {str(e)}"}


def main(params: Dict[str, str]) -> Union[Dict[str, Any], str]:
    """主函数新增isMulttPrice逻辑"""
    # 检查isMulttPrice参数
    is_multt_price = str(params.get("isMulttPrice", "false")).lower() == "true"
    if not is_multt_price:
        return {"rows": [], "needInvokeAi": False}

    """
    主入口函数
    :param params: 必须包含 username, qTraceId, startDate, endDate
    """
    required_params = ["username", "startDate", "endDate"]

    # 参数校验
    missing_params = [p for p in required_params if p not in params]
    if missing_params:
        return {
            "error": f"缺少必要参数: {', '.join(missing_params)}",
            "needInvokeAi": False,
        }

    # 日期格式校验
    date_errors = []
    if not validate_date_format(params["startDate"]):
        date_errors.append("startDate格式应为YYYY-MM-DD")
    if not validate_date_format(params["endDate"]):
        date_errors.append("endDate格式应为YYYY-MM-DD")
    if date_errors:
        return {"error": "; ".join(date_errors), "needInvokeAi": False}

    try:
        finalResult = query_trade_data(
            username=params["username"],
            start_date=params["startDate"],
            end_date=params["endDate"],
        )
        if finalResult.get("error"):
            finalResult["needInvokeAi"] = False
            return finalResult
        else:
            finalResult["needInvokeAi"] = True
            return finalResult
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "needInvokeAi": False}


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


if __name__ == "__main__":
    param = {
        "username": "khorfwq8891",
        "isMulttPrice": "true",
        "startDate": "2025-04-02",
        "endDate": "2025-04-09",
        "thirdReason": "用户未下单",
    }
    result = main(param)
    print(result)
    write_json_to_file(
        result,
        file_path="atPay/data/payPriceChange_multi_price.json",
    )
