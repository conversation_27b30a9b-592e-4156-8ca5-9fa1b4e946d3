/**
 * 表格结构分析器
 * 用于分析和结构化表格数据
 */
class TableStructureAnalyzer {
  constructor() {
    this.dataTypes = {
      NUMBER: 'number',
      CURRENCY: 'currency',
      PERCENTAGE: 'percentage',
      DATE: 'date',
      EMAIL: 'email',
      URL: 'url',
      PHONE: 'phone',
      TEXT: 'text',
      BOOLEAN: 'boolean'
    };
  }

  /**
   * 分析表格结构并转换为JSON
   * @param {Array} tableData - 二维数组表格数据
   * @param {string} tableName - 表格名称
   * @returns {Object} 结构化的表格JSON
   */
  analyzeTableStructure(tableData, tableName = '表格') {
    if (!Array.isArray(tableData) || tableData.length === 0) {
      return null;
    }

    const headers = tableData[0] || [];
    const rows = tableData.slice(1);
    
    // 分析列类型
    const columnAnalysis = this.analyzeColumns(headers, rows);
    
    // 转换为结构化数据
    const structuredData = this.convertToStructuredData(headers, rows, columnAnalysis);
    
    // 生成表格统计信息
    const statistics = this.generateStatistics(structuredData, columnAnalysis);
    
    return {
      tableName: tableName,
      metadata: {
        totalRows: rows.length,
        totalColumns: headers.length,
        hasHeaders: headers.length > 0,
        dataTypes: columnAnalysis.map(col => ({
          column: col.name,
          type: col.type,
          confidence: col.confidence
        })),
        statistics: statistics
      },
      headers: headers,
      data: structuredData,
      rawData: tableData
    };
  }

  /**
   * 分析列数据类型
   * @param {Array} headers - 表头
   * @param {Array} rows - 数据行
   * @returns {Array} 列分析结果
   */
  analyzeColumns(headers, rows) {
    return headers.map((header, index) => {
      const columnData = rows.map(row => row[index]).filter(cell => 
        cell !== null && cell !== undefined && cell.toString().trim() !== ''
      );
      
      const typeAnalysis = this.detectDataType(columnData);
      
      return {
        name: header || `列${index + 1}`,
        index: index,
        type: typeAnalysis.type,
        confidence: typeAnalysis.confidence,
        sampleValues: columnData.slice(0, 3),
        totalValues: columnData.length,
        emptyValues: rows.length - columnData.length
      };
    });
  }

  /**
   * 检测数据类型
   * @param {Array} columnData - 列数据
   * @returns {Object} 类型检测结果
   */
  detectDataType(columnData) {
    if (columnData.length === 0) {
      return { type: this.dataTypes.TEXT, confidence: 0 };
    }

    const typeScores = {
      [this.dataTypes.NUMBER]: 0,
      [this.dataTypes.CURRENCY]: 0,
      [this.dataTypes.PERCENTAGE]: 0,
      [this.dataTypes.DATE]: 0,
      [this.dataTypes.EMAIL]: 0,
      [this.dataTypes.URL]: 0,
      [this.dataTypes.PHONE]: 0,
      [this.dataTypes.BOOLEAN]: 0,
      [this.dataTypes.TEXT]: 0
    };

    columnData.forEach(value => {
      const str = value.toString().trim();
      
      // 数字检测
      if (/^\d+(\.\d+)?$/.test(str)) {
        typeScores[this.dataTypes.NUMBER]++;
      }
      
      // 货币检测
      if (/^[¥$€£]\s?\d+(\.\d+)?$|^\d+(\.\d+)?\s?[元美元欧元英镑]$/.test(str)) {
        typeScores[this.dataTypes.CURRENCY]++;
      }
      
      // 百分比检测
      if (/^\d+(\.\d+)?%$/.test(str)) {
        typeScores[this.dataTypes.PERCENTAGE]++;
      }
      
      // 日期检测
      if (/^\d{4}[-/]\d{1,2}[-/]\d{1,2}$|^\d{1,2}[-/]\d{1,2}[-/]\d{4}$/.test(str)) {
        typeScores[this.dataTypes.DATE]++;
      }
      
      // 邮箱检测
      if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str)) {
        typeScores[this.dataTypes.EMAIL]++;
      }
      
      // URL检测
      if (/^https?:\/\//.test(str)) {
        typeScores[this.dataTypes.URL]++;
      }
      
      // 电话检测
      if (/^[\d\s\-\+\(\)]{10,}$/.test(str)) {
        typeScores[this.dataTypes.PHONE]++;
      }
      
      // 布尔值检测
      if (/^(true|false|是|否|yes|no|1|0)$/i.test(str)) {
        typeScores[this.dataTypes.BOOLEAN]++;
      }
      
      // 默认为文本
      typeScores[this.dataTypes.TEXT]++;
    });

    // 找出得分最高的类型
    const maxScore = Math.max(...Object.values(typeScores));
    const detectedType = Object.keys(typeScores).find(type => typeScores[type] === maxScore);
    const confidence = maxScore / columnData.length;

    return {
      type: detectedType,
      confidence: confidence
    };
  }

  /**
   * 转换为结构化数据
   * @param {Array} headers - 表头
   * @param {Array} rows - 数据行
   * @param {Array} columnAnalysis - 列分析结果
   * @returns {Array} 结构化数据
   */
  convertToStructuredData(headers, rows, columnAnalysis) {
    return rows.map((row, rowIndex) => {
      const structuredRow = {
        _rowIndex: rowIndex,
        _rowData: {}
      };

      headers.forEach((header, colIndex) => {
        const cellValue = row[colIndex];
        const columnInfo = columnAnalysis[colIndex];
        const fieldName = header || `column_${colIndex}`;
        
        structuredRow._rowData[fieldName] = {
          value: cellValue,
          type: columnInfo.type,
          formatted: this.formatValue(cellValue, columnInfo.type)
        };
      });

      return structuredRow;
    });
  }

  /**
   * 格式化值
   * @param {*} value - 原始值
   * @param {string} type - 数据类型
   * @returns {*} 格式化后的值
   */
  formatValue(value, type) {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const str = value.toString().trim();

    switch (type) {
      case this.dataTypes.NUMBER:
        const num = parseFloat(str);
        return isNaN(num) ? str : num;
      
      case this.dataTypes.CURRENCY:
        const currencyMatch = str.match(/[\d.]+/);
        return currencyMatch ? parseFloat(currencyMatch[0]) : str;
      
      case this.dataTypes.PERCENTAGE:
        const percentMatch = str.match(/[\d.]+/);
        return percentMatch ? parseFloat(percentMatch[0]) / 100 : str;
      
      case this.dataTypes.BOOLEAN:
        return /^(true|是|yes|1)$/i.test(str);
      
      default:
        return str;
    }
  }

  /**
   * 生成统计信息
   * @param {Array} structuredData - 结构化数据
   * @param {Array} columnAnalysis - 列分析结果
   * @returns {Object} 统计信息
   */
  generateStatistics(structuredData, columnAnalysis) {
    const stats = {};

    columnAnalysis.forEach(column => {
      if (column.type === this.dataTypes.NUMBER || column.type === this.dataTypes.CURRENCY) {
        const values = structuredData
          .map(row => row._rowData[column.name]?.formatted)
          .filter(val => typeof val === 'number' && !isNaN(val));

        if (values.length > 0) {
          stats[column.name] = {
            count: values.length,
            sum: values.reduce((a, b) => a + b, 0),
            average: values.reduce((a, b) => a + b, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values)
          };
        }
      }
    });

    return stats;
  }
}
