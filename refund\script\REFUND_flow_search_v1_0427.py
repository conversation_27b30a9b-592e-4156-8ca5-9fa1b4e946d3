import requests
import json
import time
import uuid
from typing import Dict, List, Any
from datetime import datetime, <PERSON><PERSON><PERSON>

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def search_flow(
    orderNo: str = "",
    flowStatus: str = "",
    date: str = "",
    appCode: str = "",
    appToken: str = "",
    pageIndex: int = 1,
    pageSize: int = 10
) -> Dict[str, Any]:
    """
    搜索工单流
    Args:
        orderNo: 订单号
        flowStatus: 工单状态
        date: 日期，格式为YYYY-MM-DD
        appCode: 应用代码
        appToken: 应用令牌
        pageIndex: 页码，从1开始
        pageSize: 每页记录数
    Returns:
        Dict[str, Any]: 包含工单列表和分页信息
    """
    base_url = "https://hcallcenter.corp.qunar.com/callcenter/flow/search/advanced"
    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    
    # 处理日期，默认为当前日期
    if not date:
        start_time =""
        end_time=""
    else:
        current_date = date
        # 设置开始时间为当天的0点，结束时间为当天的23:59:59
        start_time = f"{current_date} 00:00:00"
        end_time = f"{current_date} 23:59:59"
    
         # URL encode the time strings
        start_time = requests.utils.quote(start_time)
        end_time = requests.utils.quote(end_time)
    
    results = []
    total_count = 0
    
    proxyData = {
        "method": "get",
        "url": f"{base_url}?bizLine=FLIGHT&flowNo=&orderNo={orderNo}&contactNumber=&agentName=&currentHandlerName=&channel=&createStartTime={start_time}&createEndTime={end_time}&createTimeDesc=&closedTimeDesc=&flowNodeIdList=&flowStatus={flowStatus}&problem1Id=Mlhshda9&problem2Id=BJDP5GU4&closerName=&userIdentity=&pageNum={pageIndex}&pageSize={pageSize}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        if "error" in result:
            return {"data": [], "totalCount": 0, "currentPage": pageIndex, "pageSize": pageSize}

        response_data = json.loads(result.get("data", "{}"))
        
        if not response_data or response_data.get("ret") is False:
            return {"data": [], "totalCount": 0, "currentPage": pageIndex, "pageSize": pageSize}

        data = response_data.get("data", {})
        current_page_data = data.get("data", [])
        total_count = data.get("totalCount", 0)
        
        # 处理当前页的数据，只保留需要的字段
        for item in current_page_data:
            create_time = item.get("createTime", "")
            # 将createTime格式化为yyyy-mm-dd
            create_date = ""
            if create_time:
                try:
                    # 假设createTime格式为 "yyyy-mm-dd HH:MM:SS"
                    create_date = create_time.split()[0]
                except:
                    create_date = create_time
            
            processed_item = {
                "uniqKey": str(uuid.uuid4()),
                "flowNo": item.get("flowNo", ""),
                "orderNo": item.get("orderNo", ""),
                "createTime": create_time,
                "createDate": create_date,
                "flowStatus": item.get("flowStatus", "")
            }
            results.append(processed_item)

    except Exception as e:
        return {"data": [], "totalCount": 0, "currentPage": pageIndex, "pageSize": pageSize, "error": str(e)}

    return {
        "data": results,
        "totalCount": total_count,
        "currentPage": pageIndex,
        "pageSize": pageSize
    }


def get_order_info(orderNo: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    获取订单的详细信息
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        Dict: 包含订单domain、航班日期、乘客信息和承运人信息
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/gongdan/order/search?orderNo={orderNo}&domain=callcenter.qunar.com",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": [], "carrier": ""}

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": [], "carrier": ""}

        # 获取list中的第一个订单信息
        order_list = response_data.get("data", {}).get("list", [])
        if not order_list:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": [], "carrier": ""}
            
        order_info = order_list[0]
        
        # 提取所需信息
        raw_domain = order_info.get("rawDomain", "")
        airline_date = order_info.get("airlineDate", [])
        
        # 提取乘客姓名
        passenger_names = []
        passenger_vos = order_info.get("passengerVos", [])
        for passenger in passenger_vos:
            name = passenger.get("name", "")
            if name:
                passenger_names.append(name)
        
        # 提取承运人信息（从航班号前两位获取）
        carrier = ""
        airline_no_list = order_info.get("airlineNo", [])
        if airline_no_list and len(airline_no_list) > 0:
            flight_num = airline_no_list[0].get("flightNum", "")
            if flight_num and len(flight_num) >= 2:
                carrier = flight_num[:2]  # 截取航班号前两位作为承运人代码
                
        return {
            "rawDomain": raw_domain,
            "airlineDate": airline_date,
            "passengerNames": passenger_names,
            "carrier": carrier
        }

    except Exception as e:
        return {"rawDomain": "", "airlineDate": [], "passengerNames": [], "carrier": ""}

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含orderNo、flowStatus、date、pageIndex、invokeAppCode和invokeToken的参数字典
    Returns:
        Dict: 处理结果
    """
    orderNo = param.get("orderNo", "")
    flowStatus = param.get("flowStatus", "")
    date = param.get("date", "")
    pageIndex = param.get("pageIndex", 1)
    pageSize = param.get("pageSize", 10)
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    
    # 验证参数
    if not orderNo and not date:
        return {
            "error": "当订单号为空时，日期不能为空",
            "results": {
                "data": [],
                "totalCount": 0,
                "currentPage": pageIndex,
                "pageSize": pageSize
            }
        }
    
    try:
        results = search_flow(orderNo, flowStatus, date, appCode, appToken, pageIndex, pageSize)
        
        # 提取唯一订单号
        unique_order_numbers = set()
        for result in results["data"]:
            if result.get("orderNo"):
                unique_order_numbers.add(result.get("orderNo"))
        
        # 获取每个唯一订单号的详细信息
        order_info_map = {}
        for order_no in unique_order_numbers:
            order_info = get_order_info(order_no, appCode, appToken)
            order_info_map[order_no] = order_info
            time.sleep(0.5)  # 避免请求过于频繁
        
        # 将订单信息添加到结果中
        for result in results["data"]:
            order_no = result.get("orderNo", "")
            if order_no in order_info_map:
                # 直接将订单信息合并到结果中，而不是创建嵌套的orderInfo字段
                order_info = order_info_map[order_no]
                result["rawDomain"] = order_info.get("rawDomain", "")
                result["airlineDate"] = ",".join(order_info.get("airlineDate", []))
                result["passengerNames"] = ",".join(order_info.get("passengerNames", []))
                result["carrier"] = order_info.get("carrier", "")
        
        return {
            "error": "",
            "results": results
        }
    except Exception as e:
        return {
            "error": f"处理失败: {str(e)}", 
            "results": {
                "data": [],
                "totalCount": 0,
                "currentPage": pageIndex,
                "pageSize": pageSize
            }
        }

def test():
    param = {
        "orderNo": "",
        "flowStatus": "",
        "date": "2025-05-12",  
        "pageIndex": 1,
        "pageSize": 10,
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 打印查询到的订单数量
    if "results" in result:
        print(f"共查询到 {len(result['results']['data'])} 条订单记录")
        if result['results']['data']:
            # 打印第一条记录的详细信息作为示例
            print("\n示例记录:")
            print(json.dumps(result['results']['data'][0], ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 