import requests
import json
from typing import Dict, List, Any
import urllib.parse

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def queryOrderDetail(orderNo: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    查询订单详情
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        Dict: 包含订单详情的字典
    """
    proxyData = {
        "method": "get",
        "url": f"https://hcallcenter.corp.qunar.com/callcenter/flight/orderDetail?orderNo={orderNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return {"error": result["error"], "data": {}}

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"error": error_msg, "data": {}}

        # 获取内层data
        inner_data = response_data.get("data", {})
        if not inner_data:
            return {"error": "Inner data is empty", "data": {}}

        return inner_data

    except Exception as e:
        return {"error": f"查询订单详情失败: {str(e)}", "data": {}}

def parsePriceDetails(priceDetails: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析价格详情
    Args:
        priceDetails: 价格详情数据
    Returns:
        List: 解析后的价格列表
    """
    result = []
    plist = priceDetails.get("plist", [])
    refundXcdAmount = priceDetails.get("refundXcdAmount", "0")
    
    for item in plist:
        if item.get("priceTypeName") == "总计":
            continue
            
        parsed_item = {
            "priceTypeName": item.get("priceTypeName", ""),
            "passengerName": item.get("passengerName", ""),
            "ticketPriceSumOfSameGeneralType": item.get("ticketPriceSumOfSameGeneralType", "0"),
            "youhuiPrice": item.get("youhuiPrice", 0),
            "viewPrice": item.get("viewPrice", 0),
            "constructionFee": item.get("constructionFee", 0),
            "fuelTaxFee": item.get("fuelTaxFee", 0),
            "refundXcdAmount": refundXcdAmount,
            "match": "否",
            "notMatchReason": ""
        }
        result.append(parsed_item)
    
    return result

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含orderNo、vDate和priceInfo的参数字典
    Returns:
        Dict: 处理结果
    """
    orderNo = param.get("orderNo", "")
    vDate = param.get("vDate", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    priceInfoStr = param.get("priceInfoStr", "")
    
    #设置results默认返回值，match/oredrNo/notMatchReason
    results = {
        "match": "否",
        "orderNo": orderNo,
        "notMatchReason": ""
    }

    priceInfo = []
    if priceInfoStr:
        # Decode the priceInfoStr first
        try:
            decoded_price_info = urllib.parse.unquote(priceInfoStr)
            priceInfo = json.loads(decoded_price_info)
        except Exception as e:
            results["notMatchReason"] = f"解析priceInfoStr失败: {str(e)}"
            return {"error": f"解析priceInfoStr失败: {str(e)}", "results": results}
        

    if not orderNo:
        return {"error": "订单号不能为空", "results": []}

    try:
        # 查询订单详情
        orderDetail = queryOrderDetail(orderNo, appCode, appToken)
        if not orderDetail:
            results["notMatchReason"] = "未查询到订单详情"
            return {"error": "未查询到订单详情", "results": results}
        
        # 解析价格详情
        priceDetails = orderDetail.get("priceDetail", {})
        parsedResults = parsePriceDetails(priceDetails)
        
        # 处理priceInfo匹配逻辑
        if priceInfo:
            # 如果priceInfo中的name和type都为空，取第一条数据
            if all(not p.get("name") and not p.get("type") for p in priceInfo):
                first_price = priceInfo[0]
                for result in parsedResults:
                    result["airLineViewPrice"] = first_price.get("viewPrice", "")
                    result["airLineConstructionFee"] = first_price.get("constructionFee", "")
                    result["airLineFuelTaxFee"] = first_price.get("fuelTaxFee", "")
            else:
                # 遍历parsedResults，优先用name匹配，name为空时才用type匹配
                for result in parsedResults:
                    matched = False
                    for price in priceInfo:
                        # 优先匹配name
                        if price.get("name") and price.get("name") == result.get("passengerName"):
                            result["airLineViewPrice"] = price.get("viewPrice", "")
                            result["airLineConstructionFee"] = price.get("constructionFee", "")
                            result["airLineFuelTaxFee"] = price.get("fuelTaxFee", "")
                            matched = True
                            break
                        # 如果name为空，则用type匹配
                        elif not price.get("name") and price.get("type") == result.get("priceTypeName"):
                            result["airLineViewPrice"] = price.get("viewPrice", "")
                            result["airLineConstructionFee"] = price.get("constructionFee", "")
                            result["airLineFuelTaxFee"] = price.get("fuelTaxFee", "")
                            matched = True
                            break
                    if not matched:
                        result["airLineViewPrice"] = ""
                        result["airLineConstructionFee"] = ""
                        result["airLineFuelTaxFee"] = ""
        
        # 检查匹配情况并设置match和notMatchReason
        for result in parsedResults:
            not_match_reasons = []
            
            # 检查youhuiPrice
            ticket_price_sum_of_same_general_type_str = result.get("ticketPriceSumOfSameGeneralType", "0")
            ticket_price_sum_of_same_general_type = float(ticket_price_sum_of_same_general_type_str) if ticket_price_sum_of_same_general_type_str else 0
            if ticket_price_sum_of_same_general_type <= 0:
                not_match_reasons.append("用户支付票面价为空或小于等于0")
            
            # 检查airLineViewPrice和refundXcdAmount
            airline_price_str = result.get("airLineViewPrice", "0")
            airline_price = float(airline_price_str) if airline_price_str else 0
            refund_xcd_amount = result.get("refundXcdAmount", "0")
            
            if refund_xcd_amount and refund_xcd_amount != "0":
                # 如果refundXcdAmount不为空且不为0，允许airLineViewPrice为空或0
                pass
            else:
                # 如果refundXcdAmount为空或为0，则airLineViewPrice不能为空或0
                if airline_price <= 0:
                    not_match_reasons.append("行程单退差金额等于0，但航司售卖票面价为空或小于等于0")
            
            # 设置match和notMatchReason
            result["match"] = "是" if len(not_match_reasons) == 0 else "否"
            result["notMatchReason"] = "、".join(not_match_reasons) if not_match_reasons else ""
        
        return {
            "error": "",
            "results": parsedResults,
        }
    except Exception as e:
        results["notMatchReason"] = f"处理失败: {str(e)}"
        return {"error": f"处理失败: {str(e)}", "results": results}

def test():
    param = {
        "orderNo": "tdt241116210558207",
        "vDate": "2024-04-09",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg=",
        "priceInfo": [
            {
                "name": "赵福英",
                "type": "儿童",
                "viewPrice": "1000",
                "constructionFee": "50",
                "fuelTaxFee": "100"
            },
            {
                "name": "张三",
                "type": "成人",
                "viewPrice": "100",
                "constructionFee": "5",
                "fuelTaxFee": "10"
            }
        ],
        
        "priceInfoStr": "%5B%7B%22name%22%3A%20%22%E5%BC%A0%E4%B8%89%22%2C%20%22type%22%3A%20%22%E6%88%90%E4%BA%BA%22%2C%20%22viewPrice%22%3A%20%220%22%2C%20%22constructionFee%22%3A%20%225%22%2C%20%22fuelTaxFee%22%3A%20%2210%22%7D%5D"
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 