class ExcelProcessor {
  constructor() {
    this.workbook = null;
    this.currentSheet = null;
  }

  /**
   * 解析Excel文件
   * @param {ArrayBuffer} arrayBuffer - Excel文件的ArrayBuffer
   * @returns {Object} 解析结果
   */
  parseExcel(arrayBuffer) {
    try {
      this.workbook = XLSX.read(arrayBuffer, { type: 'array' });
      return {
        success: true,
        sheets: this.workbook.SheetNames
      };
    } catch (error) {
      console.error('Excel解析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取指定sheet的数据
   * @param {string} sheetName - sheet名称
   * @returns {Array} 表格数据
   */
  getSheetData(sheetName) {
    if (!this.workbook || !sheetName) return null;

    try {
      const worksheet = this.workbook.Sheets[sheetName];
      return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    } catch (error) {
      console.error('获取sheet数据失败:', error);
      return null;
    }
  }

  /**
   * 将表格数据转换为AI友好的文本格式
   * @param {Array} data - 表格数据
   * @returns {string} 格式化的文本
   */
  convertToAIText(data) {
    if (!data || !data.length) return '';

    try {
      // 提取表头
      const headers = data[0];

      // 构建表格描述
      let text = '这是一个数据表格，包含以下列：\n';
      text += headers.join(', ') + '\n\n';

      // 添加数据行描述
      text += `表格共有 ${data.length - 1} 行数据：\n\n`;

      // 转换每一行数据
      for (let i = 1; i < data.length; i++) {
        text += `第 ${i} 行：\n`;
        for (let j = 0; j < headers.length; j++) {
          if (data[i][j] !== undefined && data[i][j] !== '') {
            text += `${headers[j]}: ${data[i][j]}\n`;
          }
        }
        text += '\n';
      }

      return text;
    } catch (error) {
      console.error('转换AI文本失败:', error);
      return '';
    }
  }

  /**
   * 将数据转换为飞书表格格式
   * @param {Array} data - 表格数据
   * @returns {Object} 飞书表格格式的数据
   */
  convertToFeishuFormat(data) {
    if (!data || !data.length) return null;

    try {
      return {
        valueRange: {
          range: "A1", // 默认从A1开始
          values: data
        }
      };
    } catch (error) {
      console.error('转换飞书格式失败:', error);
      return null;
    }
  }

  /**
   * 分析表格结构
   * @param {Array} data - 表格数据
   * @returns {Object} 表格结构信息
   */
  analyzeStructure(data) {
    if (!data || !data.length) return null;

    try {
      const headers = data[0];
      const structure = {
        columnCount: headers.length,
        rowCount: data.length,
        columns: [],
        dataTypes: {}
      };

      // 分析每一列
      for (let i = 0; i < headers.length; i++) {
        const columnInfo = {
          name: headers[i],
          type: 'mixed',
          nonEmptyCount: 0,
          uniqueValues: new Set()
        };

        // 分析数据类型和特征
        for (let j = 1; j < data.length; j++) {
          const value = data[j][i];
          if (value !== undefined && value !== '') {
            columnInfo.nonEmptyCount++;
            columnInfo.uniqueValues.add(value);

            // 判断数据类型
            if (!isNaN(value)) {
              if (!columnInfo.type || columnInfo.type === 'mixed') {
                columnInfo.type = 'number';
              }
            } else if (value instanceof Date || !isNaN(Date.parse(value))) {
              if (!columnInfo.type || columnInfo.type === 'mixed') {
                columnInfo.type = 'date';
              }
            } else {
              columnInfo.type = 'text';
            }
          }
        }

        // 计算列的特征
        columnInfo.fillRate = columnInfo.nonEmptyCount / (data.length - 1);
        columnInfo.uniqueRate = columnInfo.uniqueValues.size / columnInfo.nonEmptyCount;
        columnInfo.uniqueValues = Array.from(columnInfo.uniqueValues).slice(0, 10); // 只保留前10个唯一值

        structure.columns.push(columnInfo);
      }

      return structure;
    } catch (error) {
      console.error('分析表格结构失败:', error);
      return null;
    }
  }

  /**
   * 验证数据有效性
   * @param {Array} data - 表格数据
   * @returns {Object} 验证结果
   */
  validateData(data) {
    if (!data || !data.length) {
      return {
        valid: false,
        errors: ['数据为空']
      };
    }

    const errors = [];
    const warnings = [];

    // 检查表头
    if (data[0].some(header => !header)) {
      errors.push('表头存在空值');
    }

    // 检查数据一致性
    const columnCount = data[0].length;
    for (let i = 1; i < data.length; i++) {
      if (data[i].length !== columnCount) {
        warnings.push(`第 ${i + 1} 行的列数与表头不一致`);
      }
    }

    // 检查重复表头
    const headerSet = new Set(data[0]);
    if (headerSet.size !== data[0].length) {
      errors.push('存在重复的表头');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 过滤空行（所有单元格都为空的行）
   * @param {Array} data - 原始数据
   * @returns {Array} 过滤后的数据
   */
  filterEmptyRows(data) {
    if (!data || !data.length) {
      console.log('filterEmptyRows: 输入数据为空');
      return [];
    }
    
    // 保留表头
    const filteredData = [data[0]];
    console.log('filterEmptyRows: 表头数据:', data[0]);
    
    // 过滤数据行
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (!row) continue;

      // 检查行是否全为空
      const allEmpty = row.every(cell => 
        cell === null || cell === undefined || cell === '' || 
        (typeof cell === 'string' && cell.trim() === '')
      );

      if (!allEmpty) {
        filteredData.push(row);
      }
    }
    
    console.log(`filterEmptyRows: 过滤前 ${data.length} 行，过滤后 ${filteredData.length} 行`);
    return filteredData;
  }

  /**
   * 将表格数据转换为文本文件内容
   * @param {Array} data - 表格数据
   * @param {string} sheetName - 工作表名称
   * @returns {string} 文本文件内容
   */
  convertToTextFile(data, sheetName) {
    if (!data || !data.length) {
      console.log('convertToTextFile: 输入数据为空');
      return '';
    }
    
    console.log('convertToTextFile: 原始数据行数:', data.length);
    console.log('convertToTextFile: 工作表名称:', sheetName);
    
    // 获取表头
    const headers = data[0];
    let textContent = '';
    
    // 添加表头
    if (headers && headers.length) {
      textContent += headers.join('\t') + '\n';
      console.log('convertToTextFile: 添加表头:', headers);
    }
    
    // 添加数据行
    let validRowCount = 0;
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (!row) continue;

      // 格式化行数据
      const formattedRow = row.map(cell => {
        // 处理空值
        if (cell === null || cell === undefined || cell === '') return '';
        
        // 处理不同类型的数据
        switch (typeof cell) {
          case 'number':
          case 'boolean':
            return cell;
          case 'string':
            // 替换换行符和制表符
            return cell.replace(/[\n\t]/g, ' ').trim();
          default:
            return String(cell).replace(/[\n\t]/g, ' ').trim();
        }
      });

      // 检查行是否全为空
      const hasData = formattedRow.some(cell => cell !== '');
      if (hasData) {
        textContent += formattedRow.join('\t') + '\n';
        validRowCount++;
      }
    }
    
    console.log(`convertToTextFile: 处理完成，有效数据行数: ${validRowCount}`);
    console.log('convertToTextFile: 最终文本长度:', textContent.length);
    
    return textContent;
  }
}

// 导出工具类
window.ExcelProcessor = ExcelProcessor;
