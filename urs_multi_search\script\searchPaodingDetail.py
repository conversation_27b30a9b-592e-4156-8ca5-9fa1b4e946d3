import json
import requests
from typing import Optional, Dict, List, Any
from requests.exceptions import RequestException
from datetime import datetime

def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None

def filter_flight_data(request_params, target_data):
    """
    根据请求参数过滤航班数据
    :param request_params: 用户请求参数字典
    :param target_data: 目标数据列表
    :return: 过滤后的数据列表
    """
    filtered = []

    if request_params is None:
        return target_data
    
    if target_data is None:
        return target_data
    
    if not isinstance(target_data, list):
        return target_data


    # 遍历所有目标数据
    for item in target_data:
        match = True

        # 条件5：flightNumber 匹配（双方都不为空时）
        req_flight_number = request_params.get('flightNumber')  # 注意参数名拼写错误
        item_flight_number = item.get('flightNo')
        if req_flight_number and len(req_flight_number) > 2 and item_flight_number and match:
            if item_flight_number != req_flight_number:
                match = False

        req_carrier = request_params.get('carrier')  # 注意参数名拼写错误
        if req_carrier and item_flight_number and len(req_carrier) >= 2 and len(item_flight_number) >= 2 and match:
            item_carrier = item_flight_number[:2]
            carrier = req_carrier[:2]
            if carrier != item_carrier:
                match = False

        if match:
            filtered.append(item)

    return filtered

def format_passengers(passengers):
    if passengers is None or not isinstance(passengers, list):
        return ""
    result = []
    for passenger in passengers:
        sub_result = []
        for key, value in passenger.items():
            if value is not None:
                sub_result.append(f"{key}={value}")
        result.append('&'.join(sub_result))
    return ';'.join(result)

def join_list(
    lst: Optional[List], 
    separator: str = ""
) -> str:
    """
    将列表安全地拼接为字符串
    
    :param lst: 输入列表（可能为None或包含空元素）
    :param separator: 元素间分隔符（默认为空）
    :return: 拼接后的字符串
    
    >>> join_list(None)
    ''
    >>> join_list([])
    ''
    >>> join_list(["a", None, "", "b"])
    'ab'
    >>> join_list([1, None, 3.14, "test"], "-")
    '1-3.14-test'
    """
    # 处理空值输入
    if not isinstance(lst, list):
        return ""
    
    # 转换元素为字符串并处理None
    cleaned = [
        str(item) if item is not None else ""
        for item in lst
    ]
    return separator.join(cleaned)

def boolean_to_str(value: Any) -> str:
    """
    将 Boolean 类型转为字符串，非 Boolean 或 None 返回空字符串
    
    规则：
    - 输入为 True  → 返回 "True"
    - 输入为 False → 返回 "False"
    - 输入为 None  → 返回 ""
    - 输入为其他类型（如整数、字符串、列表等） → 返回 ""
    
    :param value: 输入值
    :return: 转换后的字符串或空字符串
    
    示例：
    >>> boolean_to_str(True)
    'True'
    >>> boolean_to_str(False)
    'False'
    >>> boolean_to_str(None)
    ''
    >>> boolean_to_str(0)
    ''
    >>> boolean_to_str("hello")
    ''
    """
    if value is None:
        return ""
    # 注意：Python 中 isinstance(True, int) 会返回 True，因此用 type 判断更严格
    if type(value) is bool:  # 使用 type 而非 isinstance 避免将 0/1 误判为 bool
        return "True" if value else "False"
    return ""

def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue

def parse_list_data(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    新版航班数据解析方法（支持复合航班号格式）
    
    参数：
    data: 包含flights字段的原始数据字典
    
    返回：
    结构化解析后的数据列表，包含字段：
    flightNo, price, coupon, cut, xCut, tag, oriTag, tSource, cabinType
    """
    results = []

    expansionType = data.get('expansionType', '')
    poison = data.get('poison', False)
    basicLabels = data.get('basicLabels') or []
    filters = data.get('filters') or []
    passengers = data.get('passengers') or []
    
    # 获取航班数据字典（处理空值情况）
    flights = data.get("flights", {})
    
    # 遍历所有航班条目
    for flight_no, flight_data in flights.items():
        expItems = flight_data.get('expItems', {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get('vendor')
        if expVendor is None:
            expVendor = 0
        # 构造基础数据项
        item = {
            "flightNo": flight_no,
            "tSource": "list",       # 固定值
            "cabinType": "",         # 固定空字符串
            "price": convert_price_to_numeric(flight_data.get("price")),
            "coupon": flight_data.get("coupon"),
            "cut": flight_data.get("cut"),
            "xCut": flight_data.get("xCut"),
            "tag": flight_data.get("tag"),
            "oriTag": flight_data.get("oriTag"),
            'expVendor': expVendor,
            'expansionType':expansionType,
            'poison':boolean_to_str(poison),
            'basicLabels':join_list(basicLabels, ','),
            'filters':join_list(filters, ','),
            'passengers':format_passengers(passengers)
        }
        results.append(item)
    
    return results

def parse_domestic_ota(domestic_ota: Dict) -> List[Dict]:
    """
    解析domesticOta数据，返回结构化航班舱位数据
    """
    result = []
    
    # 公共字段
    flight_no = domestic_ota.get('flightNo', '')
    t_source = "ota"  # 固定值

    expansionType = domestic_ota.get('expansionType', '')
    poison = domestic_ota.get('poison', False)
    basicLabels = domestic_ota.get('basicLabels') or []
    filters = domestic_ota.get('filters') or []
    passengers = domestic_ota.get('passengers') or []

    
    # 处理经济舱数据（evendors）
    for vendor in domestic_ota.get('evendors') or []:
        expItems = vendor.get('expItems', {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get('vendor')
        if expVendor is None:
            expVendor = 0
        record = {
            'flightNo': flight_no,
            'price': vendor.get('price'),
            'coupon': vendor.get('coupon'),
            'cut': vendor.get('cut'),
            'xCut': vendor.get('xCut'),
            'tag': vendor.get('tag'),
            'oriTag': vendor.get('oriTag'),
            'tSource': t_source,
            'cabinType': '经济舱',  # evendors固定值
            'expVendor': expVendor,
            'expansionType':expansionType,
            'poison':boolean_to_str(poison),
            'basicLabels':join_list(basicLabels, ','),
            'filters':join_list(filters, ','),
            'passengers':format_passengers(passengers)

        }
        result.append(_clean_record(record))
    
    # 处理高端舱位数据（hvendors）
    for vendor in domestic_ota.get('hvendors') or []:
        expItems = vendor.get('expItems', {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get('vendor')
        if expVendor is None:
            expVendor = 0
        record = {
            'flightNo': flight_no,
            'price': vendor.get('price'),
            'coupon': vendor.get('coupon'),
            'cut': vendor.get('cut'),
            'xCut': vendor.get('xCut'),
            'tag': vendor.get('tag'),
            'oriTag': vendor.get('oriTag'),
            'tSource': t_source,
            'cabinType': '头等/商务舱',  # hvendors固定值
            'expVendor': expVendor,
            'expansionType':expansionType,
            'poison':boolean_to_str(poison),
            'basicLabels':join_list(basicLabels, ','),
            'filters':join_list(filters, ','),
            'passengers':join_list(passengers, ',')
        }
        result.append(_clean_record(record))
    
    return result

def method2_parse_detail_table(data: Dict) -> List[Dict]:
    """
    解析detailTable数据，返回结构化价格信息
    """
    result = []
    if data is None or data.get('detailTable') is None:
        return result

    detail_table = []
    detailTable = data.get('detailTable')
    if not isinstance(detailTable, list):
        detail_table = data.get('detailTable', {}).get('data') or []
    else:
        if len(detailTable) > 0:
            detail_table = detailTable[0].get('data') or []
    
    
    
    for flight_data in detail_table:
        rows = flight_data.get('rows', [])
        for row in rows:
            # 仅处理_domesticShow为true的记录
            if not row.get('_domesticShow', False):
                continue
            
            raw_data = row.get('_raw', {})
            extMap = raw_data.get('extMap')
            ext_map = raw_data.get('extMap', {})
            all_goods = raw_data.get('allGoodsItems', {})

            cpt = ext_map.get('CPT')
            secondPrice = ext_map.get('secondPrice')
            autoPriceDecreaseAmount = ext_map.get('autoPriceDecreaseAmount')
            if extMap is None:
                cpt = row.get('CPT')
                secondPrice = row.get('secondPrice')
                autoPriceDecreaseAmount = row.get('autoPriceDecreaseAmount')
            
            # 计算商品总价
            all_good_item_price = sum(
                float(str_val) for str_val in all_goods.values() 
                if _is_number(str_val)
            )
            
            record = {
                'flightNo': flight_data.get('flightNumber', ''),
                'realPriceOriTag': row.get('realPriceOriTag'),
                'tagType': raw_data.get('tagType'),
                'flightNumber': flight_data.get('flightNumber'),
                'depDate': flight_data.get('depDate'),
                'depTime': flight_data.get('depTime'),
                'wrapperId': row.get('wrapperId'),
                'productMark': row.get('productMark'),
                'cabin': raw_data.get('cabin'),
                'packagePrice': raw_data.get('price'),
                'basePrice': raw_data.get('basePrice'),
                'viewPrice': raw_data.get('viewPrice'),
                'policyId': raw_data.get('policyId'),
                'autoPriceDecreaseAmount': autoPriceDecreaseAmount,
                'secondPrice': convert_price_to_numeric(secondPrice),
                'CPT': cpt,
                'allGoodItemPrice': round(all_good_item_price, 2)
            }
            result.append(_clean_record(record))
    
    return result

def method3_merge_data(
    method1_data: List[Dict], 
    method2_data: List[Dict]
) -> List[Dict]:
    """
    合并方法1和方法2的数据
    """
    merged = []
    
    for m1 in method1_data:
        # 生成匹配键：优先使用oriTag，否则使用tag
        match_tag = m1.get('oriTag') or m1.get('tag')
        flight_no = m1.get('flightNo')
        
        # 在方法2数据中查找匹配项
        matched = next((
            m2 for m2 in method2_data
            if m2.get('flightNo') == flight_no 
            and m2.get('realPriceOriTag') == match_tag
        ), None)
        
        # 复制需要合并的字段
        if matched:
            merge_fields = [
                'wrapperId', 'productMark', 'cabin', 'packagePrice',
                'basePrice', 'viewPrice', 'policyId', 'autoPriceDecreaseAmount',
                'secondPrice', 'CPT', 'allGoodItemPrice', 'depDate', 'depTime'
            ]
            for field in merge_fields:
                m1[field] = matched.get(field)
        
        merged.append(m1)
    
    return merged

# 辅助函数
def _clean_record(record: Dict) -> Dict:
    """处理空值和类型转换"""
    return {k: v if v not in (None, 'null', '') else None for k, v in record.items()}

def _is_number(s: Any) -> bool:
    """判断是否为有效数字"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False

def search_flight_case(
    departureDate: str,
    qTraceId: str,
    spanId: str,
    flightNumber: Optional[str] = None  # 明确参数可为空
) -> Dict[str, Any]:
    url = "http://paoding.corp.qunar.com/open/case/detail"
    
    # 参数校验（关键必填项）
    if not departureDate or not qTraceId or not spanId:
        raise ValueError("departureDate/qTraceId/spanId 为必填参数")

    params = {
        "flightType": "SINGLE",
        "departureDate": departureDate,
        "flightNumber": flightNumber or "",  # 关键处理：将null转换为空字符串
        "qTraceId": qTraceId,
        "spanId": spanId
    }

    headers = {
        "Paoding-Open-Source": "tradeCore"
    }

    try:
        response = requests.get(
            url,
            params=params,
            headers=headers,
            timeout=10  # 增加超时控制
        )
        response.raise_for_status()
        result = response.json()
    except (RequestException, ValueError) as e:
        return {"error": f"请求失败: {str(e)}"}

    # 防御性处理响应结构
    method1_result = []
    if result.get("domesticListSearchSimpleLog") is not None:
        method1_result = parse_list_data(result.get("domesticListSearchSimpleLog"))
    if result.get("domesticOta") is not None:
        method1_result = parse_domestic_ota(result.get("domesticOta"))
    # 执行方法1
    #method1_result = parse_domestic_ota(json_obj)
    #print("方法1解析结果：", json.dumps(method1_result[:1], indent=2, ensure_ascii=False))
    
    # 执行方法2
    method2_result = method2_parse_detail_table(result)
    #print("\n方法2解析结果：", json.dumps(method2_result[:1], indent=2, ensure_ascii=False))
    
    # 执行方法3
    merged_result = method3_merge_data(method1_result, method2_result)
    return {"data": merged_result}

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        searchResult = search_flight_case(
            departureDate=param.get("departureDate", ""),
            qTraceId=param.get("qTraceId", ""),
            spanId=param.get("spanId", ""),
            flightNumber=param.get("flightNumber")  # 允许为空
        )
        priceList = searchResult.get("data")
        questionParam=param.get('questionParam')
        filterDatas = filter_flight_data(questionParam, priceList)
        return {"priceList": filterDatas}
    except KeyError as e:
        return {"error": f"缺少必要参数: {str(e)}", "priceList": []}
    except ValueError as e:
        return {"error": str(e), "priceList": []}
    except Exception as e:
        return {"error": f"系统异常: {str(e)}", "priceList": []}


if __name__ == "__main__":
    # 假设输入数据存储在input_data变量中
    param = {
        "departureDate": "2025-03-06",
        "qTraceId": "ops_slugger_250303.114244.10.95.133.35.3895356.9009549036_1",
        "spanId": "1.3.1.27.1",
        "flightNumber": "MU2109",
        "questionParam": {
            "flightNumber": "MU2109",
            "searchDateTime": "2025-03-03",
            "arrivalCity": "PEK",
            "carrier": "MU",
            "departureCity": "XIY",
            "departureDate": "2025-03-06"
        }
    }
    result = main(param)
    print("\n调用结果：", json.dumps(result, indent=2, ensure_ascii=False))


