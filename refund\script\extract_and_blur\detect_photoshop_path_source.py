#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image, ImageChops, ImageEnhance
import piexif
from pathlib import Path
import requests
import io


def perform_ela(image_data, quality=90, scale=10):
    """
    执行错误等级分析(Error Level Analysis)
    优化版本：更高效的ELA实现，针对真实图片减少计算

    参数:
        image_data: 图片数据（bytes）
        quality: JPEG压缩质量
        scale: 差异放大倍数

    返回:
        ela_image: ELA结果图像
        score: ELA分数（差异程度）
    """
    # 从bytes创建PIL图像
    original = Image.open(io.BytesIO(image_data)).convert("RGB")

    # 获取图像格式
    temp_buffer = io.BytesIO(image_data)
    img_format = Image.open(temp_buffer).format

    # 如果原始图像不是JPEG，ELA分析可能不太可靠
    if img_format != "JPEG":
        # 对于非JPEG图像，返回极低的分数
        return original, 0.001

    # 优化：检查图像大小，对大图像进行下采样以提高性能
    width, height = original.size
    downsample = False
    downsampled_original = None

    if width * height > 1000000:  # 大于1MP的图像
        # 计算缩放比例
        scale_factor = min(1.0, (1000000 / (width * height)) ** 0.5)
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        # 下采样图像
        downsampled_original = original.resize((new_width, new_height), Image.LANCZOS)
        downsample = True

    # 使用原始图像或下采样图像
    img_to_process = downsampled_original if downsample else original

    # 创建内存中的JPEG
    temp_buffer = io.BytesIO()
    img_to_process.save(temp_buffer, "JPEG", quality=quality)
    temp_buffer.seek(0)

    # 读取重新保存的图像
    resaved = Image.open(temp_buffer).convert("RGB")

    # 计算原始图像和重新保存的图像之间的差异
    ela_image = ImageChops.difference(img_to_process, resaved)

    # 放大差异以便更容易看到
    extrema = ela_image.getextrema()
    max_diff = max([ex[1] for ex in extrema])
    if max_diff == 0:
        max_diff = 1

    # 优化：计算ELA分数 - 使用像素采样以提高性能
    # 对于大图像，不需要分析每个像素来获得准确的ELA得分
    ela_array = np.array(ela_image)
    sample_step = max(1, min(width, height) // 500)  # 基于图像尺寸的采样步长

    # 采样计算分数
    sampled_diff = ela_array[::sample_step, ::sample_step]
    diff_mean = np.mean(sampled_diff) / 255.0

    # 大幅降低敏感度，减少误报
    score = diff_mean / 8.0  # 进一步降低分数敏感度

    # 获取图像尺寸，小图像更容易产生假阳性结果
    size_factor = 1.0
    if width * height < 500 * 500:
        size_factor = 0.3  # 小图像大幅降低ELA分数权重

    # 快速检测边缘 - 使用采样版本
    sample_gray = (
        cv2.cvtColor(
            cv2.resize(
                np.array(img_to_process), (500, 500), interpolation=cv2.INTER_AREA
            ),
            cv2.COLOR_RGB2GRAY,
        )
        if downsample
        else cv2.cvtColor(
            np.array(img_to_process)[::sample_step, ::sample_step], cv2.COLOR_RGB2GRAY
        )
    )
    edges = cv2.Canny(sample_gray, 100, 200)
    edge_density = np.sum(edges > 0) / sample_gray.size

    # 复杂图像的分数应该降低
    complexity_factor = 1.0
    if edge_density > 0.03:  # 降低门槛，更多图像被视为复杂
        complexity_factor = 0.4  # 大幅降低ELA分数

    # 应用复杂度和尺寸因子
    score = score * size_factor * complexity_factor

    # 如果使用了下采样，需要将ELA图像恢复到原始大小以供可视化（如需要）
    if downsample and original.size != ela_image.size:
        ela_image = ela_image.resize(original.size, Image.LANCZOS)

    # 放大差异
    ela_image = ImageEnhance.Brightness(ela_image).enhance(scale / max_diff)

    return ela_image, score


def noise_analysis(image_data):
    """
    执行噪声分析，检测图像中噪声的不一致性
    优化版本：更高效的噪声分析，使用下采样和优化的计算

    参数:
        image_data: 图片数据（bytes）

    返回:
        noise_image: 噪声图像
        noise_score: 噪声不一致性分数
    """
    # 将bytes转换为numpy数组
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("无法解码图像数据")

    # 获取图像尺寸
    height, width = image.shape[:2]

    # 优化：对大图像进行下采样以提高性能
    if width * height > 1000000:  # 大于1MP的图像
        # 计算下采样比例
        scale_factor = min(1.0, (1000000 / (width * height)) ** 0.5)
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        # 下采样图像
        image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        height, width = image.shape[:2]

    # 获取图像尺寸因子，对于小图像降低检测灵敏度
    size_factor = min(1.0, (height * width) / (1500 * 1500))

    # 获取图像格式信息
    try:
        img = Image.open(io.BytesIO(image_data))
        img_format = img.format
        # 压缩图像格式通常会有更高的基础噪声
        format_factor = (
            2.0 if img_format in ["JPEG", "JPG"] else 1.0
        )  # 增加JPEG的噪声容忍度
    except Exception:
        format_factor = 1.0

    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 应用高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 计算噪声
    noise = cv2.absdiff(gray, blurred)

    # 优化：使用采样和下采样技术进行块噪声分析
    # 更大的块尺寸，减少处理的块数
    block_size = 64

    # 计算需要处理的块数，确保足够代表性
    num_blocks_height = max(4, height // block_size)
    num_blocks_width = max(4, width // block_size)

    # 均匀选择块
    block_noises = []
    for i in range(num_blocks_height):
        y = i * height // num_blocks_height
        for j in range(num_blocks_width):
            x = j * width // num_blocks_width
            # 确保不超出边界
            end_y = min(y + block_size, height)
            end_x = min(x + block_size, width)

            block = noise[y:end_y, x:end_x]
            block_noises.append(np.mean(block))

    # 计算块噪声的变异系数
    if block_noises:
        noise_cv = (
            np.std(block_noises) / np.mean(block_noises)
            if np.mean(block_noises) > 0
            else 0
        )
        # 提高阈值，减少误判
        if noise_cv < 0.05:  # 调整阈值
            noise_score = (np.std(noise) / 255.0) / format_factor * 0.7  # 降低权重
        else:
            noise_score = (np.std(noise) / 255.0) / format_factor
    else:
        noise_score = (np.std(noise) / 255.0) / format_factor

    # 优化：仅对采样区域进行复杂度分析
    # 计算边缘密度 - 使用下采样提高性能
    downsample_factor = max(1, min(width, height) // 300)  # 增加下采样因子
    small_gray = cv2.resize(
        gray, (width // downsample_factor, height // downsample_factor)
    )
    edges = cv2.Canny(small_gray, 100, 200)
    edge_density = np.sum(edges > 0) / (small_gray.shape[0] * small_gray.shape[1])

    # 复杂图像通常有更多自然噪声变化，应降低其噪声异常分数
    if edge_density > 0.04:  # 降低门槛
        noise_score *= 0.4  # 大幅降低复杂图像的噪声异常分数

    # 创建热图以显示噪声分布
    noise_image = cv2.applyColorMap(noise, cv2.COLORMAP_JET)

    return noise_image, noise_score


def check_metadata(image_data):
    """
    检查图像元数据中的PS痕迹

    参数:
        image_data: 图片数据（bytes）

    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 从bytes创建PIL图像
        img = Image.open(io.BytesIO(image_data))
        metadata_info = {}
        has_ps_metadata = False

        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])

            # 检查软件信息
            if piexif.ImageIFD.Software in exif_dict["0th"]:
                software = exif_dict["0th"][piexif.ImageIFD.Software].decode(
                    "utf-8", errors="ignore"
                )
                metadata_info["软件"] = software
                if "photoshop" in software.lower() or "adobe" in software.lower():
                    has_ps_metadata = True

            # 检查修改历史
            if piexif.ExifIFD.MakerNote in exif_dict["Exif"]:
                maker_note = exif_dict["Exif"][piexif.ExifIFD.MakerNote].decode(
                    "utf-8", errors="ignore"
                )
                metadata_info["制造商备注"] = maker_note
                if "photoshop" in maker_note.lower() or "adobe" in maker_note.lower():
                    has_ps_metadata = True

        # 检查XMP数据
        if "photoshop" in str(img.info).lower() or "adobe" in str(img.info).lower():
            has_ps_metadata = True
            metadata_info["其他元数据"] = "包含Adobe/Photoshop相关信息"

        return has_ps_metadata, metadata_info

    except Exception as e:
        print(f"检查元数据时出错: {e}")
        return False, {"错误": str(e)}


def detect_frequency_artifacts(image_data):
    """
    检测频域人工痕迹，AI生成图像常有特定频率特征
    优化版本：更高效的频域分析，使用下采样减少计算量

    参数:
        image_data: 图片数据（bytes）

    返回:
        freq_score: 频域人工痕迹分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")

        # 获取图像尺寸
        height, width = image.shape[:2]

        # 优化：对大图像进行下采样，FFT计算量与像素数成正比
        max_dimension = 512  # 频域分析最大维度

        if max(height, width) > max_dimension:
            # 保持宽高比的下采样
            scale_factor = max_dimension / max(height, width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            image = cv2.resize(
                image, (new_width, new_height), interpolation=cv2.INTER_AREA
            )
            height, width = new_height, new_width

        # 获取图像尺寸，对于小图像降低检测灵敏度
        size_factor = min(1.0, (height * width) / (1000 * 1000))

        # 转为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 优化：应用汉明窗减少边缘效应
        y, x = np.ogrid[:height, :width]
        hann_window = (0.5 - 0.5 * np.cos(2 * np.pi * y / height)) * (
            0.5 - 0.5 * np.cos(2 * np.pi * x / width)
        )
        windowed_gray = gray * hann_window

        # 应用FFT
        f = np.fft.fft2(windowed_gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift) + 1)

        # 优化：使用更精简的分析方法
        # 1. 快速检查高频比例
        max_val = np.max(magnitude_spectrum)
        high_freq_ratio = (
            np.sum(magnitude_spectrum > max_val * 0.5) / magnitude_spectrum.size
        )

        # 2. 检查径向对称性
        center_y, center_x = height // 2, width // 2

        # 创建径向距离矩阵
        y, x = np.ogrid[:height, :width]
        dist_from_center = np.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)

        # 优化：使用更少的环形分析
        max_dist = np.max(dist_from_center)
        num_rings = 10  # 减少环数
        ring_values = []

        for i in range(num_rings):
            inner_radius = i * max_dist / num_rings
            outer_radius = (i + 1) * max_dist / num_rings
            ring_mask = (dist_from_center >= inner_radius) & (
                dist_from_center < outer_radius
            )
            if np.any(ring_mask):
                ring_values.append(np.mean(magnitude_spectrum[ring_mask]))

        # 计算环形区域值的变异系数
        ring_cv = (
            np.std(ring_values) / np.mean(ring_values)
            if ring_values and np.mean(ring_values) > 0
            else 1.0
        )

        # 3. 优化：简化角度分析，使用采样而不是全像素计算
        angle_bins = 18  # 减少角度bin数
        angle_values = np.zeros(angle_bins)

        # 使用更大的步长采样角度分布
        sample_step = max(1, min(height, width) // 100)

        for y in range(0, height, sample_step):
            for x in range(0, width, sample_step):
                if abs(x - center_x) > 5 or abs(y - center_y) > 5:  # 跳过中心附近区域
                    angle = np.arctan2(y - center_y, x - center_x) + np.pi  # 0到2pi
                    bin_idx = int(angle / (2 * np.pi) * angle_bins)
                    if bin_idx < angle_bins:  # 安全检查
                        angle_values[bin_idx] += magnitude_spectrum[y, x]

        # 归一化
        angle_values = (
            angle_values / np.sum(angle_values)
            if np.sum(angle_values) > 0
            else angle_values
        )

        # 计算角度分布的均匀性
        angle_cv = (
            np.std(angle_values) / np.mean(angle_values)
            if np.mean(angle_values) > 0
            else 1.0
        )

        # 综合分析，提高阈值，降低误报
        freq_score = 0
        explanation = ""

        # 检查高频比例 - 提高阈值
        if high_freq_ratio < 0.0025:  # 大幅提高阈值
            freq_score = max(freq_score, 0.6 * size_factor)
            explanation += f"检测到可疑的频域模式，可能是AI合成图像 (高频比例: {high_freq_ratio:.4f})"
        elif high_freq_ratio < 0.01:  # 提高阈值
            freq_score = max(freq_score, 0.3 * size_factor)
            explanation += (
                f"频域特征有轻微异常，可能经过处理 (高频比例: {high_freq_ratio:.4f})"
            )

        # 检查径向对称性 - 提高阈值
        if ring_cv < 0.2:  # 大幅提高阈值
            freq_score = max(freq_score, 0.55 * size_factor)
            if explanation:
                explanation += "; "
            explanation += f"频域径向分布异常均匀 (变异系数: {ring_cv:.4f})"

        # 检查角度分布 - 提高阈值
        if angle_cv < 0.15:  # 大幅提高阈值
            freq_score = max(freq_score, 0.5 * size_factor)
            if explanation:
                explanation += "; "
            explanation += f"频域角度分布异常均匀 (变异系数: {angle_cv:.4f})"

        # 综合判断，需要多个因素同时满足才给出较高分
        if (
            (high_freq_ratio < 0.0025 and ring_cv < 0.25)
            or (high_freq_ratio < 0.0025 and angle_cv < 0.2)
            or (ring_cv < 0.2 and angle_cv < 0.15)
        ):
            freq_score = max(freq_score, 0.7 * size_factor)

        if not explanation:
            explanation = f"频域特征基本正常 (高频比例: {high_freq_ratio:.4f}, 径向变异: {ring_cv:.4f})"

        return freq_score, explanation

    except Exception as e:
        print(f"检测频域痕迹时出错: {e}")
        return 0, f"检测频域痕迹时出错: {e}"


def analyze_texture_consistency(image_data):
    """
    分析纹理一致性，AI生成图像常有纹理异常

    参数:
        image_data: 图片数据（bytes）

    返回:
        texture_score: 纹理不一致性分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")

        # 获取图像尺寸，对于小图像降低检测灵敏度
        height, width = image.shape[:2]
        size_factor = min(1.0, (height * width) / (1000 * 1000))

        # 提取纹理特征
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 使用LBP (Local Binary Pattern) 提取纹理
        def calculate_lbp(image, P=8, R=1):
            rows, cols = image.shape
            result = np.zeros((rows, cols), dtype=np.uint8)
            for i in range(R, rows - R):
                for j in range(R, cols - R):
                    center = image[i, j]
                    binary = []
                    for k in range(P):
                        angle = 2 * np.pi * k / P
                        x = i + R * np.cos(angle)
                        y = j + R * np.sin(angle)
                        x1, y1 = int(np.floor(x)), int(np.floor(y))
                        x2, y2 = min(x1 + 1, rows - 1), min(y1 + 1, cols - 1)
                        tx, ty = x - x1, y - y1
                        val = (
                            (1 - tx) * (1 - ty) * image[x1, y1]
                            + tx * (1 - ty) * image[x2, y1]
                            + (1 - tx) * ty * image[x1, y2]
                            + tx * ty * image[x2, y2]
                        )
                        binary.append(1 if val >= center else 0)
                    decimal = sum([binary[k] * (2**k) for k in range(P)])
                    result[i, j] = decimal
            return result

        lbp = calculate_lbp(gray)

        # 计算局部区域LBP直方图标准差，检测不一致性
        block_size = 32
        rows, cols = gray.shape
        stds = []

        # 确保有足够的块进行分析
        if rows < block_size or cols < block_size:
            return 0, "图像太小，无法进行纹理分析"

        for i in range(0, rows - block_size + 1, block_size // 2):
            for j in range(0, cols - block_size + 1, block_size // 2):
                block = lbp[i : i + block_size, j : j + block_size]
                hist, _ = np.histogram(block, bins=256, range=(0, 256))
                hist = hist / np.sum(hist)  # 归一化
                stds.append(np.std(hist))

        # 分析标准差的变异
        texture_variation = np.var(stds) if stds else 0

        # 额外检测纹理重复模式 - AI生成图像常有重复纹理
        # 分析灰度图像的自相关性
        autocorr = cv2.matchTemplate(gray, gray, cv2.TM_CCOEFF_NORMED)
        # 找到除了自身最大值外的其他高相关区域
        peak_threshold = 0.85  # 提高阈值
        peak_count = np.sum(autocorr > peak_threshold)

        # 调整阈值判断，显著提高阈值降低误报
        texture_score = 0
        explanation = ""

        # 检测纹理过于一致 - 提高阈值
        if texture_variation < 0.00005:  # 大幅提高阈值，真实图像很少有如此一致的纹理
            texture_score = max(texture_score, 0.65 * size_factor)
            explanation = f"检测到异常的纹理一致性，可能是AI合成图像 (纹理变异: {texture_variation:.6f})"

        # 检测纹理过于不一致 - 提高阈值
        elif texture_variation > 0.02:  # 大幅提高阈值
            texture_score = max(texture_score, 0.55 * size_factor)
            explanation = f"检测到异常的纹理不一致性，可能是拼接或AI合成图像 (纹理变异: {texture_variation:.6f})"

        # 检测重复纹理模式 - 提高阈值
        if peak_count > (height * width * 0.015):  # 提高阈值
            texture_score = max(texture_score, 0.6 * size_factor)
            explanation += f" 检测到不自然的纹理重复模式 (峰值数: {peak_count})"

        # 综合判断，需要多个因素同时满足才给出较高分
        # 添加对真实图像常见特征的额外检查
        edge_density = np.sum(cv2.Canny(gray, 100, 200) > 0) / (height * width)
        if edge_density > 0.1:  # 真实图像通常有更多自然边缘
            texture_score *= 0.8  # 降低分数

        # 检查图像中的噪声分布是否自然
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        noise = cv2.absdiff(gray, blurred)
        noise_std = np.std(noise)

        if noise_std > 5:  # 真实图像通常有自然噪声
            texture_score *= 0.85  # 降低分数

        if not explanation:
            explanation = f"纹理一致性正常 (纹理变异: {texture_variation:.6f})"

        return texture_score, explanation

    except Exception as e:
        print(f"分析纹理一致性时出错: {e}")
        return 0, f"分析纹理一致性时出错: {e}"


def check_facial_anomalies(image_data):
    """
    检查面部异常，AI生成的人脸常有细节问题

    参数:
        image_data: 图片数据（bytes）

    返回:
        face_score: 面部异常分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")

        # 获取图像尺寸
        height, width = image.shape[:2]

        # 对于低分辨率图像，面部分析可能不准确
        if height < 250 or width < 250:  # 提高最小尺寸要求
            return 0, "图像分辨率过低，面部分析可能不准确"

        # 使用人脸检测器
        face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + "haarcascade_frontalface_default.xml"
        )
        eye_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + "haarcascade_eye.xml"
        )

        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)

        if len(faces) == 0:
            return 0, "未检测到人脸"

        face_score = 0
        anomalies = []

        for x, y, w, h in faces:
            face_region = gray[y : y + h, x : x + w]
            face_color = image[y : y + h, x : x + w]

            # 检测眼睛
            eyes = eye_cascade.detectMultiScale(face_region)

            # 检查眼睛对称性和位置
            if len(eyes) == 2:
                eye1, eye2 = eyes[0], eyes[1]
                eye_distance = abs((eye1[0] + eye1[2] // 2) - (eye2[0] + eye2[2] // 2))
                eye_height_diff = abs(
                    (eye1[1] + eye1[3] // 2) - (eye2[1] + eye2[3] // 2)
                )

                # 相对于脸部宽度的眼睛水平距离
                rel_eye_distance = eye_distance / w

                # 调整阈值，提高检测门槛
                if eye_height_diff > 0.2 * h:  # 提高眼睛高度差异阈值
                    anomalies.append("眼睛高度不对称")
                    face_score = max(face_score, 0.55)  # 降低分数权重

                # 检测眼睛水平距离异常 - 提高阈值
                if (
                    rel_eye_distance < 0.15 or rel_eye_distance > 0.55
                ):  # 更极端的眼睛间距才判断为异常
                    anomalies.append(f"眼睛水平间距异常 ({rel_eye_distance:.2f})")
                    face_score = max(face_score, 0.6)

            # 检查面部纹理
            face_texture = cv2.calcHist([face_region], [0], None, [256], [0, 256])
            face_texture = cv2.normalize(face_texture, face_texture).flatten()
            texture_entropy = -np.sum(face_texture * np.log2(face_texture + 1e-10))

            # 提高阈值
            if texture_entropy < 2.5:  # 大幅提高阈值
                anomalies.append("面部纹理异常平滑")
                face_score = max(face_score, 0.65)  # 降低分数权重

            # 检查面部边缘
            edges = cv2.Canny(face_region, 100, 200)
            edge_density = np.sum(edges > 0) / (w * h)

            # 提高阈值
            if edge_density < 0.03:  # 提高阈值
                anomalies.append("面部边缘细节不足")
                face_score = max(face_score, 0.55)

            # 检查面部颜色一致性
            hsv_face = cv2.cvtColor(face_color, cv2.COLOR_BGR2HSV)
            h_channel = hsv_face[:, :, 0]
            s_channel = hsv_face[:, :, 1]

            # 分析色调和饱和度的分布 - 提高阈值
            h_std = np.std(h_channel)
            s_std = np.std(s_channel)

            if h_std < 7 or s_std < 10:  # 提高阈值
                anomalies.append("面部色调变化异常均匀")
                face_score = max(face_score, 0.5)

            # 添加更多真实面部特征检查以减少误报
            # 检查是否有自然的阴影变化
            v_channel = hsv_face[:, :, 2]
            v_std = np.std(v_channel)

            if v_std > 30:  # 真实面部通常有明显的明暗变化
                face_score *= 0.8  # 降低异常分数

            # 检查是否有自然的斑点/痘印/雀斑
            # 使用膨胀和腐蚀操作来检测小斑点
            kernel = np.ones((3, 3), np.uint8)
            dilated = cv2.dilate(face_region, kernel, iterations=1)
            eroded = cv2.erode(face_region, kernel, iterations=1)
            spots = cv2.absdiff(dilated, eroded)
            spot_count = np.sum(spots > 30)

            if spot_count > 100:  # 真实面部通常有一些斑点
                face_score *= 0.85  # 降低异常分数

            # 检查是否有肤色自然的渐变，减少过度上色的假阳性
            # 采样面部不同区域进行比较
            face_height, face_width = face_region.shape
            if face_height > 50 and face_width > 50:
                forehead = face_region[
                    0 : face_height // 3, face_width // 4 : 3 * face_width // 4
                ]
                cheek = face_region[
                    face_height // 3 : 2 * face_height // 3,
                    face_width // 4 : 3 * face_width // 4,
                ]
                chin = face_region[
                    2 * face_height // 3 :, face_width // 4 : 3 * face_width // 4
                ]

                forehead_mean = np.mean(forehead)
                cheek_mean = np.mean(cheek)
                chin_mean = np.mean(chin)

                # 计算各区域间的差异
                region_diffs = [
                    abs(forehead_mean - cheek_mean),
                    abs(cheek_mean - chin_mean),
                    abs(forehead_mean - chin_mean),
                ]

                # 如果有自然的明暗差异，降低异常分数
                if max(region_diffs) > 10:
                    face_score *= 0.9

        # 要求多个异常同时出现才给出较高分数
        if len(anomalies) < 2:
            face_score *= 0.8

        if len(anomalies) >= 3:
            face_score = min(0.85, face_score * 1.1)  # 多个异常加权，但设置上限

        if anomalies:
            explanation = f"检测到面部异常: {', '.join(anomalies)}"
        else:
            explanation = "未检测到明显的面部异常"

        return face_score, explanation

    except Exception as e:
        print(f"检查面部异常时出错: {e}")
        return 0, f"检查面部异常时出错: {e}"


def detect_document_structure_anomalies(image_data):
    """
    检测文档结构异常，AI合成的文档常有特定的规律和异常

    参数:
        image_data: 图片数据（bytes）

    返回:
        structure_score: 结构异常分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")

        # 获取图像尺寸
        height, width = image.shape[:2]

        # 转为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 检测边缘
        edges = cv2.Canny(gray, 50, 150)

        # 检测直线
        lines = cv2.HoughLinesP(
            edges, 1, np.pi / 180, threshold=100, minLineLength=50, maxLineGap=10
        )

        # 如果没有检测到足够的线条，可能不是结构化文档
        if lines is None or len(lines) < 5:
            return 0.1, "未检测到足够的结构线条，可能不是表格类文档"

        # 分析水平和垂直线的分布
        horizontal_lines = []
        vertical_lines = []

        for line in lines:
            x1, y1, x2, y2 = line[0]
            if abs(x2 - x1) > abs(y2 - y1):  # 水平线
                horizontal_lines.append((y1 + y2) / 2)  # 记录y坐标平均值
            else:  # 垂直线
                vertical_lines.append((x1 + x2) / 2)  # 记录x坐标平均值

        # 对线条位置进行排序和去重
        horizontal_lines = sorted(horizontal_lines)
        vertical_lines = sorted(vertical_lines)

        # 计算相邻线条间距的变化
        h_diffs = [
            horizontal_lines[i + 1] - horizontal_lines[i]
            for i in range(len(horizontal_lines) - 1)
        ]
        v_diffs = [
            vertical_lines[i + 1] - vertical_lines[i]
            for i in range(len(vertical_lines) - 1)
        ]

        # 计算间距的标准差，过于规则的结构可能是AI生成的
        h_std = np.std(h_diffs) if h_diffs else 0
        v_std = np.std(v_diffs) if v_diffs else 0

        # 正常文档的线条间距应有一定变化
        structure_score = 0
        anomalies = []

        # 检查水平线 - 提高阈值，降低误判
        if len(horizontal_lines) >= 4:  # 需要更多线才能判断
            h_spacing_regularity = (
                h_std / np.mean(h_diffs) if np.mean(h_diffs) > 0 else 0
            )
            if h_spacing_regularity < 0.05:  # 大幅提高阈值
                structure_score = max(structure_score, 0.65)  # 降低分数
                anomalies.append(
                    f"水平线间距过于规则 (变异系数: {h_spacing_regularity:.4f})"
                )

        # 检查垂直线 - 提高阈值，降低误判
        if len(vertical_lines) >= 4:  # 需要更多线才能判断
            v_spacing_regularity = (
                v_std / np.mean(v_diffs) if np.mean(v_diffs) > 0 else 0
            )
            if v_spacing_regularity < 0.05:  # 大幅提高阈值
                structure_score = max(structure_score, 0.65)  # 降低分数
                anomalies.append(
                    f"垂直线间距过于规则 (变异系数: {v_spacing_regularity:.4f})"
                )

        # 检测文本行的异常规律性
        # 使用OCR或文本区域检测
        # 这里使用简化的方法：检测水平投影寻找文本行
        h_projection = np.sum(gray < 128, axis=1)  # 假设深色像素是文本

        # 使用自定义函数寻找文本行的峰值，替代signal.find_peaks
        def find_peaks(x, height=None):
            """
            简单的峰值检测函数，替代signal.find_peaks

            参数:
                x: 输入数组
                height: 峰值最小高度

            返回:
                peaks: 峰值位置列表
            """
            if height is None:
                height = np.mean(x) * 0.5

            peaks = []
            for i in range(1, len(x) - 1):
                if x[i] > height and x[i] > x[i - 1] and x[i] > x[i + 1]:
                    peaks.append(i)
            return np.array(peaks)

        # 寻找文本行的峰值
        peaks = find_peaks(h_projection, height=width / 15)

        if len(peaks) >= 4:  # 需要足够多的文本行来判断规律性
            # 计算文本行间距
            text_line_spaces = [peaks[i + 1] - peaks[i] for i in range(len(peaks) - 1)]
            text_spacing_std = np.std(text_line_spaces) if text_line_spaces else 0
            text_spacing_regularity = (
                text_spacing_std / np.mean(text_line_spaces)
                if np.mean(text_line_spaces) > 0
                else 0
            )

            if text_spacing_regularity < 0.03:  # 大幅提高阈值
                structure_score = max(structure_score, 0.7)  # 降低分数
                anomalies.append(
                    f"文本行间距过于规则 (变异系数: {text_spacing_regularity:.4f})"
                )

        # 检测文本区块的密度一致性
        block_size = min(height, width) // 8
        text_densities = []

        for i in range(0, height - block_size, block_size):
            for j in range(0, width - block_size, block_size):
                block = gray[i : i + block_size, j : j + block_size]
                text_pixels = np.sum(block < 128)  # 假设深色像素是文本
                density = text_pixels / (block_size * block_size)
                if density > 0.05:  # 只考虑有文本的区块
                    text_densities.append(density)

        if text_densities and len(text_densities) >= 5:  # 需要足够多的文本块才能判断
            density_std = np.std(text_densities)
            density_mean = np.mean(text_densities)
            density_cv = density_std / density_mean if density_mean > 0 else 0

            # 提高阈值
            if density_cv < 0.15:  # 大幅提高阈值
                structure_score = max(structure_score, 0.65)  # 降低分数
                anomalies.append(f"文本密度过于一致 (变异系数: {density_cv:.4f})")

        # 要求多个异常特征同时存在才给出高分
        if len(anomalies) == 1:
            structure_score *= 0.85  # 单一异常降低得分
        elif len(anomalies) >= 3:
            structure_score = min(
                0.85, structure_score * 1.1
            )  # 多异常略微加权，但设置上限

        # 如果图像有自然的噪声和变化，降低得分
        noise = cv2.GaussianBlur(gray, (0, 0), 2)
        noise = cv2.absdiff(gray, noise)
        if np.mean(noise) > 5:  # 真实文档通常有一定噪声
            structure_score *= 0.85

        # 形成解释
        if anomalies:
            explanation = f"检测到文档结构异常: {', '.join(anomalies)}"
        else:
            explanation = "文档结构特征正常"

        return structure_score, explanation

    except Exception as e:
        print(f"检测文档结构异常时出错: {e}")
        return 0, f"检测文档结构异常时出错: {e}"


def detect_text_anomalies(image_data):
    """
    检测文本异常，AI生成的文档可能包含不自然的文本特征

    参数:
        image_data: 图片数据（bytes）

    返回:
        text_score: 文本异常分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")

        # 转为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 二值化处理，突出文本
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 寻找文本轮廓
        contours, _ = cv2.findContours(
            binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        # 过滤掉太小的轮廓
        min_contour_area = 20
        text_contours = [
            cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area
        ]

        if not text_contours:
            return 0.1, "未检测到有效文本"

        # 分析文本特征
        char_heights = []
        char_widths = []
        char_areas = []
        char_densities = []

        for cnt in text_contours:
            x, y, w, h = cv2.boundingRect(cnt)
            area = cv2.contourArea(cnt)
            density = area / (w * h) if w * h > 0 else 0

            # 只分析可能是字符的轮廓
            if 5 < h < 100 and 5 < w < 100:
                char_heights.append(h)
                char_widths.append(w)
                char_areas.append(area)
                char_densities.append(density)

        if len(char_heights) < 10:  # 需要足够多的字符才能判断
            return 0.1, "未检测到足够的有效字符"

        # 分析特征统计
        height_cv = (
            np.std(char_heights) / np.mean(char_heights)
            if np.mean(char_heights) > 0
            else 0
        )
        width_cv = (
            np.std(char_widths) / np.mean(char_widths)
            if np.mean(char_widths) > 0
            else 0
        )
        area_cv = (
            np.std(char_areas) / np.mean(char_areas) if np.mean(char_areas) > 0 else 0
        )
        density_cv = (
            np.std(char_densities) / np.mean(char_densities)
            if np.mean(char_densities) > 0
            else 0
        )

        # 检测异常 - 提高阈值，降低误判
        text_score = 0
        anomalies = []

        # 真实文本应该有一定的变化 - 提高阈值
        if height_cv < 0.08:  # 大幅提高阈值
            text_score = max(text_score, 0.55)
            anomalies.append(f"字符高度过于一致 (变异系数: {height_cv:.4f})")

        if width_cv < 0.15:  # 大幅提高阈值
            text_score = max(text_score, 0.5)
            anomalies.append(f"字符宽度过于一致 (变异系数: {width_cv:.4f})")

        if density_cv < 0.12:  # 大幅提高阈值
            text_score = max(text_score, 0.55)
            anomalies.append(f"字符密度过于一致 (变异系数: {density_cv:.4f})")

        # 检测文本边缘的锐利度
        # AI生成的文本边缘通常过于锐利或过于模糊
        edge_strength = cv2.Laplacian(gray, cv2.CV_64F).var()

        # 分析归一化边缘强度
        normalized_edge = edge_strength / (np.std(gray) + 1e-5)

        if normalized_edge > 60:  # 提高阈值
            text_score = max(text_score, 0.65)
            anomalies.append(f"文本边缘异常锐利 (边缘强度: {normalized_edge:.2f})")
        elif normalized_edge < 8:  # 提高阈值
            text_score = max(text_score, 0.5)
            anomalies.append(f"文本边缘异常模糊 (边缘强度: {normalized_edge:.2f})")

        # 检查文字笔画的一致性 - 真实文本应有一定笔画粗细变化
        # 分析字符轮廓的形态学骨架
        skel = np.zeros(binary.shape, np.uint8)
        element = cv2.getStructuringElement(cv2.MORPH_CROSS, (3, 3))
        temp = binary.copy()

        while True:
            eroded = cv2.erode(temp, element)
            temp_dil = cv2.dilate(eroded, element)
            temp2 = cv2.subtract(temp, temp_dil)
            skel = cv2.bitwise_or(skel, temp2)
            temp = eroded.copy()

            if cv2.countNonZero(temp) == 0:
                break

        # 计算骨架和原字符的比例，检测笔画一致性
        if cv2.countNonZero(binary) > 0:
            stroke_ratio = cv2.countNonZero(skel) / cv2.countNonZero(binary)

            if stroke_ratio < 0.1 or stroke_ratio > 0.4:  # 笔画比例异常
                text_score = max(text_score, 0.5)
                anomalies.append(f"文字笔画特征异常 (骨架比例: {stroke_ratio:.4f})")

        # 要求多个异常特征同时存在才给出高分
        if len(anomalies) == 1:
            text_score *= 0.8  # 单一异常大幅降低得分
        elif len(anomalies) >= 3:
            text_score = min(0.85, text_score * 1.1)  # 多异常略微加权，但设置上限

        # 形成解释
        if anomalies:
            explanation = f"检测到文本异常: {', '.join(anomalies)}"
        else:
            explanation = "文本特征正常"

        return text_score, explanation

    except Exception as e:
        print(f"检测文本异常时出错: {e}")
        return 0, f"检测文本异常时出错: {e}"


def detect_photoshop_and_ai(image_data, ela_quality=90, ela_scale=10):
    """
    综合检测图像是否被PS过或AI合成
    优化版本：针对真实图片提前退出，减少不必要的计算

    参数:
        image_data: 图片数据（bytes）
        ela_quality: ELA分析的JPEG质量
        ela_scale: ELA差异放大倍数

    返回:
        result: 检测结果字典
    """
    result = {"ps_probability": 0.0, "ai_probability": 0.0, "analysis_results": {}}

    # 尝试获取图像格式和尺寸
    image_quality_factor = 1.0
    is_document = False  # 初始化is_document变量

    try:
        img = Image.open(io.BytesIO(image_data))
        img_format = img.format
        width, height = img.size
        result["image_info"] = {"format": img_format, "width": width, "height": height}

        # 对于低分辨率图像，提高阈值，降低误判可能性
        low_res_factor = 1.0
        if width * height < 500 * 500:
            low_res_factor = 1.8  # 提高低分辨率图像的阈值因子

        # 对于非JPEG格式和小尺寸图像，ELA和噪声分析可能不太可靠
        if img_format != "JPEG" and img_format != "JPG":
            image_quality_factor = 1.8  # 提高非JPEG图像的阈值因子

        # 检测图像的位深度，高位深图像通常有更多细节
        if hasattr(img, "bits") and img.bits > 8:
            image_quality_factor *= 1.3

        # 首先尝试判断是否是文档类图像
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is not None:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            # 检测文本比例，文档类图像白底黑字居多
            text_ratio = np.sum(gray < 128) / (width * height)
            if text_ratio > 0.05 and text_ratio < 0.4:
                # 可能是文档类图像，调整分析因子
                result["image_info"]["document_type"] = "可能是文档类图像"
                image_quality_factor *= 0.8  # 更保守的文档图像调整因子
                is_document = True  # 设置is_document标志
    except Exception:
        low_res_factor = 1.0

    # 应用综合质量因子
    quality_factor = low_res_factor * image_quality_factor

    # 优化：首先检查元数据 - 速度快且可靠
    try:
        has_ps_metadata, metadata_info = check_metadata(image_data)
        result["analysis_results"]["metadata"] = metadata_info

        if has_ps_metadata:
            result["analysis_results"][
                "metadata_explanation"
            ] = "检测到Photoshop/Adobe相关元数据"
            result["ps_probability"] = max(
                result["ps_probability"], 0.85
            )  # 元数据是很强的证据，但单独不足以确定
        else:
            result["analysis_results"][
                "metadata_explanation"
            ] = "未检测到Photoshop/Adobe相关元数据"
    except Exception as e:
        result["analysis_results"]["metadata_check_error"] = str(e)

    # 优化：执行快速噪声分析
    try:
        noise_image, noise_score = noise_analysis(image_data)
        result["analysis_results"]["noise_score"] = noise_score

        # 噪声分数解释 - 大幅提高阈值减少误报
        if noise_score < 0.003 * quality_factor:  # 降低阈值
            ps_probability = 0.5  # 降低单一证据的权重
            ai_probability = 0.4
            result["analysis_results"][
                "noise_explanation"
            ] = f"检测到异常低的噪声水平，可能被PS过或AI生成 (分数: {noise_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
            result["ai_probability"] = max(result["ai_probability"], ai_probability)
        elif noise_score > 0.18 * quality_factor:  # 提高阈值
            ps_probability = 0.4  # 降低单一证据的权重
            result["analysis_results"][
                "noise_explanation"
            ] = f"检测到异常高的噪声水平，可能被PS过 (分数: {noise_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
        else:
            result["analysis_results"][
                "noise_explanation"
            ] = f"噪声水平正常 (分数: {noise_score:.4f})"
            # 如果元数据和噪声分析都显示是真实的，提前返回结果
            if (
                not has_ps_metadata
                and result["ps_probability"] < 0.3
                and result["ai_probability"] < 0.3
            ):
                result["ps_conclusion"] = "图像可能未被PS过 (可能性: 低)"
                result["ai_conclusion"] = "图像可能不是AI合成的 (可能性: 低)"
                result["final_conclusion"] = "真实图像"
                # 只计算基本分析，节省时间
                return result
    except Exception as e:
        result["analysis_results"]["noise_analysis_error"] = str(e)

    # 执行ELA分析
    try:
        ela_image, ela_score = perform_ela(
            image_data, quality=ela_quality, scale=ela_scale
        )
        result["analysis_results"]["ela_score"] = ela_score

        # ELA分数解释 - 大幅提高阈值减少误报
        if ela_score > 0.15 * quality_factor:  # 提高阈值
            ps_probability = min(0.85, ela_score * 2.5)  # 降低分数映射比例
            result["analysis_results"][
                "ela_explanation"
            ] = f"检测到较高的错误等级差异，可能被PS过 (分数: {ela_score:.4f})"
            result["ps_probability"] = max(result["ps_probability"], ps_probability)
        else:
            result["analysis_results"][
                "ela_explanation"
            ] = f"错误等级差异较低，可能未被PS过 (分数: {ela_score:.4f})"

        # 如果前三项检查都表明图像是真实的，提前返回结果
        if (
            not has_ps_metadata
            and ela_score < 0.08
            and noise_score > 0.003
            and noise_score < 0.18
            and result["ps_probability"] < 0.3
            and result["ai_probability"] < 0.3
        ):
            result["ps_conclusion"] = "图像可能未被PS过 (可能性: 低)"
            result["ai_conclusion"] = "图像可能不是AI合成的 (可能性: 低)"
            result["final_conclusion"] = "真实图像"
            # 只计算基本分析，节省时间
            return result
    except Exception as e:
        result["analysis_results"]["ela_error"] = str(e)

    # 以下是更耗时的分析，只有当初步分析显示可能有问题时才执行
    # 如果初步分析都指向真实图像，则跳过更耗时的分析
    should_continue = (
        result["ps_probability"] > 0.35 or result["ai_probability"] > 0.35
    )  # 提高继续分析的阈值

    if should_continue:
        # AI检测 - 频域分析
        try:
            freq_score, freq_explanation = detect_frequency_artifacts(image_data)
            result["analysis_results"]["frequency_score"] = freq_score
            result["analysis_results"]["frequency_explanation"] = freq_explanation
            result["ai_probability"] = max(
                result["ai_probability"], freq_score * 0.9
            )  # 降低单一项的影响
        except Exception as e:
            result["analysis_results"]["frequency_analysis_error"] = str(e)

        # AI检测 - 纹理一致性
        try:
            texture_score, texture_explanation = analyze_texture_consistency(image_data)
            result["analysis_results"]["texture_score"] = texture_score
            result["analysis_results"]["texture_explanation"] = texture_explanation
            result["ai_probability"] = max(
                result["ai_probability"], texture_score * 0.9
            )  # 降低单一项的影响
        except Exception as e:
            result["analysis_results"]["texture_analysis_error"] = str(e)

        # 根据图片类型选择性执行部分分析
        # 文档类图像专注于文档结构分析
        if is_document:
            # 检测文档结构异常（适用于发票、表格等）
            try:
                structure_score, structure_explanation = (
                    detect_document_structure_anomalies(image_data)
                )
                result["analysis_results"]["document_structure_score"] = structure_score
                result["analysis_results"][
                    "document_structure_explanation"
                ] = structure_explanation
                result["ai_probability"] = max(
                    result["ai_probability"], structure_score * 1.0
                )  # 降低加权
            except Exception as e:
                result["analysis_results"]["document_structure_error"] = str(e)

            # 检测文本异常
            try:
                text_score, text_explanation = detect_text_anomalies(image_data)
                result["analysis_results"]["text_score"] = text_score
                result["analysis_results"]["text_explanation"] = text_explanation
                result["ai_probability"] = max(
                    result["ai_probability"], text_score * 1.0
                )  # 降低加权
            except Exception as e:
                result["analysis_results"]["text_analysis_error"] = str(e)
        else:
            # 非文档类图像专注于面部分析
            # 尝试检测是否包含人脸
            try:
                nparr = np.frombuffer(image_data, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                if image is not None:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                    face_cascade = cv2.CascadeClassifier(
                        cv2.data.haarcascades + "haarcascade_frontalface_default.xml"
                    )
                    faces = face_cascade.detectMultiScale(gray, 1.3, 5)

                    if len(faces) > 0:
                        # 只有检测到人脸才执行面部分析
                        face_score, face_explanation = check_facial_anomalies(
                            image_data
                        )
                        result["analysis_results"]["face_score"] = face_score
                        result["analysis_results"][
                            "face_explanation"
                        ] = face_explanation
                        result["ai_probability"] = max(
                            result["ai_probability"], face_score * 0.9
                        )  # 降低单一项的影响
            except Exception as e:
                result["analysis_results"]["face_analysis_error"] = str(e)

    # 综合评估 - 提高阈值，减少误判
    # 要求多个证据同时指向PS
    evidence_count = sum(
        1
        for key, value in result["analysis_results"].items()
        if "_score" in key and isinstance(value, (int, float)) and value > 0.4
    )

    # 降低单一证据的权重
    if evidence_count < 2:
        result["ps_probability"] *= 0.7
        result["ai_probability"] *= 0.7

    # 提高综合判断的阈值
    if result["ps_probability"] > 0.85:
        result["ps_conclusion"] = (
            f"图像很可能被PS过 (可能性: {result['ps_probability']:.2f})"
        )
    elif result["ps_probability"] > 0.65:  # 提高阈值
        result["ps_conclusion"] = (
            f"图像可能被PS过 (可能性: {result['ps_probability']:.2f})"
        )
    else:
        result["ps_conclusion"] = (
            f"图像可能未被PS过 (可能性: {1-result['ps_probability']:.2f})"
        )

    if result["ai_probability"] > 0.85:
        result["ai_conclusion"] = (
            f"图像很可能是AI合成的 (可能性: {result['ai_probability']:.2f})"
        )
    elif result["ai_probability"] > 0.65:  # 提高阈值
        result["ai_conclusion"] = (
            f"图像可能是AI合成的 (可能性: {result['ai_probability']:.2f})"
        )
    else:
        result["ai_conclusion"] = (
            f"图像可能不是AI合成的 (可能性: {1-result['ai_probability']:.2f})"
        )

    # 加入证据数量评估
    positive_evidence_count = 0
    strong_evidence_count = 0
    total_evidence_count = 0

    for key, value in result["analysis_results"].items():
        if "_score" in key and isinstance(value, (int, float)):
            if value > 0.5:  # 提高强证据的阈值
                strong_evidence_count += 1
            elif value > 0.35:  # 提高积极证据的阈值
                positive_evidence_count += 1
            total_evidence_count += 1

    evidence_ratio = (strong_evidence_count * 2 + positive_evidence_count) / max(
        1, total_evidence_count
    )
    result["evidence_strength"] = evidence_ratio

    # 最终结论 - 提高判定为PS/AI的门槛
    result["final_conclusion"] = "真实图像"

    if is_document:
        if result["ai_probability"] > 0.75 and strong_evidence_count >= 2:  # 提高阈值
            result["final_conclusion"] = "AI合成图像，可能是伪造文档"
        elif (
            result["ps_probability"] > 0.75 and strong_evidence_count >= 2
        ):  # 提高阈值，需要多证据
            result["final_conclusion"] = "经过处理的图像，可能是修改过的文档"
        elif (
            result["ai_probability"] > 0.6 or result["ps_probability"] > 0.6
        ) and strong_evidence_count >= 2:  # 需要多证据
            result["final_conclusion"] = "可疑图像，可能经过处理或合成"
    else:
        if (
            result["ps_probability"] > 0.8 or result["ai_probability"] > 0.8
        ) and strong_evidence_count >= 2:  # 提高阈值，需要多证据
            result["final_conclusion"] = "人工处理或AI合成图像"
        elif (
            result["ps_probability"] > 0.7 or result["ai_probability"] > 0.7
        ) and strong_evidence_count >= 2:  # 需要多证据
            result["final_conclusion"] = "可疑图像，可能经过处理"

    return result


def main(image_path, fast_mode=True):
    """
    主函数，处理本地图片路径并返回PS和AI合成检测结果

    参数:
        image_path: 本地图片路径
        fast_mode: 是否使用快速模式，牺牲一些准确性换取更快的速度

    返回:
        dict: 检测结果
    """
    try:
        # 读取本地图片数据
        with open(image_path, "rb") as f:
            image_data = f.read()

        # 执行PS和AI合成检测
        result = detect_photoshop_and_ai(image_data, 90, 10)
        return result

    except Exception as e:
        print(f"处理图片时出错: {e}")
        return {"error": f"处理图片时出错: {e}"}


def fast_check_real_image(image_path):
    """
    快速检查图像，只进行基本分析判断图像是否真实
    适用于大批量处理，只筛选出有问题的图像进行更深入分析
    优化版本：大幅提高阈值，减少误报

    参数:
        image_path: 本地图片路径

    返回:
        dict: 包含真实性判断和简要分析的字典
    """
    try:
        # 读取本地图片数据
        with open(image_path, "rb") as f:
            image_data = f.read()

        # 只执行基本分析
        result = {"ps_probability": 0.0, "ai_probability": 0.0, "analysis_results": {}}

        # 获取图像信息
        img = Image.open(io.BytesIO(image_data))
        img_format = img.format
        width, height = img.size
        result["image_info"] = {"format": img_format, "width": width, "height": height}

        # 计算图像质量因子
        quality_factor = 1.0
        if width * height < 500 * 500:
            quality_factor = 1.8  # 对小图片提高阈值

        if img_format != "JPEG" and img_format != "JPG":
            quality_factor *= 1.5  # 对非JPEG图像提高阈值

        # 检查元数据
        has_ps_metadata, metadata_info = check_metadata(image_data)
        if has_ps_metadata:
            result["ps_probability"] = 0.85
            result["is_real"] = False
            result["reason"] = "检测到Adobe/Photoshop元数据"
            return result

        # 快速错误等级分析
        ela_image, ela_score = perform_ela(image_data, quality=90, scale=10)
        if ela_score > 0.15 * quality_factor:  # 大幅提高阈值
            result["ps_probability"] = min(0.85, ela_score * 2.5)  # 降低映射比例
            result["is_real"] = False
            result["reason"] = f"检测到较高的错误等级差异 (分数: {ela_score:.4f})"
            return result

        # 快速噪声分析
        noise_image, noise_score = noise_analysis(image_data)
        if noise_score < 0.003 * quality_factor:  # 大幅提高阈值
            result["ps_probability"] = 0.5
            result["ai_probability"] = 0.4
            result["is_real"] = False
            result["reason"] = f"检测到异常低的噪声水平 (分数: {noise_score:.4f})"
            return result

        # 如果通过所有基本检查，判断为真实图像
        result["is_real"] = True
        result["reason"] = "通过基本真实性检查"
        return result

    except Exception as e:
        print(f"快速检查图片时出错: {e}")
        return {"error": f"快速检查图片时出错: {e}", "is_real": None}


if __name__ == "__main__":
    # 使用本地图片路径
    local_image_path = "D:\\work\\ps_check_data\\xss250502021847594_1557198.jpeg"

    # 默认使用快速模式
    result = main(local_image_path, fast_mode=True)
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 可以取消注释以使用更快的简单检查
    # fast_result = fast_check_real_image(local_image_path)
    # print(json.dumps(fast_result, ensure_ascii=False, indent=2))
