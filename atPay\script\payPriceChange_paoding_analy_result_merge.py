from datetime import datetime
from typing import Dict, Any, List, Optional
from requests.exceptions import RequestException
from collections import defaultdict


# 第三部分：主流程整合
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # Initialize the result dictionary
    preResult = {}

    # Check if response exists in param
    if "response" in param and param.get("response"):
        # If response exists, use it
        preResult = param["response"]
    else:
        # If response doesn't exist, use notMatchFinalResult
        preResult = param.get("notMatchFinalResult")

    if not preResult:
        preResult = {
            "isMultilPrice": False,
            "matchRecord": {
                "subPayPrices": "",
                "orderNos": "",
                "subGoPayPrices": "",
                "userParentOrderPrice": "",
                "orderParentPrice": "",
                "totalSubGoPayPrice": "",
                "totalSubPayPrice": "",
                "createTime": "",
                "createOrderQtrace": "",
            },
            "priceChangeReason": {
                "firstReason": "未知异常",
                "secondReason": "未知异常",
                "thirdReason": "未知异常",
            },
        }
    return {"response": preResult}
