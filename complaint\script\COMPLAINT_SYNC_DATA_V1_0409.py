import requests
import time
import json
import base64
from datetime import datetime
from csv import DictReader, <PERSON><PERSON><PERSON> as CSVError
from io import StringIO
import uuid
from typing import List, Dict, Any

TAMIAS_RESULT_DOWNLOAD_URL = "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="

def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())

def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        return "tamias结果文件解析失败！", None

    return None, result

def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "yaowyw.wang",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False
    )
    print("----------------response:", response.text)
    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None

def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None

def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"uuid": generateId()})
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}

COMPLAINT_DATA_SYNC_SQL = """
select
    v_date,order_no,qunar_username,complaint_time,complaint_name1,complaint_name2,deal_time
from
    fuwu.dwd_complaint_flt_waiyi_detail_di
where dt='{vDate}' 
AND is_keep_to_ctrip = '1'  
and complaint_name2 {complaintNameCondition}
and business_type='国内'
"""

COMPLAINT_DATA_SYNC_SQL_WITH_ORDERNO = """
select
    v_date,order_no,qunar_username,complaint_time,complaint_name1,complaint_name2,deal_time
from
    fuwu.dwd_complaint_flt_waiyi_detail_di
where dt='{vDate}' 
AND is_keep_to_ctrip = '1'  
and complaint_name2 {complaintNameCondition}
AND order_no = '{orderNo}'
and business_type='国内'
"""

def buildSqlByQuery(vDate, orderNo, complaint_name_str=None):
    sql = COMPLAINT_DATA_SYNC_SQL

    try:
        params = {}
        params["vDate"] = vDate

        # Handle complaint_name_str
        if not complaint_name_str:
            params["complaintNameCondition"] = "= '行程单不足额'"
        else:
            # Split by comma and create IN clause
            complaint_names = [f"'{name.strip()}'" for name in complaint_name_str.split(',')]
            params["complaintNameCondition"] = f"IN ({','.join(complaint_names)})"

        if orderNo:
            params["orderNo"] = orderNo
            sql = COMPLAINT_DATA_SYNC_SQL_WITH_ORDERNO
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e

def main(param):
    complaint_name_str = param.get("complaint_name_str")
    sql = buildSqlByQuery(param.get("vDate"), param.get("orderNo"), complaint_name_str)
    print("----------------sql", sql)
    oriDataResult = queryDataFromTamias(param.get("cookie"), sql)
    if oriDataResult.get("error"):
        oriDataResult["results"] = []
        return oriDataResult

    oriDatas = oriDataResult.get("results")

    try:
        if not oriDatas or oriDatas == "当前条件未检索到数据":
            return {"error": "当前条件未检索到数据", "results": []}
        else:
            return {"error": "", "results": oriDatas}
    except Exception as e:
        return {"error": "投诉信息查询失败", "results": []}

def test():
    param = {
        "vDate": "2025-04-13",
        "complaint_name_str": "支付后降价,支付前/时变价",
        "orderNo": "sjd250413181747685",
        "cookie": "QN1=0001088014346d6618281d38; QSSOFP=95e4b28e33d941c9be383c891f597885_1744190744162; cookie=yaowyw.wang&846331&520F3F96270CFD217F49D817D2084555"
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 