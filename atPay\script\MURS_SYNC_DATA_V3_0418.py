from string import Formatter
import requests
import time  # 添加time模块用于等待
import json
import base64
from datetime import datetime, timedelta
from csv import DictReader, <PERSON>rro<PERSON> as CSVError
from io import StringIO
import uuid
from typing import List, Dict, Any


def airport_to_city(key):
    return key


TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)


def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())


def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        return
    except KeyError as e:
        return


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )

    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None


# http://************:8080/adhoc/executor/download/data?taskId=4710824
# http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId=4710824


def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"GenrecordId": generateId()})
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


DEFAULT_USER_QUERY_ID = "877"

URS_877_USER_QUERY_SQL = """
select
      dt,
      questionnaire_id,
      user_name,
      ext_json,
      create_time
    FROM
      flight.dwd_flow_dom_usertouch_ups_new_detail_di
    where
      dt = '{ursDate}'
      AND questionnaire_id in ('877')
      and is_stable in ('不稳定')
      AND flag = 'UPS一致率'
"""

URS_2449_USER_QUERY_SQL = """
select
      dt,
      questionnaire_id,
      user_name,
      ext_json,
      create_time
    from
      flight.dwd_flow_inter_usertouch_urs_new_middle_di
    where
      dt = '{ursDate}'
      and questionnaire_id in ('2449')
      and value like '有%'
      and question_label in ('最近10分钟，航班价格有发生变化吗？', '最近半小时，航班价格有发生变化吗？')
"""

URS_2584_USER_QUERY_SQL = """
select
      dt,
      questionnaire_id,
      user_name,
      ext_json,
      create_time
    from
      flight.dwd_flow_inter_usertouch_urs_new_middle_di
    where
      dt = '{ursDate}'
      and questionnaire_id in ('2584')
      and value like '有%'
      and question_label in ('最近10分钟，航班价格有发生变化吗？', '最近半小时，航班价格有发生变化吗？')
"""


URS_2458_USER_QUERY_SQL = """
select
  dt,
  questionnaire_id,
  user_name,
  ext_json,
  create_time
from
  flight.dwd_flow_inter_usertouch_urs_new_middle_di
where
  dt = '{ursDate}'
  and questionnaire_id in ('2458')
  and question_id = '11107'
"""

SQL_MAP = {
    "877": URS_877_USER_QUERY_SQL,
    "2449": URS_2449_USER_QUERY_SQL,
    "2584": URS_2584_USER_QUERY_SQL,
    "2458": URS_2458_USER_QUERY_SQL,
}


URS_DATA_SYNC_SQL = """
{user_query_sql} 
group by
  dt,
  user_name,
  ext_json,
  create_time
"""

URS_DATA_SYNC_SQL_WITH_USERNAME = """
{user_query_sql} 
and user_name = '{username}'
group by
  dt,
  user_name,
  ext_json,
  create_time
"""


def get_sql_query(userSqlId):
    """
    Get the SQL query for a given query ID.

    Args:
        query_id (int): The ID of the query to retrieve

    Returns:
        str: The corresponding SQL query, or None if the ID is not found
    """
    return SQL_MAP.get(userSqlId)


def buildSqlByQuery(ursData, username, extParam):

    userSqlId = extParam.get("userSqlId")
    if not userSqlId:
        userSqlId = DEFAULT_USER_QUERY_ID
    if not isinstance(userSqlId, str):
        userSqlId = str(userSqlId)
    #####
    sql = URS_DATA_SYNC_SQL
    user_query_sql = get_sql_query(userSqlId)
    if not user_query_sql:
        user_query_sql = URS_877_USER_QUERY_SQL
    try:
        params = {}
        params["ursDate"] = ursData

        if username:
            params["username"] = username
            sql = URS_DATA_SYNC_SQL_WITH_USERNAME
        useQueySql = user_query_sql.format_map(params)
        params["user_query_sql"] = useQueySql
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def main(param):
    # query = json.loads(param.get("query"))
    sql = buildSqlByQuery(param.get("ursDate"), param.get("username"), param)
    # print(sql)
    oriDataResult = queryDataFromTamias(param.get("cookie"), sql)
    if oriDataResult.get("error"):
        oriDataResult["results"] = []
        return oriDataResult

    oriDatas = oriDataResult.get("results")

    # Calculate statistics for the data with error handling
    try:
        if not oriDatas or oriDatas == "当前条件未检索到数据":
            return {"error": "当前条件未检索到数据", "results": []}
        else:
            count = 0
            batchCount = 3
            # Format create_time to create_date for each record
            for item in oriDatas:
                if "create_time" in item:
                    try:
                        # Parse datetime string and format to date
                        create_time = datetime.strptime(
                            item["create_time"], "%Y-%m-%d %H:%M:%S"
                        )
                        item["create_date"] = create_time.strftime("%Y-%m-%d")
                        item["batch"] = count % batchCount
                        count += 1
                    except (ValueError, TypeError):
                        item["create_date"] = ""
            return {"error": "成功！", "results": oriDatas}
    except Exception as e:
        return {"error": "统计用户URS信息计算失败", "results": []}


if __name__ == "__main__":
    param = {"ursDate": "2025-04-21", "username": "kpegrko8895", "userSqlId": "2584"}
    result = main(param)
    print(result)
