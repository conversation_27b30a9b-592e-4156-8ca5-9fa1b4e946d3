<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Word增强解析器测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .word-logo {
      text-align: center;
      font-size: 3em;
      margin-bottom: 20px;
    }
    .input-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      border: 2px solid #007bff;
    }
    .section-title {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 15px;
      padding: 8px 15px;
      background: #e3f2fd;
      border-radius: 5px;
    }
    textarea {
      width: 100%;
      height: 200px;
      padding: 15px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      resize: vertical;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 8px;
      cursor: pointer;
      margin: 5px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    button:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }
    .enhance-btn {
      background: #dc3545;
      font-weight: bold;
      font-size: 18px;
      padding: 15px 30px;
    }
    .enhance-btn:hover {
      background: #c82333;
    }
    .sample-btn {
      background: #28a745;
    }
    .sample-btn:hover {
      background: #1e7e34;
    }
    .clear-btn {
      background: #6c757d;
    }
    .clear-btn:hover {
      background: #545b62;
    }
    .results-section {
      margin-top: 30px;
      display: none;
    }
    .method-result {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 15px 0;
      border-left: 4px solid #007bff;
    }
    .method-title {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
    }
    .score-badge {
      background: #007bff;
      color: white;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 14px;
    }
    .method-content {
      background: white;
      padding: 20px;
      border-radius: 8px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
      font-size: 13px;
      line-height: 1.4;
    }
    .best-result {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      border: 2px solid #28a745;
      border-radius: 10px;
      padding: 25px;
      margin-bottom: 25px;
    }
    .best-result h3 {
      color: #155724;
      margin-top: 0;
      font-size: 24px;
    }
    .comparison-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .status {
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .warning-box {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
    }
    .feature-list {
      background: white;
      padding: 15px;
      border-radius: 8px;
      margin-top: 15px;
    }
    .feature-list ul {
      margin: 0;
      padding-left: 20px;
    }
    .feature-list li {
      margin: 8px 0;
      color: #495057;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="word-logo">📄 Microsoft Word</div>
    <h1>Word增强解析器测试</h1>
    
    <div class="warning-box">
      <strong>⚠️ 专门针对Microsoft Word乱码问题</strong><br>
      这个增强解析器使用5种不同的方法来处理Microsoft Word创建的.doc文件乱码，
      包括OLE流解析、版本特定解析、字符表解析、段落结构解析和二进制重构。
    </div>
    
    <div class="input-section">
      <div class="section-title">📝 输入Microsoft Word乱码文本</div>
      <textarea id="wordText" placeholder="粘贴Microsoft Word创建的.doc文件乱码文本..."></textarea>
    </div>

    <div class="button-group">
      <button class="enhance-btn" onclick="enhancedWordParse()">🚀 Word增强解析</button>
      <button class="sample-btn" onclick="loadWordSample()">📄 加载Word示例</button>
      <button onclick="compareWithWPS()">🆚 与WPS对比</button>
      <button class="clear-btn" onclick="clearAll()">🗑️ 清空</button>
    </div>

    <div class="results-section" id="resultsSection">
      <div id="bestResult"></div>
      <div class="comparison-grid" id="methodResults"></div>
    </div>
  </div>

  <!-- 加载必要的库 -->
  <script src="lib/word-enhanced-parser.js"></script>

  <script>
    // 初始化Word增强解析器
    let wordEnhancedParser = null;

    try {
      wordEnhancedParser = new WordEnhancedParser();
      console.log('Word增强解析器初始化成功');
      showStatus('success', 'Word增强解析器加载成功，可以开始测试');
    } catch (error) {
      console.error('Word增强解析器初始化失败:', error);
      showStatus('error', 'Word增强解析器加载失败: ' + error.message);
    }

    async function enhancedWordParse() {
      const wordText = document.getElementById('wordText').value.trim();
      
      if (!wordText) {
        showStatus('error', '请输入Microsoft Word乱码文本');
        return;
      }

      if (!wordEnhancedParser) {
        showStatus('error', 'Word增强解析器未初始化');
        return;
      }

      showStatus('info', '开始Word增强解析，使用5种不同方法...');

      try {
        // 转换文本为ArrayBuffer
        const buffer = new TextEncoder().encode(wordText).buffer;
        
        // 执行增强解析
        const result = await wordEnhancedParser.parseWordDocument(buffer, 'word-test.doc');
        
        displayResults(result, wordText);
        showStatus('success', `Word增强解析完成！最佳方法: ${result.method}`);
        
        // 显示结果区域
        document.getElementById('resultsSection').style.display = 'block';
        
      } catch (error) {
        showStatus('error', 'Word增强解析失败: ' + error.message);
        console.error('解析错误:', error);
      }
    }

    function loadWordSample() {
      // Microsoft Word典型乱码示例
      const wordSample = `Root Entry WordDocument Data 1Table 0Table SummaryInformation DocumentSummaryInformation CompObj Microsoft Word Document Times New Roman Arial Symbol Courier New Normal.dot 正文 标题1 标题2 页眉 页脚 默认段落字体 Microsoft Office Word 2019 Document1 Administrator Normal.dotm 16.0.4266.1001 Microsoft Corporation 这是一个测试文档内容 Hello World Test Document Content 测试段落1 测试段落2 表格内容 单元格1 单元格2`;
      
      document.getElementById('wordText').value = wordSample;
      showStatus('info', 'Microsoft Word示例乱码已加载，点击"Word增强解析"开始测试');
    }

    function compareWithWPS() {
      const wordText = document.getElementById('wordText').value.trim();
      
      if (!wordText) {
        showStatus('error', '请先输入Word乱码文本');
        return;
      }

      // 显示对比信息
      const comparisonInfo = `
Word vs WPS 乱码特征对比:

Microsoft Word 特征:
- 使用标准UTF-16LE编码
- 包含"Microsoft Word"、"Normal.dot"等标识
- OLE复合文档结构规范
- 版本信息格式: 16.0.xxxx.xxxx

WPS Office 特征:
- 可能使用GB2312编码
- 包含"WPS Office"、"KSOProductBuildVer"等标识
- 兼容OLE格式但有自定义扩展
- 版本信息格式: 2052-xx.x.x.xxxxx

当前文本特征分析:
${analyzeTextFeatures(wordText)}
      `;

      alert(comparisonInfo);
    }

    function analyzeTextFeatures(text) {
      const features = [];
      
      if (text.includes('Microsoft Word')) features.push('✓ 检测到Microsoft Word标识');
      if (text.includes('WPS Office')) features.push('✓ 检测到WPS Office标识');
      if (text.includes('Normal.dot')) features.push('✓ 检测到Word模板标识');
      if (text.includes('KSOProductBuildVer')) features.push('✓ 检测到WPS构建版本');
      if (/\d{2}\.\d+\.\d+\.\d+/.test(text)) features.push('✓ 检测到Microsoft版本格式');
      if (/\d{4}-\d+\.\d+\.\d+\.\d+/.test(text)) features.push('✓ 检测到WPS版本格式');
      
      return features.length > 0 ? features.join('\n') : '未检测到明显的软件特征';
    }

    function displayResults(result, originalText) {
      const bestResultDiv = document.getElementById('bestResult');
      const methodResultsDiv = document.getElementById('methodResults');
      
      // 显示最佳结果
      bestResultDiv.innerHTML = `
        <div class="best-result">
          <h3>🏆 最佳解析结果</h3>
          <div><strong>解析状态:</strong> ${result.success ? '✅ 成功' : '❌ 失败'}</div>
          <div><strong>使用方法:</strong> ${result.method}</div>
          <div><strong>文本长度:</strong> ${result.text ? result.text.length : 0} 字符</div>
          <div class="method-content" style="margin-top: 15px;">${result.text || '无法提取内容'}</div>
        </div>
      `;

      // 显示所有方法结果
      let methodsHtml = '';
      
      if (result.allResults && result.allResults.length > 0) {
        result.allResults
          .sort((a, b) => b.score - a.score)
          .forEach((methodResult, index) => {
            methodsHtml += `
              <div class="method-result">
                <div class="method-title">
                  ${index + 1}. ${methodResult.method}
                  <span class="score-badge">得分: ${methodResult.score}</span>
                </div>
                <div class="method-content">${methodResult.text || '无内容'}</div>
              </div>
            `;
          });
      } else {
        methodsHtml = `
          <div class="method-result">
            <div class="method-title">单一解析结果</div>
            <div class="method-content">${result.text || '无内容'}</div>
          </div>
        `;
      }

      // 添加原始文本分析
      methodsHtml += `
        <div class="method-result">
          <div class="method-title">📊 原始文本分析</div>
          <div class="feature-list">
            <strong>文本特征:</strong>
            <ul>
              <li>总长度: ${originalText.length} 字符</li>
              <li>中文字符: ${(originalText.match(/[\u4e00-\u9fff]/g) || []).length} 个</li>
              <li>ASCII字符: ${(originalText.match(/[a-zA-Z0-9]/g) || []).length} 个</li>
              <li>软件特征: ${analyzeTextFeatures(originalText).split('\n').length} 项</li>
            </ul>
          </div>
          <div class="method-content">${originalText.substring(0, 300)}${originalText.length > 300 ? '...' : ''}</div>
        </div>
      `;
      
      methodResultsDiv.innerHTML = methodsHtml;
    }

    function showStatus(type, message) {
      const container = document.querySelector('.container');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      
      container.insertBefore(statusDiv, container.firstChild);
      
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function clearAll() {
      document.getElementById('wordText').value = '';
      document.getElementById('resultsSection').style.display = 'none';
      showStatus('info', '已清空所有内容');
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof WordEnhancedParser !== 'undefined') {
        showStatus('success', '页面加载完成，Word增强解析器可用');
        // 自动加载示例文本
        loadWordSample();
      } else {
        showStatus('error', 'Word增强解析器未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
