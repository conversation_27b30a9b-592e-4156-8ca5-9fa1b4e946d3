/**
 * 基于JSZip的DOC文件解析器
 * 专门处理.doc文件的乱码问题
 */
class DocJSZipParser {
  constructor() {
    this.initialized = false;
    this.jszip = null;
    this.encodingFixer = null;
    this.bruteForceParser = null;
    this.initializeLibraries();
  }

  // 初始化库
  async initializeLibraries() {
    try {
      if (typeof JSZip !== 'undefined') {
        this.jszip = JSZip;
        this.initialized = true;
        console.log('JSZip库加载成功');
      } else {
        console.warn('JSZip库未加载');
        this.initialized = false;
      }

      // 初始化编码修复器
      if (typeof DocEncodingFixer !== 'undefined') {
        this.encodingFixer = new DocEncodingFixer();
        console.log('编码修复器加载成功');
      } else {
        console.warn('编码修复器未加载');
      }

      // 初始化暴力解析器
      if (typeof DocBruteForceParser !== 'undefined') {
        this.bruteForceParser = new DocBruteForceParser();
        console.log('暴力解析器加载成功');
      } else {
        console.warn('暴力解析器未加载');
      }
    } catch (error) {
      console.error('初始化JSZip失败:', error);
      this.initialized = false;
    }
  }

  // 解析.doc文件
  async parseDocFile(arrayBuffer, fileName) {
    console.log('开始使用JSZip解析.doc文件:', fileName);

    try {
      // 方法1: 尝试作为ZIP文件解析（某些.doc文件实际上是ZIP格式）
      const zipResult = await this.tryZipParsing(arrayBuffer);
      if (zipResult.success) {
        return zipResult;
      }

      // 方法2: 尝试OLE复合文档解析
      const oleResult = await this.tryOLEParsing(arrayBuffer);
      if (oleResult.success) {
        return oleResult;
      }

      // 方法3: 智能文本提取
      const textResult = await this.trySmartTextExtraction(arrayBuffer);
      if (textResult.success) {
        return textResult;
      }

      // 方法4: 多编码尝试
      const encodingResult = await this.tryMultipleEncodings(arrayBuffer);
      return encodingResult;

    } catch (error) {
      console.error('JSZip解析失败:', error);
      return {
        success: false,
        text: '解析失败，建议转换为.docx格式',
        html: '<p>解析失败，建议转换为.docx格式</p>',
        error: error.message
      };
    }
  }

  // 尝试ZIP解析
  async tryZipParsing(arrayBuffer) {
    try {
      console.log('尝试ZIP格式解析...');
      const zip = await this.jszip.loadAsync(arrayBuffer);

      // 查找可能的文档内容文件
      const contentFiles = ['word/document.xml', 'content.xml', 'document.xml'];

      for (const fileName of contentFiles) {
        const file = zip.file(fileName);
        if (file) {
          console.log(`找到内容文件: ${fileName}`);
          const content = await file.async('text');
          const text = this.extractTextFromXML(content);

          if (text && text.length > 10) {
            return {
              success: true,
              text: text,
              html: this.textToHtml(text),
              method: 'ZIP解析'
            };
          }
        }
      }

      return { success: false };
    } catch (error) {
      console.log('ZIP解析失败:', error.message);
      return { success: false };
    }
  }

  // 尝试OLE复合文档解析
  async tryOLEParsing(arrayBuffer) {
    try {
      console.log('尝试OLE复合文档解析...');

      // 检查OLE文件头
      const header = new Uint8Array(arrayBuffer, 0, 8);
      const oleSignature = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];

      const isOLE = header.every((byte, index) => byte === oleSignature[index]);

      if (!isOLE) {
        console.log('不是有效的OLE文件');
        return { success: false };
      }

      // 使用改进的文本提取方法
      const text = this.extractTextFromOLE(arrayBuffer);

      if (text && text.length > 20) {
        return {
          success: true,
          text: text,
          html: this.textToHtml(text),
          method: 'OLE解析'
        };
      }

      return { success: false };
    } catch (error) {
      console.log('OLE解析失败:', error.message);
      return { success: false };
    }
  }

  // 从OLE文件提取文本
  extractTextFromOLE(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let bestText = '';
    let maxScore = 0;

    // 尝试不同的编码和偏移量
    const encodings = [
      { name: 'UTF-16LE', decoder: this.decodeUTF16LE },
      { name: 'UTF-8', decoder: this.decodeUTF8 },
      { name: 'Windows-1252', decoder: this.decodeWindows1252 }
    ];

    const offsets = [0x200, 0x400, 0x800, 0x1000, 0x2000, 0];

    for (const encoding of encodings) {
      for (const offset of offsets) {
        try {
          const text = encoding.decoder.call(this, uint8Array, offset);
          const score = this.scoreText(text);

          if (score > maxScore) {
            maxScore = score;
            bestText = text;
          }
        } catch (e) {
          // 忽略解码错误
        }
      }
    }

    return this.cleanText(bestText);
  }

  // UTF-16LE解码
  decodeUTF16LE(uint8Array, startOffset = 0) {
    let text = '';
    let validChars = 0;

    for (let i = startOffset; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);

      if (char >= 0x20 && char <= 0x7E) {
        // ASCII可打印字符
        text += String.fromCharCode(char);
        validChars++;
      } else if (char >= 0x4e00 && char <= 0x9fff) {
        // 中文字符
        text += String.fromCharCode(char);
        validChars++;
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      } else if (char === 0x00) {
        // 空字符，可能是填充
        continue;
      } else {
        // 其他字符，如果已经有足够的有效字符，继续
        if (validChars < 5) {
          break;
        }
      }

      // 限制扫描长度
      if (text.length > 5000) break;
    }

    return text;
  }

  // UTF-8解码
  decodeUTF8(uint8Array, startOffset = 0) {
    try {
      const decoder = new TextDecoder('utf-8', { fatal: false });
      const slice = uint8Array.slice(startOffset, startOffset + 10000);
      return decoder.decode(slice);
    } catch (e) {
      return '';
    }
  }

  // Windows-1252解码
  decodeWindows1252(uint8Array, startOffset = 0) {
    try {
      const decoder = new TextDecoder('windows-1252', { fatal: false });
      const slice = uint8Array.slice(startOffset, startOffset + 10000);
      return decoder.decode(slice);
    } catch (e) {
      return '';
    }
  }

  // 智能文本提取
  async trySmartTextExtraction(arrayBuffer) {
    try {
      console.log('尝试智能文本提取...');

      const uint8Array = new Uint8Array(arrayBuffer);
      const textBlocks = this.findTextBlocks(uint8Array);

      if (textBlocks.length > 0) {
        const bestBlock = textBlocks.reduce((best, current) =>
          current.score > best.score ? current : best
        );

        if (bestBlock.text.length > 20) {
          return {
            success: true,
            text: bestBlock.text,
            html: this.textToHtml(bestBlock.text),
            method: '智能提取'
          };
        }
      }

      return { success: false };
    } catch (error) {
      console.log('智能提取失败:', error.message);
      return { success: false };
    }
  }

  // 查找文本块
  findTextBlocks(uint8Array) {
    const blocks = [];
    const windowSize = 1000;

    for (let offset = 0; offset < uint8Array.length - windowSize; offset += 500) {
      const window = uint8Array.slice(offset, offset + windowSize);

      // UTF-16LE扫描
      const utf16Text = this.scanWindowUTF16(window);
      if (utf16Text.length > 10) {
        blocks.push({
          text: utf16Text,
          score: this.scoreText(utf16Text),
          offset: offset,
          method: 'UTF-16LE'
        });
      }

      // ASCII扫描
      const asciiText = this.scanWindowASCII(window);
      if (asciiText.length > 10) {
        blocks.push({
          text: asciiText,
          score: this.scoreText(asciiText),
          offset: offset,
          method: 'ASCII'
        });
      }
    }

    return blocks.sort((a, b) => b.score - a.score);
  }

  // 扫描窗口UTF-16
  scanWindowUTF16(window) {
    let text = '';

    for (let i = 0; i < window.length - 1; i += 2) {
      const char = window[i] | (window[i + 1] << 8);

      if ((char >= 0x20 && char <= 0x7E) || (char >= 0x4e00 && char <= 0x9fff)) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
    }

    return this.cleanText(text);
  }

  // 扫描窗口ASCII
  scanWindowASCII(window) {
    let text = '';

    for (let i = 0; i < window.length; i++) {
      const char = window[i];

      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      }
    }

    return this.cleanText(text);
  }

  // 多编码尝试
  async tryMultipleEncodings(arrayBuffer) {
    console.log('尝试多种编码解析...');

    const encodings = [
      'utf-16le', 'utf-16be', 'utf-8', 'windows-1252',
      'iso-8859-1', 'gb2312', 'big5'
    ];

    let bestResult = { text: '', score: 0 };
    let rawTexts = [];

    for (const encoding of encodings) {
      try {
        const decoder = new TextDecoder(encoding, { fatal: false });
        const text = decoder.decode(arrayBuffer);
        rawTexts.push({ text, encoding });

        const cleanedText = this.cleanText(text);
        const score = this.scoreText(cleanedText);

        if (score > bestResult.score) {
          bestResult = { text: cleanedText, score: score, encoding: encoding };
        }
      } catch (e) {
        // 忽略不支持的编码
      }
    }

    // 如果有编码修复器，尝试修复最佳结果
    if (this.encodingFixer && bestResult.text.length > 0) {
      console.log('使用编码修复器修复文本...');
      try {
        const fixedResult = this.encodingFixer.fixEncoding(bestResult.text);

        if (fixedResult.text.length > bestResult.text.length * 0.1) {
          console.log(`编码修复成功，使用方法: ${fixedResult.method}`);
          return {
            success: true,
            text: fixedResult.text,
            html: this.textToHtml(fixedResult.text),
            method: `编码修复 (${fixedResult.method})`
          };
        }
      } catch (fixError) {
        console.warn('编码修复失败:', fixError.message);
      }
    }

    // 如果编码修复失败，尝试对所有原始文本进行修复
    if (this.encodingFixer && rawTexts.length > 0) {
      console.log('尝试修复所有编码结果...');
      let bestFixed = { text: '', score: 0, method: '' };

      for (const rawText of rawTexts) {
        try {
          const fixedResult = this.encodingFixer.fixEncoding(rawText.text);
          const score = this.scoreText(fixedResult.text);

          if (score > bestFixed.score) {
            bestFixed = {
              text: fixedResult.text,
              score: score,
              method: `${rawText.encoding} + ${fixedResult.method}`
            };
          }
        } catch (e) {
          // 忽略修复错误
        }
      }

      if (bestFixed.text.length > 10) {
        return {
          success: true,
          text: bestFixed.text,
          html: this.textToHtml(bestFixed.text),
          method: bestFixed.method
        };
      }
    }

    if (bestResult.text.length > 10) {
      return {
        success: true,
        text: bestResult.text,
        html: this.textToHtml(bestResult.text),
        method: `多编码解析 (${bestResult.encoding})`
      };
    }

    // 最后的回退：使用暴力解析器
    if (this.bruteForceParser) {
      console.log('所有常规方法都失败，使用暴力解析器...');
      try {
        // 使用原始文本进行暴力解析
        const decoder = new TextDecoder('utf-8', { fatal: false });
        const rawText = decoder.decode(arrayBuffer);

        const bruteResult = this.bruteForceParser.generateReport(rawText);

        if (bruteResult.bestText && bruteResult.bestText.length > 5) {
          console.log('暴力解析成功！');
          return {
            success: true,
            text: bruteResult.bestText,
            html: this.textToHtml(bruteResult.bestText),
            method: '暴力解析 (最后回退)'
          };
        }
      } catch (bruteError) {
        console.warn('暴力解析也失败:', bruteError.message);
      }
    }

    return {
      success: false,
      text: '无法提取文本内容，建议转换为.docx格式',
      html: '<p>无法提取文本内容，建议转换为.docx格式</p>',
      method: '所有方法都失败'
    };
  }

  // 从XML提取文本
  extractTextFromXML(xmlContent) {
    // 移除XML标签，提取纯文本
    return xmlContent
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;

    let score = 0;

    // 基础长度分
    score += Math.min(text.length / 10, 100);

    // 可读字符比例
    const readableChars = text.match(/[\x20-\x7E\u4e00-\u9fff]/g);
    if (readableChars) {
      score += (readableChars.length / text.length) * 50;
    }

    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    if (chineseChars) {
      score += chineseChars.length * 2;
    }

    // 常见单词加分
    const commonWords = text.match(/\b(the|and|or|but|in|on|at|to|for|of|with|by|是|的|了|在|有|我|你|他|她|它)\b/gi);
    if (commonWords) {
      score += commonWords.length * 3;
    }

    return score;
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';

    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      .replace(/\s{3,}/g, '  ')
      .replace(/\n{3,}/g, '\n\n')
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .trim();
  }

  // 文本转HTML
  textToHtml(text) {
    if (!text) return '<p>无内容</p>';

    const paragraphs = text.split('\n\n');
    let html = '';

    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        html += `<p>${this.escapeHtml(paragraph.trim())}</p>`;
      }
    });

    return html || '<p>无法提取内容</p>';
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocJSZipParser;
} else if (typeof window !== 'undefined') {
  window.DocJSZipParser = DocJSZipParser;
}
