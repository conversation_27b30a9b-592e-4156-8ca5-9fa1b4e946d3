#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据获取器 - 函数式实现
分页获取山航公务舱单次卡数据并解析priceTagMoney字段
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urlencode, urlparse, parse_qs


def get_default_headers():
    """
    获取默认请求头

    Returns:
        dict: 默认请求头字典
    """
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    }


def parse_url_params(url: str) -> Dict[str, str]:
    """
    解析URL参数

    Args:
        url: 完整的URL

    Returns:
        Dict[str, str]: 解析后的参数字典
    """
    parsed_url = urlparse(url)
    params = parse_qs(parsed_url.query)

    # 将列表值转换为字符串
    result = {}
    for key, value_list in params.items():
        if value_list:
            result[key] = value_list[0]
        else:
            result[key] = ""

    return result


def fetch_page_data(base_url: str, params: Dict[str, str], page: int = 1, page_size: int = 10) -> Optional[Dict[str, Any]]:
    """
    获取单页数据

    Args:
        base_url: API基础URL
        params: 请求参数
        page: 页码
        page_size: 每页大小

    Returns:
        Optional[Dict[str, Any]]: API响应数据，失败时返回None
    """
    try:
        # 更新分页参数
        request_params = params.copy()
        request_params['currentPage'] = str(page)
        request_params['pageSize'] = str(page_size)

        # 发送请求
        headers = get_default_headers()
        response = requests.get(base_url, params=request_params, headers=headers, timeout=30)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return None


def fetch_all_data(url: str, max_pages: int = 100) -> List[Dict[str, Any]]:
    """
    分页获取所有数据

    Args:
        url: 完整的API URL
        max_pages: 最大页数限制

    Returns:
        List[Dict[str, Any]]: 所有记录的列表
    """
    # 解析URL和参数
    parsed_url = urlparse(url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
    params = parse_url_params(url)

    all_records = []
    current_page = 1

    while current_page <= max_pages:
        # 获取当前页数据
        data = fetch_page_data(base_url, params, current_page)

        if not data:
            break

        # 尝试不同的数据字段
        page_data = None
        if 'data' in data and 'recordList' in data['data']:
            page_data = data['data']['recordList']
        elif 'data' in data:
            page_data = data['data']
        elif 'result' in data:
            page_data = data['result']
        elif 'list' in data:
            page_data = data['list']
        elif 'records' in data:
            page_data = data['records']
        elif 'recordList' in data:
            page_data = data['recordList']
        else:
            # 如果没有找到标准字段，尝试直接使用响应数据
            if isinstance(data, list):
                page_data = data
            else:
                print(f"❌ 第 {current_page} 页响应格式错误: 无法找到数据字段")
                break

        if not isinstance(page_data, list):
            print(f"❌ 第 {current_page} 页数据格式错误: 数据不是列表")
            break

        # 添加到总记录中
        all_records.extend(page_data)

        # 检查是否还有更多页
        if len(page_data) == 0:
            break

        # 检查是否是最后一页（根据返回的记录数判断）
        page_size = int(params.get('pageSize', 10))
        if len(page_data) < page_size:
            break

        current_page += 1

        # 添加请求间隔，避免过快请求
        time.sleep(0.1)

    return all_records


def parse_price_tag_money(price_tag_money: str) -> str:
    """
    解析priceTagMoney字段

    Args:
        price_tag_money: priceTagMoney字段值，格式如 "TPF:800"

    Returns:
        str: 解析后的前缀字符串，去除字符1
    """
    if not price_tag_money or ':' not in price_tag_money:
        return ""

    # 分割并取第一个字段（冒号前面的部分）
    parts = price_tag_money.split(':', 1)
    if len(parts) < 1:
        return ""

    # 取冒号前的部分，并替换字符1为空串
    prefix_part = parts[0].replace('1', '')

    return prefix_part


def process_records(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    处理记录，解析priceTagMoney字段

    Args:
        records: 原始记录列表

    Returns:
        List[Dict[str, Any]]: 处理后的记录列表
    """
    processed_records = []

    for record in records:
        # 复制原始记录
        processed_record = record.copy()

        # 处理attributes中的priceGroup
        attributes = record.get('attributes', {})
        price_group_str = attributes.get('priceGroup', '')

        if price_group_str:
            try:
                # 解析priceGroup JSON字符串
                price_group = json.loads(price_group_str)

                # 处理每个价格项
                for price_item in price_group:
                    price_tag_money = price_item.get('priceTagMoney', '')
                    if price_tag_money:
                        parsed_price = parse_price_tag_money(price_tag_money)
                        price_item['parsed_price'] = parsed_price

                # 更新处理后的priceGroup
                processed_record['priceGroup'] = price_group

            except json.JSONDecodeError as e:
                print(f"❌ priceGroup JSON解析失败: {e}")
                processed_record['priceGroup'] = []
        else:
            processed_record['priceGroup'] = []

        processed_records.append(processed_record)

    return processed_records


def main(param: dict) -> dict:
    """
    主函数

    Args:
        param: 参数字典，包含以下字段：
            - env: 环境参数，支持 "beta" 和 "prod"，默认为 "beta"

    Returns:
        dict: 包含去重后的解析价格集合的结果字典
    """
    # 从参数中获取环境配置
    env = param.get("env", "beta")

    # 环境配置
    env_configs = {
        "beta": {
            "base_url": "http://l-noah6dpkbbwz31.auto.beta.cn0.qunar.com:8080/tpCommonConfig/opt/queryList",
            "api_url": "http://l-noah6dpkbbwz31.auto.beta.cn0.qunar.com:8080/tpCommonConfig/opt/queryList?xconfigKey=&currentPage=1&activityStatus=1&attribute_carrierCode=SC&attribute_manager=yonglyy.li%2Cboyue.wang&createTimeRange=1747929600%2C1748015999&attribute_tpName=%E5%B1%B1%E8%88%AA%E5%85%AC%E5%8A%A1%E8%88%B1%E5%8D%95%E6%AC%A1%E5%8D%A1&pageSize=10&businessLine=33&useLike=1"
        },
        "prod": {
            "base_url": "http://l-noah6dpkbbwz31.auto.cn0.qunar.com:8080/tpCommonConfig/opt/queryList",
            "api_url": "http://l-noah6dpkbbwz31.auto.cn0.qunar.com:8080/tpCommonConfig/opt/queryList?xconfigKey=&currentPage=1&activityStatus=1&attribute_carrierCode=SC&attribute_manager=yonglyy.li%2Cboyue.wang&createTimeRange=1747929600%2C1748015999&attribute_tpName=%E5%B1%B1%E8%88%AA%E5%85%AC%E5%8A%A1%E8%88%B1%E5%8D%95%E6%AC%A1%E5%8D%A1&pageSize=10&businessLine=33&useLike=1"
        }
    }

    # 验证环境参数
    if env not in env_configs:
        print(f"❌ 不支持的环境: {env}，支持的环境: {list(env_configs.keys())}")
        return {
            "success": False,
            "message": f"不支持的环境: {env}",
            "data": []
        }

    # 只在beta环境下执行测试
    if env != "beta":
        print(f"⚠️ 当前环境为 {env}，只支持在 beta 环境下测试")
        return {
            "success": False,
            "message": f"当前环境为 {env}，只支持在 beta 环境下测试",
            "data": []
        }

    # 获取环境配置
    config = env_configs[env]
    api_url = config["api_url"]

    try:
        # 获取所有数据
        all_records = fetch_all_data(api_url)

        if not all_records:
            return {
                "success": False,
                "message": "没有获取到任何数据",
                "data": []
            }

        # 处理记录
        processed_records = process_records(all_records)

        # 收集所有解析后的价格字段
        parsed_prices = set()  # 使用set自动去重

        for record in processed_records:
            price_group = record.get('priceGroup', [])
            for price_item in price_group:
                parsed_price = price_item.get('parsed_price', '')
                if parsed_price:  # 只添加非空的价格
                    parsed_prices.add(parsed_price)

        # 转换为列表并排序
        result_list = sorted(list(parsed_prices))

        return {
            "success": True,
            "message": "数据获取和处理完成",
            "data": result_list
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"程序执行出错: {str(e)}",
            "data": []
        }


if __name__ == "__main__":
    import sys

    # 解析命令行参数
    env = "beta"  # 默认环境
    if len(sys.argv) > 1:
        env = sys.argv[1]

    # 构造参数字典
    param = {"env": env}

    result = main(param)

    # 输出结果
    if result.get("success"):
        print(json.dumps(result["data"], ensure_ascii=False, indent=2))
    else:
        print(json.dumps(result["data"], ensure_ascii=False, indent=2))
