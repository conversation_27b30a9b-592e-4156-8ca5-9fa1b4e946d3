import json
from ParseAndValiatePriceChangeParam import getPriceByPriceClassify

# 测试数据
test_json = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [{"parseType": "精准", "price": "275"}],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"}
            ],
            "surPrecisePriceList": [{"parseType": "精准", "price": "405"}],
            "surDurationPriceList": [
                {"leftPrice": 400, "rightPrice": 409, "parseType": "模糊的区间价格"}
            ],
            "changePrecisePriceList": [{"parseType": "精准", "price": "130"}],
            "changeDurationPriceList": [
                {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"}
            ],
        },
        {
            "prePrecisePriceList": [{"parseType": "精准", "price": "350"}],
            "preDurationPriceList": [
                {"leftPrice": 345, "rightPrice": 355, "parseType": "模糊的区间价格"}
            ],
            "surPrecisePriceList": [{"parseType": "精准", "price": "500"}],
            "surDurationPriceList": [
                {"leftPrice": 495, "rightPrice": 505, "parseType": "模糊的区间价格"}
            ],
            "changePrecisePriceList": [{"parseType": "精准", "price": "150"}],
            "changeDurationPriceList": [
                {"leftPrice": 145, "rightPrice": 155, "parseType": "模糊的区间价格"}
            ],
        },
    ]
}

# 包含无效价格的测试数据
test_json_invalid = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [
                {"parseType": "精准", "price": "275"},
                {"parseType": "精准", "price": "abc"},  # 无效价格
            ],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"},
                {
                    "leftPrice": "abc",
                    "rightPrice": 279,
                    "parseType": "模糊的区间价格",
                },  # 无效左价格
            ],
            "surPrecisePriceList": [
                {"parseType": "精准", "price": "405"},
                {"parseType": "精准"},  # 缺少价格字段
            ],
            "changeDurationPriceList": [
                {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"},
                {"leftPrice": 121, "parseType": "模糊的区间价格"},  # 缺少右价格字段
            ],
        }
    ]
}


def test_price_classify():
    # 测试单个价格分类
    print("===== 测试获取单个价格分类 =====")
    print("\n1. 前序价格(prePrice)")
    pre_prices = getPriceByPriceClassify(test_json, "preSurPriceGroups", "prePrice")
    print(f"找到 {len(pre_prices)} 个有效前序价格条目:")
    for price in pre_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    print("\n2. 后续价格(surPrice)")
    sur_prices = getPriceByPriceClassify(test_json, "preSurPriceGroups", "surPrice")
    print(f"找到 {len(sur_prices)} 个有效后续价格条目:")
    for price in sur_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    print("\n3. 变化价格(changePrice)")
    change_prices = getPriceByPriceClassify(
        test_json, "preSurPriceGroups", "changePrice"
    )
    print(f"找到 {len(change_prices)} 个有效变化价格条目:")
    for price in change_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    # 测试多个价格分类
    print("\n===== 测试获取多个价格分类 =====")
    print("\n1. 前序价格和后续价格")
    pre_sur_prices = getPriceByPriceClassify(
        test_json, "preSurPriceGroups", ["prePrice", "surPrice"]
    )
    print(f"找到 {len(pre_sur_prices)} 个有效价格条目(前序+后续):")
    for price in pre_sur_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    print("\n2. 前序价格和变化价格")
    pre_change_prices = getPriceByPriceClassify(
        test_json, "preSurPriceGroups", ["prePrice", "changePrice"]
    )
    print(f"找到 {len(pre_change_prices)} 个有效价格条目(前序+变化):")
    for price in pre_change_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    print("\n3. 所有价格类型")
    all_prices = getPriceByPriceClassify(
        test_json, "preSurPriceGroups", ["prePrice", "surPrice", "changePrice"]
    )
    print(f"找到 {len(all_prices)} 个有效价格条目(所有类型):")
    for price in all_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    # 测试无效价格数据
    print("\n===== 测试无效价格数据 =====")
    pre_prices_invalid = getPriceByPriceClassify(
        test_json_invalid, "preSurPriceGroups", "prePrice"
    )
    print(f"从无效数据中找到 {len(pre_prices_invalid)} 个有效前序价格条目:")
    for price in pre_prices_invalid:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    # 测试所有价格类型的无效数据
    all_invalid_prices = getPriceByPriceClassify(
        test_json_invalid, "preSurPriceGroups", ["prePrice", "surPrice", "changePrice"]
    )
    print(f"从无效数据中找到 {len(all_invalid_prices)} 个有效价格条目(所有类型):")
    for price in all_invalid_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    test_price_classify()
