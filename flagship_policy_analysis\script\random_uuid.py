import uuid
import hashlib
import base64
from typing import Dict, Any, List, Optional, Union

def generate_random_uuid():
    """
    生成随机UUID（通用唯一标识符）。
    
    返回:
        str: 格式为'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'的随机UUID字符串
    """
    return str(uuid.uuid4())

def generate_uuid_without_hyphens():
    """
    生成无连字符的随机UUID。
    
    返回:
        str: 无连字符的随机UUID字符串（32个字符）
    """
    return uuid.uuid4().hex

def generate_short_uuid(length=8):
    """
    生成缩短的UUID。
    
    参数:
        length (int): 缩短UUID的期望长度（默认: 8）
    
    返回:
        str: 指定长度的缩短UUID字符串
    """
    # 使用generate_uuid_without_hyphens获取无连字符的UUID
    full_uuid = generate_uuid_without_hyphens()
    
    # 使用哈希处理以获得更短的字符串
    # 这保持了唯一性同时减少了长度
    hash_object = hashlib.md5(full_uuid.encode())
    hash_hex = hash_object.hexdigest()
    
    # 取前'length'个字符
    return hash_hex[:length]

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据输入参数生成UUID的主函数。
    
    参数:
        param (Dict[str, Any]): 输入参数，包含:
            - uuid_type (str): 要生成的UUID类型（'standard'标准, 'no_hyphen'无连字符, 或 'short'短UUID）
            - length (int, 可选): 短UUID的长度（默认: 8）
    
    返回:
        Dict[str, Any]: 结果，包含:
            - status (int): 状态码，0表示成功，非0表示失败
            - message (str): 状态消息，成功或错误描述
            - data (Dict[str, Any]): 数据，包含:
                - uuid (str): 生成的UUID
                - type (str): UUID类型
                - length (int, 可选): 短UUID的长度（仅当类型为'short'时有效）
    """
    # 提取参数并设置默认值
    uuid_type = param.get('uuid_type', 'standard')
    length = param.get('length', 8)
    
    # 验证参数
    if uuid_type not in ['standard', 'no_hyphen', 'short']:
        return {
            'status': "1",
            'message': f"无效的uuid_type: {uuid_type}。必须是'standard'、'no_hyphen'或'short'。",
            'data': None
        }
    
    try:
        # 根据类型生成UUID
        if uuid_type == 'standard':
            generated_uuid = generate_random_uuid()
        elif uuid_type == 'no_hyphen':
            generated_uuid = generate_uuid_without_hyphens()
        elif uuid_type == 'short':
            generated_uuid = generate_short_uuid(length)
        
        # 返回成功结果
        return {
            'status': "0",
            'message': '成功生成UUID',
            'data': {
                'uuid': generated_uuid,
                'type': uuid_type,
                'length': length if uuid_type == 'short' else None
            }
        }
    except Exception as e:
        # 返回错误结果
        return {
            'status': "2",
            'message': f"生成UUID时发生错误: {str(e)}",
            'data': None
        }

