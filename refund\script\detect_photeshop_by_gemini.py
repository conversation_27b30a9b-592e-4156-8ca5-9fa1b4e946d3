from PIL import Image, ExifTags, ImageFilter, UnidentifiedImageError
import numpy as np
import io
import os
import cv2  # OpenCV
from scipy.fftpack import fft2, fftshift, dct
from scipy.stats import wasserstein_distance
from skimage.feature import local_binary_pattern  # <--- ***在此处补充导入***
from collections import Counter
import json

# Dlib and face_recognition are optional for facial analysis
try:
    import dlib
    import face_recognition

    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    print("警告：dlib或face_recognition库未安装，面部分析功能将不可用。")
    print("如需面部分析，请运行: pip install dlib cmake face_recognition")


# --- 0. 辅助函数 ---
def resize_image_for_analysis(img, max_dim=1200):  # 文档可以适当大一些
    """将图片调整到适合分析的大小，避免过大图片导致性能问题"""
    if max(img.size) > max_dim:
        img.thumbnail((max_dim, max_dim))
    return img


# --- 1. 元数据分析 ---
def analyze_metadata(pil_image_orig):
    try:
        exif_data = pil_image_orig._getexif()
        suspicion_score = 0.0
        details = []

        if exif_data:
            software_tags = [
                ExifTags.TAGS.get(tag_id)
                for tag_id, value in exif_data.items()
                if ExifTags.TAGS.get(tag_id) == "Software"
            ]
            if software_tags:
                for tag_id, value in exif_data.items():
                    if ExifTags.TAGS.get(tag_id, tag_id) == "Software":
                        software_name = str(value).lower()
                        # 常见的图像编辑软件
                        editors = [
                            "photoshop",
                            "gimp",
                            "paint.net",
                            "snapseed",
                            "corel",
                            "modified",
                            "edited",
                            "xnview",
                            "irfanview",
                        ]
                        if any(editor in software_name for editor in editors):
                            suspicion_score = max(
                                suspicion_score, 0.8
                            )  # 直接检测到编辑软件，高度可疑
                            details.append(f"检测到编辑软件痕迹: {value}")
                        else:
                            details.append(f"图像处理软件: {value}")

            # 可以添加对其他可疑EXIF标签的检查，如不一致的日期等
            # 例如，DateTimeOriginal vs DateTimeDigitized vs DateTime

            if not details and software_tags:  # 如果有Software标签但不在上面的列表中
                pass  # 已经由 else: details.append(f"图像处理软件: {value}") 处理
            elif not software_tags and exif_data:  # 如果有EXIF但没检测到Software标签
                details.append("找到EXIF元数据，但未发现明确的编辑软件标签。")
                suspicion_score = 0.05  # 仅有EXIF本身不构成高疑点
        else:
            details.append(
                "未找到EXIF元数据。这对于扫描文档可能是正常的，但对于相机直出的照片则可疑。"
            )
            suspicion_score = 0.0  # 对于文档，无EXIF不一定可疑

        return suspicion_score, details
    except Exception as e:
        return 0.0, [f"元数据分析错误: {e}"]


# --- 2. 错误级别分析 (ELA) ---
def analyze_ela(pil_image_rgb, quality=90, scale=12):  # scale 针对文档可以调整
    try:
        buffer_orig = io.BytesIO()
        pil_image_rgb.save(buffer_orig, format="JPEG", quality=100)
        buffer_orig.seek(0)

        buffer_resaved = io.BytesIO()
        pil_image_rgb.save(buffer_resaved, format="JPEG", quality=quality)
        buffer_resaved.seek(0)
        resaved_image = Image.open(buffer_resaved)

        original_array = np.array(pil_image_rgb, dtype=np.float32)
        resaved_array = np.array(resaved_image, dtype=np.float32)

        diff_array = np.abs(original_array - resaved_array) * scale
        diff_array = np.clip(diff_array, 0, 255).astype(np.uint8)
        ela_image_pil = Image.fromarray(diff_array)

        # 对于文档，ELA差异可能集中在文本边缘或logo处
        # 可以计算差异图像的熵或者高百分位数的平均值来量化
        non_zero_diff = diff_array[diff_array > 10]  # 考虑有意义的差异
        ela_metric = np.mean(non_zero_diff) if non_zero_diff.size > 0 else 0

        # 归一化评分，这个阈值需要针对文档类型进行校准
        suspicion_score = min(ela_metric / 50.0, 1.0)  # 假设平均差异超过50就非常可疑
        details = [f"ELA 指标 (显著差异区域的平均差异值 * {scale}): {ela_metric:.2f}"]
        if suspicion_score > 0.5:
            details.append("ELA分析显示潜在的压缩级别不一致区域。")

        return suspicion_score, details, ela_image_pil
    except Exception as e:
        return 0.0, [f"ELA分析错误: {e}"], None


# --- 3. 噪声一致性分析 ---
def analyze_noise_consistency(
    pil_image_gray, block_size=32
):  # 文档背景区域较大，block_size可以调整
    try:
        img_array = np.array(pil_image_gray, dtype=np.float32)
        # 使用高斯模糊的差异近似噪声，对文档的平滑背景可能更有效
        blurred_array = np.array(
            pil_image_gray.filter(ImageFilter.GaussianBlur(radius=0.5)),
            dtype=np.float32,
        )  # 轻微模糊
        noise_approx = np.abs(img_array - blurred_array)

        h, w = noise_approx.shape
        std_devs = []

        if h < block_size * 2 or w < block_size * 2:
            return 0.0, ["图像太小无法进行分块噪声分析。"]

        for r in range(0, h - block_size + 1, block_size):
            for c in range(0, w - block_size + 1, block_size):
                block = noise_approx[r : r + block_size, c : c + block_size]
                # 排除纯白或纯黑区域（对于文档很重要）
                if (
                    np.mean(block) > 5 and np.mean(block) < 250
                ):  # 避免分析纯色背景块的"噪声"
                    std_devs.append(np.std(block))

        if len(std_devs) < 5:  # 需要足够多的有效块进行比较
            return 0.0, ["未能从非纯色区域提取足够的噪声样本进行比较。"]

        avg_std_dev = np.mean(std_devs)
        variation_of_std_devs = np.std(std_devs)
        details = [
            f"平均近似噪声标准差 (非纯色块): {avg_std_dev:.2f}",
            f"近似噪声标准差的变异: {variation_of_std_devs:.2f}",
        ]
        suspicion_score = 0.0

        if (
            avg_std_dev > 0.1
        ):  # 避免除以非常小的值, 噪声太小本身也可能可疑或说明图像质量高
            coefficient_of_variation = variation_of_std_devs / avg_std_dev
            details.append(f"近似噪声标准差的变异系数: {coefficient_of_variation:.2f}")
            # 对于文档，如果不同区域（例如纸张背景和插入的文本背景）噪声变异大，则可疑
            suspicion_score = min(
                coefficient_of_variation * 1.5, 1.0
            )  # 变异系数的敏感度可以调高
            if suspicion_score > 0.4:
                details.append("检测到图像不同区域的噪声模式可能存在显著不一致。")
        elif avg_std_dev <= 0.1 and len(std_devs) > 0:  # 噪声非常低
            details.append("图像整体噪声水平非常低，可能为数字生成或经过强力平滑。")
            suspicion_score = 0.1  # 低噪声本身对于扫描件来说不寻常

        return suspicion_score, details
    except Exception as e:
        return 0.0, [f"噪声一致性分析错误: {e}"]


# --- 4. 复制-移动伪造检测 (CMFD) - 基础版 ---
def analyze_cmfd_basic(
    cv_image_gray, min_match_count=5, threshold_distance_ratio=0.75
):  # 文档中印章等元素较小
    try:
        orb = cv2.ORB_create(nfeatures=3000)  # 对文档可以尝试更多特征点
        kp1, des1 = orb.detectAndCompute(cv_image_gray, None)

        if des1 is None or len(des1) < min_match_count * 2:  # 需要足够多的描述子
            return 0.0, ["特征点不足，无法进行CMFD分析。"], None

        # FLANN 匹配器
        FLANN_INDEX_LSH = 6
        index_params = dict(
            algorithm=FLANN_INDEX_LSH, table_number=6, key_size=12, multi_probe_level=1
        )
        search_params = dict(checks=50)
        flann = cv2.FlannBasedMatcher(index_params, search_params)

        # 与自身进行匹配
        matches = flann.knnMatch(des1, des1, k=2)  # k=2 找到最佳和次佳匹配

        good_matches = []
        # potential_regions_count = 0 # This variable was unused

        for i, pair_match in enumerate(matches):
            if len(pair_match) == 2:
                m, n = pair_match
                # Lowe's ratio test 且 排除自身到自身的精确匹配 (queryIdx != trainIdx)
                if (
                    m.queryIdx != m.trainIdx
                    and m.distance < threshold_distance_ratio * n.distance
                ):
                    pt1 = kp1[m.queryIdx].pt
                    pt2 = kp1[m.trainIdx].pt
                    # 排除物理位置非常近的匹配点（它们可能属于同一自然纹理而非复制）
                    if (
                        np.linalg.norm(np.array(pt1) - np.array(pt2)) > 20
                    ):  # 至少相隔20像素（可调）
                        good_matches.append(m)

        details = [
            f"检测到 {len(good_matches)} 个潜在的CMFD特征匹配对 (已排除自身及近距离匹配)。"
        ]
        suspicion_score = 0.0
        highlight_img = None  # 可选的可视化

        if len(good_matches) >= min_match_count:
            # 简单评分：匹配数越多越可疑。更高级的会做聚类和几何校验。
            suspicion_score = min(len(good_matches) / (min_match_count * 10.0), 1.0)
            details.append(
                f"CMFD匹配点数量达到阈值({min_match_count})，可能存在复制粘贴的元素（如公章、logo、特定文本格式）。"
            )
            # 可在此处添加代码以可视化匹配对，帮助人工判断
            cv_image_rgb = cv2.cvtColor(cv_image_gray, cv2.COLOR_GRAY2BGR)
            highlight_img = cv2.drawMatches(
                cv_image_rgb,
                kp1,
                cv_image_rgb,
                kp1,
                good_matches,
                None,
                flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS,
            )
            highlight_img = Image.fromarray(cv_image_rgb)

        return suspicion_score, details, highlight_img
    except Exception as e:
        return 0.0, [f"CMFD分析错误: {e}"], None


# --- 5. 纹理分析 (LBP) ---
def analyze_texture_consistency(
    cv_image_gray, block_size=32, lbp_radius=2, lbp_points=16
):  # 调整LBP参数
    try:
        # local_binary_pattern is now imported at the top
        lbp = local_binary_pattern(
            cv_image_gray, P=lbp_points, R=lbp_radius, method="uniform"
        )
        n_bins = int(lbp.max() + 1)

        lbp_histograms = []
        # valid_blocks_info = [] # This variable was unused
        h, w = cv_image_gray.shape

        if h < block_size * 2 or w < block_size * 2:
            return 0.0, ["图像太小无法进行有效的纹理分块比较。"]

        for r in range(0, h - block_size + 1, block_size):
            for c in range(0, w - block_size + 1, block_size):
                block_gray = cv_image_gray[r : r + block_size, c : c + block_size]
                block_lbp_roi = lbp[
                    r : r + block_size, c : c + block_size
                ]  # Use the LBP calculated on the whole image
                # 排除过于平滑或纯色的块（例如，大片空白纸张区域）
                if np.std(block_gray) > 5:  # 块内灰度标准差大于5认为有足够纹理
                    hist, _ = np.histogram(
                        block_lbp_roi.ravel(),
                        bins=n_bins,
                        range=(0, n_bins),
                        density=True,
                    )
                    lbp_histograms.append(hist)

        if len(lbp_histograms) < 5:  # 需要足够的纹理块进行比较
            return 0.0, ["未能从图像中提取足够的纹理块进行比较。"]

        # 计算所有LBP直方图对之间的平均Wasserstein距离
        distances = []
        for i in range(len(lbp_histograms)):
            for j in range(i + 1, len(lbp_histograms)):
                dist = wasserstein_distance(lbp_histograms[i], lbp_histograms[j])
                distances.append(dist)

        if (
            not distances
        ):  # Should not happen if len(lbp_histograms) >= 2, but as a safeguard
            return 0.0, ["未能计算纹理直方图距离。"]

        mean_dist = np.mean(distances)
        std_dist = np.std(distances)

        details = [
            f"LBP: 块间平均纹理直方图距离 (Wasserstein): {mean_dist:.4f}, 标准差: {std_dist:.4f}"
        ]
        suspicion_score = 0.0
        # 纹理差异大可能意味着拼接了不同来源的纸张或打印内容
        if mean_dist > 0.08:  # 这个阈值需要针对文档类型进行校准
            suspicion_score = min(mean_dist * 8.0, 1.0)  # 归一化调整 (放大差异)
            details.append(
                f"LBP: 块间纹理差异较大，可能指示拼接或使用了不同材质/打印的区域。"
            )

        return suspicion_score, details
    except Exception as e:
        return 0.0, [f"纹理分析错误: {e}"]


# --- 6. 频域分析 ---
def analyze_frequency_domain(cv_image_gray):
    scores = []
    details = []
    try:
        f = fft2(cv_image_gray)
        fshift = fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift) + 1e-8)

        rows, cols = cv_image_gray.shape
        crow, ccol = rows // 2, cols // 2

        # 检测周期性噪声或异常高频 (对于扫描文档，可能有扫描仪引入的固定频率噪声)
        # 一个简化方法是检查频谱中是否有非中心的、对称的亮点
        # 此处简化为统计高频能量的离群点
        center_size_ratio = 0.1  # 中心10%区域认为是低频
        center_rows_half = int(rows * center_size_ratio / 2)
        center_cols_half = int(cols * center_size_ratio / 2)

        mask_hf = np.ones_like(magnitude_spectrum, dtype=bool)
        mask_hf[
            crow - center_rows_half : crow + center_rows_half,
            ccol - center_cols_half : ccol + center_cols_half,
        ] = False

        hf_values = magnitude_spectrum[mask_hf]
        if hf_values.size > 0:
            mean_hf = np.mean(hf_values)
            std_hf = np.std(hf_values)
            # 寻找远超平均值的"尖峰"
            strong_peaks = hf_values[
                hf_values > mean_hf + 3.5 * std_hf
            ]  # 3.5倍标准差之外
            num_strong_peaks = len(strong_peaks)

            details.append(
                f"FFT: 高频能量均值={mean_hf:.2f}, 标准差={std_hf:.2f}, 检测到{num_strong_peaks}个强峰。"
            )
            if (
                num_strong_peaks > 10
            ):  # 阈值可调，过多的强峰可能指示特定类型的伪影或噪声
                scores.append(min(num_strong_peaks / 50.0, 1.0) * 0.6)  # 部分分数
                details.append(
                    f"FFT: 高频区域存在较多异常能量峰，可能指示扫描伪影、特定模式或数字处理。"
                )
        else:
            details.append("FFT: 未能分析高频区域。")

    except Exception as e:
        details.append(f"FFT分析错误: {e}")

    # DCT 系数分析 (基础版，可用于检测JPEG重复压缩的某些迹象)
    try:
        img_array_float = np.array(cv_image_gray, dtype=np.float32)
        dct_coeffs_ac = []
        for r in range(0, cv_image_gray.shape[0] - 7, 8):
            for c in range(0, cv_image_gray.shape[1] - 7, 8):
                block = img_array_float[r : r + 8, c : c + 8]
                dct_block = dct(dct(block.T, norm="ortho").T, norm="ortho")
                dct_coeffs_ac.extend(dct_block.flatten()[1:16])  # 取前15个AC系数

        if dct_coeffs_ac:
            coeffs_rounded = [int(round(c)) for c in dct_coeffs_ac]
            counts = Counter(coeffs_rounded)
            num_zeros = counts.get(0, 0)
            # num_ones = counts.get(1,0) # This variable was unused
            total_ac = len(dct_coeffs_ac)
            ratio_zeros = num_zeros / total_ac if total_ac else 0

            details.append(f"DCT: AC系数中零值比例: {ratio_zeros:.3f} (前15个AC系数)")
            # 对于高质量JPEG或原始图，零值AC系数通常较少
            # 对于多次压缩或低质量JPEG，零值可能较多
            if ratio_zeros > 0.25:  # 经验阈值，文档图片可能本身有很多平滑区域
                scores.append(min(ratio_zeros * 0.5, 1.0) * 0.3)  # 部分分数
                details.append(
                    "DCT: AC系数中零值比例较高，可能指示JPEG压缩伪影或多次压缩。"
                )
    except Exception as e:
        details.append(f"DCT分析错误: {e}")

    final_score = np.mean(scores) if scores else 0.0
    return final_score, details


# --- 7. 面部分析 (针对身份证等) ---
def analyze_faces(cv_image_rgb, pil_image_rgb):
    if not DLIB_AVAILABLE:
        return 0.0, ["面部分析跳过：缺少dlib或face_recognition库。"]

    scores = []
    details = []
    num_faces_detected = 0

    try:
        face_locations = face_recognition.face_locations(
            cv_image_rgb, model="hog"
        )  # hog速度快, cnn更准但慢
        num_faces_detected = len(face_locations)

        if num_faces_detected == 0:
            details.append("未检测到人脸。")
            return 0.0, details

        details.append(f"检测到 {num_faces_detected} 张人脸。")
        face_landmarks_list = face_recognition.face_landmarks(
            cv_image_rgb, face_locations
        )

        for i, (top, right, bottom, left) in enumerate(face_locations):
            face_details_prefix = (
                f"  人脸 #{i+1} (位置 T:{top}, L:{left}, B:{bottom}, R:{right}):"
            )

            # 检查人脸区域的ELA和噪声是否有异常 (需要主函数传递ELA图和噪声图或重新计算)
            # (为简化，此处不重复计算，但实际系统中可以对人脸ROI做专门分析)

            # 检查人脸区域的清晰度/模糊度 (例如用拉普拉斯算子方差)
            face_roi_gray = cv2.cvtColor(
                cv_image_rgb[top:bottom, left:right], cv2.COLOR_RGB2GRAY
            )
            laplacian_var = cv2.Laplacian(face_roi_gray, cv2.CV_64F).var()
            details.append(
                f"{face_details_prefix} 清晰度 (Laplacian方差): {laplacian_var:.2f}"
            )
            if laplacian_var < 50:  # 阈值可调，非常模糊可能是有意处理或替换质量差
                scores.append(0.6)
                details.append(f"{face_details_prefix} 警告: 人脸区域异常模糊。")

            # 肤色一致性 (非常粗略)
            # face_roi_hsv = cv2.cvtColor(cv_image_rgb[top:bottom, left:right], cv2.COLOR_RGB2HSV)
            # (分析H通道的分布等)

            # 针对Deepfake的简单检查：眼部、牙齿的自然程度（需要更复杂的模型）
            # 此处仅为占位，实际Deepfake检测需要专用模型
            # if face_landmarks_list and i < len(face_landmarks_list):
            #     landmarks = face_landmarks_list[i]
            #     # 分析眼睛对称性、牙齿清晰度等 (复杂)

            # 如果使用了更高级的Deepfake检测器，可以在这里集成其分数
            # ext_deepfake_score, ext_deepfake_details = external_deepfake_detector(face_roi_rgb)
            # scores.append(ext_deepfake_score)
            # details.extend(ext_deepfake_details)

        if not scores and num_faces_detected > 0:
            details.append("对检测到的人脸未发现明显的、基础级伪造特征。")

    except Exception as e:
        details.append(f"面部分析错误: {e}")

    final_score = np.mean(scores) if scores else 0.0
    # 如果检测到多张脸，且某些脸可疑，分数会体现。如果都正常，分数低。
    return final_score, details


# --- 8. 文档背景一致性分析 (替代旧的亮度/颜色异常) ---
def analyze_document_background_consistency(
    pil_image_rgb, block_size=64, std_dev_threshold=12, color_diff_threshold=25
):
    img_array = np.array(pil_image_rgb)
    h, w, _ = img_array.shape
    suspicion_score = 0.0
    details = []
    flat_block_colors = []

    if h < block_size or w < block_size:
        details.append("图像太小，无法进行背景一致性分析。")
        return 0.0, details

    for r in range(0, h - block_size + 1, block_size):
        for c in range(0, w - block_size + 1, block_size):
            block = img_array[r : r + block_size, c : c + block_size, :]
            # 计算每个颜色通道的标准差的平均值
            block_rgb_std_dev = np.mean(np.std(block, axis=(0, 1)))

            if block_rgb_std_dev < std_dev_threshold:  # 认为是平坦/均匀的背景块
                avg_color = np.mean(block, axis=(0, 1))
                flat_block_colors.append(avg_color)

    if len(flat_block_colors) < 4:  # 需要至少几个平坦块才能比较
        details.append(
            f"未能找到足够数量 ({len(flat_block_colors)}/4) 的平坦背景区域进行比较。"
        )
        return 0.0, details

    # 计算平坦块颜色之间的差异
    mean_of_flat_colors = np.mean(flat_block_colors, axis=0)
    max_dist_from_mean = 0
    for color in flat_block_colors:
        dist = np.linalg.norm(color - mean_of_flat_colors)  # 欧氏距离
        if dist > max_dist_from_mean:
            max_dist_from_mean = dist

    details.append(
        f"平坦背景区域颜色与均色最大差异值: {max_dist_from_mean:.2f} (阈值参考: {color_diff_threshold})"
    )
    if (
        max_dist_from_mean > color_diff_threshold
    ):  # 如果某些平坦块的颜色与其他平坦块差异很大
        suspicion_score = (
            min((max_dist_from_mean / (color_diff_threshold * 1.5)), 1.0) * 0.7
        )  # 归一化评分
        details.append(
            "检测到平坦背景区域颜色存在显著不一致，可能指示覆盖、擦除后重写或拼接。"
        )

    return suspicion_score, details


# --- 结果聚合 ---
def aggregate_results(all_analysis_results, weights):
    total_weighted_score = 0
    sum_of_weights_applied = 0  # 只累加实际产生分数的模块的权重
    detailed_report = {}

    for tech, result in all_analysis_results.items():
        score = result.get("score", 0.0)
        # 如果一个模块因为某些原因没有产生有效评分（例如面部分析未检测到人脸），其score可能是0或接近0
        # 此时它的权重不应过多影响最终平均值，或者说它的贡献就是0
        tech_details = result.get("details", [])
        weight = weights.get(tech, 0)

        # 只有当模块确实执行了并且给出了一个有意义的上下文（比如人脸模块检测到了人脸才应用其权重）
        # 或者，如果模块设计为在不适用时返回0分，那么其权重依然适用，但贡献为0
        # 此处采用简单加权：
        if weight > 0:  # 只考虑有权重的模块
            total_weighted_score += score * weight
            sum_of_weights_applied += weight  # 累加实际使用的权重

        detailed_report[tech] = {
            "score": f"{score:.3f}",
            "weight": f"{weight:.2f}",
            "contribution": f"{score * weight:.3f}",
            "details": tech_details,
        }
        if result.get("image_saved_path"):
            detailed_report[tech]["output_image_path"] = result.get("image_saved_path")

    # 使用实际应用的权重的总和来计算最终得分，避免分母过大或过小
    final_suspicion_score = (
        total_weighted_score / sum_of_weights_applied
        if sum_of_weights_applied > 0
        else 0.0
    )
    # 进一步将分数限制在0-1之间，防止意外超出
    final_suspicion_score = np.clip(final_suspicion_score, 0.0, 1.0)

    detailed_report["final_weighted_score"] = f"{final_suspicion_score:.3f}"
    detailed_report["sum_of_weights_applied"] = f"{sum_of_weights_applied:.2f}"

    return final_suspicion_score, detailed_report


# --- 主分析流程 ---
def comprehensive_image_analysis(
    image_path, enabled_techniques, technique_weights, output_dir="analysis_outputs"
):
    all_results = {}

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    try:
        pil_image_orig = Image.open(image_path)
        # 对于文档，保留较高分辨率可能更好，但仍需限制以防过大
        pil_image_resized = resize_image_for_analysis(
            pil_image_orig.copy(), max_dim=1500
        )
        pil_image_rgb = pil_image_resized.convert("RGB")  # 确保是RGB
        pil_image_gray = pil_image_rgb.convert("L")

        # OpenCV 图像 (从Pillow转换)
        cv_image_gray = np.array(pil_image_gray)
        cv_image_rgb_for_cv = cv2.cvtColor(np.array(pil_image_rgb), cv2.COLOR_RGB2BGR)

        print(f"\n开始分析图片: {image_path} (分析尺寸: {pil_image_rgb.size})")

        if "metadata" in enabled_techniques:
            print("  执行元数据分析...")
            score, details = analyze_metadata(
                pil_image_orig
            )  # 元数据用原始图，未经resize
            all_results["metadata"] = {"score": score, "details": details}

        if "ela" in enabled_techniques:
            print("  执行ELA分析...")
            score, details, ela_img = analyze_ela(
                pil_image_rgb.copy(), quality=90, scale=12
            )
            all_results["ela"] = {"score": score, "details": details}
            if ela_img:
                ela_filename = os.path.join(
                    output_dir, f"ela_{os.path.basename(image_path)}"
                )
                ela_img.save(ela_filename)
                all_results["ela"]["image_saved_path"] = ela_filename

        if "noise" in enabled_techniques:
            print("  执行噪声一致性分析...")
            score, details = analyze_noise_consistency(
                pil_image_gray.copy(), block_size=32
            )
            all_results["noise"] = {"score": score, "details": details}

        if "cmfd" in enabled_techniques:
            print("  执行基础CMFD分析...")
            score, details, cmfd_highlight_img = analyze_cmfd_basic(
                cv_image_gray.copy(), min_match_count=5
            )
            all_results["cmfd"] = {"score": score, "details": details}
            if cmfd_highlight_img:  # 如果cmfd_highlight_img是Pillow Image
                cmfd_filename = os.path.join(
                    output_dir, f"cmfd_highlight_{os.path.basename(image_path)}"
                )
                cmfd_highlight_img.save(
                    cmfd_filename
                )  # 假设 analyze_cmfd_basic 返回 PIL Image
                all_results["cmfd"]["image_saved_path"] = cmfd_filename
                # 当前版本 analyze_cmfd_basic 未完成该可视化，仅为占位符

        if "texture" in enabled_techniques:
            print("  执行纹理一致性分析...")
            score, details = analyze_texture_consistency(
                cv_image_gray.copy(), block_size=32
            )
            all_results["texture"] = {"score": score, "details": details}

        if "frequency" in enabled_techniques:  # 包含基础的DCT分析
            print("  执行频域分析 (FFT/DCT)...")
            score, details_list = analyze_frequency_domain(cv_image_gray.copy())
            all_results["frequency"] = {"score": score, "details": details_list}

        if "doc_background" in enabled_techniques:
            print("  执行文档背景一致性分析...")
            score, details = analyze_document_background_consistency(
                pil_image_rgb.copy()
            )
            all_results["doc_background"] = {"score": score, "details": details}

        if "facial" in enabled_techniques:
            print("  执行面部分析...")
            # pil_image_rgb 用于某些面部库可能需要的原始Pillow格式，cv_image_rgb_for_cv 用于face_recognition等OpenCV兼容库
            score, details_list = analyze_faces(
                cv_image_rgb_for_cv.copy(), pil_image_rgb.copy()
            )
            all_results["facial"] = {"score": score, "details": details_list}

        # 双JPEG检测 (djpeg) 目前概念性地包含在了 frequency_domain 中对DCT的分析，不再单独列为一个主要模块
        # 如果需要更强的独立双JPEG检测，可以单独实现并添加

        # 聚合结果
        final_score, report = aggregate_results(all_results, technique_weights)

        print("\n--- 综合分析报告 ---")
        for tech, data in report.items():
            if tech in ["final_weighted_score", "sum_of_weights_applied"]:  # 跳过汇总行
                continue
            print(f"\n模块: {tech.upper()}")
            print(f"  可疑度评分: {data['score']}")
            print(f"  权重: {data['weight']}")
            print(f"  对总分贡献值: {data['contribution']}")
            print(f"  详情:")
            for d_item in data["details"]:  # 确保details是列表
                print(f"    - {d_item}")
            if data.get("output_image_path"):
                print(f"  输出图像: {data['output_image_path']}")

        print(f"\n--- 最终评估 ---")
        print(f"应用权重总和: {report['sum_of_weights_applied']}")
        print(f"综合可疑度评分: {report['final_weighted_score']}")

        final_score_float = float(report["final_weighted_score"])
        if final_score_float > 0.50:  # 阈值可根据实际情况调整
            print("结论：该文件有较高可能性被修改。建议人工审核。")
        elif final_score_float > 0.25:
            print("结论：该文件有一定可能性被修改，建议进一步检查。")
        else:
            print("结论：当前分析未发现明显的、强烈的修改痕迹。")

        # 在 comprehensive_image_analysis 函数的末尾，或在这里获取 report_details 后，
        # 可以加入更具体的、基于场景的提示：
        print("\n--- 针对常见P图类型的检查建议 (基于本次分析结果) ---")

        if final_score_float > 0.30:  # 调整总分阈值以触发建议
            high_risk_areas_identified = False
            # 检查哪些模块给出了较高的可疑度评分
            suspicious_modules_scores = {
                tech_key: float(data_val.get("score", 0))
                for tech_key, data_val in report.items()
                if isinstance(data_val, dict) and "score" in data_val
            }

            # 针对日期、姓名、诊断内容P图的提示
            if any(
                suspicious_modules_scores.get(m, 0) > 0.45
                for m in ["ela", "noise", "texture", "doc_background"]
            ):  # 模块得分阈值可调
                print("- 检测到局部图像特征（ELA、噪声、纹理、背景）可能存在异常。")
                print("  强烈建议人工核查：")
                print(
                    "    1. 【关键文本】：就诊日期、检查日期、姓名、年龄、诊断内容、金额等文字区域是否存在被修改(PS)痕迹。"
                )
                print(
                    "    2. 【字体外观】：上述关键文本区域的字体、字号、颜色、粗细、间距、基线是否与文档其他部分保持一致。"
                )
                high_risk_areas_identified = True

            # 针对证件P图 (如果有人脸分析模块且得分高)
            if suspicious_modules_scores.get("facial", 0) > 0.5:
                print("- 面部分析模块检测到异常。")
                print("  强烈建议人工核查：")
                print(
                    "    1. 【身份证照片】：核对照片是否为本人，是否存在替换、修改痕迹，照片边缘过渡是否自然。"
                )
                print(
                    "    2. 【照片清晰度与光照】：照片是否异常模糊或清晰度与其他部分不符，光照是否一致。"
                )
                high_risk_areas_identified = True

            # 针对公章等复制
            if suspicious_modules_scores.get("cmfd", 0) > 0.5:  # CMFD得分高一般证据较强
                print("- CMFD模块检测到可疑的复制-移动内容。")
                print("  强烈建议人工核查：")
                print(
                    "    1. 【公章/签名】：文档中的公章、签名、logo等是否为复制粘贴而来。"
                )
                high_risk_areas_identified = True

            if (
                not high_risk_areas_identified and final_score_float > 0.30
            ):  # 如果总分高但没有特别突出的模块
                print(
                    "- 综合可疑度较高，但未定位到特定高风险模块，建议全面人工审核所有细节。"
                )

        elif final_score_float <= 0.30:
            print("- 当前分析未发现需要特别警示的P图痕迹，但仍建议按标准流程处理。")

        return final_score_float, report

    except FileNotFoundError:
        print(f"错误: 图片文件 {image_path} 未找到。")
        return 0.0, {"error": f"File not found: {image_path}"}
    except UnidentifiedImageError:
        print(
            f"错误: 无法识别或打开图片文件 {image_path}。可能文件已损坏或格式不支持。"
        )
        return 0.0, {"error": f"Cannot identify image file: {image_path}"}
    except Exception as e:
        print(f"分析过程中发生未知错误: {e}")
        import traceback

        traceback.print_exc()
        return 0.0, {"error": f"An unexpected error occurred: {str(e)}"}


# --- 使用示例 ---
if __name__ == "__main__":
    # 针对航司病退文件（病例、身份证、住院证明等）及常见P图类型优化的权重
    # 重点提升对局部文本/元素修改的敏感度
    document_technique_weights_optimized = {
        "metadata": 0.12,  # 编辑软件信息依然重要
        "ela": 0.30,  # 对局部P图（如日期、姓名）非常敏感
        "noise": 0.16,  # 粘贴区域或数字插入文字的噪声差异
        "texture": 0.12,  # 纸张/打印纹理，或不同字体视觉纹理差异
        "doc_background": 0.08,  # 背景覆盖、拼接
        "cmfd": 0.08,  # 公章、logo等重复元素复制
        "facial": 0.08,  # 针对身份证照片，如果无有效人脸，此模块得分低，贡献也低
        "frequency": 0.06,  # 辅助，检测数字锐化或特定扫描伪影
    }
    # 确保所有在权重字典中的技术都在这个列表里
    enabled_techniques_for_documents_optimized = list(
        document_technique_weights_optimized.keys()
    )
    # 支持的图片格式
    image_extensions = {".jpg", ".jpeg", ".png", ".bmp", ".gif"}
    folder_path = "D:\\work\\ps_check_data"
    for filename in os.listdir(folder_path):
        # 检查文件扩展名
        if not any(filename.lower().endswith(ext) for ext in image_extensions):
            continue
        if not filename.startswith("xss"):
            continue
        image_path_input = os.path.join(folder_path, filename)
        final_score, report_details = comprehensive_image_analysis(
            image_path_input,
            enabled_techniques=enabled_techniques_for_documents_optimized,
            technique_weights=document_technique_weights_optimized,
            output_dir="D:\\work\\ps_check_data\\results_optimized",  # 指定输出文件夹
        )
        # 打印JSON格式的详细报告 (可用于后续处理或记录)
        print("\nJSON格式的详细报告:")
        print(json.dumps(report_details, indent=2, ensure_ascii=False))
