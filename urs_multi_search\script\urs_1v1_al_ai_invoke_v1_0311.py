import json
from typing import Any, Tuple, Optional, Dict


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj == ""
    return False


def parse_to_dict(data: Any) -> Tuple[bool, Optional[Dict], str]:
    """
    将输入数据解析为字典格式
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否成功, 解析后的字典, 错误信息)
    """
    try:
        # 如果是字符串，尝试JSON解析
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                return False, None, "JSON解析失败"

        # 如果是字典，直接返回
        if isinstance(data, dict):
            return True, data, ""

        # 如果是列表，查找包含inductionInfo的元素
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and "inductionInfo" in item:
                    return True, item, ""
            return False, None, "列表中未找到包含inductionInfo的元素"

        return False, None, f"不支持的数据类型: {type(data)}"
    except Exception as e:
        return False, None, f"解析异常: {str(e)}"


def validate_dict_structure(data: Dict) -> Tuple[bool, Optional[Dict], str]:
    """
    验证字典结构是否符合要求
    Args:
        data: 输入字典
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    try:
        if data is None:
            return False, None, "大模型返回数据为空"
        # 检查字典有效性
        if not isinstance(data, dict):
            return False, None, "大模型返回数据不是有效的字典"

        # 检查inductionInfo字段
        if "inductionInfo" not in data:
            return False, None, "大模型返回数据缺少inductionInfo字段"

        induction_info = data["inductionInfo"]
        if not isinstance(induction_info, dict):
            return False, None, "大模型返回数据inductionInfo不是有效的字典"

        # 检查必需字段
        if "hasPriceChange" not in induction_info or is_deep_empty(
            induction_info.get("hasPriceChange")
        ):
            return False, None, "大模型返回数据缺少hasPriceChange字段"

        # 设置默认值
        induction_info.setdefault("priceChageDesc", "-")
        induction_info.setdefault("attributeDesc", "-")
        induction_info.setdefault("attributeReason", "-")
        induction_info.setdefault("attributeResp", "-")

        data.setdefault("compareAndAttributeDetails", [])
        # 检查compareAndAttributeDetails字段

        return True, data, ""
    except Exception as e:
        return False, None, f"大模型返回数据格式/内容验证异常: {str(e)}"


def check_data_structure(data: Any) -> Tuple[bool, Optional[Dict], str]:
    """
    检查数据结构并进行解析验证
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    if data is None:
        return False, None, "大模型返回数据为空"
    # 第一步：解析成字典
    success, result_dict, error_msg = parse_to_dict(data)
    if not success:
        return False, None, error_msg

    # 第二步：验证字典结构
    return validate_dict_structure(result_dict)


def main(param: dict) -> dict:
    try:
        alResult = param.get("alResult")
        success, result_dict, error_msg = check_data_structure(alResult)
        if not success:
            return {
                "aIResult": {
                    "inductionInfo": {
                        "hasPriceChange": "",
                        "priceChageDesc": "",
                        "attributeDesc": "",
                        "attributeReason": "",
                        "attributeResp": "",
                    },
                    "compareAndAttributeDetails": [],
                },
                "status": "失败",
                "errMsg": error_msg,
            }
        else:
            return {
                "aIResult": result_dict,
                "status": "成功",
                "errMsg": "大模型返回数据验证成功",
            }
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        result = {
            "aIResult": {
                "inductionInfo": {
                    "hasPriceChange": "",
                    "priceChageDesc": "",
                    "attributeDesc": "",
                    "attributeReason": "",
                    "attributeResp": "",
                },
                "compareAndAttributeDetails": [],
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "aIResult": {
                "inductionInfo": {
                    "hasPriceChange": "",
                    "priceChageDesc": "",
                    "attributeDesc": "",
                    "attributeReason": "",
                    "attributeResp": "",
                },
                "compareAndAttributeDetails": [],
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result


if __name__ == "__main__":
    param = {
        "alResult": [
            {
                "inductionInfo": {
                    "hasPriceChange": "是",
                    "priceChageDesc": "前后两次搜索价格从850涨至870，确定发生变价",
                    "attributeDesc": "展示价不同，投毒相同，乘机人相同，筛选项相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，同政策，追价金额变化",
                    "attributeReason": "平台策略-追价变化",
                    "attributeResp": "平台定责",
                },
                "compareAndAttributeDetails": [
                    {
                        "flightNo": "HO1885",
                        "tradeId": "ops_slugger_250304.140545.10.95.140.64.482642.7128175734_1",
                        "price": "850",
                        "tag": "GST1",
                        "oriTag": "",
                        "searchDateTime": "2025-03-04 14:05:45",
                        "searchTimeDiff": "-",
                        "tSource": "list",
                        "isPriceChage": "-",
                        "matchUserQuestionDesc": "-",
                        "priceChageDesc": "-",
                        "attributeDesc": "-",
                        "attributeReason": "-",
                        "attributeResp": "-",
                    },
                    {
                        "flightNo": "HO1885",
                        "tradeId": "ops_slugger_250304.140828.10.95.133.37.3233375.828413005_1",
                        "price": "870",
                        "tag": "CZA1",
                        "oriTag": "",
                        "searchDateTime": "2025-03-04 14:08:28",
                        "searchTimeDiff": "2分钟43秒",
                        "tSource": "list",
                        "isPriceChage": "是",
                        "matchUserQuestionDesc": "用户反馈的变价场景被匹配（价格连续上涨）",
                        "priceChageDesc": "展示价格从850变为870",
                        "attributeDesc": "展示价不同，投毒相同，乘机人相同，筛选项相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，同政策，追价金额变化",
                        "attributeReason": "平台策略-追价变化",
                        "attributeResp": "平台定责",
                    },
                ],
            }
        ]
    }

    result = main(param)
    print(result)
