# SheetJS 库安装指南

## 重要说明

插件依赖 SheetJS 库来处理 Excel 文件，必须手动下载并放置在正确位置才能正常工作。

## 安装步骤

1. **创建 lib 目录**（如果不存在）：
   - 在插件根目录下创建 `lib` 文件夹

2. **下载 xlsx.full.min.js**：
   - 方法1：直接下载（推荐）：
     ```
     右键另存为：https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js
     ```
   - 方法2：使用 PowerShell 命令：
     ```powershell
     Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js" -OutFile "D:\gongzuo\project\urs_script\feishu-excel-plugin\lib\xlsx.full.min.js"
     ```
   - 方法3：使用 curl（如果已安装）：
     ```bash
     curl -o D:\gongzuo\project\urs_script\feishu-excel-plugin\lib\xlsx.full.min.js https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js
     ```

3. **验证文件**：
   - 确保文件路径为：`feishu-excel-plugin/lib/xlsx.full.min.js`
   - 文件大小应约为 1.5MB

4. **重新加载插件**：
   - 在 Chrome 扩展页面（chrome://extensions/）
   - 找到本插件并点击"刷新"图标

## 常见问题解决

1. **XLSX is not defined 错误**：
   - 确认文件是否下载完整
   - 检查控制台是否有404错误
   - 确保文件路径完全匹配

2. **文件下载失败**：
   - 尝试使用VPN
   - 检查网络连接
   - 尝试其他下载方法

3. **版本问题**：
   - 确保下载的是 0.18.5 版本
   - 不要使用其他版本或精简版