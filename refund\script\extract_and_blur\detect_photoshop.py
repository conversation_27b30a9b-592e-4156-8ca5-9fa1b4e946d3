#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image, ImageChops, ImageEnhance
import piexif
from pathlib import Path
import argparse

def perform_ela(image_path, quality=90, scale=10):
    """
    执行错误等级分析(Error Level Analysis)
    
    参数:
        image_path: 图片路径
        quality: JPEG压缩质量
        scale: 差异放大倍数
    
    返回:
        ela_image: ELA结果图像
        score: ELA分数（差异程度）
    """
    # 读取原始图像
    original = Image.open(image_path).convert('RGB')
    
    # 保存为临时JPEG文件
    temp_path = 'temp.jpg'
    original.save(temp_path, 'JPEG', quality=quality)
    
    # 读取临时文件
    resaved = Image.open(temp_path).convert('RGB')
    
    # 计算原始图像和重新保存的图像之间的差异
    ela_image = ImageChops.difference(original, resaved)
    
    # 放大差异以便更容易看到
    extrema = ela_image.getextrema()
    max_diff = max([ex[1] for ex in extrema])
    if max_diff == 0:
        max_diff = 1
    
    # 计算ELA分数 - 差异越大，可能被PS的概率越高
    score = sum([ex[1] for ex in extrema]) / (3 * 255)  # 归一化分数
    
    # 放大差异
    ela_image = ImageEnhance.Brightness(ela_image).enhance(scale / max_diff)
    
    # 删除临时文件
    os.remove(temp_path)
    
    return ela_image, score

def noise_analysis(image_path):
    """
    执行噪声分析，检测图像中噪声的不一致性
    
    参数:
        image_path: 图片路径
    
    返回:
        noise_image: 噪声图像
        noise_score: 噪声不一致性分数
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 计算噪声
    noise = cv2.absdiff(gray, blurred)
    
    # 计算噪声的标准差作为分数
    noise_score = np.std(noise) / 255.0  # 归一化
    
    # 创建热图以显示噪声分布
    noise_image = cv2.applyColorMap(noise, cv2.COLORMAP_JET)
    
    return noise_image, noise_score

def check_metadata(image_path):
    """
    检查图像元数据中的PS痕迹
    
    参数:
        image_path: 图片路径
    
    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 读取图像元数据
        img = Image.open(image_path)
        metadata_info = {}
        has_ps_metadata = False
        
        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])
            
            # 检查软件信息
            if piexif.ExifIFD.Software in exif_dict["Exif"]:
                software = exif_dict["Exif"][piexif.ExifIFD.Software].decode('utf-8', errors='ignore')
                metadata_info["软件"] = software
                if "photoshop" in software.lower() or "adobe" in software.lower():
                    has_ps_metadata = True
            
            # 检查修改历史
            if piexif.ExifIFD.MakerNote in exif_dict["Exif"]:
                maker_note = exif_dict["Exif"][piexif.ExifIFD.MakerNote].decode('utf-8', errors='ignore')
                metadata_info["制造商备注"] = maker_note
                if "photoshop" in maker_note.lower() or "adobe" in maker_note.lower():
                    has_ps_metadata = True
        img_info = str(img.info).lower()
        print(img_info)
        # 检查XMP数据
        if "photoshop" in img_info or "adobe" in img_info:
            has_ps_metadata = True
            metadata_info["其他元数据"] = "包含Adobe/Photoshop相关信息"
        
        return has_ps_metadata, metadata_info
    
    except Exception as e:
        print(f"检查元数据时出错: {e}")
        return False, {"错误": str(e)}

def detect_photoshop(image_path, output_dir=None, ela_quality=90, ela_scale=10):
    """
    综合检测图像是否被PS过
    
    参数:
        image_path: 图片路径
        output_dir: 输出目录，如果提供则保存分析结果图像
        ela_quality: ELA分析的JPEG质量
        ela_scale: ELA差异放大倍数
    
    返回:
        result: 检测结果字典
    """
    result = {
        "图片路径": image_path,
        "PS可能性": 0.0,
        "分析结果": {}
    }
    
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 执行ELA分析
    try:
        ela_image, ela_score = perform_ela(image_path, quality=ela_quality, scale=ela_scale)
        result["分析结果"]["ELA分数"] = ela_score
        
        # ELA分数解释
        if ela_score > 0.05:
            ps_probability = min(1.0, ela_score * 5)  # 将分数映射到0-1范围
            result["分析结果"]["ELA解释"] = f"检测到较高的错误等级差异，可能被PS过 (分数: {ela_score:.4f})"
            result["PS可能性"] = max(result["PS可能性"], ps_probability)
        else:
            result["分析结果"]["ELA解释"] = f"错误等级差异较低，可能未被PS过 (分数: {ela_score:.4f})"
        
        # 保存ELA图像
        if output_dir:
            ela_output_path = os.path.join(output_dir, f"{Path(image_path).stem}_ela.png")
            ela_image.save(ela_output_path)
            result["分析结果"]["ELA图像"] = ela_output_path
    
    except Exception as e:
        result["分析结果"]["ELA错误"] = str(e)
    
    # 执行噪声分析
    try:
        noise_image, noise_score = noise_analysis(image_path)
        result["分析结果"]["噪声分数"] = noise_score
        
        # 噪声分数解释
        if noise_score < 0.01:
            ps_probability = 0.7  # 非常低的噪声可能表示过度平滑
            result["分析结果"]["噪声解释"] = f"检测到异常低的噪声水平，可能被PS过 (分数: {noise_score:.4f})"
            result["PS可能性"] = max(result["PS可能性"], ps_probability)
        elif noise_score > 0.1:
            ps_probability = 0.6  # 非常高的噪声可能表示添加了人工噪声
            result["分析结果"]["噪声解释"] = f"检测到异常高的噪声水平，可能被PS过 (分数: {noise_score:.4f})"
            result["PS可能性"] = max(result["PS可能性"], ps_probability)
        else:
            result["分析结果"]["噪声解释"] = f"噪声水平正常 (分数: {noise_score:.4f})"
        
        # 保存噪声图像
        if output_dir:
            noise_output_path = os.path.join(output_dir, f"{Path(image_path).stem}_noise.png")
            cv2.imwrite(noise_output_path, noise_image)
            result["分析结果"]["噪声图像"] = noise_output_path
    
    except Exception as e:
        result["分析结果"]["噪声分析错误"] = str(e)
    
    # 检查元数据
    try:
        has_ps_metadata, metadata_info = check_metadata(image_path)
        result["分析结果"]["元数据"] = metadata_info
        
        if has_ps_metadata:
            result["分析结果"]["元数据解释"] = "检测到Photoshop/Adobe相关元数据"
            result["PS可能性"] = max(result["PS可能性"], 0.9)  # 元数据是很强的证据
        else:
            result["分析结果"]["元数据解释"] = "未检测到Photoshop/Adobe相关元数据"
    
    except Exception as e:
        result["分析结果"]["元数据检查错误"] = str(e)
    
    # 综合评估
    if result["PS可能性"] > 0.7:
        result["结论"] = f"图像很可能被PS过 (可能性: {result['PS可能性']:.2f})"
    elif result["PS可能性"] > 0.4:
        result["结论"] = f"图像可能被PS过 (可能性: {result['PS可能性']:.2f})"
    else:
        result["结论"] = f"图像可能未被PS过 (可能性: {result['PS可能性']:.2f})"
    
    return result

def main(params: dict) -> dict:
    image_path = "/Users/<USER>/Downloads/WechatIMG5.jpg"
    output = "/Users/<USER>/Desktop/qnideawork/urs_script/refund/script/output"
    quality = 90
    scale = 10
    
    # 检测图像是否被PS过
    result = detect_photoshop(image_path, output, quality, scale)
    
    # 打印结果
    print("\n===== 图像PS检测结果 =====")
    print(f"图片: {result['图片路径']}")
    print(f"结论: {result['结论']}")
    print("\n详细分析:")
    
    for key, value in result['分析结果'].items():
        if isinstance(value, dict):
            print(f"\n{key}:")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")

if __name__ == "__main__":
    main({})
