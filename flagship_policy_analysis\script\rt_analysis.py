# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import requests # type: ignore


def handle_request_exception(e: Exception) -> Dict[str, Any]:
    """统一处理请求异常"""
    if isinstance(e, requests.exceptions.HTTPError):
        return {"status": 1, "message": f"http错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.ConnectionError):
        return {"status": 1, "message": f"连接错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.Timeout):
        return {"status": 1, "message": f"超时错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.RequestException):
        return {"status": 1, "message": f"请求错误: {str(e)}"}
    else:
        return {"status": 1, "message": f"{e.__class__.__name__}: {str(e)}"}

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }
    
    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }
    
    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    # 获取舱位选择器
    fareDataKey = mapping_data.get("fareType", "")
    if not fareDataKey:
        return {
            "status": "error",
            "message": "fareType 不能为空",
            "data": None
        }

    # 获取qconfig的配置，并生成新的id和fare_type
    newData = process_qconfig_content(param, analysis_data, mapping_data)
    if newData["status"] != "success": 
        return {
            "status": "error",
            "message": newData["message"],
            "data": None
        }
    
    # 编辑版本
    config_edit_version= newData["data"]["config_edit_version"]
    
    # 展示方式，是否定立减
    display_method = analysis_data.get("display_method", "")
    # 根据display_method设置limitPrice和orderCut
    limit_price = "true" if display_method == "true" else "false"
    order_cut = limit_price  # orderCut与limitPrice保持一致
    
    # 航司
    carrier = mapping_data.get("carrier", "")

    
    # 设置备注文案
    product_label = analysis_data.get("product_label", "")
    product_mark = mapping_data.get("productMark", "")
    remarks = "AI工具填充" + product_label + "(" + product_mark + ")"
    

    # 构建结果对象
    result_obj = {
            "rowKey": newData["data"]["rowKey"],
            "userLabel": "HXYW",
            "limitPrice": limit_price,
            "shareCarrier": "",
            "orderCut": order_cut,
            "fareDataKey": fareDataKey,
            "showTime": "ALL",
            "channel": "App",
            "fareDataKeyElectMode": "0",
            "valid": "true",
            "carrier": carrier,
            "compareOrderCut": "false",
            "tag": newData["data"]["tag"],
            "airline": "ALL-ALL",
            "agentCarrierAuthType": newData["data"]["agentCarrierAuthType"],
            "remarks": remarks,
            "config_edit_version": config_edit_version
    }

    # 警告信息
    warn_message = newData["data"].get("warn_message", "")
    if warn_message and warn_message.strip():
        result_obj["warn_message"] = warn_message.strip()
    
    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }


def process_qconfig_content(param: Dict[str, Any], analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理qconfig配置内容，选择最优tag
    
    参数:
    param: Dict[str, Any] - 请求参数
    analysis_data: Dict[str, Any] - 分析数据
    mapping_data: Dict[str, Any] - 映射数据
    
    返回:
    Dict[str, Any] - 包含选择的最优tag的结果
    """
    # 步骤一和二：GET请求获取当前配置
    try:
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': 'unified_tag_config.t',
            'env': param['env'],
            "subenv": param.get('subenv', ''),
            'targetgroupid': 'f_twell_rt_server'
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        error = handle_request_exception(e)
        return {
            "status": "error",
            "message": error.message,
            "data": None
        }

    # 步骤三：处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": "error",
            "message": get_result.get('message', '获取配置失败'),
            "data": None
        }

    # 配置文件编辑版本
    config_edit_version = get_result['data'].get('editVersion', '')
    config_data = get_result['data'].get('data', '')
    config_list = json.loads(config_data) if config_data else []

    # 航司
    carrier = mapping_data.get("carrier", "")
    # 根据航司设置agentCarrierAuthType
    agentCarrierAuthType = "54" if carrier == "MF" else "8"
    
    # 提取tag列表并选择最优tag
    extracted_tags = extract_tags(analysis_data)
    tag_data = select_best_tag(extracted_tags, config_list, carrier, agentCarrierAuthType)
    
    # 找到最大的row值并加1
    max_row = 0
    for item in config_list:
        try:
            current_row = int(item.get('row', '0'))
            max_row = max(max_row, current_row)
        except (ValueError, TypeError):
            continue
    
    rowKey = str(max_row + 1)
    
    return {
        "status": "success",
        "message": "操作成功",
        "data": {
            "tag": tag_data["tag"],
            "agentCarrierAuthType": agentCarrierAuthType,
            "rowKey": rowKey,
            "config_edit_version": config_edit_version,
            "warn_message": tag_data.get("warn_message", "")
        }
    }


def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    

def process_age_ranges(age_ranges: List[str]) -> str:
    """
    处理年龄范围，返回对应的年龄类型标签
    
    参数:
    age_ranges: List[str] - 年龄范围列表，如 ["18-24", "55-100"]
    
    返回:
    str - 年龄类型标签: "AGE"(青年), "OLD"(老年), "SOA"(青老年)
    """
    has_young = False
    has_old = False
    
    # 定义青年和老年年龄范围
    # 青年：12-28（含）岁
    YOUNG_MIN = 12
    YOUNG_MAX = 28
    # 老年：50岁（含）以上
    OLD_MIN = 50
    
    for age_range in age_ranges:
        try:
            # 检查格式是否正确
            parts = age_range.split('-')
            if len(parts) != 2:
                continue
                
            start_age_str, end_age_str = parts
            
            # 检查是否为空字符串
            if not start_age_str.strip() or not end_age_str.strip():
                continue
                
            # 转换为整数
            start_age = int(start_age_str)
            end_age = int(end_age_str)
            
            # 检查年龄范围是否合理
            if start_age > end_age or start_age < 0 or end_age > 150:
                continue
            
            # 判断是否在青年年龄范围内
            # 青年：12-28（含）岁
            if (start_age >= YOUNG_MIN and end_age <= YOUNG_MAX):
                has_young = True
                
            # 判断是否与老年年龄范围重叠
            # 老年：50岁（含）以上
            if start_age >= OLD_MIN:
                has_old = True
                
        except (ValueError, AttributeError):
            continue
    
    if has_young and has_old:
        return "SOA"
    elif has_young:
        return "AGE"
    elif has_old:
        return "OLD"
    return ""

def extract_tags(analysis_data: Dict[str, Any]) -> List[str]:
    """
    根据限制性条件提取tag列表
    
    参数:
    analysis_data: Dict[str, Any] - 分析数据字典
    
    返回:
    List[str] - 提取的tag列表
    """
    tags = []
    
    # 性别
    gender_restriction = analysis_data.get("gender_restriction", "")
    # 回乡证
    document_restrictions = analysis_data.get("document_restrictions", "")
    # 身份证开头    
    regional_limit = analysis_data.get("regional_limit", "")

    # CZB：画像类限制 + 性别 + 回乡证 + 身份证开头
    if (analysis_data.get("customer_limit") or 
        gender_restriction in ["男", "女"] or 
        "2" in document_restrictions or 
        regional_limit):
        tags.append("CZB")

    # STU：学生限制（优先级高于年龄限制）
    if analysis_data.get("student_verification") == "true":
        tags.append("STU")
    else:
        # AGE/OLD/SOA：年龄限制（仅在无STU时判断）
        age_ranges_str = analysis_data.get("age_limit", "")
        age_ranges = []
        if age_ranges_str:
            # 处理字符串格式的年龄范围，可能是逗号分隔的多个区间
            if isinstance(age_ranges_str, str):
                age_ranges = [age_range.strip() for age_range in age_ranges_str.split(",") if age_range.strip()]
            elif isinstance(age_ranges_str, list):
                age_ranges = age_ranges_str
        
        if age_ranges:
            age_tag = process_age_ranges(age_ranges)
            if age_tag:
                tags.append(age_tag)
    
    # GST/QHY：会员限制
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit in ["新老会员", "老会员"]:
        tags.append("GST")
    elif membership_limit == "新会员":
        tags.append("QHY")
    
    # MUX,SLA,MUJ：溢价相关
    if analysis_data.get("equity_related") == "true" or analysis_data.get("remium_products") == "true":
        tags.extend(["MUX", "SLA", "MUJ"])
    
    # HUJ：家庭限制（优先级高于小团限制）
    if analysis_data.get("family_limit") == "true":
        tags.append("HUJ")
    else:
        # SLP：小团限制（仅在无HUJ时判断）
        if analysis_data.get("capacity_limit"):
            tags.append("SLP")
    
    # HUG：公务舱会员限制
    if ("会员" in membership_limit) and "公务舱" in analysis_data.get("cabin_class", ""):
        tags.append("HUG")

    # 次卡或权益卡，采用TPQ
    if analysis_data.get("card_limit") == "true":
        tags.append("TPQ")
    
    return tags

def select_best_tag(extracted_tags: List[str], config_list: List[Dict[str, Any]], carrier: str, agent_carrier_auth_type: str) -> Dict[str, Any]:
    """
    从提取的tag列表中选择最优tag
    公务舱会员 > 溢价 > 新老会员 > 画像 > 家庭 > 青老年/学生/小团
    HUG > MUX,SLA,MUJ > QHY,GST > CZB > HUJ > AGE,OLD,SOA,STU,SLP
    
    参数:
    extracted_tags: List[str] - 提取的tag列表
    config_list: List[Dict[str, Any]] - 现有配置列表
    carrier: str - 航司
    agent_carrier_auth_type: str - 代理航司认证类型
    
    返回:
    Dict[str, Any] - 包含选择的最优tag的结果
    """
    # 定义tag优先级
    TAG_PRIORITY = {
        "TPQ": 1,      # 权益卡或次卡
        "HUG": 1,      # 公务舱会员
        "MUX": 2,      # 溢价相关
        "SLA": 2,
        "MUJ": 2,
        "CZB": 3,      # 画像类限制
        "QHY": 4,      # 会员限制
        "GST": 4,
        "HUJ": 5,      # 家庭限制
        "AGE": 6,      # 青老年/学生/小团
        "OLD": 6,
        "SOA": 6,
        "STU": 6,
        "SLP": 6,
        "HUL": 7,      # 模糊tag
        "ZHT": 7,
        "CZA": 8       # 兜底tag
    }
    
    # 检查tag是否被占用
    def is_tag_occupied(tag: str) -> bool:
        for item in config_list:
            # 添加默认值处理,防止 KeyError
            config = item.get('columns', {})
            if (config.get("carrier") == carrier and 
                config.get("agentCarrierAuthType") == agent_carrier_auth_type and 
                config.get("tag") == tag):
                return True
        return False

    # 如果没有限制性tag，直接返回CZA
    if not extracted_tags:
        return {
            "tag": "CZA"
        }
    
    # 对提取的tag按优先级排序
    sorted_tags = sorted(extracted_tags, key=lambda x: TAG_PRIORITY.get(x, 999))

    # 权益卡或次卡，强出TPQ
    if "TPQ" in extracted_tags:
        return {
            "tag": "TPQ"
        }
    
    # 首先检查HUG是否在提取的tag中
    if "HUG" in extracted_tags:
        return {
            "tag": "HUG"
        }
    
    # 处理溢价相关tag的特殊情况
    premium_tags = ["MUX", "SLA", "MUJ"]
    premium_in_tags = [tag for tag in premium_tags if tag in extracted_tags]
    
    if premium_in_tags:
        # 如果存在溢价tag，尝试按顺序使用未被占用的溢价tag
        for tag in premium_in_tags:
            if not is_tag_occupied(tag):
                return {
                    "tag": tag
                }
        # 溢价类tag均被占用时，使用SLA兜底
        return {
            "tag": "SLA",
            "warn_message": "所有溢价类tag均被占用，采用SLA进行兜底"
        }

    # 返回优先级最高的限制性tag
    return {
        "tag": sorted_tags[0]
    }
