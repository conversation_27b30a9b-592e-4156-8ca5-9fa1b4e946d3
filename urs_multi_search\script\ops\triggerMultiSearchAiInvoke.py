import pandas as pd
import requests
import json
import time
from typing import List, Dict, Any


def read_excel_file(file_path: str) -> List[Dict[str, Any]]:
    """
    读取Excel文件并返回数据数组

    Args:
        file_path: Excel文件路径

    Returns:
        包含Excel数据的数组，每行数据为一个字典
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 确保必要的列存在
        required_columns = ["用户名", "uniqKey", "urs-日期"]
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"Excel文件必须包含以下列: {required_columns}")

        # 将DataFrame转换为字典列表
        data_list = df.to_dict("records")
        return data_list
    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        return []


def call_http_api(url: str, user_name: str, uniq_key: str) -> bool:
    """
    调用HTTP接口

    Args:
        url: 请求URL
        user_name: 用户名
        uniq_key: 唯一键
        urs_date: URS日期

    Returns:
        是否调用成功
    """
    try:
        # 构建请求数据
        payload = {"username": user_name, "uniqKey": uniq_key}

        # 发送POST请求
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, json=payload, headers=headers)

        # 检查响应状态
        if response.status_code == 200:
            print(f"成功调用接口: {user_name}")
            return True
        else:
            print(f"调用接口失败: {user_name}, 状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"调用接口时发生错误: {str(e)}")
        return False


def main():
    # Excel文件路径
    date_str = """dcbysmk6039	5c6b2957-6f60-4399-8481-0febcb1c957b		54f8eff6-4b33-40b5-97a4-10d809e0ae27
dcbysmk6039	c797e728-7016-47d2-a1e8-b898b91b1af6		54f8eff6-4b33-40b5-97a4-10d809e0ae27"""

    # API URL
    api_url = "https://hf7l9aiqzx.feishu.cn/base/workflow/webhook/event/UxZNaOXnZwd8aUhXUaWcEfEgnqc"  # 请根据实际情况修改API URL

    # 用于存储已处理过的组合
    processed_combinations = set()

    param_list = date_str.split("\n")
    for param in param_list:
        if not param.strip():  # 跳过空行
            continue

        param = param.split("\t")
        user_name = param[0]
        uniq_key = param[3]

        # 创建组合键
        combination = f"{user_name}_{uniq_key}"

        # 检查是否已经处理过
        if combination in processed_combinations:
            print(f"跳过重复数据: {user_name} - {uniq_key}")
            continue

        success = call_http_api(api_url, user_name, uniq_key)
        if success:
            # 只有在成功调用后才添加到已处理集合
            processed_combinations.add(combination)
        else:
            print(f"处理数据失败: {user_name}")


if __name__ == "__main__":
    main()
