#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import cv2
import numpy as np
from pathlib import Path
import argparse
import requests
from io import BytesIO
from PIL import Image

# 定义敏感信息的正则表达式模式
PATTERNS = {
    '电话': [r'电话[：:]\s*(\d+)', r'联系电话[：:]\s*(\d+)', r'手机[：:]\s*(\d+)', r'联系方式[：:]\s*(\d+)', r'\d{11}'],
    '身份证号码': [r'([0-9]{17}[0-9X])', r'([0-9]{15})', r'身份证[：:]\s*([0-9X]{15,18})']
}

def download_image(url):
    """
    从URL下载图片并返回图片流
    
    参数:
        url: 图片URL地址
    
    返回:
        image: OpenCV格式的图片
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        image = Image.open(BytesIO(response.content))
        return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"下载图片失败: {e}")
        return None

def apply_mosaic(image, box, mosaic_size=8):
    """
    对图像的指定区域应用马赛克效果
    
    参数:
        image: 原始图像
        box: 要应用马赛克的区域 [x1, y1, x2, y2]
        mosaic_size: 马赛克块的大小（值越小，马赛克效果越明显）
    
    返回:
        处理后的图像
    """
    x1, y1, x2, y2 = box
    
    # 添加一些padding，使马赛克区域更大
    padding = 10
    height, width = image.shape[:2]
    
    # 确保坐标在图像范围内
    x1 = max(0, x1 - padding)
    y1 = max(0, y1 - padding)
    x2 = min(width, x2 + padding)
    y2 = min(height, y2 + padding)
    
    print(f"Original box: {box}")
    print(f"Adjusted box with padding: [{x1}, {y1}, {x2}, {y2}]")
    print(f"Image dimensions: {width}x{height}")
    
    # 提取区域
    region = image[y1:y2, x1:x2].copy()
    
    # 应用马赛克效果
    h, w = region.shape[:2]
    
    print(f"Region dimensions: {w}x{h}")
    
    # 如果区域太小，直接返回
    if h <= 0 or w <= 0:
        print("Region too small, skipping mosaic")
        return image
    
    # 缩小然后放大以创建马赛克效果
    # 使用更小的mosaic_size值来创建更明显的马赛克效果
    mosaic_h = max(1, h // mosaic_size)
    mosaic_w = max(1, w // (mosaic_size * 2))  # 宽度缩小1/4
    
    print(f"Mosaic dimensions: {mosaic_w}x{mosaic_h}")
    
    # 缩小
    small = cv2.resize(region, (mosaic_w, mosaic_h), interpolation=cv2.INTER_LINEAR)
    # 放大
    mosaic = cv2.resize(small, (w, h), interpolation=cv2.INTER_NEAREST)
    
    # 将马赛克区域放回原图
    result = image.copy()
    result[y1:y2, x1:x2] = mosaic
    
    # 验证马赛克是否被应用
    diff = np.sum(np.abs(result[y1:y2, x1:x2] - image[y1:y2, x1:x2]))
    print(f"Difference between original and mosaic region: {diff}")
    
    return result

def process_image(image_url, ocr_data, output_path):
    """
    处理单个图片URL，下载图片，进行OCR识别，并处理敏感信息
    
    参数:
        image_url: 图片URL地址
        ocr_data: OCR识别结果
        output_path: 输出文件路径
    """
    # 下载图片
    image = download_image(image_url)
    if image is None:
        print(f"无法下载图片: {image_url}")
        return False
    
    print(f"Processing image with shape: {image.shape}")
    print(f"Image type: {type(image)}")
    print(f"Image dtype: {image.dtype}")
    
    # 提取OCR结果
    ocr_results = []
    try:
        if "data" in ocr_data and "res" in ocr_data["data"]:
            for item in ocr_data["data"]["res"]:
                if "ocr_result" in item:
                    ocr_results.extend(item["ocr_result"])
            print(f"Found {len(ocr_results)} OCR results")
        else:
            print(f"OCR数据结构不符合预期")
            return False
    except Exception as e:
        print(f"处理OCR数据错误: {e}")
        return False
    
    # 找出敏感信息
    sensitive_boxes = []
    
    for item in ocr_results:
        text = item.get("text", "")
        box = item.get("box", [])
        
        # 检查是否包含敏感信息
        is_sensitive = False
        
        # 检查电话
        for pattern in PATTERNS['电话']:
            if re.search(pattern, text):
                is_sensitive = True
                print(f"找到电话: {text}")
                break
        
        # 检查身份证号码
        for pattern in PATTERNS['身份证号码']:
            match = re.search(pattern, text)
            if match:
                # 如果文本中包含"身份证"，只打码身份证号码部分
                if "身份证" in text and match.group(1):
                    # 获取匹配的身份证号码的位置
                    start_idx = match.start(1)
                    end_idx = match.end(1)
                    
                    # 计算身份证号码在原始文本中的比例
                    if len(text) > 0:
                        id_ratio = (end_idx - start_idx) / len(text)
                        
                        # 根据比例计算身份证号码在box中的位置
                        if len(box) == 4:
                            x1, y1, x2, y2 = box
                            width = x2 - x1
                            
                            # 调整box只包含身份证号码部分
                            adjusted_x1 = int(x1 + start_idx / len(text) * width)
                            adjusted_x2 = int(x1 + end_idx / len(text) * width)
                            
                            # 添加调整后的box
                            sensitive_boxes.append([adjusted_x1, y1, adjusted_x2, y2])
                            print(f"找到身份证号码: {match.group(1)}，只打码号码部分")
                            continue  # 跳过下面的代码，不添加整个box
                
                # 如果不是上面的特殊情况，标记整个文本为敏感
                is_sensitive = True
                print(f"找到身份证号码: {text}")
                break
        
        # 如果是敏感信息，记录其位置
        if is_sensitive and len(box) == 4:
            # box格式为 [x1, y1, x2, y2]
            sensitive_boxes.append(box)
    
    print(f"Found {len(sensitive_boxes)} sensitive areas to blur")
    
    # 对敏感区域进行马赛克处理
    for box in sensitive_boxes:
        print(f"Applying mosaic to box: {box}")
        image = apply_mosaic(image, box)
    
    # 保存处理后的图片
    try:
        success = cv2.imwrite(output_path, image)
        if success:
            print(f"已处理并保存图片: {output_path}")
        else:
            print(f"保存图片失败: {output_path}")
        return success
    except Exception as e:
        print(f"保存图片时发生错误: {e}")
        return False

def main(image_url, ocr_data):
    """
    主函数
    
    参数:
        image_url: 图片URL地址
        ocr_data: OCR识别结果
    """
    # 设置输出目录
    output_dir = Path('/Users/<USER>/Desktop/qnideawork/urs_script/refund/script/output')
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件名
    output_filename = f"processed_2.jpg"
    output_path = output_dir / output_filename
    
    # 处理图片
    success = process_image(image_url, ocr_data, output_path)
    
    if not success:
        print("处理图片失败")
        exit(1)
    
    return {"status": "success", "output_path": str(output_path)}

if __name__ == "__main__":
    # 测试数据
    test_url = "https://fuwu.qunar.com/orderview/upload/queryFile/VYVYGHgN1yA828T4gQ8qiInT8VmRUglkBwQ39m7__ZDV_J_3c1f7cZdmaWIgbA5U5pqG_t4y6BPOo9pM-_iEqkf-Xff3uzBq8MiCnevDO_d5m4rCJQI1UCHDXGSJF0-Y17sSuABD4pOsUxwPpYR8Lx7IDbifACrglfbG12-T8Jp0au9uQTbDRV4OtmZ6ZMT1c-4RtRTCgZN6eCw6AU5fGp975WG_kmS1AofIe4Sc-nc.jpg"
    test_ocr_data = {
    "status": 0,
    "message": "",
    "data": {
        "msg": "screen shot ocr调用成功",
        "res": [
            {
                "top_key": "",
                "img_status": 0,
                "img_id": "0",
                "ocr_result": [
                    {
                        "line": 0,
                        "box": [
                            463,
                            80,
                            789,
                            111
                        ],
                        "text": "成都锦欣沙河堡医院"
                    },
                    {
                        "line": 1,
                        "box": [
                            509,
                            128,
                            747,
                            162
                        ],
                        "text": "病情诊断证明书"
                    },
                    {
                        "line": 2,
                        "box": [
                            48,
                            165,
                            133,
                            206
                        ],
                        "text": "姓名:"
                    },
                    {
                        "line": 3,
                        "box": [
                            135,
                            165,
                            273,
                            204
                        ],
                        "text": "陈思羽"
                    },
                    {
                        "line": 4,
                        "box": [
                            285,
                            166,
                            369,
                            204
                        ],
                        "text": "性别："
                    },
                    {
                        "line": 5,
                        "box": [
                            713,
                            166,
                            797,
                            204
                        ],
                        "text": "科室："
                    },
                    {
                        "line": 6,
                        "box": [
                            384,
                            167,
                            421,
                            200
                        ],
                        "text": "女"
                    },
                    {
                        "line": 7,
                        "box": [
                            471,
                            168,
                            553,
                            202
                        ],
                        "text": "年龄:"
                    },
                    {
                        "line": 8,
                        "box": [
                            814,
                            168,
                            913,
                            205
                        ],
                        "text": "口腔科"
                    },
                    {
                        "line": 9,
                        "box": [
                            566,
                            169,
                            636,
                            201
                        ],
                        "text": "26岁"
                    },
                    {
                        "line": 10,
                        "box": [
                            48,
                            202,
                            191,
                            243
                        ],
                        "text": "身份证号："
                    },
                    {
                        "line": 11,
                        "box": [
                            205,
                            208,
                            464,
                            238
                        ],
                        "text": "510524199810301067"
                    },
                    {
                        "line": 12,
                        "box": [
                            45,
                            244,
                            190,
                            284
                        ],
                        "text": "主要病情"
                    },
                    {
                        "line": 13,
                        "box": [
                            194,
                            249,
                            289,
                            281
                        ],
                        "text": "牙龈炎"
                    },
                    {
                        "line": 14,
                        "box": [
                            41,
                            289,
                            287,
                            331
                        ],
                        "text": "临床诊断:牙龈炎"
                    },
                    {
                        "line": 15,
                        "box": [
                            407,
                            336,
                            520,
                            380
                        ],
                        "text": "拔智齿"
                    },
                    {
                        "line": 16,
                        "box": [
                            40,
                            341,
                            399,
                            380
                        ],
                        "text": "处置建议:冠周冲洗上药、"
                    },
                    {
                        "line": 17,
                        "box": [
                            848,
                            485,
                            1006,
                            528
                        ],
                        "text": "医生签名"
                    }
                ],
                "top_text": []
            }
        ],
        "status": 0
    }
}
    
    result = main(test_url, test_ocr_data)
    print(json.dumps(result, ensure_ascii=False, indent=2))

