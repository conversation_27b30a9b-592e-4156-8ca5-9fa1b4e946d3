from datetime import datetime, date, timedelta
import json
import traceback
from typing import Union, Optional
from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from string import Formatter
import re
from typing import Union


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d:%H:%M",  # 新增格式支持 YYYY-MM-DD:HH:mm
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError, Exception) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        # print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def parseReasonAndMergeData(param: dict) -> tuple:
    """
    Parse and merge reason data from platReasonData and mayPlatReasonData

    Args:
        param (dict): Dictionary containing ursDate, userSqlId, platReasonData, and mayPlatReasonData

    Returns:
        tuple: (merged_data, error_info)
            - merged_data: List of merged records
            - error_info: Dictionary containing status and message
    """
    # Parse platReasonData
    plat_data, plat_error = parse_urlencoded_structured_data(param, "platReasonData")

    # Parse mayPlatReasonData
    may_plat_data, may_plat_error = parse_urlencoded_structured_data(
        param, "mayPlatReasonData"
    )

    # Check if both parsing failed
    if plat_data is None and may_plat_data is None:
        return None, {
            "status": "error",
            "message": f"Both data sources failed to parse. Plat error: {plat_error['message']}, May Plat error: {may_plat_error['message']}",
        }

    # Initialize merged data with plat data if available
    merged_data = []
    if plat_data:
        merged_data = plat_data.copy()

    # Merge may_plat_data if available
    if may_plat_data:
        # Create a set of existing userNames from plat_data
        existing_users = {record.get("userName") for record in merged_data}

        # Add records from may_plat_data that don't have duplicate userNames
        for record in may_plat_data:
            if record.get("userName") not in existing_users:
                merged_data.append(record)

    return merged_data, {"status": "success"}


def main(param):
    try:
        # Parse and merge reason data
        reason_data, reason_error = parseReasonAndMergeData(param)
        if reason_error["status"] == "error":
            return {
                "errMsg": reason_error["message"],
                "statisticData": {},
            }

        statisticData = statisticByReasonPriority(reason_data)
        # Return success response with merged data
        return {"errMsg": "", "statisticData": statisticData}
    except Exception as e:
        # Handle any unexpected errors
        stack_trace = traceback.format_exc()
        return {
            "errMsg": f"Unexpected error occurred: {stack_trace}",
            "statisticData": {},
        }


def statisticByReasonPriority(reason_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze reason data based on priority order and count occurrences of each reason type.

    Args:
        reason_data (List[Dict[str, Any]]): List of reason data records

    Returns:
        Dict[str, Any]: Statistics of reason occurrences
    """
    # Define reason priority mapping
    REASON_PRIORITY = {
        "代理或政策变化": "agencyOrPolicyAttr",
        "次低价导致追价金额变化": "secondLowestPriceAttr",
        "追价金额变化": "autoPriceDecreaseAttr",
        "底价变化-其他": "policyPriceAttr",
        "平台报价膨胀金金额不一致": "expansionAmountAttr",
        "营销金额变化": "activityAmountAttr",
        "代金券变化": "bonusAmountAttr",
        "展示价变化-其他": "showPriceOtherAttr",
    }

    # Initialize result dictionary with default values
    result = {
        "ursDate": "",
        "userSqlId": "",
        "totalCount": 0,
        "agencyOrPolicyAttr": 0,
        "secondLowestPriceAttr": 0,
        "autoPriceDecreaseAttr": 0,
        "policyPriceAttr": 0,
        "expansionAmountAttr": 0,
        "activityAmountAttr": 0,
        "bonusAmountAttr": 0,
        "showPriceOtherAttr": 0,
    }

    if not reason_data:
        return result

    # Get first record to extract common fields
    first_record = reason_data[0]
    result["ursDate"] = first_record.get("ursDate", "")
    result["userSqlId"] = first_record.get("userSqlId", "")

    for record in reason_data:
        # Parse multiSearchSecReason from the record
        reasons = safe_json_parse(record.get("multiSearchSecReason"), [])
        if not isinstance(reasons, list):
            continue

        # Find the highest priority reason that exists in the record's reasons
        selected_reason = None
        for priority_reason in REASON_PRIORITY.keys():
            if priority_reason in reasons:
                selected_reason = priority_reason
                break

        # Increment the count for the selected reason
        if selected_reason:
            result[REASON_PRIORITY[selected_reason]] += 1
            result["totalCount"] += 1

    return result
