# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
from dataclasses import dataclass
import datetime


# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }
    
    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }
    
    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 航司
    carrier = mapping_data.get("carrier", "")
    if not carrier:
        return {
            "status": "error",
            "message": "carrier 不能为空",
            "data": None
        }
    
    # product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    # 域名
    domain = mapping_data.get("domain", "")
    if not domain:
        return {
            "status": "error",
            "message": "domain 不能为空",
            "data": None
        }

    # 调用识别黑儿童场景的方法
    result_obj = identify_black_child_scenario(analysis_data, carrier, product_mark, domain)
    
    # 如果没有满足任何场景，则不返回结果
    if not result_obj:
        return {
            "status": "ignore",
            "message": "未满足任何黑儿童场景",
            "data": None
        }
    
    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }

def identify_black_child_scenario(analysis_data: Dict[str, Any], carrier: str, product_mark: str, domain: str) -> Optional[Dict[str, Any]]:
    """
    识别黑儿童场景并返回对应的配置
    
    参数:
    analysis_data: Dict[str, Any] - 分析数据
    carrier: str - 航司
    product_mark: str - 产品标记
    domain: str - 域名
    
    返回:
    Optional[Dict[str, Any]] - 如果满足任何场景则返回配置，否则返回None
    """
    # 检查年龄限制，则不用配置黑儿童
    if analysis_data.get("age_limit"):
        return None

    # 获取当前日期作为operator的一部分
    current_date = datetime.datetime.now().strftime("%Y.%m.%d")
    operator = f"{current_date}AI工具"
    
    # 构建基础结果对象
    result_obj = {
        "carrier": carrier,
        "mark": product_mark,
        "domain": domain,
        "cabin": "ALL",
        "airline": "ALL-ALL",
        "childSource": "",
        "searchType": "ALL",
        "remark": "",
        "operator": operator
    }
    
    # 场景1: 溢价产品
    if (analysis_data.get("equity_related") == "true" or 
        analysis_data.get("remium_products") == "true"):
        if analysis_data.get("child_applicable") != "false":
            result_obj["childSource"] = "1,3,4,11,12,13,14,15,16,17,18,19,20,5,6,7,9,10,21,22,23,24"
            result_obj["remark"] = "溢价产品不适用儿童"
            return result_obj
    
    # 场景2: 不适用于儿童
    if analysis_data.get("child_applicable") == "false":
        product_label = analysis_data.get("product_label", "")
        result_obj["childSource"] = "1,2,3,4,8,11,12,13,14,15,16,17,18,19,20"
        result_obj["remark"] = f"{product_label}不适用儿童"
        return result_obj
    
    # 场景3: 会员产品
    if "会员" in str(analysis_data.get("membership_limit", "")):
        result_obj["childSource"] = "1,2,3,4,8,11,12,13,14,15,16,17,18,19,20"
        result_obj["remark"] = "会员产品不适用儿童"
        return result_obj
    
    # 未满足任何场景，返回None
    return None

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    
