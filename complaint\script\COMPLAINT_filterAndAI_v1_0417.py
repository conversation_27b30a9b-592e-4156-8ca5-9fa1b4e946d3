from collections import defaultdict
from urllib.parse import unquote_to_bytes,quote
import json
from typing import Tuple, Optional, List, Dict, Any, Union
from datetime import datetime,date
from string import Formatter
import re
from typing import Union
import uuid


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:    
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False



# 需实现的提示生成函数（示例）
def genPriceComparePrompt(
    pre: Dict, sur: Dict, compare_type: str, templatePrompt: str
) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    preData = json.dumps(pre, ensure_ascii=False, indent=None)  # 紧凑格式
    surData = json.dumps(sur, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "preData": preData,
        "surData": surData,
        "compareType": compare_type,
    }

    try:
        formatter = Formatter()
        required_fields = [fn for _, fn, _, _ in formatter.parse(templatePrompt) if fn]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return templatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def genPrompt(soarTemplatePrompt, priceData) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    param = json.dumps(priceData, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "param": param,
    }

    try:
        formatter = Formatter()
        required_fields = [
            fn for _, fn, _, _ in formatter.parse(soarTemplatePrompt) if fn
        ]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return soarTemplatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def process_passenger_data(data: List[Dict], order_no: str, uniqkey: str, compare_prompt: str) -> Dict:
    """
    处理乘客数据并返回指定格式的结果
    
    参数:
    data: List[Dict] - 乘客数据列表
    order_no: str - 订单号
    unique_key: str - 唯一键
    compare_prompt: str - 比较提示
    
    返回:
    Dict - 处理后的结果
    """
    result = {
        "orderNo": order_no,
        "uniqKey": uniqkey,
        "needExecAl": "是",
        "notExecAlReason": "",
        "comparePrompt": compare_prompt,
        "userInfo": [],
        "airlineInfo": []
    }
    
    # 遍历数据
    for item in data:
        passenger_name = item.get("passengerName", "")
        view_price = item.get("viewPrice", "")
        airline_view_price = item.get("airLineViewPrice", "")
        
        # 构建用户信息字符串
        user_info = f"{passenger_name}，支付票面价：{view_price}"
        result["userInfo"].append(user_info)
        
        # 构建航司信息字符串
        airline_info = f"{passenger_name}，航司票面价：{airline_view_price}"
        result["airlineInfo"].append(airline_info)
        
        # 检查是否需要执行AL
        if item.get("match") != "是":
            result["needExecAl"] = "否"
            result["notExecAlReason"] = f"乘机人归因信息不全"
    
    # 将列表转换为分号分隔的字符串
    result["userInfo"] = "；".join(result["userInfo"])
    result["airlineInfo"] = "；".join(result["airlineInfo"])
    
    return result

def main(param: dict) -> dict:
    try:
        # 解析入参
        data, parseStatus = parse_urlencoded_structured_data(param, "param")
        if parseStatus["status"] != "success":
            return {
                "status": 404,
                "errMsg": parseStatus["message"],
                "needExecAl": "否",
                "notExecAlReason": "数据解析失败"
            }

        # 获取模板和必要参数
        oneToOnetemplatePrompt = param.get("oneToOnetemplatePrompt")
        orderNo = param.get("orderNo")
        uniqKey = param.get("uniqKey")

        if not all([oneToOnetemplatePrompt, orderNo, uniqKey]):
            return {
                "status": 404,
                "errMsg": "缺少必要参数",
                "needExecAl": "否",
                "notExecAlReason": "缺少必要参数"
            }

        # 处理数据并生成提示词
        compare_prompt = genPrompt(oneToOnetemplatePrompt, data)

        # 处理乘客数据
        result = process_passenger_data(data, orderNo, uniqKey, compare_prompt)
        
        return {
            "status": 200,
            "errMsg": "",
            "result": result
        }

    except Exception as e:
        return {
            "status": 404,
            "errMsg": f"处理异常: {str(e)}",
            "needExecAl": "否",
            "notExecAlReason": "处理过程发生异常"
        }

# 测试代码
if __name__ == "__main__":
    # 测试数据
    test_data = {
        "param": "fuelTaxFee:20#*#constructionFee:20#*#refundXcdAmount:0#*#airLineFuelTaxFee:0#*#youhuiPrice:100#*#priceTypeName:成人#*#passengerName:李四#*#orderNo:123#*#airLineConstructionFee:20#*#viewPrice:109#*#uniqKey:123#*#notMatchReason:#*#match:是#*#airLineViewPrice:100~~*~~fuelTaxFee:20#*#constructionFee:20#*#refundXcdAmount:0#*#airLineFuelTaxFee:0#*#youhuiPrice:100#*#priceTypeName:成人#*#passengerName:张三#*#orderNo:123#*#airLineConstructionFee:20#*#viewPrice:109#*#uniqKey:123#*#notMatchReason:#*#match:否#*#airLineViewPrice:100~~*~~",
        "oneToOnetemplatePrompt": "对比数据：{param}",
        "orderNo": "TEST123",
        "uniqKey": "UNIQUE123"
    }

    # 运行测试
    result = main(test_data)
    print("测试结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
