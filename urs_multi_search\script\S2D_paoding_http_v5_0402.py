import json
import requests
from typing import Union, List, Set, Optional, Dict, Any, Tuple
from requests.exceptions import RequestException
from datetime import datetime
from urllib.parse import unquote_to_bytes
from urllib.parse import quote
import numbers


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict, fields: List, date_format: str = None, case_sensitive: bool = False
) -> Set[Union[str, datetime.date]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt.date() if isinstance(dt, datetime) else dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def filter_flight_data_and_merge(
    flightNumber: str,
    ota_data: List[dict],
    list_data: List[dict]
):
    """
    增强版多条件过滤（支持全量多值参数）
    """
    if ota_data is None:
        ota_data = []
    if list_data is None:
        list_data = []
        
    # 初始化结果集
    matched_ota = []
    matched_list = []
    notMatched = []

    # 需要过滤的特定tag列表
    filtered_tags = {
        "TOU1",
        "FXD1",
        "PTJ1",
        "WYY1",
        "SLN1",
        "SLP1",
        "CZB1",
        "SLD1",
        "SLG1",
        "BYJ1",
        "GPP1",
        "STU1",
        "YHU1",
        "CLP1",
        "LHO1",
        "LHS1",
        "LHN1",
        "LHO1",
        "LHT1",
        "LHX1",
        "LHM1",
        "LHE1",
        "HUJ1",
        "JTP1",
        "SOA1",
        "AGE1",
        "OLD1",
        "OKR1",
        "OKK1",
        "HUB1",
        "ZZM1",
        "HUL1",
        "XFQ1",
        "XFL1",
        "XFZ1",
        "XFA1",
        "XLE1",
        "QHY1",
        "GSX1",
        "HET1",
        "LNF1",
        "JCY1",
        "JCB1",
        "ZHZ1",
        "KFW1",
        "HYC1",
        "NQX1",
        "XFF1",
        "TTD1",
        "SLB1",
    }


    # 处理OTA数据
    for item in ota_data:
        # 检查是否为OTA数据且tag在过滤列表中
        if item.get("tSource") == "ota" and item.get("tag") in filtered_tags:
            item.update({
                "matchQuestion": "不匹配",
                "desc": f"OTA数据tag {item.get('tag')} 在过滤列表中",
            })
            notMatched.append(item)
            continue   
        # 标记为匹配
        item.update({
            "matchQuestion": "匹配",
            "desc": "",
        })
        matched_ota.append(item)
    
    # 处理LIST数据
    for item in list_data:
        
        # 检查航班号是否匹配
        item_flight_no = item.get("flightNo", "")
        if flightNumber and item_flight_no != flightNumber:
            item.update({
                "matchQuestion": "不匹配",
                "desc": f"航班号不匹配 (预期: {flightNumber}, 实际: {item_flight_no})",
            })
            notMatched.append(item)
            continue
        
        # 都匹配，标记为匹配
        item.update({
            "matchQuestion": "匹配",
            "desc": "",
        })
        matched_list.append(item)

    # 检查两个集合是否都不为空
    if not matched_ota or not matched_list:
        # 如果任一集合为空，将另一个集合中的所有项移动到notMatched
        if not matched_ota and matched_list:
            for item in matched_list:
                item.update({
                    "matchQuestion": "不匹配",
                    "desc": "无可用OTA价格数据进行对比",
                })
                notMatched.append(item)
            matched_list = []
        elif matched_ota and not matched_list:
            for item in matched_ota:
                item.update({
                    "matchQuestion": "不匹配",
                    "desc": "无可用LIST价格数据进行对比",
                })
                notMatched.append(item)
            matched_ota = []
    
    # 合并匹配数据
    matched = matched_ota + matched_list
    
    return matched, notMatched


def _check_price(item: dict, priceInfoParam: dict) -> tuple[bool, str]:
    """
    检查航班价格是否在指定的价格范围内
    Args:
        item: 航班信息字典
        priceInfoParam: 价格信息参数，包含 precisePriceList 和 durationPriceList
    Returns:
        tuple[bool, str]: (是否匹配, 原因)
    """
    if not priceInfoParam:
        return False, "用户问题中未指定价格信息"

    flight_price = item.get("price")
    if not flight_price:
        return False, "航班无价格信息"

    # 检查精确价格列表
    precise_prices = priceInfoParam.get("precisePriceList", [])
    for price_info in precise_prices:
        if price_info.get("price") == flight_price:
            return True, f"价格匹配精确价格: {flight_price}"

    # 检查区间价格列表
    duration_prices = priceInfoParam.get("durationPriceList", [])
    for price_info in duration_prices:
        left_price = price_info.get("leftPrice")
        right_price = price_info.get("rightPrice")
        if left_price is not None and right_price is not None:
            if left_price <= flight_price <= right_price:
                return True, f"价格在区间内: {left_price}-{right_price}"

    return False, f"价格 {flight_price} 不在任何指定范围内"


def _check_dates(item: dict, search_dates: set, depart_dates: set) -> tuple:
    """日期检查（返回匹配状态和带值的错误信息）"""
    errors = []

    # 搜索日期检查
    if search_dates:
        item_search_date = safe_parse_date(item.get("searchDateTime"), "%Y-%m-%d")
        req_dates_str = ",".join(sorted([d.strftime("%Y-%m-%d") for d in search_dates]))
        item_date_str = (
            item_search_date.strftime("%Y-%m-%d") if item_search_date else "无"
        )
        if not item_search_date or item_search_date not in search_dates:
            errors.append(
                f"搜索日期不匹配（请求值：{req_dates_str}，数据值：{item_date_str}）"
            )

    # 起飞日期检查
    if depart_dates:
        item_depart_date = safe_parse_date(item.get("departureDate"), "%Y-%m-%d")
        req_departs_str = ",".join(
            sorted([d.strftime("%Y-%m-%d") for d in depart_dates])
        )
        item_depart_str = (
            item_depart_date.strftime("%Y-%m-%d") if item_depart_date else "无"
        )
        if not item_depart_date or item_depart_date not in depart_dates:
            errors.append(
                f"起飞日期不匹配（请求值：{req_departs_str}，数据值：{item_depart_str}）"
            )

    return (len(errors) == 0, "; ".join(errors))


def _check_cities(item: dict, dep_city: str, arr_city: str) -> tuple:
    """城市代码检查（带值对比）"""
    errors = []
    item_dep = item.get("departureCity", "")
    item_arr = item.get("arrivalCity", "")

    if (
        not is_deep_empty(item_dep)
        and not is_deep_empty(dep_city)
        and item_dep != dep_city
    ):
        errors.append(f"出发城市不匹配（请求值：{dep_city}，数据值：{item_dep}）")
    if (
        not is_deep_empty(arr_city)
        and not is_deep_empty(arr_city)
        and item_arr != arr_city
    ):
        errors.append(f"到达城市不匹配（请求值：{arr_city}，数据值：{item_arr}）")

    return (len(errors) == 0, "; ".join(errors))


def _check_carrier(item: dict, carriers: set) -> tuple:
    """航司检查（带值对比）"""
    if carriers:
        item_flight_number = item.get("flightNo")
        if (
            not is_deep_empty(carriers)
            and item_flight_number
            and len(item_flight_number) >= 2
        ):
            item_carrier = item_flight_number[:2]
            if item_carrier not in carriers:
                return (
                    False,
                    f"航司不匹配（请求值：{str(carriers)}，数据值：{item_carrier}）",
                )
    return (True, "")


def _check_flight_number(item: dict, flight_numbers: set) -> tuple:
    """航班号检查（带值对比）"""
    if flight_numbers:
        item_flight = item.get("flightNo", "")
        req_flights_str = ",".join(sorted(flight_numbers))

        if (
            not is_deep_empty(flight_numbers)
            and item_flight
            and len(item_flight) > 2
            and item_flight not in flight_numbers
        ):
            return (
                False,
                f"航班号不匹配（请求值：{req_flights_str}，数据值：{item_flight}）",
            )
    return (True, "")


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def format_passengers(passengers):
    if passengers is None or not isinstance(passengers, list):
        return ""
    result = []
    for passenger in passengers:
        sub_result = []
        for key, value in passenger.items():
            if value is not None:
                sub_result.append(f"{key}={value}")
        result.append("&".join(sub_result))
    return ";".join(result)


def join_list(lst: Optional[List], separator: str = "") -> str:
    """
    将列表安全地拼接为字符串

    :param lst: 输入列表（可能为None或包含空元素）
    :param separator: 元素间分隔符（默认为空）
    :return: 拼接后的字符串

    >>> join_list(None)
    ''
    >>> join_list([])
    ''
    >>> join_list(["a", None, "", "b"])
    'ab'
    >>> join_list([1, None, 3.14, "test"], "-")
    '1-3.14-test'
    """
    # 处理空值输入
    if not isinstance(lst, list):
        return ""

    # 转换元素为字符串并处理None
    cleaned = [str(item) if item is not None else "" for item in lst]
    return separator.join(cleaned)


def boolean_to_str(value: Any) -> str:
    """
    将 Boolean 类型转为字符串，非 Boolean 或 None 返回空字符串

    规则：
    - 输入为 True  → 返回 "True"
    - 输入为 False → 返回 "False"
    - 输入为 None  → 返回 ""
    - 输入为其他类型（如整数、字符串、列表等） → 返回 ""

    :param value: 输入值
    :return: 转换后的字符串或空字符串

    示例：
    >>> boolean_to_str(True)
    'True'
    >>> boolean_to_str(False)
    'False'
    >>> boolean_to_str(None)
    ''
    >>> boolean_to_str(0)
    ''
    >>> boolean_to_str("hello")
    ''
    """
    if value is None:
        return ""
    # 注意：Python 中 isinstance(True, int) 会返回 True，因此用 type 判断更严格
    if type(value) is bool:  # 使用 type 而非 isinstance 避免将 0/1 误判为 bool
        return "True" if value else "False"
    return ""


def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue


def parse_list_data(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    新版航班数据解析方法（支持复合航班号格式）

    参数：
    data: 包含flights字段的原始数据字典

    返回：
    结构化解析后的数据列表，包含字段：
    flightNo, price, coupon, cut, xCut, tag, oriTag, tSource, cabinType
    """
    results = []

    expansionType = data.get("expansionType", "")
    poison = data.get("poison", False)
    basicLabels = data.get("basicLabels") or []
    filters = data.get("filters") or []
    passengers = data.get("passengers") or []

    # 获取航班数据字典（处理空值情况）
    flights = data.get("flights", {})

    # 遍历所有航班条目
    for flight_no, flight_data in flights.items():
        expItems = flight_data.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        # 构造基础数据项
        item = {
            "flightNo": flight_no,
            "tSource": "list",  # 固定值
            "cabinType": "",  # 固定空字符串
            "price": convert_price_to_numeric(flight_data.get("price")),
            "coupon": flight_data.get("coupon"),
            "cut": flight_data.get("cut"),
            "xCut": flight_data.get("xCut"),
            "tag": flight_data.get("tag"),
            "oriTag": flight_data.get("oriTag"),
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": format_passengers(passengers),
        }
        results.append(item)

    return results


def parse_domestic_ota(domestic_ota: Dict) -> List[Dict]:
    """
    解析domesticOta数据，返回结构化航班舱位数据
    """
    result = []

    # 公共字段
    flight_no = domestic_ota.get("flightNo", "")
    t_source = "ota"  # 固定值

    expansionType = domestic_ota.get("expansionType", "")
    poison = domestic_ota.get("poison", False)
    basicLabels = domestic_ota.get("basicLabels") or []
    filters = domestic_ota.get("filters") or []
    passengers = domestic_ota.get("passengers") or []

    # 处理经济舱数据（evendors）
    for vendor in domestic_ota.get("evendors") or []:
        expItems = vendor.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        record = {
            "flightNo": flight_no,
            "price": vendor.get("price"),
            "coupon": vendor.get("coupon"),
            "cut": vendor.get("cut"),
            "xCut": vendor.get("xCut"),
            "tag": vendor.get("tag"),
            "oriTag": vendor.get("oriTag"),
            "tSource": t_source,
            "cabinType": "经济舱",  # evendors固定值
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": format_passengers(passengers),
        }
        result.append(_clean_record(record))

    # 处理高端舱位数据（hvendors）
    for vendor in domestic_ota.get("hvendors") or []:
        expItems = vendor.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        record = {
            "flightNo": flight_no,
            "price": vendor.get("price"),
            "coupon": vendor.get("coupon"),
            "cut": vendor.get("cut"),
            "xCut": vendor.get("xCut"),
            "tag": vendor.get("tag"),
            "oriTag": vendor.get("oriTag"),
            "tSource": t_source,
            "cabinType": "头等/商务舱",  # hvendors固定值
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": join_list(passengers, ","),
        }
        result.append(_clean_record(record))

    return result


def method2_parse_detail_table(data: Dict) -> List[Dict]:
    """
    解析detailTable数据，返回结构化价格信息
    """
    result = []
    if data is None or data.get("detailTable") is None:
        return result

    detail_table = []
    detailTable = data.get("detailTable")
    if not isinstance(detailTable, list):
        detail_table = data.get("detailTable", {}).get("data") or []
    else:
        if len(detailTable) > 0:
            detail_table = detailTable[0].get("data") or []

    for flight_data in detail_table:
        rows = flight_data.get("rows", [])
        for row in rows:
            # 仅处理_domesticShow为true的记录
            if not row.get("_domesticShow", False):
                continue

            raw_data = row.get("_raw", {})
            extMap = raw_data.get("extMap")
            ext_map = raw_data.get("extMap", {})
            all_goods = raw_data.get("allGoodsItems", {})

            cpt = ext_map.get("CPT")
            secondPrice = ext_map.get("secondPrice")
            autoPriceDecreaseAmount = ext_map.get("autoPriceDecreaseAmount")
            if extMap is None:
                cpt = row.get("CPT")
                secondPrice = row.get("secondPrice")
                autoPriceDecreaseAmount = row.get("autoPriceDecreaseAmount")

            # 计算商品总价
            all_good_item_price = sum(
                float(str_val) for str_val in all_goods.values() if _is_number(str_val)
            )

            record = {
                "flightNo": flight_data.get("flightNumber", ""),
                "realPriceOriTag": row.get("realPriceOriTag"),
                "tagType": raw_data.get("tagType"),
                "flightNumber": flight_data.get("flightNumber"),
                "depDate": flight_data.get("depDate"),
                "depTime": flight_data.get("depTime"),
                "wrapperId": row.get("wrapperId"),
                "productMark": row.get("productMark"),
                "cabin": raw_data.get("cabin"),
                "packagePrice": raw_data.get("price"),
                "basePrice": raw_data.get("basePrice"),
                "viewPrice": raw_data.get("viewPrice"),
                "policyId": raw_data.get("policyId"),
                "autoPriceDecreaseAmount": autoPriceDecreaseAmount,
                "secondPrice": convert_price_to_numeric(secondPrice),
                "CPT": cpt,
                "allGoodItemPrice": round(all_good_item_price, 2),
            }
            result.append(_clean_record(record))

    print(f"method2_result------------------: {result}")
    return result


def method3_merge_data(
    method1_data: List[Dict], method2_data: List[Dict]
) -> List[Dict]:
    """
    合并方法1和方法2的数据
    """
    merged = []

    for m1 in method1_data:
        # 生成匹配键：优先使用oriTag，否则使用tag
        match_tag = m1.get("oriTag") or m1.get("tag")
        flight_no = m1.get("flightNo")

        # 在方法2数据中查找匹配项
        matched = next(
            (
                m2
                for m2 in method2_data
                if m2.get("flightNo") == flight_no
                and m2.get("realPriceOriTag") == match_tag
            ),
            None,
        )

        # 复制需要合并的字段
        if matched:
            merge_fields = [
                "wrapperId",
                "productMark",
                "cabin",
                "packagePrice",
                "basePrice",
                "viewPrice",
                "policyId",
                "autoPriceDecreaseAmount",
                "secondPrice",
                "CPT",
                "allGoodItemPrice",
                "depDate",
                "depTime",
            ]
            for field in merge_fields:
                m1[field] = matched.get(field)

        merged.append(m1)

    return merged


# 辅助函数
def _clean_record(record: Dict) -> Dict:
    """处理空值和类型转换"""
    return {k: v if v not in (None, "null", "") else None for k, v in record.items()}


def _is_number(s: Any) -> bool:
    """判断是否为有效数字"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False


def search_flight_case(
    departureDate: str,
    qTraceId: str,
    flightNumber: Optional[str] = None,  # 明确参数可为空
) -> Dict[str, Any]:
    url = "http://paoding.corp.qunar.com/open/case/detail"

    # 参数校验（关键必填项）
    if not departureDate or not qTraceId:
        raise ValueError("departureDate/qTraceId 为必填参数")

    params = {
        "flightType": "SINGLE",
        "departureDate": "2025-04-04",
        "flightNumber": "AQ1707",  # 允许为空，转换为空字符串
        "qTraceId": "ops_slugger_250331.182855.10.90.75.73.1296666.2879156600_1",
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    try:
        response = requests.get(
            url, params=params, headers=headers, timeout=10  # 增加超时控制
        )
        response.raise_for_status()
        result = response.json()
    except (RequestException, ValueError) as e:
        return {"error": f"请求失败: {str(e)}"}

    print(f"result: {result}")

    # 防御性处理响应结构
    method1_result = []
    if result.get("domesticListSearchSimpleLog") is not None:
        method1_result = parse_list_data(result.get("domesticListSearchSimpleLog"))
    if result.get("domesticOta") is not None:
        method1_result = parse_domestic_ota(result.get("domesticOta"))
    # 执行方法1
    # method1_result = parse_domestic_ota(json_obj)
    # print("方法1解析结果：", json.dumps(method1_result[:1], indent=2, ensure_ascii=False))

    print(f"method1_result: {method1_result}")
    # 执行方法2
    method2_result = method2_parse_detail_table(result)
    # print("\n方法2解析结果：", json.dumps(method2_result[:1], indent=2, ensure_ascii=False))

    print(f"method2_result: {method2_result}")
    # 执行方法3
    merged_result = method3_merge_data(method1_result, method2_result)
    print(f"merged_result: {merged_result}")
    return {"data": merged_result}


def parse_and_validate_price_info(price_info: Any) -> Tuple[bool, str, Dict[str, Any]]:
    """
    校验价格信息的有效性
    Args:
        price_info: 价格信息字典
    Returns:
        校验后的价格信息字典
    """
    # 1. 整体校验
    if not price_info:
        return False, "价格信息为空", None
    if not isinstance(price_info, dict):
        return False, "价格信息类型错误", None

    result = {
        "userPriceType": price_info.get("userPriceType"),
        "precisePriceList": [],
        "durationPriceList": [],
    }

    # 2. 校验 precisePriceList
    precise_list = price_info.get("precisePriceList")
    if precise_list is not None:
        if isinstance(precise_list, list):
            for item in precise_list:
                if (
                    isinstance(item, dict)
                    and "price" in item
                    and is_valid_number(item.get("price"))
                ):
                    result["precisePriceList"].append(item)
        elif (
            isinstance(precise_list, dict)
            and "price" in precise_list
            and is_valid_number(precise_list.get("price"))
        ):
            result["precisePriceList"].append(precise_list)

    # 3. 校验 durationPriceList
    duration_list = price_info.get("durationPriceList")
    if duration_list is not None:
        if isinstance(duration_list, list):
            for item in duration_list:
                if isinstance(item, dict):
                    # 检查是否有 leftPrice 和 rightPrice
                    if "leftPrice" in item and "rightPrice" in item:
                        left_price = item.get("leftPrice")
                        right_price = item.get("rightPrice")
                        if (
                            is_valid_number(left_price)
                            and is_valid_number(right_price)
                            and left_price <= right_price
                        ):
                            result["durationPriceList"].append(item)
                    # 检查是否有 price
                    elif "price" in item and is_valid_number(item.get("price")):
                        result["precisePriceList"].append(item)
        elif isinstance(duration_list, dict):
            # 检查是否有 leftPrice 和 rightPrice
            if "leftPrice" in duration_list and "rightPrice" in duration_list:
                left_price = duration_list.get("leftPrice")
                right_price = duration_list.get("rightPrice")
                if (
                    is_valid_number(left_price)
                    and is_valid_number(right_price)
                    and left_price <= right_price
                ):
                    result["durationPriceList"].append(duration_list)
                # 检查是否有 price
            elif "price" in duration_list and is_valid_number(
                duration_list.get("price")
            ):
                result["precisePriceList"].append(duration_list)

    # 4. 检查是否有效
    if not result["precisePriceList"] and not result["durationPriceList"]:
        return False, "价格信息无效", None

    return True, "价格信息有效", result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        departureDate = param.get("departureDate", "")  
        qTraceId = param.get("qTraceId", "")
        flightNumber = param.get("flightNumber", "")
        listQTraceId = param.get("listQTraceId", "")
        if not departureDate or not qTraceId or not flightNumber or not listQTraceId:
            return {"errorMsg": f"缺少必要参数: departureDate/qTraceId/flightNumber/listQTraceId", "priceList": []}

        # 查询ota价格
        searchResult = search_flight_case(
            departureDate=param.get("departureDate", ""),
            qTraceId=param.get("qTraceId", ""),
            flightNumber=param.get("flightNumber"),  # 允许为空
        )

        # 查询list价格
        searchResultList = search_flight_case(
            departureDate=param.get("departureDate", ""),
            qTraceId=listQTraceId,
            flightNumber="",  # 允许为空
        )
        listPriceList = searchResultList.get("data")
        priceList = searchResult.get("data")
        if priceList is None or listPriceList is None:
            if searchResult.get("error") is not None:
                return {"errorMsg": "庖丁请求异常！", "priceList": []}
        print(f"priceList: {priceList}")
        print(f"listPriceList: {listPriceList}")
        filterDatas, notMatched = filter_flight_data_and_merge(
            flightNumber,
            priceList,
            listPriceList
        )

        return {"errorMsg": "", "priceList": filterDatas, "notMatched": notMatched}
    except KeyError as e:
        return {"errorMsg": f"缺少必要参数: {str(e)}", "priceList": []}
    except ValueError as e:
        return {"errorMsg": str(e), "priceList": []}
    except Exception as e:
        return {"errorMsg": f"系统异常: {str(e)}", "priceList": []}


if __name__ == "__main__":
    # 假设输入数据存储在input_data变量中
    param = {
        "question": "【多次搜索变价】用户（本机）于2025-03-05搜索航班，发现同一航线起飞时间为2025-03-24/25左右出发海口-深圳南航 深航航空承运的直达航班的L页价格从300元间隔1个小时以内上涨至400元。用户未观察其他平台，未购票",
        "departureDate": "2025-04-04",
        "qTraceId": "ops_slugger_250331.182901.10.90.75.72.601913.5307611936_1",
        "listQTraceId": "ops_slugger_250331.182855.10.90.75.73.1296666.2879156600_1",
        "flightNumber": "AQ1707",
        "questionParam": {
            "searchDateTime": "2025-03-05",
            "searchDateList": "2025-03-05",
            "departureCityName": "海口",
            "departureCity": "HAK",
            "arrivalCityName": "深圳",
            "arrivalCity": "SZX",
            "departureDate": "",
            "departureDateList": "2025-03-24,2025-03-25",
            "flightNumber": "",
            "carrier": "",
            "carrierList": "CZ,ZH",
        },
        "questionPriceInfo": "%7B%0A%20%20%22userPriceType%22%3A%20%22mixPreciseAndDiffPrice%22%2C%0A%20%20%22precisePriceList%22%3A%20%5B%0A%20%20%20%20%7B%0A%20%20%20%20%20%20%22price%22%3A%20300%2C%0A%20%20%20%20%20%20%22parseType%22%3A%20%22%E7%B2%BE%E5%87%86%22%0A%20%20%20%20%7D%0A%20%20%5D%2C%0A%20%20%22durationPriceList%22%3A%20%5B%0A%20%20%20%20%7B%0A%20%20%20%20%20%20%22leftPrice%22%3A%20400%2C%0A%20%20%20%20%20%20%22rightPrice%22%3A%20600%2C%0A%20%20%20%20%20%20%22parseType%22%3A%20%22%E6%A8%A1%E7%B3%8A%E7%9A%84%E5%8C%BA%E9%97%B4%E4%BB%B7%E6%A0%BC%22%0A%20%20%20%20%7D%0A%20%20%5D%0A%7D",
    }
    result = main(param)
    print("\n调用结果：", json.dumps(result, indent=2, ensure_ascii=False))
    """priceList = [
        {
            "flightNo": "DZ6251",
            "tSource": "list",
            "cabinType": "",
            "price": 631,
            "coupon": 35,
            "tag": "YCP1",
            "expVendor": 5,
            "expansionType": "ALLOW",
            "poison": "False",
            "basicLabels": "ZSJ6HEI,ZSJ6",
            "filters": "ECONOMY,,PRICE_ASC",
            "passengers": "",
            "wrapperId": "ttsgnd04084",
            "productMark": "894",
            "cabin": "V",
            "packagePrice": 666,
            "basePrice": 666,
            "viewPrice": 690,
            "policyId": 3126615162,
            "autoPriceDecreaseAmount": "24.0",
            "secondPrice": 674,
            "CPT": "1741140220",
            "allGoodItemPrice": 0,
            "depDate": "2025-03-24",
            "depTime": "06:40",
        },
        {
            "flightNo": "CZ5922",
            "tSource": "list",
            "cabinType": "",
            "price": 599,
            "coupon": 35,
            "cut": 36,
            "tag": "YCP1",
            "expVendor": 6,
            "expansionType": "ALLOW",
            "poison": "False",
            "basicLabels": "ZSJ6HEI,ZSJ6",
            "filters": "ECONOMY,,PRICE_ASC",
            "passengers": "",
            "wrapperId": "ttsgnd00111",
            "productMark": "894",
            "cabin": "Q",
            "packagePrice": 670,
            "basePrice": 624.9,
            "viewPrice": 670,
            "policyId": 20015109918,
            "autoPriceDecreaseAmount": "30.5",
            "secondPrice": 625,
            "CPT": "1741140460",
            "allGoodItemPrice": 0,
            "depDate": "2025-03-24",
            "depTime": "19:15",
        },
    ]
    param = {
        "searchDateTime": "2025-03-05",
        "searchDateList": "2025-03-05",
        "departureCityName": "海口",
        "departureCity": "HAK",
        "arrivalCityName": "深圳",
        "arrivalCity": "SZX",
        "departureDate": "",
        "departureDateList": "2025-03-24,2025-03-25",
        "flightNumber": "",
        "carrier": "",
        "carrierList": "CZ,ZH",
    }
    values, notMatched = filter_flight_data(param, priceList, priceInfoParam)
    print("\n调用结果：", json.dumps(values, indent=2, ensure_ascii=False))
    print("\n未匹配结果：", json.dumps(notMatched, indent=2, ensure_ascii=False))"""
