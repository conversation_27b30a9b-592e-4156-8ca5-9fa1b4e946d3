# SQL query mapping dictionary
SQL_MAP = {
    877: """
    SELECT * FROM table_877
    WHERE condition = 'value'
    """,
    2449: """
    SELECT column1, column2
    FROM table_2449
    WHERE id = :id
    """,
    2584: """
    SELECT *
    FROM table_2584
    WHERE status = 'active'
    """,
}


def get_sql_query(query_id):
    """
    Get the SQL query for a given query ID.

    Args:
        query_id (int): The ID of the query to retrieve

    Returns:
        str: The corresponding SQL query, or None if the ID is not found
    """
    return SQL_MAP.get(query_id)


# Example usage
if __name__ == "__main__":
    # Test the mapping
    test_ids = [877, 2449, 2584]
    for query_id in test_ids:
        sql = get_sql_query(query_id)
        if sql:
            print(f"Query ID {query_id}:")
            print(sql)
            print("-" * 50)
        else:
            print(f"No SQL query found for ID {query_id}")
