from collections import defaultdict
from urllib.parse import unquote_to_bytes,quote
import json
from typing import Tuple, Optional, List, Dict, Any, Union
from datetime import datetime,date
from string import Formatter
import re
from typing import Union
import uuid

def main(param: dict) -> dict:
    try:
        
        # 获取模板和必要参数
        oneToOnetemplatePrompt = param.get("oneToOnetemplatePrompt")
        imgContent = param.get("imgContent")
        airlineDate = param.get("airlineDate", "")  # 新增airlineDate参数，默认为空字符串

        prompt = generate_prompt(oneToOnetemplatePrompt, imgContent, airlineDate)    
        
        return {
            "status": 200,
            "errMsg": "",
            "prompt": prompt
        }

    except Exception as e:  
        return {
            "status": 404,
            "errMsg": f"处理异常: {str(e)}",
            "prompt": ""
        }

def generate_prompt(template_prompt: str, img_content: dict, airline_date: str = "") -> str:
    """
    将模板中的 {msg} 和 {airlineDate} 替换为对应内容
    
    参数:
    template_prompt: str - 模板提示词
    img_content: dict - 需要替换的内容
    airline_date: str - 航班日期
    
    返回:
    str - 替换后的完整提示词
    """
    # 将 img_content 转换为 JSON 字符串
    if isinstance(img_content, str):
        try:
            # 尝试解析 JSON 字符串
            img_content_dict = json.loads(img_content)
            img_content_str = json.dumps(img_content_dict, ensure_ascii=False)
        except json.JSONDecodeError:
            # 如果不是有效的 JSON 字符串，直接使用
            img_content_str = img_content
    else:
        # 如果已经是字典，转换为 JSON 字符串
        img_content_str = json.dumps(img_content, ensure_ascii=False)
    
    # 替换模板中的 {msg} 和 {airlineDate} 占位符
    prompt = template_prompt.replace("{msg}", img_content_str)
    prompt = prompt.replace("{airlineDate}", airline_date)
    
    return prompt


def test_generate_prompt():
    """
    测试生成提示词的方法
    """
    # 测试数据
    template_prompt = """###角色###
你是一名机票病退运营审核人员，现有用户申请病退时提供的医生出具的诊断证明等材料内容，需要你提取有用信息，根据后面的要求，给出最终结论。
###诊断证明材料内容###
{msg}
###诊断证明材料字段###
####字段名是汉字####
证件类型：识别为病历、诊断证明、住院证明、检查报告、诊断书等
证明标题: 证明材料的标题
医院名称：医院
医生姓名：医生姓名
姓名：姓名
性别:性别
年龄:年龄
住院号：住院号
门诊号：门诊号
病历号：病历号
就诊号：就诊号
科室：科室
就诊日期：就诊日期
开具时间：开具日期
入院时间：入院时间
是否有盖章：文件中是否有盖章
章：章的内容
是否有医生签字：文件中是否有医生的签字
病历内容:诊断的内容，主诉、就诊意见等等
###任务说明###
根据"诊断证明材料内容内容"，分析是否"不宜乘机"，主要解析"病历内容"字段的说明，结合就诊日期、开具日期进行分析，输出结论
###重点说明###
1.如果诊断内容中明确说明了"不宜乘机"或者类似表述，则结果为是
2.如果诊断内容中提到休息一周、休息XX日等话语，则需要根据就诊日期、开具日期分析出不宜乘机的时间范围,精确到天，逗号拼接，格式"yyyy-mm-dd,yyyy-mm-dd"
3.上述诊断证明材料字段为汉字，诊断证明材料内容为对应的json数据
###输出字段###
1、not_suitable_for_flying：是否不宜乘机
2、not_suitable_for_flying_timeRange:不宜乘机时间范围，示例（"2025-01-01,2025-01-07"）"""

    img_content = {"科室":"关节外科专科门诊","是否有盖章":"","姓名":"田**","就诊号":"0011","医生姓名":"李宝文","住院号":"","病历内容":"","性别":"男","材料类型":"诊断证明","开具时间":"09:00:31","病历号":"","是否有医生签字":"","年龄":"24岁","门诊号":"","入院时间":"","姓名_md5":"5452f425db4e58b5cbf086a1f9857837","就诊号_md5":"ae2bac2e4b4da805d01b2952d7e35ba4","就诊日期":"2025-05-09","医院名称":"大连医科大学附属第二医院"}
    
    # 生成提示词
    prompt = generate_prompt(template_prompt, img_content)
    
    # 打印结果
    print("生成的提示词：")
    print(prompt)
    
    return prompt


if __name__ == "__main__":
    # 测试数据
    test_param = {
        "oneToOnetemplatePrompt": """###角色###
你是一名机票病退运营审核人员，现有用户申请病退时提供的医生出具的诊断证明等材料内容，需要你提取有用信息，根据后面的要求，给出最终结论。
###诊断证明材料内容###
{msg}
###诊断证明材料字段###
####字段名是汉字####
证件类型：识别为病历、诊断证明、住院证明、检查报告、诊断书等
证明标题: 证明材料的标题
医院名称：医院
医生姓名：医生姓名
姓名：姓名
性别:性别
年龄:年龄
住院号：住院号
门诊号：门诊号
病历号：病历号
就诊号：就诊号
科室：科室
就诊日期：就诊日期
开具时间：开具日期
入院时间：入院时间
是否有盖章：文件中是否有盖章
章：章的内容
是否有医生签字：文件中是否有医生的签字
病历内容:诊断的内容，主诉、就诊意见等等
###任务说明###
根据"诊断证明材料内容内容"，分析是否"不宜乘机"，主要解析"病历内容"字段的说明，结合就诊日期、开具日期进行分析，输出结论
###重点说明###
1.如果诊断内容中明确说明了"不宜乘机"或者类似表述，则结果为是
2.如果诊断内容中提到休息一周、休息XX日等话语，则需要根据就诊日期、开具日期分析出不宜乘机的时间范围,精确到天，逗号拼接，格式"yyyy-mm-dd,yyyy-mm-dd"
3.上述诊断证明材料字段为汉字，诊断证明材料内容为对应的json数据
###输出字段###
1、not_suitable_for_flying：是否不宜乘机
2、not_suitable_for_flying_timeRange:不宜乘机时间范围，示例（"2025-01-01,2025-01-07"）""",
        "imgContent": {"科室":"关节外科专科门诊","是否有盖章":"","姓名":"田**","就诊号":"0011","医生姓名":"李宝文","住院号":"","病历内容":"患者因腰痛一周，诊断为腰椎间盘突出症，建议休息一周，暂不宜乘机。","性别":"男","材料类型":"诊断证明","开具时间":"09:00:31","病历号":"","是否有医生签字":"","年龄":"24岁","门诊号":"","入院时间":"","姓名_md5":"5452f425db4e58b5cbf086a1f9857837","就诊号_md5":"ae2bac2e4b4da805d01b2952d7e35ba4","就诊日期":"2025-05-09","医院名称":"大连医科大学附属第二医院"}
    }
    
    # 执行main方法
    result = main(test_param)
    
    # 打印结果
    print("测试main方法结果：")
    print(json.dumps(result, ensure_ascii=False, indent=2))
