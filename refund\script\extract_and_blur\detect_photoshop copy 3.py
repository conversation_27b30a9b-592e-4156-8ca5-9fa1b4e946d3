#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageChops, ImageFilter
import piexif
import numpy as np
from pathlib import Path
from io import BytesIO
import sys
import json
import re
import requests

def analyze_metadata(img):
    """
    分析图像元数据，寻找PS痕迹，但更谨慎判断
    
    参数:
        img: PIL Image对象
    
    返回:
        metadata_score: 元数据PS痕迹分数 (0-1)
        metadata_info: 相关元数据信息
    """
    metadata_info = {}
    metadata_score = 0.0
    evidence = []
    
    try:
        # 提取所有元数据
        img_info = img.info
        print(f"元数据字段: {list(img_info.keys())}")
        
        # 检查EXIF数据中的编辑软件信息
        if "exif" in img_info:
            try:
                exif_dict = piexif.load(img_info["exif"])
                
                # 检查软件信息 - 有些相机会自带Adobe相关的字段，但不一定是PS编辑
                if piexif.ImageIFD.Software in exif_dict.get("0th", {}):
                    software = exif_dict["0th"][piexif.ImageIFD.Software].decode('utf-8', errors='ignore')
                    metadata_info["软件信息"] = software
                    
                    # 明确识别Photoshop编辑软件
                    if re.search(r'\badobe photoshop\b', software.lower()):
                        metadata_score += 0.5
                        evidence.append(f"专业编辑软件信息: {software}")
                
                # 检查修改历史/日期 - 比较原始创建日期和修改日期
                if (piexif.ExifIFD.DateTimeOriginal in exif_dict.get("Exif", {}) and 
                    piexif.ExifIFD.DateTimeDigitized in exif_dict.get("Exif", {})):
                    date_original = exif_dict["Exif"][piexif.ExifIFD.DateTimeOriginal].decode('utf-8', errors='ignore')
                    date_digitized = exif_dict["Exif"][piexif.ExifIFD.DateTimeDigitized].decode('utf-8', errors='ignore')
                    
                    if date_original != date_digitized:
                        metadata_info["拍摄日期"] = date_original
                        metadata_info["修改日期"] = date_digitized
                        metadata_score += 0.2
                        evidence.append(f"拍摄日期与修改日期不一致: {date_original} vs {date_digitized}")
            
            except Exception as exif_error:
                print(f"处理EXIF数据时出错: {exif_error}")
                metadata_info["EXIF解析错误"] = str(exif_error)
                
        # 检查Photoshop特有元数据块
        # 这里我们更严格，必须有非空的Photoshop数据内容
        if "adobe" in img_info:
            ps_data = img_info["photoshop"]
            if isinstance(ps_data, dict) and ps_data:  # 确保字典不为空
                non_empty_values = False
                for key, value in ps_data.items():
                    if isinstance(value, bytes) and len(value) > 5:  # 避免空或很小的数据块
                        non_empty_values = True
                        break
                
                if non_empty_values:
                    metadata_score += 0.4
                    metadata_info["Photoshop数据"] = "包含非空Photoshop专有数据块"
                    evidence.append("包含非空Photoshop专有数据块")
            elif ps_data == b'' or not ps_data:
                metadata_info["注意"] = "存在空的Photoshop字段，很可能是标准元数据而非编辑痕迹"
        
        # 检查XMP数据中的Adobe编辑痕迹
        if "xmp" in img_info and isinstance(img_info["xmp"], bytes):
            xmp_data = img_info["xmp"].lower()
            if b"<photoshop:" in xmp_data and b"<xmpmeta" in xmp_data:
                # 寻找具体的编辑历史记录
                if b"photoshop:historystate" in xmp_data or b"<stEvt:action>" in xmp_data:
                    metadata_score += 0.5
                    metadata_info["XMP数据"] = "包含Photoshop编辑历史记录"
                    evidence.append("XMP数据中包含编辑历史记录")
                else:
                    metadata_score += 0.2
                    metadata_info["XMP数据"] = "包含Adobe XMP元数据，但无明确编辑记录"
        
        # 避免误判: 一些手机和相机会在元数据中包含Adobe相关字段
        if "dpi" in img_info and img_info["dpi"] == (72.0, 72.0) and metadata_score < 0.3:
            metadata_info["注意"] = "这些元数据可能是设备自动添加的标准字段，不一定表示PS编辑"
            metadata_score *= 0.5  # 降低置信度
        
        # 确保分数不超过1.0
        metadata_score = min(metadata_score, 1.0)
        
        metadata_info["证据"] = evidence
        metadata_info["元数据PS评分"] = f"{metadata_score:.2f}"
        
        return metadata_score, metadata_info
        
    except Exception as e:
        print(f"元数据分析出错: {e}")
        return 0.0, {"错误": str(e)}

def check_noise_pattern(img):
    """
    分析图像的噪声模式，寻找PS编辑痕迹
    PS过的图像在噪声分布上通常会有特征
    
    参数:
        img: PIL Image对象
    
    返回:
        noise_score: 噪声分析PS痕迹分数 (0-1)
        noise_info: 噪声分析信息
    """
    noise_info = {}
    try:
        # 转换为灰度图
        gray_img = img.convert('L')
        
        # 使用高通滤波器突出噪声
        noise = gray_img.filter(ImageFilter.FIND_EDGES)
        
        # 将图像转换为Numpy数组进行分析
        noise_array = np.array(noise)
        
        # 计算噪声统计特征
        noise_mean = noise_array.mean()
        noise_std = noise_array.std()
        noise_info["噪声平均值"] = f"{noise_mean:.2f}"
        noise_info["噪声标准差"] = f"{noise_std:.2f}"
        
        # 基于经验规则的噪声评分 (这些阈值需要根据实际数据调整)
        noise_score = 0.0
        
        # 异常平滑区域检测 (可能是PS涂抹或修复的区域)
        if noise_std < 15:  # 过于平滑的图像通常是经过处理的
            noise_score += 0.3
            noise_info["异常"] = "图像整体过于平滑，可能经过处理"
        
        # 噪声不均匀性检测 (局部噪声差异可能表明局部编辑)
        # 将图像分为4x4网格，检查噪声分布是否均匀
        h, w = noise_array.shape
        grid_h, grid_w = h // 4, w // 4
        grid_stds = []
        
        for i in range(4):
            for j in range(4):
                grid = noise_array[i*grid_h:(i+1)*grid_h, j*grid_w:(j+1)*grid_w]
                grid_stds.append(grid.std())
        
        grid_std_of_stds = np.std(grid_stds)
        noise_info["局部噪声差异"] = f"{grid_std_of_stds:.2f}"
        
        # 如果局部噪声差异大，可能是局部编辑的迹象
        if grid_std_of_stds > 5:
            noise_score += 0.3
            noise_info["局部差异"] = "图像不同区域噪声特征差异明显，可能有局部编辑"
        
        # 边缘一致性检查 (PS过的图像边缘特征可能不一致)
        edges = gray_img.filter(ImageFilter.FIND_EDGES)
        edges_array = np.array(edges)
        edge_mean = edges_array.mean()
        
        if edge_mean < 10 or edge_mean > 40:  # 边缘分布异常
            noise_score += 0.2
            noise_info["边缘特征"] = f"边缘特征分布异常 (均值: {edge_mean:.2f})"
            
        # 确保分数不超过1.0
        noise_score = min(noise_score, 1.0)
        noise_info["噪声PS评分"] = f"{noise_score:.2f}"
        
        return noise_score, noise_info
        
    except Exception as e:
        print(f"噪声分析出错: {e}")
        return 0.0, {"错误": str(e)}

def check_compression_artifacts(img):
    """
    分析图像的压缩痕迹，查找多次保存/编辑的迹象
    
    参数:
        img: PIL Image对象
    
    返回:
        compression_score: 压缩分析PS痕迹分数 (0-1)
        compression_info: 压缩分析信息 
    """
    compression_info = {}
    try:
        # 只分析JPEG格式图像
        if img.format != 'JPEG' and hasattr(img, 'format'):
            return 0.0, {"说明": "非JPEG图像，跳过压缩分析"}
        
        # 将图像转换为YCbCr色彩空间 (适合分析JPEG压缩痕迹)
        if img.mode != 'L':
            ycbcr = img.convert('YCbCr')
            y, cb, cr = ycbcr.split()
            
            # 分析色度通道 (Cb, Cr)，这些通道在JPEG中会有明显的块状压缩痕迹
            cb_array = np.array(cb)
            cr_array = np.array(cr)
            
            # 检测8x8块状痕迹 (JPEG特征)
            # 计算色度通道的局部变化
            cb_diff_h = np.abs(cb_array[:, 1:] - cb_array[:, :-1]).mean()
            cb_diff_v = np.abs(cb_array[1:, :] - cb_array[:-1, :]).mean()
            cr_diff_h = np.abs(cr_array[:, 1:] - cr_array[:, :-1]).mean()
            cr_diff_v = np.abs(cr_array[1:, :] - cr_array[:-1, :]).mean()
            
            compression_info["水平色度变化"] = f"{cb_diff_h:.2f}/{cr_diff_h:.2f}"
            compression_info["垂直色度变化"] = f"{cb_diff_v:.2f}/{cr_diff_v:.2f}"
            
            # 重复压缩痕迹得分
            compression_score = 0.0
            
            # 色度变化的异常模式可能表明多次压缩
            cb_cr_ratio = cb_diff_h / (cr_diff_h + 0.001)
            if cb_cr_ratio < 0.5 or cb_cr_ratio > 2.0:
                compression_score += 0.3
                compression_info["色度异常"] = "色度通道比例异常，可能是多次编辑/保存导致"
            
            # 检查8x8边界上的不连续性 (多次JPEG压缩的特征)
            block_artifacts = 0
            for i in range(7, cb_array.shape[0]-1, 8):
                block_diff = np.abs(cb_array[i, :] - cb_array[i+1, :]).mean()
                if block_diff > cb_diff_v * 1.5:
                    block_artifacts += 1
            
            compression_info["块状痕迹数"] = block_artifacts
            if block_artifacts > 10:
                compression_score += 0.3
                compression_info["块状痕迹"] = "检测到明显的JPEG块状痕迹，可能经过多次保存/编辑"
            
            # 检查亮度通道的锐化痕迹 (通常PS时会有锐化)
            y_array = np.array(y)
            y_laplacian = np.abs(y_array[1:-1, 1:-1] * 4 - y_array[:-2, 1:-1] - y_array[2:, 1:-1] - 
                                 y_array[1:-1, :-2] - y_array[1:-1, 2:]).mean()
            
            compression_info["锐化程度"] = f"{y_laplacian:.2f}"
            if y_laplacian > 15:  # 过度锐化
                compression_score += 0.2
                compression_info["锐化痕迹"] = "检测到过度锐化痕迹，常见于图像编辑"
            
            # 确保分数不超过1.0
            compression_score = min(compression_score, 1.0)
            compression_info["压缩PS评分"] = f"{compression_score:.2f}"
            
            return compression_score, compression_info
        else:
            return 0.0, {"说明": "灰度图像，跳过色度压缩分析"}
            
    except Exception as e:
        print(f"压缩分析出错: {e}")
        return 0.0, {"错误": str(e)}
    
def detect_photoshop_from_url(image_url):
    """
    从URL下载图像并检测是否被PS过
    
    参数:
        image_url: 图像URL地址
    
    返回:
        result: 检测结果字典
    """
    result = {
        "图片URL": image_url,
        "PS综合评分": 0.0,
        "分析结果": {}
    }
    
    try:
        print(f"正在从URL下载图像: {image_url}")
        response = requests.get(image_url, stream=True)
        
        if response.status_code != 200:
            result["错误"] = f"下载图像失败，状态码: {response.status_code}"
            result["结论"] = "检测失败（下载图像失败）"
            return result
        
        # 读取图像到内存
        img_data = BytesIO(response.content)
        img = Image.open(img_data)
        print(f"图像下载成功，格式: {img.format}, 大小: {img.size}, 模式: {img.mode}")
        
        # 1. 元数据分析 (占总评分的40%)
        metadata_score, metadata_info = analyze_metadata(img)
        result["分析结果"]["元数据分析"] = metadata_info
        
        # 2. 噪声分析 (占总评分的30%)
        noise_score, noise_info = check_noise_pattern(img)
        result["分析结果"]["噪声分析"] = noise_info
        
        # 3. 压缩痕迹分析 (占总评分的30%)
        compression_score, compression_info = check_compression_artifacts(img)
        result["分析结果"]["压缩痕迹分析"] = compression_info
        
        # 计算加权综合评分
        weighted_score = (metadata_score * 0.5 + 
                          noise_score * 0.45 + 
                          compression_score * 0.05)
        
        result["PS综合评分"] = weighted_score
        result["各项评分"] = {
            "元数据评分": metadata_score,
            "噪声评分": noise_score,
            "压缩痕迹评分": compression_score
        }
        
        # 综合判断
        if weighted_score >= 0.7:
            result["结论"] = "图像很可能被PS过（高置信度）"
            result["可信度"] = "高"
        elif weighted_score >= 0.4:
            result["结论"] = "图像可能被PS过（中等置信度）"
            result["可信度"] = "中"
        elif weighted_score >= 0.2:
            result["结论"] = "图像存在轻微PS痕迹，但置信度较低"
            result["可信度"] = "低"
        else:
            result["结论"] = "未检测到明确的PS痕迹"
            result["可信度"] = "极低"
        
    except Exception as e:
        print(f"处理图像时出错: {e}", file=sys.stderr)
        result["错误"] = str(e)
        result["结论"] = "检测过程出错"
    
    return result

def main(params: dict) -> dict:
    # 检查是否提供了URL
    image_url = params.get("image_url", "")
    
    if image_url:
        print(f"开始从URL分析图像: {image_url}")
        result = detect_photoshop_from_url(image_url)
    else:
        result = {
            "错误": "未提供图像URL或路径",
            "结论": "检测失败（缺少输入）"
        }
    
    # 打印结果
    print("\n===== 图像PS检测综合结果 =====")
    if "图片URL" in result:
        print(f"图片URL: {result.get('图片URL', '未知')}")
    else:
        print(f"图片路径: {result.get('图片路径', '未知')}")
    
    print(f"结论: {result.get('结论', '未知')}")
    print(f"PS综合评分: {result.get('PS综合评分', 0):.2f} (可信度: {result.get('可信度', '未知')})")
    
    # 显示各项分析得分
    if "各项评分" in result:
        print("\n各项评分:")
        for key, value in result["各项评分"].items():
            print(f"- {key}: {value:.2f}")
    
    print("\n详细分析结果:")
    for category, details in result.get("分析结果", {}).items():
        print(f"\n{category}:")
        if isinstance(details, dict):
            for key, value in details.items():
                if key == "证据" and isinstance(value, list):
                    print(f"  - {key}:")
                    for item in value:
                        print(f"    * {item}")
                else:
                    print(f"  - {key}: {value}")
        else:
            print(f"  {details}")
    
    # 如果有错误，单独打印错误信息
    if "错误" in result:
        print(f"\n错误: {result['错误']}")
    
    return result

if __name__ == "__main__":
    # 直接传入测试URL
    #test_url = "https://fuwu.qunar.com/qbcp/file/download?fileName=4.jpg&uniqueName=attach20250211141627753777"
    test_url = "https://fuwu.qunar.com/qbcp/file/download?fileName=6.jpg&uniqueName=attach20250211141835378330"
    main({"image_url": test_url})
