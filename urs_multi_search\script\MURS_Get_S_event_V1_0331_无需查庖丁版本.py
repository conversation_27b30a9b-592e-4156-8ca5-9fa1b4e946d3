import numbers
import re
from string import Formatter
import requests
import copy
import traceback


import json
import base64
from datetime import datetime, date
from csv import Di<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as CSVError
from io import StringIO
import uuid
from typing import List, Dict, Any, Set, Tuple, Union, Optional
import time
from urllib.parse import quote

TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)

URS_PRICE_CHANGE_SQL = """
SELECT 
    f.dt,
    f.create_time,
    f.questionnaire_id,
    f.user_name,
    f.time_interval,
    f.order_num,
    f.ticket_num,
    f.user_value,
    f.is_all_answer,
    f.is_scalper,
    f.order_if,
    f.danpiao_if,
    f.ext_json,
    f.page_json,
    f.routetype_json,
    p.is_bianjia,
    p.up_or_down,
    p.mintue_cha,
    p.type,
    p.od,
    p.depdate,
    p.orig_flight_type,
    p.flightkey,
    p.is_trans,
    p.showprice,
    p.last_showprice,
    p.qtraceid,
    p.last_qtraceid,
    p.search_time,
    p.last_search_time,
    p.price_cha
FROM 
    flight.dwd_flow_dom_usertouch_ups_new_detail_di f
LEFT JOIN 
    flight.dwd_urs_price_changes_detail_di p
ON 
    f.user_name = p.qunar_username
WHERE 
      f.dt = '{ursDate}'
    AND f.questionnaire_id in ('877')
    AND f.is_stable in ('不稳定')
    AND f.flag = 'UPS一致率'
    AND p.dt = '{ursDate}'
    AND p.price_cha != 0
    and p.orig_flight_type = '直飞'
    and f.user_name = '{username}'
"""


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())


def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None


def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        return
    except KeyError as e:
        return


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )

    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None


def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"error": "当前条件未检索到数据", "results": [], "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"compareId": generateId()})
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


def buildSqlByQuery(ursData, username, extParam):
    sql = URS_PRICE_CHANGE_SQL

    try:
        params = {}
        params["ursDate"] = ursData
        params["username"] = username

        # Check if extParam exists and filterTransData is 'true'
        if (
            extParam
            and isinstance(extParam, dict)
            and extParam.get("filterTransData") == "true"
        ):
            # Add is_trans condition to the SQL query
            sql = sql.replace(
                "and p.orig_flight_type = '直飞'",
                "and p.orig_flight_type = '直飞'\n    and p.is_trans = '0'",
            )

        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj.strip() == ""
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict,
    fields: List,
    date_format: str = None,
    case_sensitive: bool = False,
    supportTimePreciseMatch: bool = False,
) -> Set[Union[str, date, datetime]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :param supportTimePreciseMatch: 是否支持精确时间匹配（默认False，仅匹配日期）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            if supportTimePreciseMatch:
                # 尝试解析完整的时间格式
                dt = safe_parse_datetime(v)
                if dt:
                    parsed.add(dt)
                    continue
            # 如果精确时间解析失败或不需要精确时间，则按日期解析
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def matchSingleTargetData(
    request_params: dict, target_data: dict, supportTimePreciseMatch: bool = False
) -> tuple[bool, str]:
    """
    检查单个目标数据是否匹配过滤条件

    Args:
        request_params: 请求参数字典
        target_data: 单个目标数据字典
        supportTimePreciseMatch: 是否支持精确时间匹配

    Returns:
        tuple[bool, str]: (是否匹配, 不匹配原因)
    """
    # 预处理所有多值参数（含智能类型转换）
    search_dates = parse_multi_values(
        request_params,
        ["searchDateTime", "searchDateList"],
        "%Y-%m-%d",
        supportTimePreciseMatch=supportTimePreciseMatch,
    )
    depart_dates = parse_multi_values(
        request_params,
        ["departureDate", "departureDateList"],
        "%Y-%m-%d",
        supportTimePreciseMatch=supportTimePreciseMatch,
    )
    carriers = parse_multi_values(request_params, ["carrier", "carrierList"])
    flight_numbers = parse_multi_values(request_params, ["flightNumber"])

    # 预处理城市代码（单值）
    dep_city = request_params.get("departureCity", "")
    arr_city = request_params.get("arrivalCity", "")
    dep_city_name = request_params.get("departureCityName", "")
    arr_city_name = request_params.get("arrivalCityName", "")

    # 多条件检查
    checks = [
        _check_dates(target_data, search_dates, depart_dates, supportTimePreciseMatch),
        _check_cities(target_data, dep_city, arr_city),
        _check_cities_name(target_data, dep_city_name, arr_city_name),
        _check_carrier(target_data, carriers),
        _check_flight_number(target_data, flight_numbers),
    ]

    # 提取每个check的布尔状态
    is_all_match = all([check[0] for check in checks])

    # 收集所有错误信息
    error_msgs = [check[1] for check in checks if not check[0]]

    return is_all_match, "; ".join(error_msgs) if error_msgs else "匹配"


def filter_flight_data(
    request_params: dict, target_data: List[dict], supportTimePreciseMatch: bool = False
):
    """
    增强版多条件过滤（支持全量多值参数）
    """
    matched = []
    notMatched = []

    # 预处理所有多值参数（含智能类型转换）
    search_dates = parse_multi_values(
        request_params,
        ["searchDateTime", "searchDateList"],
        "%Y-%m-%d",
        supportTimePreciseMatch=supportTimePreciseMatch,
    )
    depart_dates = parse_multi_values(
        request_params,
        ["departureDate", "departureDateList"],
        "%Y-%m-%d",
        supportTimePreciseMatch=supportTimePreciseMatch,
    )
    carriers = parse_multi_values(request_params, ["carrier", "carrierList"])
    flight_numbers = parse_multi_values(
        request_params, ["flightNumber"]
    )  # 注意原始参数名拼写错误

    # 预处理城市代码（单值）
    dep_city = request_params.get("departureCity", "")
    arr_city = request_params.get("arrivalCity", "")
    dep_city_name = request_params.get("departureCityName", "")
    arr_city_name = request_params.get("arrivalCityName", "")

    for item in target_data:
        # 多条件检查
        checks = [
            _check_dates(item, search_dates, depart_dates, supportTimePreciseMatch),
            _check_cities(item, dep_city, arr_city),
            _check_cities_name(item, dep_city_name, arr_city_name),
            _check_carrier(item, carriers),
            _check_flight_number(item, flight_numbers),
        ]

        # 关键修复点：提取每个check的布尔状态
        is_all_match = all([check[0] for check in checks])

        # 收集所有错误信息
        error_msgs = [check[1] for check in checks if not check[0]]

        # 标注匹配状态及错误详情
        item.update(
            {
                "matchQeTag": "匹配" if is_all_match else "不匹配",
                "notMatchQeMsg": "; ".join(error_msgs) if error_msgs else "匹配",
            }
        )

        if is_all_match:
            matched.append(item)
        else:
            notMatched.append(item)

    return matched, notMatched


def _check_precise_search_time(
    item_search_datetime: datetime, search_dates: set
) -> tuple:
    """
    精准匹配搜索时间（精确到小时）
    :param item_search_datetime: 数据中的搜索时间
    :param search_dates: 请求的搜索时间集合
    :return: (是否匹配, 错误信息)
    """
    if not item_search_datetime:
        return False, "数据中搜索时间为空"

    # 将时间精确到小时
    item_search_hour = item_search_datetime.replace(minute=0, second=0, microsecond=0)
    req_dates_str = ",".join(
        sorted(
            [
                (
                    d.strftime("%Y-%m-%d %H:00")
                    if isinstance(d, datetime)
                    else d.strftime("%Y-%m-%d")
                )
                for d in search_dates
            ]
        )
    )

    # 检查是否有任何请求时间匹配（精确到小时）
    is_match = any(
        (
            isinstance(d, datetime)
            and d.replace(minute=0, second=0, microsecond=0) == item_search_hour
        )
        or (not isinstance(d, datetime) and d == item_search_datetime.date())
        for d in search_dates
    )

    if not is_match:
        return (
            False,
            f"搜索时间不匹配（请求值：{req_dates_str}，数据值：{item_search_datetime.strftime('%Y-%m-%d %H:00')}）",
        )
    return True, ""


def _check_precise_depart_time(
    item_depart_datetime: datetime, depart_dates: set
) -> tuple:
    """
    精准匹配起飞时间（精确到小时）
    :param item_depart_datetime: 数据中的起飞时间
    :param depart_dates: 请求的起飞时间集合
    :return: (是否匹配, 错误信息)
    """
    if not item_depart_datetime:
        return False, "数据中起飞时间为空"

    # 将时间精确到小时
    item_depart_hour = item_depart_datetime.replace(minute=0, second=0, microsecond=0)
    req_departs_str = ",".join(
        sorted(
            [
                (
                    d.strftime("%Y-%m-%d %H:00")
                    if isinstance(d, datetime)
                    else d.strftime("%Y-%m-%d")
                )
                for d in depart_dates
            ]
        )
    )

    # 检查是否有任何请求时间匹配（精确到小时）
    is_match = any(
        (
            isinstance(d, datetime)
            and d.replace(minute=0, second=0, microsecond=0) == item_depart_hour
        )
        or (not isinstance(d, datetime) and d == item_depart_datetime.date())
        for d in depart_dates
    )

    if not is_match:
        return (
            False,
            f"起飞时间不匹配（请求值：{req_departs_str}，数据值：{item_depart_datetime.strftime('%Y-%m-%d %H:00')}）",
        )
    return True, ""


def _check_dates(
    item: dict,
    search_dates: set,
    depart_dates: set,
    supportTimePreciseMatch: bool = False,
) -> tuple:
    """日期检查（返回匹配状态和带值的错误信息）"""
    errors = []

    # 搜索日期检查
    if search_dates:
        # 从多个字段收集时间
        datetime_fields = ["searchDateTime", "search_time", "last_search_time"]
        item_search_datetimes = []

        for field in datetime_fields:
            field_value = item.get(field)
            if field_value:
                parsed_dt = safe_parse_datetime(field_value)
                if parsed_dt:
                    item_search_datetimes.append(parsed_dt)

        if not item_search_datetimes:
            errors.append("数据中搜索时间为空")
        else:
            if supportTimePreciseMatch:
                # 尝试精确时间匹配，任一时间匹配即可
                any_match = False
                for item_search_datetime in item_search_datetimes:
                    is_match, _ = _check_precise_search_time(
                        item_search_datetime, search_dates
                    )
                    if is_match:
                        any_match = True
                        break

                if not any_match:
                    datetime_strs = [
                        dt.strftime("%Y-%m-%d %H:00") for dt in item_search_datetimes
                    ]
                    req_dates_str = ",".join(
                        sorted(
                            [
                                (
                                    d.strftime("%Y-%m-%d %H:00")
                                    if isinstance(d, datetime)
                                    else d.strftime("%Y-%m-%d")
                                )
                                for d in search_dates
                            ]
                        )
                    )
                    errors.append(
                        f"搜索时间不匹配（请求值：{req_dates_str}，数据值：{','.join(datetime_strs)}）"
                    )
            else:
                # 日期匹配，任一日期匹配即可
                item_search_dates = [
                    dt.date() if isinstance(dt, datetime) else dt
                    for dt in item_search_datetimes
                ]
                any_match = any(date in search_dates for date in item_search_dates)

                if not any_match:
                    date_strs = [dt.strftime("%Y-%m-%d") for dt in item_search_dates]
                    req_dates_str = ",".join(
                        sorted([d.strftime("%Y-%m-%d") for d in search_dates])
                    )
                    errors.append(
                        f"搜索日期不匹配（请求值：{req_dates_str}，数据值：{','.join(date_strs)}）"
                    )

    # 起飞日期检查
    if depart_dates:
        # 从多个字段收集起飞日期
        depart_fields = ["departureDate", "depdate"]
        item_depart_dates = []

        for field in depart_fields:
            field_value = item.get(field)
            if field_value:
                parsed_date = safe_parse_date(field_value, "%Y-%m-%d")
                if parsed_date:
                    item_depart_dates.append(parsed_date)

        if not item_depart_dates:
            errors.append("数据中起飞日期为空")
        else:
            # 任一日期匹配即可
            any_match = any(date in depart_dates for date in item_depart_dates)
            if not any_match:
                date_strs = [dt.strftime("%Y-%m-%d") for dt in item_depart_dates]
                req_dates_str = ",".join(
                    sorted([d.strftime("%Y-%m-%d") for d in depart_dates])
                )
                errors.append(
                    f"起飞日期不匹配（请求值：{req_dates_str}，数据值：{','.join(date_strs)}）"
                )

    return (len(errors) == 0, "; ".join(errors))


def _check_cities(item: dict, dep_city: str, arr_city: str) -> tuple:
    """城市代码检查（带值对比）"""
    errors = []
    item_dep = item.get("departureCity", "")
    item_arr = item.get("arrivalCity", "")

    if (
        not is_deep_empty(item_dep)
        and not is_deep_empty(dep_city)
        and item_dep != dep_city
    ):
        errors.append(f"出发城市不匹配（请求值：{dep_city}，数据值：{item_dep}）")
    if (
        not is_deep_empty(item_arr)
        and not is_deep_empty(arr_city)
        and item_arr != arr_city
    ):
        errors.append(f"到达城市不匹配（请求值：{arr_city}，数据值：{item_arr}）")

    return (len(errors) == 0, "; ".join(errors))


def _check_cities_name(item: dict, dep_city_name: str, arr_city_name: str) -> tuple:
    """城市名称检查（带值对比）"""
    errors = []
    itemDepArr = item.get("od", "")
    if is_deep_empty(itemDepArr) or not isinstance(itemDepArr, str):
        return (True, "")

    try:
        item_dep, item_arr = itemDepArr.split("-")
    except ValueError:
        return (True, "")  # Invalid format, skip validation

    if (
        not is_deep_empty(dep_city_name)
        and not is_deep_empty(item_dep)
        and item_dep not in dep_city_name
    ):
        errors.append(
            f"出发城市名称不匹配（请求值：{dep_city_name}，数据值：{item_dep}）"
        )
    if (
        not is_deep_empty(arr_city_name)
        and not is_deep_empty(item_arr)
        and item_arr not in arr_city_name
    ):
        errors.append(
            f"到达城市名称不匹配（请求值：{arr_city_name}，数据值：{item_arr}）"
        )

    return (len(errors) == 0, "; ".join(errors))


def _check_carrier(item: dict, carriers: set) -> tuple:
    """航司检查（带值对比）"""
    if carriers:
        # Get flight number from either flightNumber or flightkey field
        flight_number = item.get("flightNumber") or item.get("flightkey")

        if (
            not is_deep_empty(carriers)
            and flight_number
            and len(str(flight_number)) >= 2
        ):
            item_carrier = str(flight_number)[:2]
            if item_carrier not in carriers:
                return (
                    False,
                    f"航司不匹配（请求值：{str(carriers)}，数据值：{item_carrier}）",
                )
    return (True, "")


def _check_flight_number(item: dict, flight_numbers: set) -> tuple:
    """航班号检查（带值对比）"""
    if flight_numbers:
        item_flight = item.get("flightNumber") or item.get("flightkey")
        req_flights_str = ",".join(sorted(flight_numbers))

        if (
            not is_deep_empty(flight_numbers)
            and item_flight
            and len(item_flight) > 2
            and item_flight not in flight_numbers
        ):
            return (
                False,
                f"航班号不匹配（请求值：{req_flights_str}，数据值：{item_flight}）",
            )
    return (True, "")


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d:%H:%M",  # 新增格式支持 YYYY-MM-DD:HH:mm
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError, Exception) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None


def parse_and_validate_price_info(price_info: Any) -> Tuple[bool, str, Dict[str, Any]]:
    """
    校验价格信息的有效性
    Args:
        price_info: 价格信息字典
    Returns:
        校验后的价格信息字典
    """
    # 1. 整体校验
    if not price_info:
        return False, "价格信息为空", None
    if not isinstance(price_info, dict):
        return False, "价格信息类型错误", None

    result = {
        "userPriceType": price_info.get("userPriceType"),
        "precisePriceList": [],
        "durationPriceList": [],
    }

    # 2. 校验 precisePriceList
    precise_list = price_info.get("precisePriceList")
    if precise_list is not None:
        if isinstance(precise_list, list):
            for item in precise_list:
                if (
                    isinstance(item, dict)
                    and "price" in item
                    and is_valid_number(item.get("price"))
                ):
                    result["precisePriceList"].append(item)
        elif (
            isinstance(precise_list, dict)
            and "price" in precise_list
            and is_valid_number(precise_list.get("price"))
        ):
            result["precisePriceList"].append(precise_list)

    # 3. 校验 durationPriceList
    duration_list = price_info.get("durationPriceList")
    if duration_list is not None:
        if isinstance(duration_list, list):
            for item in duration_list:
                if isinstance(item, dict):
                    # 检查是否有 leftPrice 和 rightPrice
                    if "leftPrice" in item and "rightPrice" in item:
                        left_price = item.get("leftPrice")
                        right_price = item.get("rightPrice")
                        if (
                            is_valid_number(left_price)
                            and is_valid_number(right_price)
                            and left_price <= right_price
                        ):
                            result["durationPriceList"].append(item)
                    # 检查是否有 price
                    elif "price" in item and is_valid_number(item.get("price")):
                        result["precisePriceList"].append(item)
        elif isinstance(duration_list, dict):
            # 检查是否有 leftPrice 和 rightPrice
            if "leftPrice" in duration_list and "rightPrice" in duration_list:
                left_price = duration_list.get("leftPrice")
                right_price = duration_list.get("rightPrice")
                if (
                    is_valid_number(left_price)
                    and is_valid_number(right_price)
                    and left_price <= right_price
                ):
                    result["durationPriceList"].append(duration_list)
                # 检查是否有 price
            elif "price" in duration_list and is_valid_number(
                duration_list.get("price")
            ):
                result["precisePriceList"].append(duration_list)

    # 4. 检查是否有效
    if not result["precisePriceList"] and not result["durationPriceList"]:
        return False, "价格信息无效", None

    return True, "价格信息有效", result


def convertToNumeric(priceValue):
    """
    将字典中的 price 字段转为数值类型

    Args:
        priceValue: 要转换的价格值

    Returns:
        tuple: (是否转换成功, 转换后的值)
            - 如果转换成功，返回 (True, 转换后的数值)
            - 如果转换失败，返回 (False, 原始值)
    """
    try:
        if priceValue is None:
            return False, priceValue

        if isinstance(priceValue, (int, float)):
            # 如果已经是正数字，直接返回成功
            return True, priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                value = int(priceValue)
                return True, value
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                try:
                    price = float(priceValue)

                    # 如果是整数型浮点数（例如 "100.0" → 100）
                    if price.is_integer():
                        return True, int(price)
                    return True, price
                except:
                    return False, priceValue

        return False, priceValue
    except (ValueError, TypeError, AttributeError, Exception):
        # 捕获所有可能的转换异常，返回转换失败
        return False, priceValue


def checkIsPriceMatchWithDeviation(
    priceNumber, comparePriceList, deviationDuration=None
):
    """
    检查价格是否匹配比较价格列表中的任意一个价格，支持误差区间

    Args:
        priceNumber: 要检查的价格数值
        comparePriceList: 通过getPriceByPriceClassify获取的价格列表
        deviationDuration: 误差区间，格式如 {"minDeviation":-10, "maxDeviation": 10}
                          如果为None，则进行精准匹配

    Returns:
        tuple: (是否匹配, 匹配的价格项)
            - 如果匹配成功，返回 (True, 匹配的价格项)
            - 如果匹配失败，返回 (False, None)
            - 如果误差区间为None，则进行精准匹配
            - 如果comparePriceList为空，则返回True, None
    """
    # 首先确保priceNumber是数值类型
    success, price_value = convertToNumeric(priceNumber)
    if not success:
        return False, None

    # 如果comparePriceList为空，直接返回不匹配
    if (
        not comparePriceList
        or not isinstance(comparePriceList, list)
        or len(comparePriceList) == 0
    ):
        return True, None

    # 提取误差范围
    min_deviation = 0
    max_deviation = 0

    if deviationDuration is not None and isinstance(deviationDuration, dict):
        min_deviation = deviationDuration.get("minDeviation", 0)
        max_deviation = deviationDuration.get("maxDeviation", 0)

        # 确保误差值是数值类型
        if isinstance(min_deviation, str):
            success, min_deviation = convertToNumeric(min_deviation)
            if not success:
                min_deviation = 0
            if min_deviation > 0:
                min_deviation = 0

        if isinstance(max_deviation, str):
            success, max_deviation = convertToNumeric(max_deviation)
            if not success:
                max_deviation = 0
            if max_deviation < 0:
                max_deviation = 0

    # 遍历比较价格列表，判断是否匹配
    for price_item in comparePriceList:
        # 判断精准价格匹配 (含误差)
        if "price" in price_item:
            compare_price = price_item["price"]

            # 应用误差范围
            lower_bound = compare_price + min_deviation
            upper_bound = compare_price + max_deviation

            # 检查价格是否在允许误差的范围内
            if lower_bound <= price_value <= upper_bound:
                return True, price_item

        # 判断区间价格匹配 (含误差)
        if "leftPrice" in price_item and "rightPrice" in price_item:
            left_price = price_item["leftPrice"]
            right_price = price_item["rightPrice"]

            # 应用误差范围扩展区间
            left_with_deviation = left_price + min_deviation
            right_with_deviation = right_price + max_deviation

            # 检查价格是否在扩展的区间内
            if left_with_deviation <= price_value <= right_with_deviation:
                return True, price_item

    # 遍历完所有价格项都没有匹配成功，返回不匹配
    return False, None


def getPriceByPriceClassify(param, indexKey, priceClassifyList):
    """
    获取按照价格分类的价格信息

    Args:
        param: 包含价格参数的字典
        indexKey: 通常是'preSurPriceGroups'
        priceClassifyList: 价格分类类型列表，可以包含'prePrice', 'surPrice', 'changePrice'

    Returns:
        列表，包含符合条件的价格信息
    """
    result = []

    # 检查参数有效性
    if not param or indexKey not in param:
        return result

    group_list = param.get(indexKey)
    if not group_list or not isinstance(group_list, list):
        return result

    # 将单个字符串转换为列表处理
    if isinstance(priceClassifyList, str):
        priceClassifyList = [priceClassifyList]

    # 确保priceClassifyList是列表
    if not isinstance(priceClassifyList, list):
        return result

    # 遍历每个价格分类类型
    for priceClassify in priceClassifyList:
        # 根据分类确定要处理的精准价格和区间价格列表名称
        precise_key = ""
        duration_key = ""

        if priceClassify == "prePrice":
            precise_key = "prePrecisePriceList"
            duration_key = "preDurationPriceList"
        elif priceClassify == "surPrice":
            precise_key = "surPrecisePriceList"
            duration_key = "surDurationPriceList"
        elif priceClassify == "changePrice":
            precise_key = "changePrecisePriceList"
            duration_key = "changeDurationPriceList"
        else:
            continue  # 跳过不支持的价格分类

        for group in group_list:
            # 处理精准价格
            if precise_key in group and isinstance(group[precise_key], list):
                for price_info in group[precise_key]:
                    try:
                        if "price" in price_info:
                            # 使用改进的转换函数
                            success, price_value = convertToNumeric(price_info["price"])
                            if success:
                                price_data = {
                                    "priceType": priceClassify + "Precise",
                                    "parseType": price_info.get("parseType", "精准"),
                                    "price": price_value,
                                }
                                result.append(price_data)
                    except (ValueError, AttributeError, TypeError, Exception):
                        # 跳过无效的价格条目
                        pass

            # 处理区间价格
            if duration_key in group and isinstance(group[duration_key], list):
                for price_info in group[duration_key]:
                    try:
                        if "leftPrice" in price_info and "rightPrice" in price_info:
                            # 处理左右价格值，使用改进的转换函数
                            left_success, left_price = convertToNumeric(
                                price_info["leftPrice"]
                            )
                            right_success, right_price = convertToNumeric(
                                price_info["rightPrice"]
                            )

                            # 只有左右价格都转换成功才添加到结果
                            if left_success and right_success:
                                price_data = {
                                    "priceType": priceClassify + "Duration",
                                    "parseType": price_info.get(
                                        "parseType", "模糊的区间价格"
                                    ),
                                    "leftPrice": left_price,
                                    "rightPrice": right_price,
                                }
                                result.append(price_data)
                    except (ValueError, AttributeError, TypeError, Exception):
                        # 跳过无效的价格条目
                        pass

    return result


def search_flight_case(username: str, searchDate: str, dptDate: str) -> dict:
    url = "http://paoding.corp.qunar.com/open/case/mainSearch"

    params = {
        "flightType": "SINGLE",
        "keywordType": "USERNAME",
        "keyword": username,
        "channelKey": "App",
        "searchDate": searchDate or "",
        "dptDate": dptDate or "",
        "qTracePrefixes": "ops_slugger,f_athena_gateway",
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    response = requests.get(url, params=params, headers=headers)

    return response.json()


def filtrByHiveSearchEventDatas(param, paoDingAllDatas, hivePriceCompareDatas):
    """
    根据hive数据中的qtraceid过滤庖丁数据

    Args:
        param: 请求参数
        paoDingAllDatas: 庖丁返回的所有数据
        hivePriceCompareDatas: hive查询的变价数据

    Returns:
        list: 过滤后的庖丁数据列表
    """
    hiveGroupByTraceDatas = group_hive_data_by_qtrace(hivePriceCompareDatas)
    # 1. 收集所有qtraceid
    qtrace_ids = set(hiveGroupByTraceDatas.keys())
    if not qtrace_ids:
        print("[Warning] 未从hive数据中找到任何有效的qtraceid")
        return []

    # print(f"[Info] 总共收集到 {len(qtrace_ids)} 个唯一qtraceid")

    # 2. 过滤庖丁数据
    filtered_results = []
    for item in paoDingAllDatas:
        paoding_qtrace = item.get(
            "qtraceId"
        )  # 注意：庖丁数据中是qTraceId而不是qtraceid

        if not paoding_qtrace:
            # print(f"[Debug] 跳过无qTraceId的庖丁记录")
            continue

        if paoding_qtrace in qtrace_ids:
            item["relateTargetFlights"] = hiveGroupByTraceDatas[paoding_qtrace]
            encoded_str, error = json_to_urlencoded(
                hiveGroupByTraceDatas[paoding_qtrace]
            )
            if encoded_str:
                item["relateTargetFlightJson"] = encoded_str
            else:
                item["relateTargetFlightJson"] = ""
            filtered_results.append(item)
            # print(f"[Debug] 匹配到qTraceId: {paoding_qtrace}")
        # else:
        # print(f"[Debug] 未匹配的qTraceId: {paoding_qtrace}")

    # 3. 输出过滤结果统计
    # match_rate = (
    #     len(filtered_results) / len(paoDingAllDatas) * 100 if paoDingAllDatas else 0
    # )
    # print(f"[Info] 过滤结果统计:")
    # print(f"  - 原始庖丁数据数量: {len(paoDingAllDatas)}")
    # print(f"  - 过滤后数据数量: {len(filtered_results)}")
    # print(f"  - 匹配率: {match_rate:.2f}%")

    return filtered_results


def filterPaodingEventsByRevisitParam(param, paodingDatas):
    """
    Parse revisitParam from param and filter paoding data using filter_flight_data
    """
    revisit_param = param.get("revisitParam")
    if not revisit_param:
        return paodingDatas

    # Safely parse JSON from revisitParam
    request_params = safe_json_parse(revisit_param, {})
    if not request_params:
        return paodingDatas

    # Apply filter_flight_data with parsed params
    timePreciseMatch = param.get("timePreciseMatch")
    supportTimePreciseMatch = False
    if timePreciseMatch and timePreciseMatch == "true":
        supportTimePreciseMatch = True
    matched_data, not_matched_data = filter_flight_data(
        request_params, paodingDatas, supportTimePreciseMatch=supportTimePreciseMatch
    )
    if not matched_data or len(matched_data) == 0:
        return paodingDatas

    return matched_data


def safe_convert_to_number(value: Any) -> Optional[Union[int, float]]:
    """
    安全地将输入值转换为数字（整数或浮点数）

    Args:
        value: 要转换的值，可以是字符串、整数、浮点数等

    Returns:
        Optional[Union[int, float]]: 转换后的数字，如果转换失败则返回None

    Examples:
        >>> safe_convert_to_number("123")
        123
        >>> safe_convert_to_number("123.45")
        123.45
        >>> safe_convert_to_number("abc")
        None
        >>> safe_convert_to_number(None)
        None
    """
    if value is None:
        return None

    # 如果已经是数字类型，直接返回
    if isinstance(value, (int, float)) and not isinstance(value, bool):
        return value

    # 如果是字符串，尝试转换
    if isinstance(value, str):
        # 移除空白字符
        value = value.strip()
        try:
            # 尝试转换为整数
            if value.isdigit():
                return int(value)
            # 尝试转换为浮点数
            return float(value)
        except (ValueError, TypeError):
            return None

    return None


def group_hive_data_by_qtrace(hive_data: List[dict]) -> Dict[str, Dict[str, List[str]]]:
    """
    按照qtraceid和last_qtraceid分组提取航班信息

    Args:
        hive_data: hive查询结果数据列表

    Returns:
        Dict[str, Dict[str, List[str]]]: 按qtraceid分组的航班信息
        格式如：{
            "qtraceid": {
                "flightNos": ["航班号列表"],
                "searchTime": ["搜索时间列表"],
                "depArrCity": ["出发到达城市列表"],
                "prices": ["价格列表"]
            }
        }
    """
    result = {}

    for item in hive_data:
        # 获取当前和历史qtraceid
        current_qtrace = item.get("qtraceid")
        last_qtrace = item.get("last_qtraceid")
        showprice = item.get("showprice", "")
        last_showprice = item.get("last_showprice", "")

        # 提取需要的信息
        flight_key = item.get("flightkey", "")
        search_time = item.get("search_time", "")
        last_search_time = item.get("last_search_time", "")
        od = item.get("od", "")  # 出发到达城市

        # 处理当前qtraceid的数据
        if current_qtrace:
            if current_qtrace not in result:
                result[current_qtrace] = {
                    "flightNos": [],
                    "searchTime": [],
                    "depArrCity": [],
                    "prices": [],
                }

            if flight_key and flight_key not in result[current_qtrace]["flightNos"]:
                result[current_qtrace]["flightNos"].append(flight_key)
            if search_time and search_time not in result[current_qtrace]["searchTime"]:
                result[current_qtrace]["searchTime"].append(search_time)
            if od and od not in result[current_qtrace]["depArrCity"]:
                result[current_qtrace]["depArrCity"].append(od)
            if showprice and showprice not in result[current_qtrace]["prices"]:
                result[current_qtrace]["prices"].append(showprice)

        # 处理历史qtraceid的数据
        if last_qtrace:
            if last_qtrace not in result:
                result[last_qtrace] = {
                    "flightNos": [],
                    "searchTime": [],
                    "depArrCity": [],
                    "prices": [],
                }

            if flight_key and flight_key not in result[last_qtrace]["flightNos"]:
                result[last_qtrace]["flightNos"].append(flight_key)
            if (
                last_search_time
                and last_search_time not in result[last_qtrace]["searchTime"]
            ):
                result[last_qtrace]["searchTime"].append(last_search_time)
            if od and od not in result[last_qtrace]["depArrCity"]:
                result[last_qtrace]["depArrCity"].append(od)
            if last_showprice and last_showprice not in result[last_qtrace]["prices"]:
                result[last_qtrace]["prices"].append(last_showprice)

    return result


def filterHiveDataByTargetPrice(
    revisitOrSurveyParam, hivePriceCompareDatas, deviationDuration=None
):
    """
    Filter paoding data based on revisitPriceInfo price ranges
    """
    prePrice = getPriceByPriceClassify(
        revisitOrSurveyParam, "preSurPriceGroups", "prePrice"
    )
    surPrice = getPriceByPriceClassify(
        revisitOrSurveyParam, "preSurPriceGroups", "surPrice"
    )

    if not prePrice and not surPrice:
        return hivePriceCompareDatas

    filtered_results = []
    for item in hivePriceCompareDatas:
        # 安全转换价格为数字
        showprice = safe_convert_to_number(item.get("showprice"))
        last_showprice = safe_convert_to_number(item.get("last_showprice"))

        if showprice is None or last_showprice is None:
            continue

        # Check precise price list
        lastPriceHit, _ = checkIsPriceMatchWithDeviation(
            last_showprice, prePrice, deviationDuration
        )
        curPriceHit, _ = checkIsPriceMatchWithDeviation(
            showprice, surPrice, deviationDuration
        )
        if lastPriceHit and curPriceHit:
            filtered_results.append(item)

    return filtered_results


def queryAndFilterPaodingSearchEventDatas(param, hivePriceCompareDatas):
    try:
        # Query Paoding for each date combination
        result = search_flight_case(
            username=param["username"], searchDate=None, dptDate=None
        )
        if result is None:
            return {"error": "庖丁查询搜索事件数据失败", "results": []}

        if result.get("data") is None:
            return {"error": "庖丁查询搜索事件数据为空", "results": []}
        if not (
            result.get("data")
            and isinstance(result.get("data"), list)
            and len(result.get("data")) > 0
        ):
            return {"error": "庖丁查询搜索事件数据为空", "results": []}

        paoDingAllDatas = result.get("data")
        filtered_results = filtrByHiveSearchEventDatas(
            param, paoDingAllDatas, hivePriceCompareDatas
        )
        if not filtered_results or len(filtered_results) == 0:
            return {
                "error": "庖丁查询搜索事件不存在匹配hive变价的搜索记录， 请检查paoding hive数据是否有gap",
                "results": [],
            }

        filtered_results = filterPaodingEventsByRevisitParam(param, filtered_results)

        return {"error": None, "results": filtered_results}
    except Exception as e:
        return {"error": str(e), "results": []}


def queryHivePriceCompareDatas(param):
    try:
        # query = json.loads(param.get("query"))
        searchTimeDurationLimit = param.get("searchTimeDurationLimit")
        listSearchDurationLimit = convert_to_seconds(searchTimeDurationLimit)
        sql = buildSqlByQuery(param.get("ursDate"), param.get("username"), param)
        # print(sql)
        oriDataResult = queryDataFromTamias(param.get("cookie"), sql)
        if oriDataResult.get("error"):
            oriDataResult["results"] = []
            oriDataResult["needExecAl"] = "否"
            oriDataResult["notExecAlReason"] = (
                f"hive未查到用户单程变价数据, {oriDataResult.get('error')}"
            )
            oriDataResult["processStage"] = "URS-hive查询用户单程变价数据"
            oriDataResult["flightCompareTraceIds"] = {}
            return oriDataResult

        oriPriceCompareDatas = oriDataResult.get("results")

        if not oriPriceCompareDatas or len(oriPriceCompareDatas) == 0:
            return {
                "error": "hive未查到用户单程变价数据",
                "results": [],
                "flightCompareTraceIds": {},
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAl": "否",
                "notExecAlReason": "hive未查到用户单程变价数据",
            }

        filtered_datas = oriPriceCompareDatas
        if listSearchDurationLimit:
            # 过滤搜索时间间隔大于listSearchDurationLimit的记录
            filtered_datas = []
            for item in oriPriceCompareDatas:
                search_time = safe_parse_datetime(item.get("search_time"))
                last_search_time = safe_parse_datetime(item.get("last_search_time"))

                if not search_time or not last_search_time:
                    continue

                # 计算时间差（秒）
                time_diff = (search_time - last_search_time).total_seconds()

                if time_diff <= listSearchDurationLimit:
                    filtered_datas.append(item)
                else:
                    # print(
                    #     f"[Debug] 过滤掉搜索间隔过大的记录: search_time={search_time}, last_search_time={last_search_time}, diff={time_diff}秒"
                    # )
                    continue

        if not filtered_datas or len(filtered_datas) == 0:
            return {
                "error": f"hive查询到都是搜索时间间隔大于{listSearchDurationLimit}秒的记录",
                "results": [],
                "flightCompareTraceIds": {},
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAl": "否",
                "notExecAlReason": f"hive查询到都是搜索时间间隔大于{listSearchDurationLimit}秒的记录",
            }

        ursSubmitTime = param.get("ursSubmitTime")
        removeAfterSubmitRecord = param.get(
            "removeAfterSubmitRecord"
        ) and "true" == param.get("removeAfterSubmitRecord")
        if ursSubmitTime and removeAfterSubmitRecord:
            ursSubmitTime = safe_parse_datetime(ursSubmitTime)
            if ursSubmitTime:
                filtered_datas = [
                    item
                    for item in filtered_datas
                    if safe_parse_datetime(item.get("search_time"))
                    and safe_parse_datetime(item.get("last_search_time"))
                    and safe_parse_datetime(item.get("search_time")) <= ursSubmitTime
                    and safe_parse_datetime(item.get("last_search_time"))
                    <= ursSubmitTime
                ]
        if not filtered_datas or len(filtered_datas) == 0:
            return {
                "error": f"hive过滤掉URS填写后的数据后无变价记录:{param.get('ursSubmitTime')}",
                "results": [],
                "flightCompareTraceIds": {},
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAl": "否",
                "notExecAlReason": f"hive过滤掉URS填写后的数据后无变价记录:{param.get('ursSubmitTime')}",
            }

        # filtered_datas = filterHiveDataByRevisitPriceInfo(param, filtered_datas)
        filtered_datas = filterHiveDataByRevisitParam(param, filtered_datas)

        filtered_datas = filterHiveDataByAiConversationParam(param, filtered_datas)
        return {
            "error": None,
            "results": filtered_datas,
            "flightCompareTraceIds": {},
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAl": "是",
            "notExecAlReason": "",
        }
    except Exception as e:
        # 获取完整的异常堆栈信息
        stack_trace = traceback.format_exc()
        error_msg = (
            f"hive查询搜索有变价的搜索记录异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        )
        return {
            "error": error_msg,
            "results": [],
            "flightCompareTraceIds": {},
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAl": "否",
            "notExecAlReason": error_msg,
        }


def validateAiContextParam(serveyAIParam: str) -> dict:
    """
    验证AI参数的有效性

    Args:
        aiParam: AI参数字符串(JSON格式)

    Returns:
        dict: 包含验证结果的字典
        {
            "isValid": bool,  # 是否有效
            "errMsg": str,    # 错误信息
            "serveyAIParam": dict  # 解析后的AI参数
        }
    """
    # 初始化返回结果
    result = {"isValid": False, "errMsg": "", "serveyAIParam": None}

    if not serveyAIParam:
        result["errMsg"] = "AI参数为空"
        return result

    # 检查isValidSession是否为"是"
    if serveyAIParam.get("isValidSession") != "是":
        result["errMsg"] = "AI会话无效"
        return result

    # 检查必要字段是否全部为空
    required_fields = ["departureCityName", "departureDateList", "arrivalCityName"]
    field_values = [serveyAIParam.get(field) for field in required_fields]

    # 如果所有必要字段都为空，则返回无效
    if all(is_deep_empty(value) for value in field_values):
        result["errMsg"] = "必要字段(出发城市、出发日期、到达城市)全部为空"
        return result

    # 验证通过
    result["isValid"] = True
    result["serveyAIParam"] = serveyAIParam
    return result


def parseAndValidateAiParam(param: dict) -> dict:
    """
    解析并验证AI参数的有效性

    Args:
        param: 包含surveyAIContext的参数字典

    Returns:
        dict: 包含验证结果的字典
        {
            "isValid": bool,  # 是否有效
            "errMsg": str,    # 错误信息
            "serveyAIParam": dict  # 解析后的AI参数
        }
    """
    # 初始化返回结果
    result = {"isValid": False, "errMsg": "", "serveyAIParam": None}

    # 1. 检查surveyAIContext是否存在且非空
    surveyAIContext = param.get("surveyAIContext")
    if is_deep_empty(surveyAIContext):
        result["errMsg"] = "surveyAIContext为空"
        return result

    # 2. 使用safe_json_parse解析surveyAIContext
    serveyAIParam = safe_json_parse(surveyAIContext)
    if not serveyAIParam:
        result["errMsg"] = "surveyAIContext解析失败"
        return result

    # 3. 调用validateAiParam验证参数有效性
    validation_result = validateAiContextParam(serveyAIParam)

    # 4. 返回验证结果
    return validation_result


def convert_to_flight_time_format(time_str: str) -> str:
    """
    将时间格式从 YYYY-MM-DD:HH:mm 转换为 YYYY-MM-DD HH:mm

    Args:
        time_str: 输入的时间字符串

    Returns:
        str: 转换后的时间字符串，格式为 YYYY-MM-DD HH:mm
    """
    if not time_str:
        return time_str

    # 使用safe_parse_datetime解析时间字符串
    dt = safe_parse_datetime(time_str)
    if dt:
        # 如果成功解析，则格式化为标准格式
        return dt.strftime("%Y-%m-%d %H:%M")

    return time_str


def canUseUrsPageSameFlightFilter(
    serveyAIParam: dict,
    hiveData: dict,
    usePageSameFlightLogic: str,
) -> tuple[bool, str]:
    """
    判断是否可以使用URS填写页同航班过滤

    Args:
        serveyAIParam: AI参数字典
        hiveData: hive数据字典
        usePageSameFlightLogic: 是否使用URS填写页同航班过滤

    Returns:
        tuple[bool, str]: (是否可以使用同航班过滤, 错误信息)
    """
    if usePageSameFlightLogic != "true":
        return False, ""

    # 1. 获取urs_depAndArrTime
    urs_depAndArrTime = hiveData.get("urs_depAndArrTime")

    # 2. 检查urs_depAndArrTime是否为空
    if is_deep_empty(urs_depAndArrTime):
        return False, "urs_depAndArrTime为空"

    # 3. 解析起飞时间
    ursPageDepTime = ""
    try:
        ursPageDepTime = urs_depAndArrTime.split("_")[0]
        # 转换时间格式
        ursPageDepTime = convert_to_flight_time_format(ursPageDepTime)
    except (IndexError, AttributeError):
        return False, f"urs_depAndArrTime格式错误: {urs_depAndArrTime}"

    # 4. 检查serveyAIParam条件
    # 检查flightMatchFisrtQA是否为"是"
    if serveyAIParam.get("flightMatchFisrtQA") != "是":
        return False, "flightMatchFisrtQA不为'是'"

    # 检查必要字段是否都不为空
    required_fields = ["departureCityName", "departureDateList", "arrivalCityName"]
    for field in required_fields:
        if is_deep_empty(serveyAIParam.get(field)):
            return False, f"AI助手问答解析出的字段：{field}为空"

    # 5. 对比起飞时间
    departureDateList = serveyAIParam.get("departureDateList")
    # 如果departureDateList是字符串，转换为列表
    if isinstance(departureDateList, str):
        departureDateList = [departureDateList]

    # 检查是否有任何一个时间匹配
    for departure_time in departureDateList:
        if convert_to_flight_time_format(departure_time) == ursPageDepTime:
            return True, ""

    return (
        False,
        f"起飞时间不匹配: urs页面航班时间={ursPageDepTime}, AIAI助手问答解析出航班时间={departureDateList}",
    )


def matchHiveDataAndServeyAIParam(
    serveyAIParam: dict,
    hiveData: dict,
    usePageSameFlightLogic: str,
) -> tuple[bool, str]:
    """
    判断hiveData和serveyAIParam是否匹配

    Args:
        serveyAIParam: AI参数字典
        hiveData: hive数据字典

    Returns:
        tuple[bool, str]: (是否匹配, 错误信息)
    """
    # 1. 首先判断是否可以走urs提交页面同航班过滤
    can_use_same_flight, err_msg = canUseUrsPageSameFlightFilter(
        serveyAIParam, hiveData, usePageSameFlightLogic
    )

    if can_use_same_flight:
        # 获取航班号进行匹配
        urs_flight_nos = hiveData.get("urs_flightNos")
        flight_key = hiveData.get("flightkey")

        # 如果任一航班号为空，返回不匹配
        if is_deep_empty(urs_flight_nos) or is_deep_empty(flight_key):
            return False, "航班号为空"

        # 检查航班号是否匹配
        if urs_flight_nos != flight_key:
            return (
                False,
                f"航班号不匹配: urs页面航班={urs_flight_nos}, hive数据航班={flight_key}",
            )

    # 2. 调用matchSingleTargetData判断是否匹配
    is_match, match_err_msg = matchSingleTargetData(serveyAIParam, hiveData)

    # 3. 返回匹配结果
    return is_match, match_err_msg


def filterHiveDataByAiConversationParam(
    param: dict, hiveDatas: List[dict]
) -> List[dict]:
    """
    根据AI会话参数过滤hive数据

    Args:
        param: 请求参数字典
        filtered_datas: 已过滤的hive数据列表

    Returns:
        List[dict]: 过滤后的数据列表
    """
    if hiveDatas is None or len(hiveDatas) == 0:
        return hiveDatas

    usePageSameFlightLogic = param.get("usePageSameFlightLogic", "false")

    # 1. 解析和验证AI参数
    validation_result = parseAndValidateAiParam(param)
    if not validation_result["isValid"]:
        # AI参数无效，返回原始数据
        return hiveDatas

    serveyAIParam = validation_result["serveyAIParam"]
    matched_datas = []

    # 2. 遍历数据进行匹配
    for hiveData in hiveDatas:
        is_match, _ = matchHiveDataAndServeyAIParam(
            serveyAIParam, hiveData, usePageSameFlightLogic
        )
        if is_match:
            matched_datas.append(hiveData)

    # 3. 过滤目标价格
    price_filtered_datas = filterHiveDataByTargetPrice(serveyAIParam, matched_datas)
    if price_filtered_datas and len(price_filtered_datas) != 0:
        return price_filtered_datas

    # 如果没有匹配的数据，返回原始数据
    return matched_datas if matched_datas else hiveDatas


def filterHiveDataByRevisitParam(param, hiveDatas):
    """
    Filter hive data based on revisitParam criteria
    """
    revisit_param = param.get("revisitParam")
    if not revisit_param:
        return hiveDatas

    request_params = safe_json_parse(revisit_param, {})
    if not request_params:
        return hiveDatas

    timePreciseMatch = param.get("timePreciseMatch")
    supportTimePreciseMatch = False
    if timePreciseMatch and timePreciseMatch == "true":
        supportTimePreciseMatch = True
    matched_data, not_matched_data = filter_flight_data(
        request_params, hiveDatas, supportTimePreciseMatch
    )
    if not matched_data or len(matched_data) == 0:
        return hiveDatas

    # 过滤目标价格
    filtered_data = filterHiveDataByTargetPrice(revisit_param, matched_data)
    if filtered_data and len(filtered_data) != 0:
        return filtered_data

    return matched_data


def extract_carrier_from_flight_no(flight_no: str) -> str:
    """
    从航班号中提取航司代码（前两位字母）

    Args:
        flight_no: 航班号

    Returns:
        str: 航司代码
    """
    if not flight_no or len(flight_no) < 2:
        return ""
    return flight_no[:2].upper()


def parse_carriers_from_flight_nos(flight_nos: str) -> List[str]:
    """
    从航班号字符串中解析所有航司代码

    Args:
        flight_nos: 航班号字符串，可能包含多个航班号，用/分隔

    Returns:
        List[str]: 航司代码列表
    """
    if not flight_nos:
        return []

    carriers = []
    # 分割可能的多段航班
    for flight_no in flight_nos.split("/"):
        tempCarrier = []
        carrier = extract_carrier_from_flight_no(flight_no.strip())
        tempCarrier.append(carrier)
        if tempCarrier:
            carriers.append("/".join(tempCarrier))
    return carriers


def get_nearest_search_time(
    search_times: List[str], urs_submit_time: str
) -> Optional[datetime]:
    """
    获取最接近URS提交时间的搜索时间

    Args:
        search_times: 搜索时间列表
        urs_submit_time: URS提交时间

    Returns:
        Optional[datetime]: 最接近的搜索时间，如果无法解析则返回None
    """
    if not search_times or not urs_submit_time:
        return None

    urs_time = safe_parse_datetime(urs_submit_time)
    if not urs_time:
        return None

    parsed_times = []
    for time_str in search_times:
        dt = safe_parse_datetime(time_str)
        if dt:
            parsed_times.append(dt)

    if not parsed_times:
        return None

    # 找到时间差最小的搜索时间
    return min(parsed_times, key=lambda x: abs((x - urs_time).total_seconds()))


def checkAndCutPaodingDataSize(
    param: dict, paodingEvents: List[dict]
) -> Tuple[List[dict], str]:
    """
    检查并剪裁庖丁事件数据

    Args:
        param: 参数字典
        paodingEvents: 庖丁事件数据列表

    Returns:
        Tuple[List[dict], str]: (过滤后的数据列表, 剪枝说明)
    """
    if not paodingEvents:
        return [], "符合条件的庖丁数据为空，无需裁剪"

    record_limits = int(param.get("recordLimits", 50))
    survey_info = param.get("surveySubmitPageInfo", {})
    urs_submit_time = param.get("ursSubmitTime")

    cut_desc = []
    current_events = paodingEvents
    if paodingEvents and len(paodingEvents) <= record_limits:
        return paodingEvents, "数据量未超限，无需裁剪"

    # Step 1: 使用surveySubmitPageInfo进行过滤
    if survey_info and len(current_events) > record_limits:
        carrier = survey_info.get("carrier")
        if carrier:
            # Step 1a: 按航司过滤
            filtered_by_carrier = []
            for event in current_events:
                target_flights = event.get("relateTargetFlights", {})
                flight_nos = target_flights.get("flightNos", [])

                # 解析所有航班号中的航司
                event_carriers = set()
                for flight_no in flight_nos:
                    event_carriers.update(parse_carriers_from_flight_nos(flight_no))

                # 如果有任何一个航司匹配，保留该记录
                if carrier.upper() in event_carriers:
                    filtered_by_carrier.append(event)

            if filtered_by_carrier:
                if len(filtered_by_carrier) <= record_limits:
                    cut_desc.append(
                        f"航司过滤,是,{len(current_events)-len(filtered_by_carrier)},过滤后记录数在限制范围内"
                    )
                    return filtered_by_carrier, ";".join(cut_desc)
                current_events = filtered_by_carrier
                cut_desc.append(
                    f"航司过滤,是,{len(paodingEvents)-len(current_events)},过滤后记录数仍超限"
                )
            else:
                cut_desc.append(f"航司过滤,否,0,过滤后无数据")

        # Step 1b: 如果航司过滤后仍超限，按具体航班号过滤
        if len(current_events) > record_limits:
            target_flight_nos = survey_info.get("flightNos", "")
            if target_flight_nos:
                filtered_by_flight = []
                for event in current_events:
                    target_flights = event.get("relateTargetFlights", {})
                    event_flight_nos = target_flights.get("flightNos", [])

                    # 如果有任何一个航班号匹配，保留该记录
                    if target_flight_nos in event_flight_nos:
                        filtered_by_flight.append(event)

                if filtered_by_flight:
                    cut_desc.append(
                        f"航班号过滤,是,{len(current_events)-len(filtered_by_flight)},"
                    )
                    current_events = filtered_by_flight
                else:
                    cut_desc.append(f"航班号过滤,否,0,过滤后无数据")

    # Step 2: 如果仍然超限，按时间最近排序取前N条
    if len(current_events) > record_limits and urs_submit_time:
        # 为每个事件计算最近的搜索时间
        events_with_time = []
        for event in current_events:
            target_flights = event.get("relateTargetFlights", {})
            search_times = target_flights.get("searchTime", [])
            nearest_time = get_nearest_search_time(search_times, urs_submit_time)

            if nearest_time:
                events_with_time.append((event, nearest_time))

        # 按时间差排序
        if events_with_time:
            urs_time = safe_parse_datetime(urs_submit_time)
            events_with_time.sort(key=lambda x: abs((x[1] - urs_time).total_seconds()))
            # 确保只取record_limits条记录
            current_events = [event for event, _ in events_with_time[:record_limits]]
            removed_count = len(events_with_time) - record_limits
            cut_desc.append(
                f"时间排序截断,是,{removed_count},保留最近的{record_limits}条记录"
            )
        else:
            cut_desc.append(f"时间排序截断,否,0,无法解析时间")

    return current_events, ";".join(cut_desc)


def clean_time_string(time_str: str) -> str:
    """
    Remove decimal point and any data after it from time string

    Args:
        time_str: Input time string

    Returns:
        str: Cleaned time string
    """
    if not time_str:
        return time_str
    return time_str.split(".")[0]


def transform_hive_data(hive_data: List[dict]) -> List[dict]:
    """
    Transform hive data into the desired format with separate qtraceid and last_qtraceid entries

    Args:
        hive_data: List of dictionaries containing hive query results

    Returns:
        List of transformed dictionaries in the new format
    """
    transformed_data = []

    # Define flight type mapping
    flight_type_mapping = {"zf": "SINGLE", "zz": "CONNECT", "round": "PACKAGE"}

    for item in hive_data:
        # Parse departure and arrival cities from od field
        dep_city = ""
        arr_city = ""
        if item.get("od"):
            try:
                dep_city, arr_city = item["od"].split("-")
            except ValueError:
                pass

        # Get flight type from routetype_json
        flight_type = flight_type_mapping.get(item.get("routetype_json", ""), "SINGLE")

        # Create base structure
        base_structure = {
            "flightType": flight_type,
            "departureCity": dep_city,
            "arrivalCity": arr_city,
            "departureDate": item.get("depdate", ""),
            "flightNumber": item.get("flightkey", ""),
            "spanId": "",
            "listQTraceId": "",
            "username": item.get("user_name", ""),
            "matchQeTag": "匹配",
            "notMatchQeMsg": "匹配",
            "urs_flightNos": item.get("urs_flightNos", ""),
            "urs_depAndArrTime": item.get("urs_depAndArrTime", ""),
            "urs_page": item.get("urs_page", ""),
            "urs_routeType": item.get("urs_routeType", ""),
        }

        # Create relateTargetFlights structure
        relate_target_flights = {
            "flightPrices": [],
            "flightNos": [],
            "searchTime": [],
            "depArrCity": [],
            "prices": [],
        }

        # Add flight information if available
        if item.get("flightkey"):
            flight_info = {
                "flightNo": item["flightkey"],
                "priceList": [],
                "depArrCity": [],
                "relateTraceIds": [],  # Add relateTraceIds field
                "flightType": flight_type,  # Add flightType field
            }

            # Add depArrCity if available
            if item.get("od"):
                flight_info["depArrCity"].append(item["od"])

            relate_target_flights["flightPrices"].append(flight_info)
            relate_target_flights["flightNos"].append(item["flightkey"])

        # Add depArrCity if available
        if item.get("od"):
            relate_target_flights["depArrCity"].append(item["od"])

        # Create last trace entry (for last_qtraceid)
        if item.get("last_qtraceid"):
            last_entry = copy.deepcopy(base_structure)
            last_entry["id"] = generateId()
            last_entry["searchDateTime"] = clean_time_string(
                item.get("last_search_time", "")
            )
            last_entry["qtraceId"] = item.get("last_qtraceid")
            # Get source from last_qtraceid's type
            last_source = (
                item.get("type", "").split("-")[0].upper() if item.get("type") else ""
            )
            last_entry["source"] = last_source
            if last_source == "LIST":
                last_entry["flightNumber"] = ""

            # Create last trace relateTargetFlights
            last_relate = copy.deepcopy(relate_target_flights)
            if item.get("last_showprice"):
                last_relate["prices"].append(str(item["last_showprice"]))
                last_relate["flightPrices"][0]["priceList"].append(
                    str(item["last_showprice"])
                )
                # Add last_qtraceid to relateTraceIds
                last_relate["flightPrices"][0]["relateTraceIds"].append(
                    item["qtraceid"]
                )
            if item.get("last_search_time"):
                last_relate["searchTime"].append(
                    clean_time_string(item["last_search_time"])
                )

            last_entry["relateTargetFlights"] = last_relate
            transformed_data.append(last_entry)

        # Create current trace entry (for qtraceid)
        if item.get("qtraceid"):
            current_entry = copy.deepcopy(base_structure)
            current_entry["id"] = generateId()
            current_entry["searchDateTime"] = clean_time_string(
                item.get("search_time", "")
            )
            current_entry["qtraceId"] = item.get("qtraceid")
            # Get source from qtraceid's type
            current_source = (
                item.get("type", "").split("-")[1].upper() if item.get("type") else ""
            )
            current_entry["source"] = current_source
            if current_source == "LIST":
                current_entry["flightNumber"] = ""

            # Create current trace relateTargetFlights
            current_relate = copy.deepcopy(relate_target_flights)
            if item.get("showprice"):
                current_relate["prices"].append(str(item["showprice"]))
                current_relate["flightPrices"][0]["priceList"].append(
                    str(item["showprice"])
                )
                # Add qtraceid to relateTraceIds
                current_relate["flightPrices"][0]["relateTraceIds"].append(
                    item["last_qtraceid"]
                )
            if item.get("search_time"):
                current_relate["searchTime"].append(
                    clean_time_string(item["search_time"])
                )

            current_entry["relateTargetFlights"] = current_relate
            transformed_data.append(current_entry)

    return transformed_data


def merge_transformed_data(transformed_data: List[dict]) -> List[dict]:
    """
    Merge transformed data based on traceId, combining arrays and removing duplicates

    Args:
        transformed_data: List of transformed dictionaries

    Returns:
        List of merged dictionaries
    """
    # Group data by traceId
    grouped_data = {}
    for item in transformed_data:
        trace_id = item.get("qtraceId")
        if not trace_id:
            continue

        if trace_id not in grouped_data:
            grouped_data[trace_id] = []
        grouped_data[trace_id].append(item)

    # Merge each group
    merged_data = []
    for trace_id, group in grouped_data.items():
        if not group:
            continue

        # Use first item as base
        merged_item = group[0].copy()

        # Initialize merged arrays
        merged_arrays = {
            "flightNos": set(),
            "searchTime": set(),
            "depArrCity": set(),
            "prices": set(),
        }

        # Initialize flight prices dictionary
        flight_prices_dict = {}

        # Merge data from all items in group
        for item in group:
            target_flights = item.get("relateTargetFlights", {})

            # Merge arrays
            for key in merged_arrays:
                if key in target_flights:
                    merged_arrays[key].update(target_flights[key])

            # Merge flight prices
            for flight_price in target_flights.get("flightPrices", []):
                flight_no = flight_price.get("flightNo")
                if not flight_no:
                    continue

                if flight_no not in flight_prices_dict:
                    flight_prices_dict[flight_no] = {
                        "flightNo": flight_no,
                        "priceList": set(),
                        "depArrCity": set(),
                        "relateTraceIds": set(),
                        "flightType": flight_price.get("flightType", ""),
                    }

                # Merge arrays in flight price
                flight_prices_dict[flight_no]["priceList"].update(
                    flight_price.get("priceList", [])
                )
                flight_prices_dict[flight_no]["depArrCity"].update(
                    flight_price.get("depArrCity", [])
                )
                flight_prices_dict[flight_no]["relateTraceIds"].update(
                    flight_price.get("relateTraceIds", [])
                )

        # Convert sets back to lists and update merged item
        merged_target_flights = {
            "flightPrices": [],
            "flightNos": list(merged_arrays["flightNos"]),
            "searchTime": list(merged_arrays["searchTime"]),
            "depArrCity": list(merged_arrays["depArrCity"]),
            "prices": list(merged_arrays["prices"]),
        }

        # Convert flight prices dictionary to list
        for flight_price in flight_prices_dict.values():
            flight_price["priceList"] = list(flight_price["priceList"])
            flight_price["depArrCity"] = list(flight_price["depArrCity"])
            flight_price["relateTraceIds"] = list(flight_price["relateTraceIds"])
            merged_target_flights["flightPrices"].append(flight_price)

        merged_item["relateTargetFlights"] = merged_target_flights

        # Add relateTargetFlightJson field
        encoded_str, error = json_to_urlencoded(merged_target_flights)
        if encoded_str:
            merged_item["relateTargetFlightJson"] = encoded_str
        else:
            merged_item["relateTargetFlightJson"] = ""

        merged_data.append(merged_item)

    return merged_data


def groupFlightCompareTraceIds(
    hiveDatas: List[dict],
) -> Dict[str, List[Dict[str, str]]]:
    """
    按照航班号分组，每个分组包含相关的 last_qtraceid 和 qtraceid

    Args:
        hiveDatas: hive数据列表

    Returns:
        Dict[str, List[Dict[str, str]]]: 按航班号分组的trace IDs
        格式如：
        {
            "航班号1": [
                {"last_qtraceid": "xxx", "qtraceid": "yyy"},
                ...
            ],
            "航班号2": [
                {"last_qtraceid": "xxx", "qtraceid": "yyy"},
                ...
            ]
        }
    """
    result = {}
    if not hiveDatas:
        return result

    for data in hiveDatas:
        flight_key = data.get("flightkey")
        if not flight_key:
            continue

        last_qtraceid = data.get("last_qtraceid")
        qtraceid = data.get("qtraceid")

        if not last_qtraceid or not qtraceid:
            continue

        trace_info = {"last_qtraceid": last_qtraceid, "qtraceid": qtraceid}

        if flight_key not in result:
            result[flight_key] = []

        # 避免重复添加相同的trace信息
        if trace_info not in result[flight_key]:
            result[flight_key].append(trace_info)

    return result


def remove_down_price(datas: List[dict]) -> List[dict]:
    """
    Remove records where price has decreased
    """
    filtered_data = []
    for data in datas:
        showprice = safe_convert_to_number(data.get("showprice"))
        last_showprice = safe_convert_to_number(data.get("last_showprice"))

        if (
            showprice is not None
            and last_showprice is not None
            and showprice >= last_showprice
        ):
            filtered_data.append(data)
    return filtered_data


def checkAndCutHiveDataSize(
    param: dict, hiveDatas: List[dict]
) -> Tuple[List[dict], str]:
    """
    检查并剪裁hive数据

    Args:
        param: 参数字典
        hiveDatas: hive数据列表

    Returns:
        Tuple[List[dict], str]: (过滤后的数据列表, 剪枝说明)
    """
    if not hiveDatas:
        return [], "符合条件的hive数据为空，无需裁剪"

    record_limits = int(param.get("recordLimits", 50))
    survey_info = param.get("surveySubmitPageInfo", {})
    urs_submit_time = param.get("ursSubmitTime")

    cutDataSizeRemoveDownPrice = param.get("cutDataSizeRemoveDownPrice")

    cutFilterTimeDuration = param.get("cutFilterTimeDuration")
    cutFilterTimeDurationLimit = convert_to_seconds(cutFilterTimeDuration)

    cut_desc = []
    current_datas = hiveDatas
    if hiveDatas and len(hiveDatas) <= record_limits:
        return hiveDatas, "数据量未超限，无需裁剪"

    # Step 1: 使用surveySubmitPageInfo进行过滤
    if survey_info and len(current_datas) > record_limits:
        carrier = survey_info.get("carrier")
        if carrier:
            # Step 1a: 按航司过滤
            filtered_by_carrier = []
            for data in current_datas:
                flight_key = data.get("flightkey", "")
                if not flight_key:
                    continue

                # 解析航班号中的航司
                event_carriers = parse_carriers_from_flight_nos(flight_key)

                # 如果有任何一个航司匹配，保留该记录
                if carrier.upper() in event_carriers:
                    filtered_by_carrier.append(data)

            if filtered_by_carrier:
                if len(filtered_by_carrier) <= record_limits:
                    cut_desc.append(
                        f"航司过滤,是,{len(current_datas)-len(filtered_by_carrier)},过滤后记录数在限制范围内"
                    )
                    return filtered_by_carrier, ";".join(cut_desc)
                current_datas = filtered_by_carrier
                cut_desc.append(
                    f"航司过滤,是,{len(hiveDatas)-len(current_datas)},过滤后记录数仍超限"
                )
            else:
                cut_desc.append(f"航司过滤,否,0,过滤后无数据")

        # Step 1b: 如果航司过滤后仍超限，按具体航班号过滤
        if len(current_datas) > record_limits:
            target_flight_nos = survey_info.get("flightNos", "")
            if target_flight_nos:
                filtered_by_flight = []
                for data in current_datas:
                    flight_key = data.get("flightkey", "")
                    if flight_key == target_flight_nos:
                        filtered_by_flight.append(data)

                if filtered_by_flight:
                    cut_desc.append(
                        f"航班号过滤,是,{len(current_datas)-len(filtered_by_flight)},"
                    )
                    current_datas = filtered_by_flight
                else:
                    cut_desc.append(f"航班号过滤,否,0,过滤后无数据")

    if (
        cutDataSizeRemoveDownPrice
        and cutDataSizeRemoveDownPrice == "true"
        and len(current_datas) > record_limits
    ):
        filterByDownPrice = remove_down_price(current_datas)
        if filterByDownPrice:
            cut_desc.append(
                f"过滤价格下降数据,是,{len(current_datas)-len(filterByDownPrice)},"
            )
            current_datas = filterByDownPrice
        else:
            cut_desc.append(f"过滤价格下降数据,否,0,过滤后无数据")

    if (
        cutFilterTimeDurationLimit
        and cutFilterTimeDurationLimit > 0
        and len(current_datas) > record_limits
    ):
        filtered_datas = []
        for item in current_datas:
            search_time = safe_parse_datetime(item.get("search_time"))
            last_search_time = safe_parse_datetime(item.get("last_search_time"))

            if not search_time or not last_search_time:
                continue

            # 计算时间差（秒）
            time_diff = (search_time - last_search_time).total_seconds()

            if time_diff <= cutFilterTimeDurationLimit:
                filtered_datas.append(item)
            else:
                continue
        if filtered_datas:
            cut_desc.append(
                f"{cutFilterTimeDuration} 时间过滤,是,{len(current_datas)-len(filtered_datas)},"
            )
            current_datas = filtered_datas
        else:
            cut_desc.append(f"时间过滤,否,0,过滤后无数据")

    # Step 2: 如果仍然超限，按时间最近排序取前N条
    if len(current_datas) > record_limits and urs_submit_time:
        # 为每个数据计算搜索时间
        datas_with_time = []
        for data in current_datas:
            search_time = safe_parse_datetime(data.get("search_time"))
            if search_time:
                datas_with_time.append((data, search_time))

        # 按时间差排序
        if datas_with_time:
            urs_time = safe_parse_datetime(urs_submit_time)
            datas_with_time.sort(key=lambda x: abs((x[1] - urs_time).total_seconds()))
            # 确保只取record_limits条记录
            current_datas = [data for data, _ in datas_with_time[:record_limits]]
            removed_count = len(datas_with_time) - record_limits
            cut_desc.append(
                f"时间排序截断,是,{removed_count},保留最近的{record_limits}条记录"
            )
        else:
            cut_desc.append(f"时间排序截断,否,0,无法解析时间")

    return current_datas, ";".join(cut_desc)


def main(param):
    queryResult = queryHivePriceCompareDatas(param)
    if queryResult.get("error"):
        return queryResult

    hivePriceCompareDatas = queryResult.get("results")

    finalHiveComparseDatas, cutMsg = checkAndCutHiveDataSize(
        param, hivePriceCompareDatas
    )
    flightCompareTraceIds = groupFlightCompareTraceIds(finalHiveComparseDatas)
    # Transform the hive data into the new format
    transformedDatas = transform_hive_data(finalHiveComparseDatas)

    # Merge transformed data based on traceId
    mergedDatas = merge_transformed_data(transformedDatas)

    # paodingQueryResult = queryAndFilterPaodingSearchEventDatas(
    #    param, hivePriceCompareDatas
    # )
    # if paodingQueryResult.get("error"):
    #    error_msg = f"庖丁查询和过滤数据失败: {paodingQueryResult.get('error')}"
    #    return {
    #        "error": error_msg,
    #        "results": [],
    #        "processStage": "URS-hive查询用户单程变价数据",
    #        "needExecAl": "否",
    #        "notExecAlReason": error_msg,
    #    }

    # paodingSearchEventDatas = paodingQueryResult.get("results")
    # if not paodingSearchEventDatas or len(paodingSearchEventDatas) == 0:
    #    return {
    #        "error": "庖丁查询和过滤数据后为空",
    #        "results": [],
    #        "processStage": "URS-庖丁查询和过滤用户搜索事件",
    #        "needExecAl": "否",
    #        "notExecAlReason": "庖丁查询和过滤数据后为空",
    #    }

    # finalEvents, cutMsg = checkAndCutPaodingDataSize(param, paodingSearchEventDatas)
    # finalEvents, cutMsg = checkAndCutPaodingDataSize(param, mergedDatas)
    return {
        "error": None,
        "results": mergedDatas,
        "flightCompareTraceIds": flightCompareTraceIds,
        "processStage": "URS-hive查询有变价的用户搜索事件",
        "needExecAl": "是",
        "notExecAlReason": "",
        "dataCutMsg": cutMsg,
    }


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象安全地序列化为 JSON 并写入文件

    :param obj: 要序列化的对象，支持基础类型/列表/字典/自定义对象
    :param file_path: 目标文件路径（支持自动创建目录）
    :param encoding: 文件编码格式，默认utf-8
    :param ensure_ascii: 是否转义非ASCII字符，默认False保留Unicode
    :param indent: JSON缩进空格数，默认2（设为None可压缩输出）
    :param default: 自定义对象序列化函数，默认使用__dict__转换
    :param json_kwargs: 其他json.dump参数（如sort_keys等）
    :return: 是否成功写入文件

    :raises IOError: 当遇到文件系统级错误时会抛出（非返回False的情况）

    异常处理策略：
    - 类型错误：打印建议信息并返回False
    - 权限错误：打印错误路径并返回False
    - 其他错误：打印错误信息并返回False
    """
    try:
        target_path = Path(file_path)

        # 自动创建父目录（exist_ok防止竞态条件）
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的序列化
        serialize_default = default or (
            lambda o: o.__dict__ if hasattr(o, "__dict__") else repr(o)
        )

        # 使用上下文管理器确保文件正确关闭
        with target_path.open("w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(
            f"[序列化失败] 对象类型错误: {str(e)}，建议：1.检查数据类型 2.提供自定义序列化函数"
        )
    except PermissionError:
        print(f"[权限拒绝] 无法写入文件: {file_path}，请检查文件权限")
    except json.JSONEncodeError as e:
        print(f"[编码错误] 非法数据: {str(e)}，请检查特殊字符")
    except Exception as e:
        print(f"[系统错误] 操作异常: {str(e)}")

    return False


from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font


def write_dicts_to_excel(
    data: list[dict],
    filename: str = "output.xlsx",
    sheet_name: str = "Data",
    autofit: bool = True,
) -> None:
    """
    将字典列表写入Excel文件

    :param data: 字典数据列表，要求所有字典的键一致
    :param filename: 输出文件名（默认：output.xlsx）
    :param sheet_name: 工作表名称（默认：Data）
    :param autofit: 是否自动调整列宽（默认：True）
    """
    if not data:
        raise ValueError("输入数据不能为空列表")

    # 验证数据结构
    keys = data[0].keys()
    if any(d.keys() != keys for d in data[1:]):
        raise ValueError("字典字段名不一致")

    # 创建Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = sheet_name

    # 写入表头（带格式）
    header_font = Font(bold=True)
    for col_num, key in enumerate(keys, 1):
        cell = ws.cell(row=1, column=col_num, value=str(key))
        cell.font = header_font

    # 写入数据
    for row_num, record in enumerate(data, 2):  # 从第2行开始
        for col_num, key in enumerate(keys, 1):
            value = record.get(key, "")

            # 特殊类型处理
            if isinstance(value, datetime):
                value = value.replace(tzinfo=None)  # 移除时区信息
            elif isinstance(value, (list, dict)):
                value = str(value)  # 复杂对象转为字符串

            ws.cell(row=row_num, column=col_num, value=value)

    # 自动调整列宽
    if autofit:
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    cell_length = len(str(cell.value))
                    # 加粗表头需要额外长度补偿
                    if cell.row == 1 and cell.font.bold:
                        cell_length += 2
                    max_length = max(max_length, cell_length)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

    # 保存文件
    try:
        wb.save(filename)
        print(f"文件已成功保存至：{filename}")
    except PermissionError:
        raise RuntimeError("文件被其他程序占用，请关闭Excel后重试")
    except Exception as e:
        raise RuntimeError(f"保存文件时发生错误：{str(e)}")


if __name__ == "__main__":
    # 假设输入数据存储在input_data变量中
    param = {
        "cutFilterTimeDuration": "10m",
        "removeAfterSubmitRecord": "false",
        "filterTransData": "true",
        "recordLimits": 20,
        "uniqKey": "a9c3223e-4f90-4bbf-8077-131863b64072",
        "ursDate": "2025-04-27",
        "username": "inadhga5279",
        "uid": "8c64cbe2cfdda305",
        "ursSubmitTime": "2025-04-27 16:05:07",
        "revisitOriquestion": "",
        "revisitParam": "",
        "revisitPriceInfo": "",
        "surveyAISlot": "",
        "surveyAIContext": "",
        "surveySubmitPageInfo": {
            "carrier": "JD",
            "flightNos": "JD5577,MU2734,MU2949",
            "depCity": "",
            "arrCity": "",
            "depAndArrTime": "2025-04-28:07:25_2025-04-28:11:30,2025-05-02:14:50_2025-05-02:18:10,2025-05-02:20:10_2025-05-02:22:15",
            "routeType": "round",
        },
        "priceCompareStatInfo": {
            "needExecAl": "是",
            "hasDirectPriceChange": "是",
            "directCount": 143,
            "directPriceChangeCount": 29,
            "totalCompareCount": 600,
            "hasRoundTripPriceChange": "是",
            "roundTripPriceChangeCount": 111,
            "roundTripCount": 457,
        },
    }
    print(
        "请求参数Json:"
        + json.dumps(
            param,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    )
    reuslt = main(param)
    # if reuslt.get("error"):
    #    print("获取记录失败，错误信息:" + reuslt.get("error"))
    # else:
    #    print("获取记录条数:" + str(len(reuslt.get("results"))))

    write_json_to_file(
        reuslt.get("results"),
        "urs_multi_search/data/URS_ZF_20250320_URS_INFO.json",
    )
