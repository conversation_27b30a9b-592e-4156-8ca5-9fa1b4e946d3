import os
import pyperclip  # 导入 pyperclip 库


def find_images_in_directory(directory_path, keyword, image_extensions=None):
    """
    遍历指定目录，查找文件名包含特定关键字的图片的完整路径。
    """
    if image_extensions is None:
        image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"]

    found_image_paths = []
    keyword_lower = keyword.lower()

    if not os.path.isdir(directory_path):
        print(f"错误：目录 '{directory_path}' 不存在。")
        return found_image_paths

    for root, _, files in os.walk(directory_path):
        for filename in files:
            filename_lower = filename.lower()
            if keyword_lower in filename_lower and any(
                filename_lower.endswith(ext) for ext in image_extensions
            ):
                full_path = os.path.join(root, filename)
                found_image_paths.append(full_path)
    return found_image_paths


if __name__ == "__main__":
    target_directory = (
        "D:\\work\\需求\\202505\\病退分析\\测试集\\20250521\\有ps痕迹测试集"
    )
    search_keyword = "yck250430161228642"

    image_paths = find_images_in_directory(target_directory, search_keyword)

    if image_paths:
        print("\n找到的图片完整路径:")
        paths_string = ""  # 用于存储所有路径，每行一个
        for path in image_paths:
            print(path)
            paths_string += path + "\n"  # 将每个路径添加到字符串中，并换行

        try:
            pyperclip.copy(paths_string.strip())  # 去掉末尾多余的换行符并复制到剪贴板
            print("\n--- 路径已自动复制到剪贴板！---")
        except pyperclip.PyperclipException as e:
            print(f"\n--- 无法自动复制到剪贴板：{e} ---")
            print("--- 你仍然可以从上面的输出中手动复制路径。---")
    else:
        print(
            f"\n在目录 '{target_directory}' 中没有找到文件名包含 '{search_keyword}' 的图片。"
        )
