import json
import requests
from typing import Union, List, Set, Optional, Dict, Any, Tuple
from requests.exceptions import RequestException
from datetime import datetime, date
from urllib.parse import unquote_to_bytes
import numbers
import uuid
from io import StringIO
from csv import DictReader, Error as CSVError  
import base64
import time 
import re





TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)

#todo 需要修改
UPS_PRICE_CHANGE_SQL = """
SELECT 
    f.dt,
    f.create_time,
    f.questionnaire_id,
    f.user_name,
    f.time_interval,
    f.order_num,
    f.ticket_num,
    f.user_value,
    f.is_all_answer,
    f.is_scalper,
    f.order_if,
    f.danpiao_if,
    f.ext_json,
    f.page_json,
    f.routetype_json,
    p.is_bianjia,
    p.up_or_down,
    p.mintue_cha,
    p.type,
    p.od,
    p.depdate,
    p.orig_flight_type,
    p.flightkey,
    p.is_trans,
    p.showprice,
    p.last_showprice,
    p.qtraceid,
    p.last_qtraceid,
    p.search_time,
    p.last_search_time,
    p.price_cha
FROM 
    flight.dwd_flow_dom_usertouch_ups_new_detail_di f
LEFT JOIN 
    flight.dwd_urs_price_changes_detail_di p
ON 
    f.user_name = p.qunar_username
WHERE 
      f.dt = '{ursDate}'
    AND f.questionnaire_id in ('877')
    AND f.is_stable in ('不稳定')
    AND f.routetype_json in ('zf', 'zz')
    AND f.flag = 'UPS一致率'
    AND f.scene = '多次搜索'
    AND p.dt = '{ursDate}'
    AND p.price_cha != 0
    and p.orig_flight_type = '直飞'
    and f.user_name = '{username}'
"""

#todo 需要修改
def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        return
    except KeyError as e:
        return


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


#模拟查询请求、获取taskId
def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )

    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


#模拟获取taskId结果
def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None


def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                #item.update({"compareId": generateId()})
                #这一步是为了提取json中的字段到item中，数据拍平
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


def buildSqlByQuery(ursData, username, extParam):

    #####
    sql = UPS_PRICE_CHANGE_SQL

    try:
        params = {}
        params["ursDate"] = ursData
        params["username"] = username
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e

#todo 需要修改
def queryHivePriceCompareDatas(param):
    try:
        return {
                "error": "hive未查到用户单程变价数据",
                "results": [],
                "processStage": "UPS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "hive未查到用户单程变价数据",
            }

        # query = json.loads(param.get("query"))
        searchTimeDurationLimit = param.get("searchTimeDurationLimit")
        listSearchDurationLimit = convert_to_seconds(searchTimeDurationLimit)

        sql = buildSqlByQuery(param.get("ursDate"), param.get("username"), None)
        # print(sql)
        oriDataResult = queryDataFromTamias(param.get("cookie"), sql)
        if oriDataResult.get("error"):
            oriDataResult["results"] = []
            oriDataResult["needExecAI"] = "否"
            oriDataResult["notExecAIReason"] = (
                f"hive未查到用户单程变价数据, {oriDataResult.get('error')}"
            )
            oriDataResult["processStage"] = "URS-hive查询用户单程变价数据"
            return oriDataResult

        oriPriceCompareDatas = oriDataResult.get("results")

        if not oriPriceCompareDatas or len(oriPriceCompareDatas) == 0:
            return {
                "error": "hive未查到用户单程变价数据",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "hive未查到用户单程变价数据",
            }

        filtered_datas = oriPriceCompareDatas
        if listSearchDurationLimit:
            # 过滤搜索时间间隔大于listSearchDurationLimit的记录
            filtered_datas = []
            for item in oriPriceCompareDatas:
                search_time = safe_parse_datetime(item.get("search_time"))
                last_search_time = safe_parse_datetime(item.get("last_search_time"))

                if not search_time or not last_search_time:
                    continue

                # 计算时间差（秒）
                time_diff = (search_time - last_search_time).total_seconds()

                if time_diff <= listSearchDurationLimit:
                    filtered_datas.append(item)
                else:
                    # print(
                    #     f"[Debug] 过滤掉搜索间隔过大的记录: search_time={search_time}, last_search_time={last_search_time}, diff={time_diff}秒"
                    # )
                    continue

        if not filtered_datas or len(filtered_datas) == 0:
            return {
                "error": f"hive查询到都是搜索时间间隔大于{listSearchDurationLimit}秒的记录",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": f"hive查询到都是搜索时间间隔大于{listSearchDurationLimit}秒的记录",
            }

        ursSubmitTime = param.get("ursSubmitTime")
        if ursSubmitTime:
            ursSubmitTime = safe_parse_datetime(ursSubmitTime)
            if ursSubmitTime:
                filtered_datas = [
                    item
                    for item in filtered_datas
                    if safe_parse_datetime(item.get("search_time"))
                    and safe_parse_datetime(item.get("last_search_time"))
                    and safe_parse_datetime(item.get("search_time")) <= ursSubmitTime
                    and safe_parse_datetime(item.get("last_search_time"))
                    <= ursSubmitTime
                ]
        if not filtered_datas or len(filtered_datas) == 0:
            return {
                "error": f"hive过滤掉URS填写后的数据后无变价记录:{param.get('ursSubmitTime')}",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": f"hive过滤掉URS填写后的数据后无变价记录:{param.get('ursSubmitTime')}",
            }

        return {
            "error": None,
            "results": filtered_datas,
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAI": "是",
            "notExecAIReason": "",
        }
    except Exception as e:
        return {
            "error": f"hive查询搜索有变价的搜索记录异常: {str(e)}",
            "results": [],
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAI": "否",
            "notExecAIReason": f"hive查询搜索有变价的搜索记录异常: {str(e)}",
        }


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None

def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict, fields: List, date_format: str = None, case_sensitive: bool = False
) -> Set[Union[str, datetime.date]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt.date() if isinstance(dt, datetime) else dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def filter_flight_data(
    ota_seatch_info: dict, target_data: List[dict], priceInfoParam: dict,qTraceId: str
):
    """
    增强版多条件过滤（支持全量多值参数）
    """
    matched = []
    notMatched = []

    # 需要过滤的特定tag列表
    filtered_tags = {
        "TOU1",
        "FXD1",
        "PTJ1",
        "WYY1",
        "SLN1",
        "SLP1",
        "CZB1",
        "SLD1",
        "SLG1",
        "BYJ1",
        "GPP1",
        "STU1",
        "YHU1",
        "CLP1",
        "LHO1",
        "LHS1",
        "LHN1",
        "LHO1",
        "LHT1",
        "LHX1",
        "LHM1",
        "LHE1",
        "HUJ1",
        "JTP1",
        "SOA1",
        "AGE1",
        "OLD1",
        "OKR1",
        "OKK1",
        "HUB1",
        "ZZM1",
        "HUL1",
        "XFQ1",
        "XFL1",
        "XFZ1",
        "XFA1",
        "XLE1",
        "QHY1",
        "GSX1",
        "HET1",
        "LNF1",
        "JCY1",
        "JCB1",
        "ZHZ1",
        "KFW1",
        "HYC1",
        "NQX1",
        "XFF1",
        "TTD1",
        "SLB1",
    }

    # 航班号检查
    flight_numbers = parse_multi_values(
        ota_seatch_info, ["flightNumber"]
    )  # 注意原始参数名拼写错误

    for item in target_data:
        # 转换数据结构
        pair_item = transform_item_to_pair(ota_seatch_info, item, qTraceId)
        
        # 检查是否为OTA数据且tag在过滤列表中
        if item.get("preTag") in filtered_tags:
            pair_item.update(
                {
                    "needExec": "不匹配",
                    "desc": f"OTA数据tag {item.get('tag')} 在过滤列表中",
                }
            )
            notMatched.append(pair_item)
            continue

        # 多条件检查
        checks = [
            _check_flight_number(pair_item, flight_numbers),
            _check_price(pair_item, priceInfoParam),
        ]

        # 关键修复点：提取每个check的布尔状态
        is_all_match = all([check[0] for check in checks])

        # 收集所有错误信息
        error_msgs = [check[1] for check in checks if not check[0]]

        # 标注匹配状态及错误详情
        pair_item.update(
            {
                "matchQuestion": "匹配" if is_all_match else "不匹配",
                "desc": "; ".join(error_msgs) if error_msgs else "匹配",
            }
        )

        if is_all_match:
            matched.append(pair_item)
        else:
            notMatched.append(pair_item)

    return matched, notMatched


def transform_item_to_pair(ota_seatch_info: dict, item: dict, qTraceId: str) -> dict:
    """
    将item转换为pair_item结构，提取指定的字段
    """ 
    pair_item = {}
    
    # 从baseInfo.basicAttributes中获取searchPriceInfo和syncPriceInfo
    basic_attrs = item.get("baseInfo", {}).get("basicAttributes", {})
    
    # 1. 处理searchPriceInfo，添加pre前缀
    search_price_info = basic_attrs.get("searchPriceInfo", {})
    pair_item.update({
        "preTag": search_price_info.get("tag"),
        "preCabin": search_price_info.get("cabin"),
        "prePolicyId": search_price_info.get("policyId"),
        "preViewPrice": search_price_info.get("printPrice"),
        "preBasePrice": search_price_info.get("basePrice"),
        "prePackagePrice": search_price_info.get("barePrice"),
        "preProductMark": search_price_info.get("productMark"),
        "preChildCabin": search_price_info.get("childCabin"),
        "preChildPrice": search_price_info.get("childPrice"),
        "preInfantCabin": search_price_info.get("infantCabin"),
        "preInfantPrice": search_price_info.get("infantPrice")
    })
    
    # 2. 处理syncPriceInfo，添加sur前缀
    sync_price_info = basic_attrs.get("syncPriceInfo", {})
    pair_item.update({
        "surTag": sync_price_info.get("tag"),
        "surCabin": sync_price_info.get("cabin"),
        "surPolicyId": sync_price_info.get("policyId"),
        "surViewPrice": sync_price_info.get("printPrice"),
        "surBasePrice": sync_price_info.get("basePrice"),
        "surPackagePrice": sync_price_info.get("barePrice"),
        "surProductMark": sync_price_info.get("productMark"),
        "surChildCabin": sync_price_info.get("childCabin"),
        "surChildPrice": sync_price_info.get("childPrice"),
        "surInfantCabin": sync_price_info.get("infantCabin"),
        "surInfantPrice": sync_price_info.get("infantPrice")
    })

    # 3. 处理computingProcess
    sync_data = item.get("computingProcess", {}).get("sync", {})
    async_data = item.get("computingProcess", {}).get("async", {})
    
    # 处理flightType
    flight_type = sync_data.get("flightType")
    if flight_type is None:
        flight_type = async_data.get("flightType")
    pair_item["flightType"] = flight_type
    
    # 处理flightNo
    flight_no = sync_data.get("flightNo")
    if flight_no is None:
        flight_no = async_data.get("flightNo")
    pair_item["flightNo"] = flight_no
    
    # 添加preTraceId
    pair_item["preTraceId"] = qTraceId
    
    # 添加bookingQTrace作为surTraceId
    pair_item["surTraceId"] = item.get("qTraceInfo", {}).get("bookingQTrace")

    # 添加compareId
    pair_item["compareId"] = generate_compare_id()

    # 添加compareType
    pair_item["compareType"] = "ota-booking"

    # 添加请求参数
    pair_item["departureDate"] = ota_seatch_info.get("departureDate")
    pair_item["arrivalDate"] = ota_seatch_info.get("arrivalDate")
    pair_item["departureCity"] = ota_seatch_info.get("departureCity")
    pair_item["arrivalCity"] = ota_seatch_info.get("arrivalCity")
    pair_item["preSearchDateTime"] = ota_seatch_info.get("searchDateTime")
    
    return pair_item


def generate_compare_id() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())

def _check_flight_number(pair_item: dict, flight_numbers: set) -> tuple:
    """航班号检查（带值对比）"""
    if flight_numbers:
        item_flight = pair_item.get("flightNo")
        
        # 如果没有找到航班号，则不做对比，直接返回 True
        if not item_flight:
            return (True, "")
            
        req_flights_str = ",".join(sorted(flight_numbers))

        if len(item_flight) > 2 and item_flight not in flight_numbers:
            return (
                False,
                f"航班号不匹配（请求值：{req_flights_str}，数据值：{item_flight}）",
            )
    return (True, "")


def _check_specific_price(price_value: int, priceInfoParam: dict) -> tuple[bool, str]:
    """
    检查特定价格是否在指定的价格范围内
    Args:
        price_value: 要检查的价格值
        priceInfoParam: 价格信息参数，包含 precisePriceList 和 durationPriceList
    Returns:
        tuple[bool, str]: (是否匹配, 匹配说明)
    """
    if not price_value:
        return False, f"价格值无效: {price_value}"
        
    # 检查精确价格列表
    precise_prices = priceInfoParam.get("precisePriceList", [])
    for price_info in precise_prices:
        target_price = price_info.get("price")
        if target_price == price_value:
            return True, f"价格匹配精确价格: {price_value}"

    # 检查区间价格列表
    duration_prices = priceInfoParam.get("durationPriceList", [])
    for price_info in duration_prices:
        left_price = price_info.get("leftPrice")
        right_price = price_info.get("rightPrice")
        if left_price is not None and right_price is not None:
            if left_price <= price_value <= right_price:
                return True, f"价格在区间内: {left_price}-{right_price}"

    return False, f"价格 {price_value} 不在任何指定范围内"


def _check_price(pair_item: dict, priceInfoParam: dict) -> tuple[bool, str]:
    """
    检查航班价格是否在指定的价格范围内，同时比较 searchPriceInfo 和 syncPriceInfo 的 printPrice
    Args:
        pair_item: 扁平化后的航班信息字典
        priceInfoParam: 价格信息参数，包含 precisePriceList 和 durationPriceList
    Returns:
        tuple[bool, str]: (是否匹配, 原因)
    """
    if not priceInfoParam:
        return False, "用户问题中未指定价格信息"
    
    search_print_price = pair_item.get("prePrintPrice")
    sync_print_price = pair_item.get("surPrintPrice")
    
    # 如果任一价格不存在，则返回失败
    if not search_print_price or not sync_print_price:
        return False, "航班无完整价格信息"

    # 分别检查两个价格
    search_result = _check_specific_price(search_print_price, priceInfoParam)
    sync_result = _check_specific_price(sync_print_price, priceInfoParam)
    
    # 两个价格都必须匹配才返回 True
    if search_result[0] and sync_result[0]:
        return True, f"搜索价格和同步价格都匹配: {search_print_price}, {sync_print_price}"
    
    # 收集错误消息
    error_msgs = []
    if not search_result[0]:
        error_msgs.append(f"搜索价格不匹配: {search_print_price}")
    if not sync_result[0]:
        error_msgs.append(f"同步价格不匹配: {sync_print_price}")
        
    return False, "; ".join(error_msgs)

def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def format_passengers(passengers):
    if passengers is None or not isinstance(passengers, list):
        return ""
    result = []
    for passenger in passengers:
        sub_result = []
        for key, value in passenger.items():
            if value is not None:
                sub_result.append(f"{key}={value}")
        result.append("&".join(sub_result))
    return ";".join(result)


def join_list(lst: Optional[List], separator: str = "") -> str:
    """
    将列表安全地拼接为字符串

    :param lst: 输入列表（可能为None或包含空元素）
    :param separator: 元素间分隔符（默认为空）
    :return: 拼接后的字符串

    >>> join_list(None)
    ''
    >>> join_list([])
    ''
    >>> join_list(["a", None, "", "b"])
    'ab'
    >>> join_list([1, None, 3.14, "test"], "-")
    '1-3.14-test'
    """
    # 处理空值输入
    if not isinstance(lst, list):
        return ""

    # 转换元素为字符串并处理None
    cleaned = [str(item) if item is not None else "" for item in lst]
    return separator.join(cleaned)


def boolean_to_str(value: Any) -> str:
    """
    将 Boolean 类型转为字符串，非 Boolean 或 None 返回空字符串

    规则：
    - 输入为 True  → 返回 "True"
    - 输入为 False → 返回 "False"
    - 输入为 None  → 返回 ""
    - 输入为其他类型（如整数、字符串、列表等） → 返回 ""

    :param value: 输入值
    :return: 转换后的字符串或空字符串

    示例：
    >>> boolean_to_str(True)
    'True'
    >>> boolean_to_str(False)
    'False'
    >>> boolean_to_str(None)
    ''
    >>> boolean_to_str(0)
    ''
    >>> boolean_to_str("hello")
    ''
    """
    if value is None:
        return ""
    # 注意：Python 中 isinstance(True, int) 会返回 True，因此用 type 判断更严格
    if type(value) is bool:  # 使用 type 而非 isinstance 避免将 0/1 误判为 bool
        return "True" if value else "False"
    return ""


def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue


def parse_list_data(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    新版航班数据解析方法（支持复合航班号格式）

    参数：
    data: 包含flights字段的原始数据字典

    返回：
    结构化解析后的数据列表，包含字段：
    flightNo, price, coupon, cut, xCut, tag, oriTag, tSource, cabinType
    """
    results = []

    expansionType = data.get("expansionType", "")
    poison = data.get("poison", False)
    basicLabels = data.get("basicLabels") or []
    filters = data.get("filters") or []
    passengers = data.get("passengers") or []

    # 获取航班数据字典（处理空值情况）
    flights = data.get("flights", {})

    # 遍历所有航班条目
    for flight_no, flight_data in flights.items():
        expItems = flight_data.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        # 构造基础数据项
        item = {
            "flightNo": flight_no,
            "tSource": "list",  # 固定值
            "cabinType": "",  # 固定空字符串
            "price": convert_price_to_numeric(flight_data.get("price")),
            "coupon": flight_data.get("coupon"),
            "cut": flight_data.get("cut"),
            "xCut": flight_data.get("xCut"),
            "tag": flight_data.get("tag"),
            "oriTag": flight_data.get("oriTag"),
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": format_passengers(passengers),
        }
        results.append(item)

    return results


def parse_domestic_ota(domestic_ota: Dict) -> List[Dict]:
    """
    解析domesticOta数据，返回结构化航班舱位数据
    """
    result = []

    # 公共字段
    flight_no = domestic_ota.get("flightNo", "")
    t_source = "ota"  # 固定值

    expansionType = domestic_ota.get("expansionType", "")
    poison = domestic_ota.get("poison", False)
    basicLabels = domestic_ota.get("basicLabels") or []
    filters = domestic_ota.get("filters") or []
    passengers = domestic_ota.get("passengers") or []

    # 处理经济舱数据（evendors）
    for vendor in domestic_ota.get("evendors") or []:
        expItems = vendor.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        record = {
            "flightNo": flight_no,
            "price": vendor.get("price"),
            "coupon": vendor.get("coupon"),
            "cut": vendor.get("cut"),
            "xCut": vendor.get("xCut"),
            "tag": vendor.get("tag"),
            "oriTag": vendor.get("oriTag"),
            "tSource": t_source,
            "cabinType": "经济舱",  # evendors固定值
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": format_passengers(passengers),
        }
        result.append(_clean_record(record))

    # 处理高端舱位数据（hvendors）
    for vendor in domestic_ota.get("hvendors") or []:
        expItems = vendor.get("expItems", {})
        expVendor = 0
        if expItems is not None:
            expVendor = expItems.get("vendor")
        if expVendor is None:
            expVendor = 0
        record = {
            "flightNo": flight_no,
            "price": vendor.get("price"),
            "coupon": vendor.get("coupon"),
            "cut": vendor.get("cut"),
            "xCut": vendor.get("xCut"),
            "tag": vendor.get("tag"),
            "oriTag": vendor.get("oriTag"),
            "tSource": t_source,
            "cabinType": "头等/商务舱",  # hvendors固定值
            "expVendor": expVendor,
            "expansionType": expansionType,
            "poison": boolean_to_str(poison),
            "basicLabels": join_list(basicLabels, ","),
            "filters": join_list(filters, ","),
            "passengers": join_list(passengers, ","),
        }
        result.append(_clean_record(record))

    return result


def method2_parse_detail_table(data: Dict) -> List[Dict]:
    """
    解析detailTable数据，返回结构化价格信息
    """
    result = []
    if data is None or data.get("detailTable") is None:
        return result

    detail_table = []
    detailTable = data.get("detailTable")
    if not isinstance(detailTable, list):
        detail_table = data.get("detailTable", {}).get("data") or []
    else:
        if len(detailTable) > 0:
            detail_table = detailTable[0].get("data") or []

    for flight_data in detail_table:
        rows = flight_data.get("rows", [])
        for row in rows:
            # 仅处理_domesticShow为true的记录
            if not row.get("_domesticShow", False):
                continue

            raw_data = row.get("_raw", {})
            extMap = raw_data.get("extMap")
            ext_map = raw_data.get("extMap", {})
            all_goods = raw_data.get("allGoodsItems", {})

            cpt = ext_map.get("CPT")
            secondPrice = ext_map.get("secondPrice")
            autoPriceDecreaseAmount = ext_map.get("autoPriceDecreaseAmount")
            if extMap is None:
                cpt = row.get("CPT")
                secondPrice = row.get("secondPrice")
                autoPriceDecreaseAmount = row.get("autoPriceDecreaseAmount")

            # 计算商品总价
            all_good_item_price = sum(
                float(str_val) for str_val in all_goods.values() if _is_number(str_val)
            )

            record = {
                "flightNo": flight_data.get("flightNumber", ""),
                "realPriceOriTag": row.get("realPriceOriTag"),
                "tagType": raw_data.get("tagType"),
                "flightNumber": flight_data.get("flightNumber"),
                "depDate": flight_data.get("depDate"),
                "depTime": flight_data.get("depTime"),
                "wrapperId": row.get("wrapperId"),
                "productMark": row.get("productMark"),
                "cabin": raw_data.get("cabin"),
                "packagePrice": raw_data.get("price"),
                "basePrice": raw_data.get("basePrice"),
                "viewPrice": raw_data.get("viewPrice"),
                "policyId": raw_data.get("policyId"),
                "autoPriceDecreaseAmount": autoPriceDecreaseAmount,
                "secondPrice": convert_price_to_numeric(secondPrice),
                "CPT": cpt,
                "allGoodItemPrice": round(all_good_item_price, 2),
            }
            result.append(_clean_record(record))

    return result


def method3_merge_data(
    method1_data: List[Dict], method2_data: List[Dict]
) -> List[Dict]:
    """
    合并方法1和方法2的数据
    """
    merged = []

    for m1 in method1_data:
        # 生成匹配键：使用surTraceId
        match_surTraceId = m1.get("surTraceId")

        # 在方法2数据中查找匹配项
        matched = next(
            (
                m2
                for m2 in method2_data
                if m2.get("surTraceId") == match_surTraceId
            ),
            None,
        )

        # 复制需要合并的字段
        if matched:
            merge_fields = [
                "prePrice",
                "surPrice",
                "surSearchDataTime",
                "preZeroCard",
                "surZeroCard",
                "preExpCut",
                "surExpCut",
                "preCoupon",
                "surCoupon",
                "preActivityCut",
                "surActivityCut",
                "forceBindPrice",
            ]
            for field in merge_fields:
                m1[field] = matched.get(field)

        merged.append(m1)

    return merged


# 辅助函数
def _clean_record(record: Dict) -> Dict:
    """处理空值和类型转换"""
    return {k: v if v not in (None, "null", "") else None for k, v in record.items()}


def _is_number(s: Any) -> bool:
    """判断是否为有效数字"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False


def search_flight_case(
    qTraceId: str,
) -> Dict[str, Any]:
    url = "http://paoding.corp.qunar.com/visual/mainSearch"

    # 参数校验（关键必填项）
    if not qTraceId:
        raise ValueError("qTraceId 为必填参数")

    params = {
        "qTraceId": qTraceId,
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    try:
        response = requests.get(
            url, params=params, headers=headers, timeout=10  # 增加超时控制
        )
        response.raise_for_status()
        return response.json()
    except (RequestException, ValueError) as e:
        return {"error": f"请求失败: {str(e)}"}


def parse_and_validate_price_info(price_info: Any) -> Tuple[bool, str, Dict[str, Any]]:
    """
    校验价格信息的有效性
    Args:
        price_info: 价格信息字典
    Returns:
        校验后的价格信息字典
    """
    # 1. 整体校验
    if not price_info:
        return False, "价格信息为空", None
    if not isinstance(price_info, dict):
        return False, "价格信息类型错误", None

    result = {
        "userPriceType": price_info.get("userPriceType"),
        "precisePriceList": [],
        "durationPriceList": [],
    }

    # 2. 校验 precisePriceList
    precise_list = price_info.get("precisePriceList")
    if precise_list is not None:
        if isinstance(precise_list, list):
            for item in precise_list:
                if (
                    isinstance(item, dict)
                    and "price" in item
                    and is_valid_number(item.get("price"))
                ):
                    result["precisePriceList"].append(item)
        elif (
            isinstance(precise_list, dict)
            and "price" in precise_list
            and is_valid_number(precise_list.get("price"))
        ):
            result["precisePriceList"].append(precise_list)

    # 3. 校验 durationPriceList
    duration_list = price_info.get("durationPriceList")
    if duration_list is not None:
        if isinstance(duration_list, list):
            for item in duration_list:
                if isinstance(item, dict):
                    # 检查是否有 leftPrice 和 rightPrice
                    if "leftPrice" in item and "rightPrice" in item:
                        left_price = item.get("leftPrice")
                        right_price = item.get("rightPrice")
                        if (
                            is_valid_number(left_price)
                            and is_valid_number(right_price)
                            and left_price <= right_price
                        ):
                            result["durationPriceList"].append(item)
                    # 检查是否有 price
                    elif "price" in item and is_valid_number(item.get("price")):
                        result["precisePriceList"].append(item)
        elif isinstance(duration_list, dict):
            # 检查是否有 leftPrice 和 rightPrice
            if "leftPrice" in duration_list and "rightPrice" in duration_list:
                left_price = duration_list.get("leftPrice")
                right_price = duration_list.get("rightPrice")
                if (
                    is_valid_number(left_price)
                    and is_valid_number(right_price)
                    and left_price <= right_price
                ):
                    result["durationPriceList"].append(duration_list)
                # 检查是否有 price
            elif "price" in duration_list and is_valid_number(
                duration_list.get("price")
            ):
                result["precisePriceList"].append(duration_list)

    # 4. 检查是否有效
    if not result["precisePriceList"] and not result["durationPriceList"]:
        return False, "价格信息无效", None

    return True, "价格信息有效", result

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # 获取用户问题中的价格信息和参数
        questionPriceInfo = param.get("questionPriceInfo")
        # 解析价格信息,校验用户描述的价格消息是否有效
        parse_result = parse_and_validate_price_info(questionPriceInfo)
        if not parse_result[0]:
            return {
                "errorMsg": "用户问题中未指定价格信息（questionPriceInfo），直接不分析！",
                "priceList": [],
            }
        priceInfoParam = parse_result[2]
        # 方法1 调用庖丁接口，获取价格信息，此处查出来直接为成对的价格消息
        searchResult = search_flight_case(
            qTraceId=param.get("qTraceId", ""),
        )
        priceList = searchResult.get("data")
        if priceList is None:
            if searchResult.get("error") is not None:
                return {"errorMsg": "庖丁请求异常！", "priceList": [], "notMatchData": []}
        otaSeatchInfo = param.get("otaSeatchInfo")
        qTraceId=param.get("qTraceId", "")
        mahed, notMahed = filter_flight_data(
            otaSeatchInfo, priceList, priceInfoParam, qTraceId
        )
        # 方法2 查询hive数据
        hiveResult = queryHivePriceCompareDatas(otaSeatchInfo)
        if hiveResult.get("error"):
            return {"errorMsg": "hive查询异常！", "priceList": [], "notMatchData": []}
        hiveResult = hiveResult.get("results")
        # 方法3 合并数据
        mahed = method3_merge_data(mahed, hiveResult)

        return {"errorMsg": "", "priceList": mahed, "notMatchData": notMahed}
    except KeyError as e:
        return {"errorMsg": f"缺少必要参数: {str(e)}", "priceList": []}
    except ValueError as e:
        return {"errorMsg": str(e), "priceList": []}
    except Exception as e:
        return {"errorMsg": f"系统异常: {str(e)}", "priceList": []}
  