from string import Formatter
import requests
import time  # 添加time模块用于等待
import json
import base64
from datetime import datetime, timed<PERSON>ta
from csv import Dict<PERSON><PERSON>er, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>
from io import StringIO
import uuid
from typing import List, Dict, Any


def airport_to_city(key):
    return key


TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)


def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())


def count_distinct_users(items: List[Dict[str, Any]]) -> int:
    """
    统计过滤后数据中不同user_name的数量

    :param items: 经过过滤的字典列表
    :return: 去重后的用户数量（包含空值检测）

    >>> test_data = [
    ...    {"user_name": "Alice", "urs_flightNos": "AB123"},
    ...    {"user_name": "<PERSON>", "urs_flightNos": "CD456"},
    ...    {"user_name": "Alice", "urs_flightNos": "EF789"},
    ...    {"user_name": ""},  # 空用户名
    ...    {"user_name": None},  # None值
    ...    {"no_name_field": "data"}  # 缺失字段
    ... ]
    >>> count_distinct_users(test_data)
    3
    """
    if not isinstance(items, list):
        return 0

    seen_users = set()
    missing_count = 0
    empty_count = 0

    for idx, item in enumerate(items, 1):
        if not isinstance(item, dict):
            continue

        # 安全获取并标准化用户名
        username = item.get("user_name")

        # 处理空值情况
        if username is None:
            missing_count += 1
            continue

        # 转换为字符串并去除首尾空格
        cleaned_name = str(username).strip()

        if not cleaned_name:
            empty_count += 1
            continue

        seen_users.add(cleaned_name)

    return seen_users


def filter_items(items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    过滤数据列表，只保留 flightkey == urs_flightNos 的有效记录

    :param items: 包含航班数据的字典列表
    :return: 过滤后的数据列表
    """
    if not isinstance(items, list):
        logging.error("输入参数类型错误，期望list类型")
        return []

    valid_records = []
    error_counter = {
        "missing_flightkey": 0,
        "missing_urs_flightNos": 0,
        "value_mismatch": 0,
    }

    for idx, item in enumerate(items):
        if not isinstance(item, dict):
            continue

        # 并行获取字段值避免中途修改
        flightkey = item.get("flightkey")
        urs_flightnos = item.get("urs_flightNos")

        # 字段存在性检查
        if flightkey is None:
            error_counter["missing_flightkey"] += 1
            continue
        if urs_flightnos is None:
            error_counter["missing_urs_flightNos"] += 1
            continue

        # 值类型校验
        if not isinstance(flightkey, str) or not isinstance(urs_flightnos, str):
            continue

        # 值比对逻辑
        if flightkey.strip() == urs_flightnos.strip():
            valid_records.append(item)
        else:
            error_counter["value_mismatch"] += 1
    return valid_records


def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        print(f"[ext_json解析失败] 行数据: {item}，错误: {str(e)}")
    except KeyError as e:
        print(f"[字段缺失] 缺少必要字段: {str(e)}")


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )

    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            if retry_count == 60:
                print(
                    "Task is still running, waiting 10 seconds... (Attempt 5/30)"
                    + response.text
                )
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                print("Failed to parse JSON response")
            return None

    return None


# http://10.88.67.150:8080/adhoc/executor/download/data?taskId=4710824
# http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId=4710824


def getData(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"GenrecordId": generateId()})
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


URS_PRICE_CHANGE_SQL = """
SELECT 
    f.dt,
    f.create_time,
    f.questionnaire_id,
    f.user_name,
    f.time_interval,
    f.order_num,
    f.ticket_num,
    f.user_value,
    f.is_all_answer,
    f.is_scalper,
    f.order_if,
    f.danpiao_if,
    f.ext_json,
    f.page_json,
    f.routetype_json,
    p.is_bianjia,
    p.up_or_down,
    p.mintue_cha,
    p.type,
    p.od,
    p.depdate,
    p.orig_flight_type,
    p.flightkey,
    p.is_trans,
    p.showprice,
    p.last_showprice,
    p.qtraceid,
    p.last_qtraceid,
    p.search_time,
    p.last_search_time,
    p.price_cha
FROM 
    flight.dwd_flow_dom_usertouch_ups_new_detail_di f
LEFT JOIN 
    flight.dwd_urs_price_changes_detail_di p
ON 
    f.user_name = p.qunar_username
WHERE 
      f.dt = '{ursDate}'
    AND f.questionnaire_id in ('877')
    AND f.is_stable in ('不稳定')
    AND f.routetype_json in ('zf', 'zz')
    AND f.flag = 'UPS一致率'
    AND f.scene = '多次搜索'
    AND p.dt = '{ursDate}'
    AND p.price_cha != 0
    and p.orig_flight_type = '直飞'
"""


def buildSqlByQuery(ursData, username, extParam):

    #####
    sql = URS_PRICE_CHANGE_SQL

    try:
        params = {}
        params["ursDate"] = ursData

        if username:
            params["username"] = username
            sql = URS_PRICE_CHANGE_SQL + "and f.user_name = '{username}'"
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


# 必须有一个main函数，作为入口
def main(param):
    # query = json.loads(param.get("query"))
    sql = buildSqlByQuery(param.get("ursDate"), param.get("username"), None)
    # print(sql)
    return getData(param.get("cookie"), sql)


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象安全地序列化为 JSON 并写入文件

    :param obj: 要序列化的对象，支持基础类型/列表/字典/自定义对象
    :param file_path: 目标文件路径（支持自动创建目录）
    :param encoding: 文件编码格式，默认utf-8
    :param ensure_ascii: 是否转义非ASCII字符，默认False保留Unicode
    :param indent: JSON缩进空格数，默认2（设为None可压缩输出）
    :param default: 自定义对象序列化函数，默认使用__dict__转换
    :param json_kwargs: 其他json.dump参数（如sort_keys等）
    :return: 是否成功写入文件

    :raises IOError: 当遇到文件系统级错误时会抛出（非返回False的情况）

    异常处理策略：
    - 类型错误：打印建议信息并返回False
    - 权限错误：打印错误路径并返回False
    - 其他错误：打印错误信息并返回False
    """
    try:
        target_path = Path(file_path)

        # 自动创建父目录（exist_ok防止竞态条件）
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的序列化
        serialize_default = default or (
            lambda o: o.__dict__ if hasattr(o, "__dict__") else repr(o)
        )

        # 使用上下文管理器确保文件正确关闭
        with target_path.open("w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(
            f"[序列化失败] 对象类型错误: {str(e)}，建议：1.检查数据类型 2.提供自定义序列化函数"
        )
    except PermissionError:
        print(f"[权限拒绝] 无法写入文件: {file_path}，请检查文件权限")
    except json.JSONEncodeError as e:
        print(f"[编码错误] 非法数据: {str(e)}，请检查特殊字符")
    except Exception as e:
        print(f"[系统错误] 操作异常: {str(e)}")

    return False


from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
import datetime


def write_dicts_to_excel(
    data: list[dict],
    filename: str = "output.xlsx",
    sheet_name: str = "Data",
    autofit: bool = True,
) -> None:
    """
    将字典列表写入Excel文件

    :param data: 字典数据列表，要求所有字典的键一致
    :param filename: 输出文件名（默认：output.xlsx）
    :param sheet_name: 工作表名称（默认：Data）
    :param autofit: 是否自动调整列宽（默认：True）
    """
    if not data:
        raise ValueError("输入数据不能为空列表")

    # 验证数据结构
    keys = data[0].keys()
    if any(d.keys() != keys for d in data[1:]):
        raise ValueError("字典字段名不一致")

    # 创建Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = sheet_name

    # 写入表头（带格式）
    header_font = Font(bold=True)
    for col_num, key in enumerate(keys, 1):
        cell = ws.cell(row=1, column=col_num, value=str(key))
        cell.font = header_font

    # 写入数据
    for row_num, record in enumerate(data, 2):  # 从第2行开始
        for col_num, key in enumerate(keys, 1):
            value = record.get(key, "")

            # 特殊类型处理
            if isinstance(value, datetime.datetime):
                value = value.replace(tzinfo=None)  # 移除时区信息
            elif isinstance(value, (list, dict)):
                value = str(value)  # 复杂对象转为字符串

            ws.cell(row=row_num, column=col_num, value=value)

    # 自动调整列宽
    if autofit:
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    cell_length = len(str(cell.value))
                    # 加粗表头需要额外长度补偿
                    if cell.row == 1 and cell.font.bold:
                        cell_length += 2
                    max_length = max(max_length, cell_length)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

    # 保存文件
    try:
        wb.save(filename)
        print(f"文件已成功保存至：{filename}")
    except PermissionError:
        raise RuntimeError("文件被其他程序占用，请关闭Excel后重试")
    except Exception as e:
        raise RuntimeError(f"保存文件时发生错误：{str(e)}")


if __name__ == "__main__":
    # 假设输入数据存储在input_data变量中
    param = {
        "cookie": "QN1=00014b0014346c74ca982920; cookie=wanzhou.zheng&744122&8A2EE47906AB79B2BEA42F3838EEA290; JSESSIONID=82B0396E4DA55F340BEC8276A78DFB93",
        "ursDate": "2025-03-15",
        # "username": "hfud5225",
    }
    reuslt = main(param)
    allUsers = count_distinct_users(reuslt.get("results"))
    meetUrsFlightItems = filter_items(reuslt.get("results"))
    meetUsers = count_distinct_users(meetUrsFlightItems)
    print(
        "获取记录条数:"
        + str(len(reuslt.get("results")))
        + "用户数量："
        + str(len(allUsers))
    )
    print(
        "匹配url航班获取记录条数:"
        + str(len(meetUrsFlightItems))
        + "用户数量:"
        + str(len(meetUsers))
    )

    success = write_json_to_file(
        meetUrsFlightItems,
        "urs_multi_search/data/urs_meet_page_flightNo.json",
        indent=4,
        ensure_ascii=False,
        sort_keys=True,
    )

    write_dicts_to_excel(
        reuslt.get("results"),
        filename="urs_multi_search/data/URS_ZF_20250315_ALL.xlsx",
        sheet_name="URS用户数据",
    )
    write_dicts_to_excel(
        meetUrsFlightItems,
        filename="urs_multi_search/data/URS_ZF_20250315_same_flight.xlsx",
        sheet_name="URS用户数据",
    )
    # print("执行结果：" + json.dumps(reuslt, indent=2, ensure_ascii=False))
