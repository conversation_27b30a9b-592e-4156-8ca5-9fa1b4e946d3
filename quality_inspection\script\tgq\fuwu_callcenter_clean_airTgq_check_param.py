import json
import re
import traceback
from typing import Any, Optional, Union


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def validate_parameters(params):
    """
    校验入参参数
    Args:
        params: 入参字典
    Returns:
        tuple: (是否校验通过(bool), 校验失败原因(str))
    """
    # Step 1: 校验外层参数是否为空
    if not params:
        return False, "入参为空"

    required_outer_fields = ["baseInfo", "cabinPriceInfo", "tgEventInfo", "tgqInfo"]
    for field in required_outer_fields:
        if field not in params:
            return False, f"缺少必要参数: {field}"
        if not isinstance(params[field], dict):
            return False, f"参数 {field} 必须是字典类型"

    # Step 3: 校验成人和儿童信息
    cabin_price_info = params["cabinPriceInfo"]
    has_adult_info = bool(
        cabin_price_info.get("aduCabin") and cabin_price_info.get("aduViewPrice")
    )
    has_child_info = bool(
        cabin_price_info.get("chdCabin") and cabin_price_info.get("chdViewPrice")
    )

    if not (has_adult_info or has_child_info):
        return (
            False,
            "成人信息(成人舱位和票面价)或儿童信息(儿童舱位和票面价)至少要填写一组",
        )

    # Step 4: 校验 firstRefundTime 和 firstChangeTime 不能同时为空
    tg_event_info = params["tgEventInfo"]
    if not (
        tg_event_info.get("firstRefundTime") or tg_event_info.get("firstChangeTime")
    ):
        return False, "firstRefundTime 和 firstChangeTime 不能同时为空"

    # Step 5: 校验 tgqInfo.ticketLogInfos 和 airConversations 不能同时为空
    tgq_info = params["tgqInfo"]
    if not (tgq_info.get("ticketLogInfos") or tgq_info.get("airConversations")):
        return False, "tgqInfo.ticketLogInfos 和 airConversations 不能同时为空"

    reset_ticket_log_infos(tgq_info)

    return True, ""


def reset_ticket_log_infos(tgq_info: dict) -> dict:
    """
    重置并过滤tgqInfo.ticketLogInfos信息

    Args:
        tgq_info (dict): tgqInfo字典

    Returns:
        dict: 处理后的tgqInfo字典
    """
    if not tgq_info.get("ticketLogInfos"):
        return tgq_info

    # 解析ticketLogInfos为JSON
    ticket_log_infos = safe_json_parse(tgq_info["ticketLogInfos"], [])

    if not isinstance(ticket_log_infos, list):
        return tgq_info

    # 定义允许的logTypeName和logListType
    allowed_log_type_names = {"创建", "关单", "待分配", "保存日志", "外呼"}
    allowed_log_list_types = {"FLOW_OPT", "ORDER_OPT", "COMPENSATION"}

    # 过滤ticketLogInfos
    filtered_log_infos = [
        log_info
        for log_info in ticket_log_infos
        if isinstance(log_info, dict)
        and log_info.get("logTypeName") in allowed_log_type_names
        and log_info.get("logListType") in allowed_log_list_types
    ]

    # 更新tgqInfo中的ticketLogInfos
    tgq_info["ticketLogInfos"] = filtered_log_infos

    return tgq_info


def main(param: dict) -> dict:
    try:
        is_valid, message = validate_parameters(param)
        return {"is_valid": is_valid, "errorMsg": message}
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg}


def test_validate_parameters():
    """
    测试参数校验函数
    """
    # 测试用例1：正常数据
    test_data_1 = {
        "baseInfo": {
            "orderNo": "hfx250318003219294",
            "domain": "hfx.trade.qunar.com",
            "orderStatus": "改签完成",
            "flowNo": "NWF20250321195151983664",
            "fromSID": "JF250318003219294001331129",
            "fOrderNo": "hfx250318003219294001",
            "passengerInfo": "订单类型：单程;成人,舱位：N,票面价：710,基建：50,燃油：40,周边产品：0.0",
        },
        "cabinPriceInfo": {
            "aduCabin": "N",
            "aduViewPrice": "710",
            "chdCabin": "",
            "chdViewPrice": "0",
            "aduTicketCabin": "",
            "chdTicketCabin": "",
        },
        "tgEventInfo": {
            "tgType": "改签",
            "firstRefundTime": "",
            "firstRefundAmount": "",
            "secRefundTime": "",
            "secRefundAmount": "",
            "firstChangeTime": "2025-03-21 16:14:31",
            "firstChangePayAmount": "211",
            "firstChangeFeeAmount": "211",
            "firstChangeUpCabinAmount": "0",
        },
        "tgqInfo": {
            "ticketLogInfos": '{"nodeName":"结束","contentExpansion":"","createTime":"2025-03-22 17:48:36","orderNo":"hfx250318003219294","showSummaryBtn":"false","flowNo":"NWF20250321195151983664","displayConversionButton":false,"logTypeName":"审核操作","bgColor":"#F4FBFE","logListType":"COMPENSATION","createName":"尹征","flowLogId":"d2eab41a86a148e6b833f7aca2e73f02","problemNames":"自愿退改/病退,不认可退改规则","id":81827211,"shortContent":"{#logTag<action>type=detail</action>#}","flowLogType":"reviewOperation","contentType":"","content":"备注：【自动赔付审核通过】"}{"content":"","bgColor":"#F4FBFE","flowNo":"NWF20250321195151983664","logListType":"STRUCTURED","shortContent":"","displayConversionButton":false,"showSummaryBtn":"false","id":1,"nodeName":"结束","createTime":"2025-03-21 20:50:26","flowLogId":"3208ae73a9de4b9e94aae44c8cc9c53e","createName":"system","logTypeName":"1 结束结构化"}{"createTime":"2025-03-21 20:50:26","contentExpansion":"","shortContent":"打款成功,金额：863.00","content":"备注：打款成功,金额：863.00","flowNo":"NWF20250321195151983664","id":81758064,"logTypeName":"打款 ","contentType":"","showSummaryBtn":"false","bgColor":"#F4FBFE","flowLogId":"7e282d54fe7d46029c323059a56289f2","flowLogType":"paySuccess","displayConversionButton":false,"createName":"system","logListType":"COMPENSATION","nodeName":"赔付审核","problemNames":"自愿退改/病退,不认可退改规则","orderNo":"hfx250318003219294"}{"logTypeName":"关单","contentExpansion":"","contentType":"","flowLogId":"38d2dce67a2c45bfb0ad3498667777ab","flowLogType":"close","shortContent":"审核完成","problemNames":"自愿退改/病退,不认可退改规则","flowNo":"NWF20250321195151983664","id":81758065,"logListType":"SUM_UP","createName":"system","content":"备注：审核完成","createTime":"2025-03-21 20:50:26","showSummaryBtn":"false","orderNo":"hfx250318003219294","displayConversionButton":false,"bgColor":"#FEF8EF","nodeName":"结束"}{"flowLogId":"bc34e1f67a8b4bb7b1b7404aa1e8c14a","displayConversionButton":false,"contentType":"","problemNames":"自愿退改/病退,不认可退改规则","flowNo":"NWF20250321195151983664","logTypeName":"赔付操作","flowLogType":"compensatinOperation","nodeName":"结束","content":"备注：赔付成功短信已发送","showSummaryBtn":"false","id":81758066,"createTime":"2025-03-21 20:50:26","createName":"system","orderNo":"hfx250318003219294","shortContent":"{#logTag<action>type=detail</action>#}","logListType":"COMPENSATION","bgColor":"#F4FBFE","contentExpansion":""}{"flowNo":"NWF20250321195151983664","createName":"","problemNames":"自愿退改/病退,不认可退改规则","logTypeName":"打款中清空责任人","bgColor":"#F4FBFE","createTime":"2025-03-21 20:50:23","content":"备注：审核完成","id":81758063,"displayConversionButton":false,"showSummaryBtn":"false","orderNo":"hfx250318003219294","flowLogType":"clearResponser","shortContent":"{#logTag<action>type=detail</action>#}","contentType":"","logListType":"COMPENSATION","flowLogId":"3712bb223b914b51a612e212a4d297d9","nodeName":"赔付审核","contentExpansion":""}{"flowLogType":"compensatinOperation","id":81758061,"flowLogId":"84c75565c2424aff97114d93ac1f3930","createTime":"2025-03-21 20:50:21","contentExpansion":"","nodeName":"赔付审核","problemNames":"自愿退改/病退,不认可退改规则","flowNo":"NWF20250321195151983664","displayConversionButton":false,"orderNo":"hfx250318003219294","logTypeName":"赔付操作","contentType":"","content":"备注：同步赔付信息","showSummaryBtn":"false","logListType":"COMPENSATION","shortContent":"{#logTag<action>type=detail</action>#}","createName":"CC南通施回国","bgColor":"#F4FBFE"}{"contentExpansion":"","bgColor":"#F4FBFE","contentType":"","createTime":"2025-03-21 20:50:12","flowLogType":"DIRECTOR_AUDIT_RESULT","nodeName":"外部投诉节点","id":81758050,"displayConversionButton":false,"logListType":"FLOW_OPT","flowLogId":"08ae029b2de64d5b80a9364fc1749bdd","logTypeName":"主管审核","content":"备注：任务id：691437\\n审核结果：同意\\n审核备注：","problemNames":"自愿退改/病退,不认可退改规则","orderNo":"hfx250318003219294","showSummaryBtn":"false","createName":"吴磊m","flowNo":"NWF20250321195151983664","shortContent":"任务id：691437\\n审核结果：同意\\n审核备注："}{"showSummaryBtn":"false","displayConversionButton":false,"flowNo":"NWF20250321195151983664","orderNo":"hfx250318003219294","logTypeName":"待分配","createTime":"2025-03-21 20:50:12","createName":"","content":"","logListType":"TRANSFER","flowLogType":"waitAssign","nodeName":"赔付审核","bgColor":"#FFFFFF","id":81758051,"problemNames":"自愿退改/病退,不认可退改规则","shortContent":"","contentType":"","flowLogId":"3734f9c7dcac4814a296424128cd9100","contentExpansion":""}{"logListType":"SUM_UP","contentExpansion":"","displayConversionButton":false,"orderNo":"hfx250318003219294","problemNames":"自愿退改/病退,不认可退改规则","shortContent":"","flowLogId":"6261525e967e4603b3f16f5b904502d2","id":81758052,"flowNo":"NWF20250321195151983664","contentType":"","nodeName":"赔付审核","bgColor":"#FEF8EF","flowLogType":"transfer","content":"","createName":"CC南通施回国","showSummaryBtn":"false","logTypeName":"转接 - 赔付审核","createTime":"2025-03-21 20:50:12"}{"orderNo":"hfx250318003219294","createName":"CC南通施回国","problemNames":"自愿退改/病退,不认可退改规则","createTime":"2025-03-21 20:49:02","flowLogId":"e7cd89bf02e441118825789942cf286e","id":81757983,"logListType":"FLOW_OPT","showSummaryBtn":"false","flowLogType":"DIRECTOR_AUDIT_COMMIT","contentExpansion":"","contentType":"","flowNo":"NWF20250321195151983664","displayConversionButton":false,"logTypeName":"提交主管审核","content":"备注：任务id：691437\\n审核类型：赔付金额审核\\n用户是否扬言投诉：否\\n用户遭遇和诉求：null\\n航司/代理方案：null\\n对客回复方案：null\\n需主管审核的内容：【一单一议原因：已有流程未配置方案】，用户不认可高额改签费，核实航司504125告知改签手续费355，差价290元我司收取差价297元差价7元，第二次改签住手续费211元 航空公司实际收取200元，不执行航规属实，协商承担一半改签费用户不认可，全部承担用户认可 652+211=863元打款致乘机人卡，记代理责任","bgColor":"#F4FBFE","nodeName":"外部投诉节点","shortContent":"任务id：691437\\n审核类型：赔付金额审核\\n用户是否扬言投诉：否\\n用户遭遇和诉求：null\\n航司/代理方案：null\\n对客回复方案：null\\n需主管审核的内容：【一单一议原因：已有流程未配置方案】，用户不认可高额改签费，核实航司504125告知改签手续费355，差价290元我司收取差价297元差价7元，第二次改签住手续费211元 航空公司实际收取200元，不执行航规属实，协商承担一半改签费用户不认可，全部承担用户认可 652+211=863元打款致乘机人卡，记代理责任"}{"contentType":"","nodeName":"外部投诉节点","contentExpansion":"","content":"备注：【赔付计算器】：\\n赔付方案：一单一议\\n赔付公式：承诺赔付金额(863)=+实际损失:863(qunar)+额外金额:0(qunar)+用户体验:0(qunar)\\n用户意见：认可赔付金额\\n打款信息：赔付方式：银行卡；账户类型：对私；收款人：骆冬芳；银行名称：交通银行；银行卡号：7519622695d7aba91d64a0f0edac70c9；支行信息：北京市-北京市-交通银行股份有限公司北京青塔支行\\n客服责任人：无\\n备注：用户不认可高额改签费，核实航司504125告知改签手续费355，差价290元我司收取差价297元差价7元，第二次改签住手续费211元 航空公司实际收取200元，不执行航规属实，协商承担一半改签费用户不认可，全部承担用户认可 652+211=863元打款致乘机人卡，记代理责任\\n一单一议原因：已有流程未配置方案","flowLogId":"19eea18c8aeb4ac7b5d2774eca67c0cd","id":81757980,"problemNames":"自愿退改/病退,不认可退改规则","createTime":"2025-03-21 20:49:01","showSummaryBtn":"false","orderNo":"hfx250318003219294","shortContent":"{#logTag<action>type=detail</action>#}","logTypeName":"赔付计算器","createName":"CC南通施回国","bgColor":"#F4FBFE","displayConversionButton":false,"flowLogType":"compensationCalculator","logListType":"FLOW_OPT","flowNo":"NWF20250321195151983664"}{"contentExpansion":"","logTypeName":"新增赔付","id":81757982,"flowLogId":"1e6f7bc9e1d24f129e1732014d98495c","displayConversionButton":false,"problemNames":"自愿退改/病退,不认可退改规则","shortContent":"{#logTag<action>type=detail</action>#}","orderNo":"hfx250318003219294","content":"备注：打款方式：银行卡\\n赔付原因：不认可退改规则\\n解决方案：\\n责任方：qunar\\n赔付总金额：863.00\\n详细事由：用户不认可高额改签费，核实航司504125告知改签手续费355，差价290元我司收取差价297元差价7元，第二次改签住手续费211元 航空公司实际收取200元，不执行航规属实，协商承担一半改签费用户不认可，全部承担用户认可 652+211=863元打款致乘机人卡，记代理责任","logListType":"COMPENSATION","flowNo":"NWF20250321195151983664","contentType":"","nodeName":"外部投诉节点","showSummaryBtn":"false","bgColor":"#F4FBFE","flowLogType":"insertCompensatin","createTime":"2025-03-21 20:49:01","createName":"CC南通施回国"}{"contentExpansion":"022FJE3EJGBM1C5E1MGLU2LAES0A7MBG","contentType":"PHONE","nodeName":"外部投诉节点","flowLogId":"8475de2a29c84708b5d32d3b6a6339e0","orderNo":"hfx250318003219294","bgColor":"#F4FBFE","flowLogType":"outbound","logListType":"FLOW_OPT","shortContent":"渠道：电话\\n呼出用户号码:1393d536466","displayConversionButton":false,"content":"渠道：电话\\n备注：呼出用户号码:1393d536466","id":81757136,"flowNo":"NWF20250321195151983664","createTime":"2025-03-21 20:38:56","problemNames":"自愿退改/病退,不认可退改规则","showSummaryBtn":"false","logTypeName":"外呼","createName":"CC南通施回国"}{"showSummaryBtn":"false","createTime":"2025-03-21 20:37:17","logTypeName":"外呼","contentExpansion":"022FJE3EJGBM1C5E1MGLU2LAES0A7M5K","createName":"CC南通施回国","contentType":"PHONE","orderNo":"hfx250318003219294","content":"渠道：电话\\n备注：呼出航司号码:HSJIB57","shortContent":"渠道：电话\\n呼出航司号码:HSJIB57","flowNo":"NWF20250321195151983664","bgColor":"#F4FBFE","flowLogId":"e59651ac3b064bde8328ef8e883e79e1","nodeName":"外部投诉节点","problemNames":"自愿退改/病退,不认可退改规则","id":81756979,"logListType":"FLOW_OPT","displayConversionButton":false,"flowLogType":"outbound"}{"content":"备注：从【我的工单-处理中页】进入工单详情页","createTime":"2025-03-21 20:35:46","logListType":"SYSTEM","problemNames":"自愿退改/病退,不认可退改规则","shortContent":"从【我的工单-处理中页】进入工单详情页","displayConversionButton":false,"orderNo":"hfx250318003219294","bgColor":"#FFFFFF","logTypeName":"工单详情页","flowLogId":"f32305b671c1434685ba65a4201feb21","flowLogType":"entryProcess","flowNo":"NWF20250321195151983664","showSummaryBtn":"false","contentExpansion":"","id":81756856,"nodeName":"外部投诉节点","contentType":"","createName":"CC南通施回国"}{"logListType":"SYSTEM","contentExpansion":"","createName":"CC南通施回国","flowLogType":"entryProcess","flowLogId":"3c29655575bb4914b063e9f5dda658ff","id":81756764,"displayConversionButton":false,"flowNo":"NWF20250321195151983664","orderNo":"hfx250318003219294","shortContent":"从【我的工单-处理中页】进入工单详情页","showSummaryBtn":"false","contentType":"","bgColor":"#FFFFFF","logTypeName":"工单详情页","nodeName":"外部投诉节点","createTime":"2025-03-21 20:34:35","content":"备注：从【我的工单-处理中页】进入工单详情页","problemNames":"自愿退改/病退,不认可退改规则"}{"logTypeName":"工单详情页","id":81756377,"flowLogId":"e181f26987604c00920a48303602772e","createName":"CC南通施回国","shortContent":"从【我的工单-处理中页】进入工单详情页","nodeName":"外部投诉节点","content":"备注：从【我的工单-处理中页】进入工单详情页","orderNo":"hfx250318003219294","createTime":"2025-03-21 20:29:14","contentExpansion":"","displayConversionButton":false,"flowNo":"NWF20250321195151983664","showSummaryBtn":"false","bgColor":"#FFFFFF","flowLogType":"entryProcess","logListType":"SYSTEM","contentType":"","problemNames":"自愿退改/病退,不认可退改规则"}{"problemNames":"自愿退改/病退,不认可退改规则","logTypeName":"外呼","bgColor":"#F4FBFE","shortContent":"渠道：电话\\n呼出用户号码:1393d536466","orderNo":"hfx250318003219294","content":"渠道：电话\\n备注：呼出用户号码:1393d536466","createName":"CC南通施回国","id":81753952,"displayConversionButton":false,"flowLogId":"b81ee532925c4cda91edfa123e9bc066","nodeName":"外部投诉节点","contentExpansion":"022FJE3EJGBM1C5E1MGLU2LAES0A7HIK","createTime":"2025-03-21 19:59:58","showSummaryBtn":"false","flowLogType":"outbound","contentType":"PHONE","flowNo":"NWF20250321195151983664","logListType":"FLOW_OPT"}{"showSummaryBtn":"false","flowNo":"NWF20250321195151983664","createTime":"2025-03-21 19:59:22","orderNo":"hfx250318003219294","createName":"CC南通施回国","problemNames":"自愿退改/病退,不认可退改规则","logListType":"SYSTEM","shortContent":"从【我的工单-处理中页】进入工单详情页","content":"备注：从【我的工单-处理中页】进入工单详情页","bgColor":"#FFFFFF","contentType":"","nodeName":"外部投诉节点","logTypeName":"工单详情页","flowLogId":"fb4226989d9b4137a343e0513b881da4","flowLogType":"entryProcess","displayConversionButton":false,"id":81753906,"contentExpansion":""}{"flowLogType":"assign","logTypeName":"分配责任人","content":"备注：分配人：huiguo.shi","createTime":"2025-03-21 19:56:30","displayConversionButton":false,"id":81753681,"bgColor":"#FFFFFF","logListType":"TRANSFER","flowNo":"NWF20250321195151983664","contentType":"","flowLogId":"47979a1be3554b659ddc876ad66f2434","orderNo":"hfx250318003219294","createName":"system","contentExpansion":"","problemNames":"自愿退改/病退,不认可退改规则","nodeName":"外部投诉节点","shortContent":"分配人：huiguo.shi","showSummaryBtn":"false"}{"flowLogType":"waitAssign","orderNo":"hfx250318003219294","id":81753339,"createName":"system","content":"","showSummaryBtn":"false","flowNo":"NWF20250321195151983664","shortContent":"","bgColor":"#FFFFFF","problemNames":"自愿退改/病退,不认可退改规则","logListType":"TRANSFER","nodeName":"外部投诉节点","logTypeName":"待分配","contentExpansion":"","createTime":"2025-03-21 19:52:10","displayConversionButton":false,"contentType":"","flowLogId":"5de0bc1e833b460799db82ef452aafba"}{"displayConversionButton":false,"contentType":"","flowLogType":"transfer","flowLogId":"f8b80d379c8e4e4082dc44a95a7f755e","flowNo":"NWF20250321195151983664","showSummaryBtn":"false","contentExpansion":"","orderNo":"hfx250318003219294","shortContent":"渠道：微博\\n预约时间：2025-03-21 21:52:08\\n1","logTypeName":"转接 - 外部投诉节点","bgColor":"#FEF8EF","createTime":"2025-03-21 19:52:09","id":81753337,"problemNames":"自愿退改/病退,不认可退改规则","nodeName":"外部投诉节点","content":"渠道：微博\\n预约时间：2025-03-21 21:52:08\\n回库时间：2025-03-21 18:52:08\\n暂缓回库时间：无\\n系统推荐预约时间：（无）\\n备注：1","createName":"外部账号宋云鹏","logListType":"SUM_UP"}{"logTypeName":"保存日志","logListType":"FLOW_OPT","flowLogId":"a17b1325edea43299fcd393031ec6d42","contentExpansion":"","content":"渠道：微博\\n备注：【外部投诉-民航局】\\t2025-03-21 15:32:24\\t骆冬芳\\t13978066466\\t旅客来电表示，因行程有变，提交改期，改期手续费被高额收取。平台告知可退回改期手续费，如需改期22日航班，可退票，重新购买，但退票被收取手续费。对此不满故来电投诉。需要将改期费退回以及免费改期至3月22日航班。\\n","contentType":"","shortContent":"渠道：微博\\n【外部投诉-民航局】\\t2025-03-21 15:32:24\\t骆冬芳\\t13978066466\\t旅客来电表示，因行程有变，提交改期，改期手续费被高额收取。平台告知可退回改期手续费，如需改期22日航班，可退票，重新购买，但退票被收取手续费。对此不满故来电投诉。需要将改期费退回以及免费改期至3月22日航班。\\n","nodeName":"投诉监控","showSummaryBtn":"false","displayConversionButton":false,"orderNo":"hfx250318003219294","createName":"外部账号宋云鹏","id":81753329,"flowNo":"NWF20250321195151983664","flowLogType":"saveNote","createTime":"2025-03-21 19:52:04","problemNames":"自愿退改/病退,不认可退改规则","bgColor":"#F4FBFE"}{"contentExpansion":"","problemNames":"自愿退改/病退,不认可退改规则","flowLogId":"55b5a38386374e75a71d7e86a8120430","createName":"外部账号宋云鹏","flowLogType":"saveNote","bgColor":"#F4FBFE","flowNo":"NWF20250321195151983664","content":"渠道：微博\\n备注：更新【以下场景若和解失败需要代理商/退改提供凭证：\\n一、退款\\n1、未出票拦截失败\\n2、自愿退票不认可退款规则（收费凭证）\\n3、非自愿退款航司拒单\\n4、病退（无法病退或病退所需材料的凭证）\\n5. 变价场景提交要求全退，需核实退票规则\\n6. 提及错购需要核实是否有错购政策\\n二、改签\\n1、非自愿改签被拒绝（拒绝凭证）\\n2、自愿改签不认可改签规则（收费凭证）\\n3、签转失败（无法签转或签转失败凭证）\\n\\n三、修改证件/姓名需要收费或无法修改\\n四、出票\\n1、票价疑义（出票金额凭证）\\n2、儿童票/婴儿票无权限添加或添加失败\\n五、航变未通知/航变通知有误（如航司已通知至出票方，需要代理提供通知凭证）\\n六、航班正常附上动态截图，不正常需核实不正常原因，是否有补偿需核实\\n七、投诉中提交法律条款需确认法务并工单备注】","logTypeName":"保存日志","createTime":"2025-03-21 19:52:04","id":81753330,"shortContent":"渠道：微博\\n更新【以下场景若和解失败需要代理商/退改提供凭证：\\n一、退款\\n1、未出票拦截失败\\n2、自愿退票不认可退款规则（收费凭证）\\n3、非自愿退款航司拒单\\n4、病退（无法病退或病退所需材料的凭证）\\n5. 变价场景提交要求全退，需核实退票规则\\n6. 提及错购需要核实是否有错购政策\\n二、改签\\n1、非自愿改签被拒绝（拒绝凭证）\\n2、自愿改签不认可改签规则（收费凭证）\\n3、签转失败（无法签转或签转失败凭证）\\n\\n三、修改证件/姓名需要收费或无法修改\\n四、出票\\n1、票价疑义（出票金额凭证）\\n2、儿童票/婴儿票无权限添加或添加失败\\n五、航变未通知/航变通知有误（如航司已通知至出票方，需要代理提供通知凭证）\\n六、航班正常附上动态截图，不正常需核实不正常原因，是否有补偿需核实\\n七、投诉中提交法律条款需确认法务并工单备注】","showSummaryBtn":"false","nodeName":"投诉监控","logListType":"FLOW_OPT","contentType":"","displayConversionButton":false,"orderNo":"hfx250318003219294"}{"id":81753319,"showSummaryBtn":"false","flowLogId":"155c0cbc0c2e451391ed8d4846aff500","displayConversionButton":false,"flowNo":"NWF20250321195151983664","createTime":"2025-03-21 19:51:52","logTypeName":"创建","logListType":"FLOW_OPT","flowLogType":"create","orderNo":"hfx250318003219294","contentExpansion":"","shortContent":"渠道：微博\\n一级分类:自愿退改/病退\\n二级分类: 不认可退改规则","problemNames":"自愿退改/病退,不认可退改规则","nodeName":"投诉监控","contentType":"","createName":"外部账号宋云鹏","content":"渠道：微博\\n备注：一级分类:自愿退改/病退\\n二级分类: 不认可退改规则","bgColor":"#F4FBFE"}',
            "airConversations": '{"status":1,"receiver":"hszx.fuwu.qunar.com","msgTypeTwo":"查询客票信息","processTime":122000,"change":false,"msgType":"出票","timestamp":"183257566","resolved":true,"totalTime":0,"hideRely":true,"isFromEvent":0,"passToOTA":false,"conversationStatus":1,"outline":"本会话由【厦航驻场朱纪珂】标记为已解决，附言【1000 50 40，N舱，改签2025年03月21日 17:20前 ¥200/人\\r\\n退票2025年03月21日 17:20前 ¥284/人\\r\\n】","deadline":0,"taskTypeKey":"msg_default_template","mappingOrderNo":"hfx250318003219294001","taskNo":"MSG20250321160034186811","urgencyDegree":1,"sourceDesc":"callcenter","sender":"callcenter.qunar.com","taskType":"hs_check_ticket_info","owner":true,"dueTime":0,"msgStatus":"已解决","userCode":"yujie.ji","flowNo":"NPF20250321144320228929","urgencyDescription":"紧急","targetType":"厦门航空","orderNo":"hfx250318003219294","createTime":"2025-03-21 16:00:34","selfOrderNo":"hfx250318003219294"}{"resolved":true,"taskType":"gaiqian_ask_hs","urgencyDescription":"一般","selfOrderNo":"hfx250318003219294","receiver":"hszx.fuwu.qunar.com","change":false,"userCode":"liandong.liao","deadline":0,"isFromEvent":0,"msgType":"改签","dueTime":0,"taskNo":"MSG20250320202241779387","orderNo":"hfx250318003219294","conversationStatus":1,"processTime":183000,"taskTypeKey":"msg_default_template","timestamp":"183208429","status":1,"outline":"本会话由【厦航驻场朱纪珂】标记为已解决，附言【客票没有改签记录】","hideRely":true,"msgTypeTwo":"咨询改签费用","flowNo":"NITPF20250320201622547368","mappingOrderNo":"hfx250318003219294001","targetType":"厦门航空","msgStatus":"已解决","passToOTA":false,"sourceDesc":"callcenter","createTime":"2025-03-20 20:22:42","totalTime":0,"owner":true,"sender":"callcenter.qunar.com","urgencyDegree":3}',
        },
    }

    is_valid, message = validate_parameters(test_data_1)
    print("Test case 1 - Valid data:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")

    # 测试用例2：缺少必要参数
    test_data_2 = {"baseInfo": {}, "tgEventInfo": {}}

    is_valid, message = validate_parameters(test_data_2)
    print("\nTest case 2 - Missing required fields:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")


def filterTicketLog(flowLogList: list):
    allowed_log_type_names = {"创建", "关单", "待分配", "保存日志", "外呼"}
    allowed_log_list_types = {"FLOW_OPT", "ORDER_OPT", "COMPENSATION"}

    # 过滤ticketLogInfos
    filtered_log_infos = [
        log_info
        for log_info in flowLogList
        if isinstance(log_info, dict)
        and log_info.get("logTypeName") in allowed_log_type_names
        and log_info.get("logListType") in allowed_log_list_types
    ]
    return filtered_log_infos


if __name__ == "__main__":
    # test_validate_parameters()
    param = {
        "success": true,
        "error": "",
        "data": {
            "response": {
                "flowLogList": [
                    {
                        "logListType": "COMPENSATION",
                        "logTypeName": "审核操作",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：【自动赔付审核通过】",
                    },
                    {
                        "logListType": "STRUCTURED",
                        "logTypeName": "1 结束结构化",
                        "problemNames": null,
                        "content": "",
                    },
                    {
                        "logListType": "SUM_UP",
                        "logTypeName": "关单",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：审核完成",
                    },
                    {
                        "logListType": "COMPENSATION",
                        "logTypeName": "赔付操作",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：同步赔付信息",
                    },
                    {
                        "logListType": "COMPENSATION",
                        "logTypeName": "打款成功",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：赔付至余额成功，金额：228.00，流水号：276d7c54229ab33b96a02db06d1eb12a",
                    },
                    {
                        "logListType": "TRANSFER",
                        "logTypeName": "待分配",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "",
                    },
                    {
                        "logListType": "SUM_UP",
                        "logTypeName": "转接 - 赔付审核",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：【赔付计算器】：\n赔付方案：一单一议\n赔付公式：承诺赔付金额(228)=+实际损失:228(代理商)+额外金额:0(代理商)+用户体验:0(代理商)\n用户意见：认可赔付金额\n打款信息：赔付方式：余额账户；类型：下单账户；打款账户：1517wBY5195\n客服责任人：无\n备注：前段航变，影响此单，不认可手续费，要求全退。航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致。一个乘机人。已反馈可按照未执行航规，退票完成后二次补退501-428=73之后，逐级协商线下打款228给用户，记代理责任。用户认可\n一单一议原因：已有流程未配置方案",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "赔付计算器",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：【赔付计算器】：\n赔付方案：一单一议\n赔付公式：承诺赔付金额(228)=+实际损失:228(代理商)+额外金额:0(代理商)+用户体验:0(代理商)\n用户意见：认可赔付金额\n打款信息：赔付方式：余额账户；类型：下单账户；打款账户：1517wBY5195\n客服责任人：无\n备注：前段航变，影响此单，不认可手续费，要求全退。航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致。一个乘机人。已反馈可按照未执行航规，退票完成后二次补退501-428=73之后，逐级协商线下打款228给用户，记代理责任。用户认可\n一单一议原因：已有流程未配置方案",
                    },
                    {
                        "logListType": "COMPENSATION",
                        "logTypeName": "新增赔付",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：打款方式：余额账户\n赔付原因：不认可退改规则\n解决方案：\n责任方：代理商\n赔付总金额：228.00\n详细事由：前段航变，影响此单，不认可手续费，要求全退。航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致。一个乘机人。已反馈可按照未执行航规，退票完成后二次补退501-428=73之后，逐级协商线下打款228给用户，记代理责任。用户认可",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：其他渠道\n备注：同意",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "赔付计算器",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：【赔付计算器】：\n赔付方案：一单一议\n赔付公式：承诺赔付金额(228)=+实际损失:228(代理商)+额外金额:0(代理商)+用户体验:0(代理商)\n用户意见：认可赔付金额\n打款信息：赔付方式：余额账户；类型：下单账户；打款账户：1517wBY5195\n客服责任人：无\n备注：前段航变，影响此单，不认可手续费，要求全退。航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致。一个乘机人。已反馈可按照未执行航规，退票完成后二次补退501-428=73之后，逐级协商线下打款228给用户，记代理责任。用户认可\n一单一议原因：已有流程未配置方案",
                    },
                    {
                        "logListType": "ORDER_OPT",
                        "logTypeName": "提交退款",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：提交的原因：我要改变行程计划、我不想飞了。\n提交乘机人数：1。\n",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：其他渠道\n备注：【工单来源】内部升级\n【来电原因和需求】前段hrn250411173621811航变，影响此单，不认可手续费，要求全退\n【核实过程】航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致\n【投诉根因:责任方】代理\n【投诉根因:一级场景】自愿退改\n【投诉根因:二级场景】非个人不可抗力不认可退款费用\n【投诉根因:缺陷描述】联程航变影响行程产生损失\n【处理结论】已反馈可按照未执行航规，退票完成后二次补退501-428=73之后，逐级协商线下打款228给用户，记代理责任。用户认可\n【回复时间】无需回访\n【是否投诉】是",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "外呼",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：电话\n备注：呼出用户号码:137kzSj7716",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "外呼",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：电话\n备注：呼出用户号码:137kzSj7716",
                    },
                    {
                        "logListType": "USER_OPT",
                        "logTypeName": "上传附件",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：{{$//osd.corp.qunar.com/h_n_callcenter_h_n_callcenter/prod.ee347c53694b4fed995e6e446e3255d9.png$}}",
                    },
                    {
                        "logListType": "USER_OPT",
                        "logTypeName": "上传附件",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "备注：{{$//osd.corp.qunar.com/h_n_callcenter_h_n_callcenter/prod.0adf4088818c4e938d238f101b9dd4a7.png$}}",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：其他渠道\n备注：【工单来源】内部升级\n【来电原因和需求】前段hrn250411173621811航变，影响此单，不认可手续费，要求全退\n【核实过程】航变段因天气原因导致航班取消 无补偿。此段查看知识库无联运协议。航司工号76345告知票价500，V舱。页面再次核实退票手续费我司¥501/人，航司 ¥428/人，仓位一致\n【投诉根因:责任方】代理\n【投诉根因:一级场景】自愿退改\n【投诉根因:二级场景】非个人不可抗力不认可退款费用\n【投诉根因:缺陷描述】联程航变影响行程产生损失\n【处理结论】\n【回复时间】\n【是否投诉】是",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "修改问题类型",
                        "problemNames": "自愿退改/病退,不认可退改规则",
                        "content": "渠道：其他渠道",
                    },
                    {
                        "logListType": "TRANSFER",
                        "logTypeName": "待分配",
                        "problemNames": "航变,咨询联程航变",
                        "content": "",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "强制处理",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：其他渠道",
                    },
                    {
                        "logListType": "TRANSFER",
                        "logTypeName": "处理人变更",
                        "problemNames": "航变,咨询联程航变",
                        "content": "",
                    },
                    {
                        "logListType": "SYSTEM",
                        "logTypeName": "工单详情页",
                        "problemNames": "航变,咨询联程航变",
                        "content": "备注：从【我的工单-处理中页】进入工单详情页",
                    },
                    {
                        "logListType": "SYSTEM",
                        "logTypeName": "清除暂缓时间",
                        "problemNames": "航变,咨询联程航变",
                        "content": "备注：操作清空暂缓回库时间",
                    },
                    {
                        "logListType": "SYSTEM",
                        "logTypeName": "工单详情页",
                        "problemNames": "航变,咨询联程航变",
                        "content": "备注：从【我的工单-暂缓中页】进入工单详情页",
                    },
                    {
                        "logListType": "SUM_UP",
                        "logTypeName": "暂缓",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n收单类型：商家原因\n预约时间：2025-04-13 18:02:09\n暂缓时间：2025-04-13 18:02:09\n暂缓原因：联系不上商家\n回库时间：无\n暂缓回库时间：2025-04-13 17:42:09\n系统推荐预约时间：2025-04-13 18:02:09（继承）\n系统推荐暂缓时间：2025-04-13 18:02:09（继承）",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n备注：D【用户遭遇】：前段航变影响此段\nN【用户诉求】：申请此单联程退\nA【处理方案】：此段查看知识库无联运协议，有再次核实退改，航班正常，航司工号76345告知票价500，V舱，用户投诉我司坑人\nT【回复时间】：2小时\n【是否投诉】：否",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n备注：76345告知票价500，V舱",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "外呼",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n备注：呼出号码:EjW39",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "新增号码",
                        "problemNames": "航变,咨询联程航变",
                        "content": "备注：号码：EjW39；角色：订单其他电话",
                    },
                    {
                        "logListType": "SYSTEM",
                        "logTypeName": "清除暂缓时间",
                        "problemNames": "航变,咨询联程航变",
                        "content": "备注：操作清空暂缓回库时间",
                    },
                    {
                        "logListType": "SUM_UP",
                        "logTypeName": "暂缓",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n收单类型：流程要求\n预约时间：2025-04-13 18:02:09\n暂缓时间：2025-04-13 18:02:09\n暂缓原因：流程要求\n回库时间：无\n暂缓回库时间：2025-04-13 17:42:09\n系统推荐预约时间：2025-04-13 17:02:06（推荐）\n系统推荐暂缓时间：2025-04-13 17:02:06（推荐）",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "保存日志",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n备注：D【用户遭遇】：前段航变影响此段\nN【用户诉求】：申请此单联程退\nA【处理方案】：此段查看知识库无联运协议，有再次核实退改，航班正常，\nT【回复时间】：2小时\n【是否投诉】：否",
                    },
                    {
                        "logListType": "FLOW_OPT",
                        "logTypeName": "创建",
                        "problemNames": "航变,咨询联程航变",
                        "content": "渠道：电话\n电话：137kzSj7716\n备注：一级分类:航变\n问题1:请点击此按钮进行二级分类选择-是\n推荐分类：多个\n最终推荐分类: 咨询联程航变",
                    },
                ]
            }
        },
    }
