<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>增强Markdown格式化器测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .test-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: #fafafa;
    }
    .test-title {
      font-size: 18px;
      font-weight: bold;
      color: #2196F3;
      margin-bottom: 15px;
    }
    .test-data {
      background: #f0f0f0;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 15px;
      font-family: monospace;
      font-size: 12px;
      overflow-x: auto;
    }
    .test-result {
      background: white;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 6px;
      white-space: pre-wrap;
      font-family: monospace;
      font-size: 12px;
      max-height: 400px;
      overflow-y: auto;
    }
    button {
      background: #2196F3;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #1976D2;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .status.success {
      background: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }
    .status.error {
      background: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 增强Markdown格式化器测试</h1>
    
    <div class="test-section">
      <div class="test-title">📊 财务数据表格测试</div>
      <div class="test-data" id="financialData">
        [
          ["项目", "预算", "实际支出", "完成率", "负责人", "联系方式"],
          ["网站开发", "¥50000", "¥45000", "90%", "张三", "<EMAIL>"],
          ["移动应用", "$30000", "$28500", "95%", "李四", "13800138000"],
          ["数据分析", "€25000", "€23000", "92%", "王五", "<EMAIL>"],
          ["市场推广", "¥80000", "¥75000", "94%", "赵六", "15900159000"]
        ]
      </div>
      <button onclick="testFinancialTable()">测试财务表格</button>
      <div class="test-result" id="financialResult"></div>
    </div>

    <div class="test-section">
      <div class="test-title">📅 时间安排表格测试</div>
      <div class="test-data" id="scheduleData">
        [
          ["任务", "开始时间", "结束时间", "状态", "优先级"],
          ["需求分析", "2024-01-15", "2024-01-25", "已完成", "高"],
          ["系统设计", "2024-01-26", "2024-02-10", "进行中", "高"],
          ["编码开发", "2024-02-11", "2024-03-15", "未开始", "中"],
          ["测试验收", "2024-03-16", "2024-03-30", "未开始", "中"],
          ["上线部署", "2024-04-01", "2024-04-05", "未开始", "低"]
        ]
      </div>
      <button onclick="testScheduleTable()">测试时间安排表格</button>
      <div class="test-result" id="scheduleResult"></div>
    </div>

    <div class="test-section">
      <div class="test-title">👥 联系人表格测试</div>
      <div class="test-data" id="contactData">
        [
          ["姓名", "部门", "职位", "电话", "邮箱", "入职日期"],
          ["张明", "技术部", "高级工程师", "13812345678", "<EMAIL>", "2023-03-15"],
          ["李华", "产品部", "产品经理", "13987654321", "<EMAIL>", "2022-08-20"],
          ["王强", "设计部", "UI设计师", "15612345678", "<EMAIL>", "2023-01-10"],
          ["刘芳", "市场部", "市场专员", "18712345678", "<EMAIL>", "2023-06-01"]
        ]
      </div>
      <button onclick="testContactTable()">测试联系人表格</button>
      <div class="test-result" id="contactResult"></div>
    </div>

    <div class="test-section">
      <div class="test-title">📦 库存数据表格测试</div>
      <div class="test-data" id="inventoryData">
        [
          ["产品编号", "产品名称", "当前库存", "安全库存", "单价", "供应商"],
          ["P001", "笔记本电脑", "150", "50", "¥5999", "联想科技"],
          ["P002", "无线鼠标", "500", "100", "¥89", "罗技公司"],
          ["P003", "机械键盘", "80", "30", "¥299", "雷蛇公司"],
          ["P004", "显示器", "25", "10", "¥1299", "戴尔公司"],
          ["P005", "耳机", "200", "50", "¥199", "索尼公司"]
        ]
      </div>
      <button onclick="testInventoryTable()">测试库存表格</button>
      <div class="test-result" id="inventoryResult"></div>
    </div>

    <div class="test-section">
      <div class="test-title">📊 绩效数据表格测试</div>
      <div class="test-data" id="performanceData">
        [
          ["员工姓名", "目标销售额", "实际销售额", "完成率", "客户满意度", "评级"],
          ["陈伟", "1000000", "1150000", "115%", "4.8", "优秀"],
          ["林静", "800000", "750000", "93.75%", "4.5", "良好"],
          ["周杰", "1200000", "1080000", "90%", "4.2", "合格"],
          ["吴敏", "900000", "945000", "105%", "4.7", "优秀"],
          ["黄涛", "1100000", "990000", "90%", "4.0", "合格"]
        ]
      </div>
      <button onclick="testPerformanceTable()">测试绩效表格</button>
      <div class="test-result" id="performanceResult"></div>
    </div>

    <div class="test-section">
      <div class="test-title">🔧 综合测试</div>
      <button onclick="runAllTests()">运行所有测试</button>
      <button onclick="clearAllResults()">清空结果</button>
      <div class="status" id="testStatus" style="display: none;"></div>
    </div>
  </div>

  <script src="lib/enhanced-markdown-formatter.js"></script>
  <script>
    let formatter;
    
    // 初始化格式化器
    function initFormatter() {
      try {
        formatter = new EnhancedMarkdownFormatter();
        showStatus('增强Markdown格式化器初始化成功', 'success');
        return true;
      } catch (error) {
        showStatus('格式化器初始化失败: ' + error.message, 'error');
        return false;
      }
    }

    function showStatus(message, type) {
      const status = document.getElementById('testStatus');
      status.textContent = message;
      status.className = 'status ' + type;
      status.style.display = 'block';
      setTimeout(() => {
        status.style.display = 'none';
      }, 3000);
    }

    function testFinancialTable() {
      if (!formatter && !initFormatter()) return;
      
      const data = [
        ["项目", "预算", "实际支出", "完成率", "负责人", "联系方式"],
        ["网站开发", "¥50000", "¥45000", "90%", "张三", "<EMAIL>"],
        ["移动应用", "$30000", "$28500", "95%", "李四", "13800138000"],
        ["数据分析", "€25000", "€23000", "92%", "王五", "<EMAIL>"],
        ["市场推广", "¥80000", "¥75000", "94%", "赵六", "15900159000"]
      ];
      
      const result = formatter.formatTable(data, {
        tableName: "项目财务数据",
        includeMetadata: true,
        includeUsageTips: true
      });
      
      document.getElementById('financialResult').textContent = result;
    }

    function testScheduleTable() {
      if (!formatter && !initFormatter()) return;
      
      const data = [
        ["任务", "开始时间", "结束时间", "状态", "优先级"],
        ["需求分析", "2024-01-15", "2024-01-25", "已完成", "高"],
        ["系统设计", "2024-01-26", "2024-02-10", "进行中", "高"],
        ["编码开发", "2024-02-11", "2024-03-15", "未开始", "中"],
        ["测试验收", "2024-03-16", "2024-03-30", "未开始", "中"],
        ["上线部署", "2024-04-01", "2024-04-05", "未开始", "低"]
      ];
      
      const result = formatter.formatTable(data, {
        tableName: "项目时间安排",
        includeMetadata: true,
        includeUsageTips: true
      });
      
      document.getElementById('scheduleResult').textContent = result;
    }

    function testContactTable() {
      if (!formatter && !initFormatter()) return;
      
      const data = [
        ["姓名", "部门", "职位", "电话", "邮箱", "入职日期"],
        ["张明", "技术部", "高级工程师", "13812345678", "<EMAIL>", "2023-03-15"],
        ["李华", "产品部", "产品经理", "13987654321", "<EMAIL>", "2022-08-20"],
        ["王强", "设计部", "UI设计师", "15612345678", "<EMAIL>", "2023-01-10"],
        ["刘芳", "市场部", "市场专员", "18712345678", "<EMAIL>", "2023-06-01"]
      ];
      
      const result = formatter.formatTable(data, {
        tableName: "员工联系信息",
        includeMetadata: true,
        includeUsageTips: true
      });
      
      document.getElementById('contactResult').textContent = result;
    }

    function testInventoryTable() {
      if (!formatter && !initFormatter()) return;
      
      const data = [
        ["产品编号", "产品名称", "当前库存", "安全库存", "单价", "供应商"],
        ["P001", "笔记本电脑", "150", "50", "¥5999", "联想科技"],
        ["P002", "无线鼠标", "500", "100", "¥89", "罗技公司"],
        ["P003", "机械键盘", "80", "30", "¥299", "雷蛇公司"],
        ["P004", "显示器", "25", "10", "¥1299", "戴尔公司"],
        ["P005", "耳机", "200", "50", "¥199", "索尼公司"]
      ];
      
      const result = formatter.formatTable(data, {
        tableName: "产品库存信息",
        includeMetadata: true,
        includeUsageTips: true
      });
      
      document.getElementById('inventoryResult').textContent = result;
    }

    function testPerformanceTable() {
      if (!formatter && !initFormatter()) return;
      
      const data = [
        ["员工姓名", "目标销售额", "实际销售额", "完成率", "客户满意度", "评级"],
        ["陈伟", "1000000", "1150000", "115%", "4.8", "优秀"],
        ["林静", "800000", "750000", "93.75%", "4.5", "良好"],
        ["周杰", "1200000", "1080000", "90%", "4.2", "合格"],
        ["吴敏", "900000", "945000", "105%", "4.7", "优秀"],
        ["黄涛", "1100000", "990000", "90%", "4.0", "合格"]
      ];
      
      const result = formatter.formatTable(data, {
        tableName: "员工绩效数据",
        includeMetadata: true,
        includeUsageTips: true
      });
      
      document.getElementById('performanceResult').textContent = result;
    }

    function runAllTests() {
      showStatus('正在运行所有测试...', 'success');
      testFinancialTable();
      testScheduleTable();
      testContactTable();
      testInventoryTable();
      testPerformanceTable();
      showStatus('所有测试完成', 'success');
    }

    function clearAllResults() {
      const results = ['financialResult', 'scheduleResult', 'contactResult', 'inventoryResult', 'performanceResult'];
      results.forEach(id => {
        document.getElementById(id).textContent = '';
      });
      showStatus('结果已清空', 'success');
    }

    // 页面加载时初始化
    window.onload = function() {
      initFormatter();
    };
  </script>
</body>
</html>
