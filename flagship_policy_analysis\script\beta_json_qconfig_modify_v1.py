# 导入需要的包(本案例不需要导入以下两个python包，如果有不支持的包，可以找平台管理员安装)
# import json
#
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple, Union
import random
import string
import json
import requests
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> Dict[str, Any]:
    """统一处理请求异常，返回统一的错误格式"""
    if isinstance(e, requests.exceptions.HTTPError):
        return {"status": 1, "message": f"http错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.ConnectionError):
        return {"status": 1, "message": f"连接错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.Timeout):
        return {"status": 1, "message": f"超时错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.RequestException):
        return {"status": 1, "message": f"请求错误: {str(e)}"}
    else:
        # 其他异常转换为统一格式
        return {"status": 1, "message": f"{e.__class__.__name__}: {str(e)}"}

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 校验入参
    required_params = ['targetgroupid', 'dataid', 'env', 'content', 'processId', 'oaId']
    for param_name in required_params:
        if param_name not in param or not param[param_name]:
            return {
                "status": 1,
                "message": f"参数 {param_name} 不能为空"
            }
    
    # 校验processId和oaId格式并转换为int
    try:
        process_id = int(param['processId'])
        oa_id = int(param['oaId'])
    except ValueError:
        return {
            "status": 1,
            "message": "参数 processId 或 oaId 格式不正确，必须为整数"
        }

    # 步骤一和二：GET请求获取当前配置
    try:
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': param['dataid'],
            'env': param['env'],
            "subenv": param.get('subenv', ''),
            'targetgroupid': param['targetgroupid']
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        return handle_request_exception(e)

    # 步骤三：处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": get_result.get('status', 1),
            "message": get_result.get('message', '获取配置失败')
        }

    config_data = get_result['data']
    current_data = config_data.get('data', '')

    # 步骤四：处理数据
    new_content = process_content(param['dataid'], current_data, param['content'], param)
    if not new_content:
        return {"status": 1, "message": "入参配置解析失败"}


    #步骤五：POST请求更新配置
    try:
        post_url = "http://f-tts-activity-config.fd-329539-tros.inner3.beta.qunar.com/activityConfig/visualizationPublish/submitQConfigByAi"
        headers = {
            "Content-Type": "application/json",
            "Cookie": "_xconfig_userId=pengyy.tan"
        }

        post_data = {
            "processId": process_id,
            "oaId": oa_id,
            "configData": generate_config(param['dataid'],new_content)
        }
        post_response = requests.post(
            post_url,
            headers=headers,
            json=post_data,
            timeout=10
        )
        post_response.raise_for_status()
        post_result = post_response.json()

        # 处理post返回结果
        if post_result.get('status') != 0:
            return {
                "status": post_result.get('status', 1),
                "message": post_result.get('msg', '上传配置失败')
            }
        
        # 成功时返回统一格式的字典
        return {
            "status": 0,  # 成功状态
            "message": "操作成功",
            "data": post_result.get('data')
        }

    except Exception as e:
        return handle_request_exception(e)

def generate_random_row(existing_rows: set, length: int = 4) -> str:
    """生成不重复的随机行号

    参数:
    existing_rows: set - 已存在的row值集合
    length: int - 随机字符串长度，默认为4位

    返回:
    str - 生成的随机行号
    """
    characters = string.ascii_uppercase + string.digits  # 只包含大写字母和数字
    while True:
        # 生成指定长度的随机字符串
        random_row = ''.join(random.choices(characters, k=length))
        # 确保生成的row值不重复
        if random_row not in existing_rows:
            return random_row

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result

def process_properties_content(current_data: str, new_content: str) -> str:
    """处理.properties格式的配置内容（修复版）"""
    current_dict = {}

    # 解析当前数据
    if current_data:
        for line in current_data.split('\n'):
            line = line.strip()
            if line and '=' in line:
                key, value = line.split('=', 1)
                current_dict[key.strip()] = value.strip()

    # 解析新内容（假设 parse_urlencoded_structured_data 返回字段在 columns 中）
    content_items, parse_status = parse_urlencoded_structured_data(new_content)
    if parse_status["status"] != "success":
        raise ValueError("新内容解析失败")
    
    merged_list = []
    merged_dict = current_dict.copy()  # 保留现有数据

    for item in content_items:
        field_name = str(item.get("field_name", "")).strip()
        field_value = str(item.get("field_value", "")).strip()
        split_char = str(item.get("split_char", "")).strip().upper()

        if not field_name:
            continue  # 跳过无效字段

        # 确定分隔符（默认为逗号）
        delimiter = "," if split_char == "COMMA" else ""

        # 合并逻辑
        if field_name in merged_dict:
            if delimiter:
                # 去重合并
                existing_values = merged_dict[field_name].split(delimiter)
                if field_value not in existing_values:
                    merged_dict[field_name] += f"{delimiter}{field_value}"
                    merged_list.append({field_name:merged_dict[field_name]})
            else:
                # 覆盖模式
                merged_dict[field_name] = field_value
                merged_list.append({field_name:merged_dict[field_name]})
        else:
            # 新增字段
            merged_dict[field_name] = field_value
            merged_list.append({field_name:merged_dict[field_name]})

    return merged_list

def _generate_row_value(new_item: Dict[str, Any], existing_rows: set, row_key_format: str = None, param: Dict[str, Any] = None, max_numeric_row: int = 1) -> Tuple[str, int]:
    """生成row值

    参数:
    new_item: Dict[str, Any] - 新项目数据
    existing_rows: set - 已存在的row值集合
    row_key_format: str - rowKeyFormat设置格式
    param: Dict[str, Any] - 参数字典
    max_numeric_row: int - 当前最大数字row值

    返回:
    Tuple[str, int] - 生成的row值和更新后的max_numeric_row
    """
    # 先检查columns中的rowKey字段
    columns = new_item.get('columns', {})
    row_key = columns.get('rowKey', '')

    # 检查rowKey是否符合rowKeyFormat的要求且不重复
    use_row_key = False
    if row_key:
        # 检查rowKey是否不重复
        if row_key not in existing_rows:
            # 如果rowKeyFormat为numeric，则还需要检查rowKey是否为数字
            if row_key_format == 'numeric':
                if row_key.isdigit():
                    use_row_key = True
            else:
                # 随机生成模式，只要不重复即可
                use_row_key = True

    if use_row_key:
        # 使用columns中的rowKey作为row值
        return row_key, max_numeric_row
    else:
        # 根据rowKeyFormat设置格式生成row值
        if row_key_format == 'numeric':
            # 数字递增模式
            max_numeric_row += 1
            new_row = str(max_numeric_row)

            # 检查生成的数字row值是否已存在，如果存在则继续递增
            while new_row in existing_rows:
                max_numeric_row += 1
                new_row = str(max_numeric_row)
        else:
            # 默认随机生成模式
            # 获取随机字符串长度，默认为4位
            random_length = 4
            if param and 'randomLength' in param:
                try:
                    random_length = int(param['randomLength'])
                except (ValueError, TypeError):
                    pass
            # 生成不重复的随机row值
            new_row = generate_random_row(existing_rows, random_length)

        return new_row, max_numeric_row

def process_t_content(current_data: str, new_content: str, param: Dict[str, Any] = None) -> str:
    """处理.t格式的配置内容

    参数:
    current_data: str - 当前数据
    new_content: str - 新内容
    param: Dict[str, Any] - 参数字典，包含rowKeyFormat设置格式

    返回:
    str - 处理后的内容
    """
    try:
        result_list = []
        current_list = json.loads(current_data) if current_data else []
        content_items, parse_status = parse_urlencoded_structured_data(new_content)
        if parse_status["status"] != "success":
            return ""
        new_items = [{"columns": item} for item in content_items]

        # 获取现有的所有row值
        existing_rows = {item.get('row') for item in current_list if item.get('row')}

        # 创建行号到索引的映射
        row_map = {item.get('row'): i for i, item in enumerate(current_list)}

        # 获取rowKeyFormat设置格式
        row_key_format = None
        if param and 'rowKeyFormat' in param:
            row_key_format = param['rowKeyFormat']

        # 如果是数字递增模式，找出当前最大数字row值
        max_numeric_row = 1
        if row_key_format == 'numeric':
            for item in current_list:
                row = item.get('row')
                if row and row.isdigit():
                    try:
                        row_value = int(row)
                        max_numeric_row = max(max_numeric_row, row_value)
                    except (ValueError, TypeError):
                        # 忽略非数字的row值
                        pass

        for new_item in new_items:
            # 生成row值
            new_row, max_numeric_row = _generate_row_value(
                new_item, existing_rows, row_key_format, param, max_numeric_row
            )
            new_item['row'] = new_row
            existing_rows.add(new_row)
            # 添加到列表末尾
            result_list.append(new_item)
        return result_list

    except json.JSONDecodeError:
        return new_content

def process_content(dataid: str, current_data: str, new_content: str, param: Dict[str, Any] = None) -> str:
    """根据文件类型处理配置内容

    参数:
    dataid: str - 数据ID
    current_data: str - 当前数据
    new_content: str - 新内容
    param: Dict[str, Any] - 参数字典，包含rowKeyFormat设置格式

    返回:
    str - 处理后的内容
    """
    if dataid.endswith('.properties'):
        return process_properties_content(current_data,new_content)
    elif dataid.endswith('.t'):
        content = process_t_content(current_data, new_content, param)
        return transform_data(content)
    return new_content

def transform_data(original_data):
    transformed = []
    for item in original_data:
        new_item = {
            "_xconfig_id_": item["row"],
            "@data_row_id@": item["row"],
            **item["columns"]
        }
        transformed.append(new_item)
    return transformed

def generate_config(dataid: str, config_data: list) -> dict:
    """
    将配置数据按 dataid 封装为指定格式

    :param dataid: 配置文件名（如 "product_code.t"）
    :param config_data: 配置数据列表，格式为 List[Dict]
    :return: 结构化配置字典
    """
    return {dataid: config_data}