import json
import re
from typing import Any, Dict, Optional, Union
import requests


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    if not proxy:
        raise ValueError("proxy parameter is required")


    try:
        HEADER_SERVER_TOKEN = "Q-Server-Token"
        HEADER_APP_CODE = "Q-App-Code"
        # Make POST request with proxyData as JSON
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        response = requests.post(
            proxy, json=proxyData, headers=headers
        )

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def queryOrderDetail(appCode: str, 
    appToken: str, orderNo: str) -> Dict[str, Any]:
    """
    查询订单详情
    Args:
        orderNo: 订单号
        currentId: 当前用户ID
        mdp: 用户mdp
    Returns:
        Dict: 包含订单详情的字典
    """
    proxyData = {
        "method": "get",
        "url": f"https://hcallcenter.corp.qunar.com/callcenter/flight/orderDetail?orderNo={orderNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        print("--------------------------------result:",result)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": {}}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": {}}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": {}}

        return inner_data

    except Exception as e:
        return {}

def parse_response_data(response_data):
    """
    解析响应数据并提取所需字段
    """
    print("--------------------------------response_data:",json.dumps(response_data, ensure_ascii=False, indent=2))
    ordinary_order = response_data.get("ordinaryOrderVO", {})
    passenger_info = response_data.get("passengerInfoVO", {})
    price_detail = response_data.get("priceDetail", {})
    print("--------------------------------ordinary_order:",json.dumps(ordinary_order, ensure_ascii=False, indent=2))
    # 1. 航程类型
    flight_type = ordinary_order.get("flightType", "")
    
    # 2. 订单状态
    status_str = ordinary_order.get("statusStr", "")
    
    # 3. 订单总价
    total_price = price_detail.get("totalPaymentDetails", {}).get("allPrice", 0)
    
    # 4. 创建时间
    create_time = response_data.get("createTime", "")
    
    # 5. 订单人数（成人）
    adult_count = 0
    adult_cabin = ""
    adult_view_price = 0
    adult_construction_fee = 0
    adult_fuel_tax_fee = 0
    adult_marketing_price = 0
    
    child_cabin = ""
    child_view_price = 0
    child_construction_fee = 0
    child_fuel_tax_fee = 0
    child_marketing_price = 0
    
    # 6. 新增字段：客户端域名
    client_domain = ordinary_order.get("clientDomain", "")
    
    # 7. 新增字段：退款金额
    refund_xcd_amount = price_detail.get("refundXcdAmount", 0)
    
    # 处理乘客信息
    for passenger in passenger_info.get("passengerVOs", []):
        if passenger.get("passengerType") == "成人":
            adult_count += 1
            if not adult_cabin:
                adult_cabin = passenger.get("cabin", "")
        elif passenger.get("passengerType") == "儿童":
            if not child_cabin:
                child_cabin = passenger.get("cabin", "")
    
    # 处理价格信息
    for price in price_detail.get("plist", []):
        if price.get("priceTypeName") == "成人":
            adult_view_price = price.get("viewPrice", 0)
            adult_construction_fee = price.get("constructionFee", 0)
            adult_fuel_tax_fee = price.get("fuelTaxFee", 0)
            adult_marketing_price = price.get("marketingPrice", 0)
        elif price.get("priceTypeName") == "儿童":
            child_view_price = price.get("viewPrice", 0)
            child_construction_fee = price.get("constructionFee", 0)
            child_fuel_tax_fee = price.get("fuelTaxFee", 0)
            child_marketing_price = price.get("marketingPrice", 0)
    
    # 拼接乘客信息
    passenger_info_parts = [f"订单类型：{flight_type}"]
    
    # 添加成人信息（如果存在）
    if adult_cabin or adult_view_price or adult_construction_fee or adult_fuel_tax_fee or adult_marketing_price:
        adult_info = (
            f"成人,舱位：{adult_cabin},票面价：{adult_view_price},"
            f"基建：{adult_construction_fee},燃油：{adult_fuel_tax_fee},"
            f"周边产品：{adult_marketing_price}"
        )
        passenger_info_parts.append(adult_info)
    
    # 添加儿童信息（如果存在）
    if child_cabin or child_view_price or child_construction_fee or child_fuel_tax_fee or child_marketing_price:
        child_info = (
            f"儿童,舱位：{child_cabin},票面价：{child_view_price},"
            f"基建：{child_construction_fee},燃油：{child_fuel_tax_fee},"
            f"周边产品：{child_marketing_price}"
        )
        passenger_info_parts.append(child_info)
    
    passenger_info_str = ";".join(passenger_info_parts)
    
    return {
        "error": "",
        "results": {
            "flightType": flight_type,
            "statusStr": status_str,
            "totalPrice": total_price,
            "createTime": create_time,
            "passengerNum": adult_count,
            "adultCabin": adult_cabin,
            "adultViewPrice": adult_view_price,
            "adultConstructionFee": adult_construction_fee,
            "adultFuelTaxFee": adult_fuel_tax_fee,
            "adultMarketingPrice": adult_marketing_price,
            "childCabin": child_cabin,
            "childViewPrice": child_view_price,
            "childConstructionFee": child_construction_fee,
            "childFuelTaxFee": child_fuel_tax_fee,
            "childMarketingPrice": child_marketing_price,
            "passengerInfo": passenger_info_str,
            "clientDomain": client_domain,
            "refundXcdAmount": refund_xcd_amount
        }
    }

def main(param: Dict[str, str]) -> Dict[str, Any]:
    """
    主入口函数
    """
    # 从参数中提取必要的字段
    orderNo = param.get("orderNo")
    appCode = param.get("invokeAppCode")
    invokeToken = param.get("invokeToken")
    
    # 检查参数是否完整
    if not orderNo:
        return {"error": "缺少必要的参数: orderNo", "results": {}}


    # 发起请求并获取数据
    response_data = queryOrderDetail(appCode, invokeToken, orderNo)

    # 解析响应数据
    return parse_response_data(response_data)

def test():
    param = {
        "orderNo": "jkj250313165954035",
        "invokeAppCode": "f_pangu",
        "invokeToken": "oABPAGnqeGY7y8OtsKjLiHQ2K1SzBuOLT8zYWyHCnviQX+KqugKDLwDcGpD8qRXSGTz2BFiDyWrwmI1otiXHGjlAIndwbOQh3LdaHBI7NR6s6G04LJB3fUFQHpVpjC4ISGT7gjw8IgiGR+b+ln/t0BsdLbkp1ZeHRbCZcgLw2Z0="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 