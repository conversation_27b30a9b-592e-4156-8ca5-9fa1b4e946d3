import pandas as pd
import requests
import json
import time
from typing import List, Dict, Any


def read_excel_file(file_path: str) -> List[Dict[str, Any]]:
    """
    读取Excel文件并返回数据数组

    Args:
        file_path: Excel文件路径

    Returns:
        包含Excel数据的数组，每行数据为一个字典
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 确保必要的列存在
        required_columns = ["用户名", "uniqKey", "urs-日期"]
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"Excel文件必须包含以下列: {required_columns}")

        # 将DataFrame转换为字典列表
        data_list = df.to_dict("records")
        return data_list
    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        return []


def call_http_api(url: str, user_name: str, uniq_key: str, urs_date: str) -> bool:
    """
    调用HTTP接口

    Args:
        url: 请求URL
        user_name: 用户名
        uniq_key: 唯一键
        urs_date: URS日期

    Returns:
        是否调用成功
    """
    try:
        # 构建请求数据
        payload = {
            "needExecAl": "是",
            "notExecAlReason": "",
            "uid": "",
            "uniqKey": uniq_key,
            "ursDate": urs_date,
            "user_name": user_name,
        }

        # 发送POST请求
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, json=payload, headers=headers)

        # 检查响应状态
        if response.status_code == 200:
            print(f"成功调用接口: {user_name}")
            return True
        else:
            print(f"调用接口失败: {user_name}, 状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"调用接口时发生错误: {str(e)}")
        return False


def main():
    # Excel文件路径
    excel_path = "D:/work/需求/202502/UPS-多次搜索全量数据分析/43-46重跑数据.xlsx"  # 请根据实际情况修改文件路径

    # API URL
    api_url = "https://hf7l9aiqzx.feishu.cn/base/workflow/webhook/event/Li30aiwkgwv5qohLvUmc2e19nRf"  # 请根据实际情况修改API URL

    # 读取Excel文件
    data_list = read_excel_file(excel_path)

    if not data_list:
        print("没有读取到数据，程序退出")
        return

    # 遍历数据并调用接口
    for row in data_list:
        user_name = row["用户名"]
        uniq_key = row["uniqKey"]
        urs_date = row["urs-日期"]

        # 调用HTTP接口
        success = call_http_api(api_url, user_name, uniq_key, urs_date)

        if not success:
            print(f"处理数据失败: {user_name}")

        # 每次调用后延迟1分钟
        print("等待1分钟后继续...")
        time.sleep(60)  # 60秒 = 1分钟


if __name__ == "__main__":
    main()
