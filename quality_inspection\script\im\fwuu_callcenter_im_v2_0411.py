#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Any, Dict
import requests
import json
import urllib.parse
from datetime import datetime
import time
import re


def mask_sensitive_info(text: str) -> str:
    """
    对文本中的敏感信息进行脱敏处理
    处理规则：
    1. 身份证号：保留前3位，后面用*替代
    2. 手机号：保留前3位，后面用*替代
    3. 银行卡号：保留前3位，后面用*替代
    
    Args:
        text: 需要脱敏的文本
        
    Returns:
        脱敏后的文本
    """
    if not text:
        return text
        
    # 身份证号脱敏 (18位数字或17位数字+X)
    text = re.sub(r'(\d{3})\d{11}(\d{3}[0-9Xx])', r'\1***********\2', text)
    text = re.sub(r'(\d{3})\d{11}(\d{3})', r'\1***********\2', text)
    
    # 手机号脱敏 (11位数字)
    text = re.sub(r'(\d{3})\d{4}(\d{4})', r'\1****\2', text)
    
    # 银行卡号脱敏 (16-19位数字)
    text = re.sub(r'(\d{3})\d{9,13}(\d{4})', r'\1************\2', text)
    
    return text


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def get_flow_log_list(appCode: str, appToken: str, flow_no: str) -> Dict[str, Any]:
    """
    调用flowLog/list接口获取会话流程日志
    :param flow_no: 流程编号
    :return: 接口返回的数据
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/callcenter/flowLog/list?flowNo={flow_no}&newPage=true",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        print("--------------------------------result: ", result)
        
        # 检查是否有错误
        if "error" in result:
            return {"error": result["error"], "data": {}}

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"error": error_msg, "data": {}}

        # 获取内层data
        inner_data = response_data.get("data", {})
        if not inner_data:
            return {"error": "Inner data is empty", "data": {}}

        return inner_data

    except Exception as e:
        return {"error": f"查询处理失败: {str(e)}", "data": {}}


def get_chat_detail(appCode: str, appToken: str, session_id: str) -> Dict[str, Any]:
    """
    调用chat/detail/new接口获取聊天详情
    :param session_id: 会话ID
    :return: 接口返回的数据
    """
    # 确保session_id已编码
    encoded_session_id = urllib.parse.quote(session_id)
    
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/callcenter/logs/chat/detail/new?sessionId={encoded_session_id}&bizLine=FLIGHT",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        print("--------------------------------result: ", result)
        # 检查是否有错误
        if "error" in result:
            return {"error": result["error"], "data": {}}

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"error": error_msg, "data": {}}

        # 获取内层data
        inner_data = response_data.get("data", {})
        if not inner_data:
            return {"error": "Inner data is empty", "data": {}}

        return inner_data

    except Exception as e:
        return {"error": f"查询处理失败: {str(e)}", "data": {}}


def process_timestamp(timestamp):
    """将毫秒时间戳转换为可读时间格式"""
    dt = datetime.fromtimestamp(timestamp / 1000)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def main(param: Dict[str, str]) -> Dict[str, Any]:  
    """
    主函数
    :param flow_no: 流程编号
    :return: 处理后的会话内容列表
    """
    flow_no = param.get("flow_no", "")
    appCode = param.get("invokeAppCode")
    appToken = param.get("invokeToken")
    
    # 调用第一个接口获取flow_log_list
    flow_log_response = get_flow_log_list(appCode, appToken, flow_no)
    
    if not flow_log_response or "error" in flow_log_response:
        error_msg = flow_log_response.get("error", "当前条件未检索到数据")
        return {"error": error_msg, "results": [], "jsonResult": ""}
    
    # 提取flow_log_list
    flow_log_list = flow_log_response.get("flowLogList", [])
    # 处理session_id列表
    session_id_list = []
    for log in flow_log_list:
        content = log.get("content", "")
        content_expansion = log.get("contentExpansion", "")
        
        # 检查是否包含chat且contentExpansion包含sessionId
        if "chat" in content and "sessionId" in content_expansion:
            try:
                expansion_data = json.loads(content_expansion)
                session_id = expansion_data.get("sessionId", "")
                if session_id:
                    # URL解码session_id(如果需要)
                    decoded_session_id = urllib.parse.unquote(session_id)
                    session_id_list.append(decoded_session_id)
            except json.JSONDecodeError:
                continue
                
    if not session_id_list:
        return {"error": "当前条件未检索到数据", "results": [], "jsonResult": ""}
    
    # session_id_list去重
    session_id_list = list(set(session_id_list))

    # 存储最终结果
    results = []
    
    # 遍历所有sessionId调用第二个接口
    for session_id in session_id_list:
        chat_detail_response = get_chat_detail(appCode, appToken, session_id)
        
        # 提取消息列表
        msg_list = chat_detail_response.get("baseMsgListResponseDto", {}).get("msgList", [])
        if not msg_list:
            continue
        # 过滤消息
        for msg in msg_list:
            user_name = msg.get("userName", "")
            digest = msg.get("digest", "")
            timestamp = msg.get("time", 0)
            
            # 过滤条件
            if (user_name == "智能机器人小驼" or 
                not digest or 
                digest == "用户回来" or 
                digest == "用户离开" or 
                digest == "[位置]"):
                continue
            
            # 对digest进行脱敏处理
            masked_digest = mask_sensitive_info(digest)
            
            formatted_time = process_timestamp(timestamp)
            results.append({
                "userName": user_name,
                "time": timestamp,  # 保留原始时间戳用于排序
                "formatted_time": formatted_time,
                "digest": masked_digest
            })
    
    # 按时间升序排序
    results.sort(key=lambda x: x["time"])
    
    # 格式化最终结果列表
    formatted_results = []
    for item in results:
        formatted_results.append({
            "userName": item["userName"],
            "time": item["formatted_time"],
            "digest": item["digest"]
        })
    # 如果results过大，截取前2000个
    if len(formatted_results) > 2000:
        formatted_results = formatted_results[:2000]
    # json中文编码
    json_result = json.dumps(formatted_results, ensure_ascii=False)
    return {"error": "", "results": formatted_results, "jsonResult": json_result}  


if __name__ == "__main__":
    param = {
        "flow_no": "NIMAF20250414102822861983",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))
