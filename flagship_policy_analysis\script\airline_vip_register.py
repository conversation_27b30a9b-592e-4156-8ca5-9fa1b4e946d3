# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import requests # type: ignore
from dataclasses import dataclass
import random
import string
import traceback

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> ConfigError:
    """统一处理请求异常"""
    if isinstance(e, requests.exceptions.HTTPError):
        return ConfigError(1, f"http错误: {str(e)}")
    elif isinstance(e, requests.exceptions.ConnectionError):
        return ConfigError(1, "连接错误")
    elif isinstance(e, requests.exceptions.Timeout):
        return ConfigError(1, "超时错误")
    elif isinstance(e, requests.exceptions.RequestException):
        return ConfigError(1, "请求错误")
    else:
        # 返回异常栈信息以便于调试
        error_stack = traceback.format_exc()
        return ConfigError(1, f"未知错误: {error_stack}")

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }

    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }

    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }

    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取必要字段并检查
    carrier = mapping_data.get("carrier", "")
    if not carrier:
        return {
            "status": "error",
            "message": "carrier 不能为空",
            "data": None
        }
    
    product_tag = mapping_data.get("productTag", "")
    if not product_tag:
        return {
            "status": "error",
            "message": "productTag 不能为空",
            "data": None
        }
    # 赋值为子tag
    product_tag = product_tag + "1"
    
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    flagship_name = mapping_data.get("flagshipName", "")
    if not flagship_name:
        return {
            "status": "error",
            "message": "flagshipName 不能为空",
            "data": None
        }
    
    carrier_site = mapping_data.get("carrierSite", "")
    if not carrier_site:
        return {
            "status": "error",
            "message": "carrierSite 不能为空",
            "data": None
        }

    # 会员限制为空，则跳过配置
    membership_limit = analysis_data.get("membership_limit", "")
    if not membership_limit or membership_limit not in ["新会员", "老会员", "新老会员"]:
        return {
            "status": "ignore",
            "message": "未满足配置场景",
            "data": None
        }

    # 设置member值
    member = {"新会员": "1", "新老会员": "0"}.get(membership_limit, "2")

    member_name = mapping_data.get("memberName", "航空会员")

    # 处理标题
    if member_name == "金鹏会员":
        title = f"{member_name}验证失败"
        airlineDesc = "验证结果来自金鹏俱乐部，如有疑问请拨打航司客服电话"
        airlineTitle = member_name
    else:
        title = "航司新会员验证失败" if membership_limit == "新会员" else "航司会员验证失败"
        airlineDesc = f"验证结果来自{flagship_name}，如有疑问请拨打航司客服电话"
        airlineTitle = f"{flagship_name}会员"


    # 在"会员"前插入"新"
    if "会员" in member_name:
        new_member_name = member_name.replace("会员", "新会员")
    else:
        new_member_name = "新会员"

    # 填充文案内容
    if membership_limit == "新会员":
        content = f"您预订的是{new_member_name}产品，当前乘机人无法通过航司会员验证，不能以会员价预订，若继续预订，请您修改乘机人信息后再尝试购买或选择预订其他产品"
        no_register_content = f"该价格为{new_member_name}专享产品，乘机人%s验证不通过，请您修改乘机人信息或者选择其他产品"
    else:
        content = f"您预订的是{member_name}产品，当前乘机人无法通过航司会员验证，不能以会员价预订，若继续预订，请您修改乘机人信息后再尝试购买或选择预订其他产品"
        no_register_content = f"该价格为{member_name}专享产品，乘机人%s验证不通过，请您修改乘机人信息或者选择其他产品"
    
    # 构建结果对象
    result_obj = {
        "carrier": carrier,
        "tag": product_tag,
        "mark": product_mark,
        "member": member,
        "passengerSatisfy": "0",
        "site": carrier_site,
        "title": title,
        "subTitle": "本单乘机人会员验证结果",
        "content": content,
        "airlineDesc": airlineDesc,
        "noRegisterContent": no_register_content,
        "userDataLabel": "business_cardno_passengername_gs_fail",
        "airlineTitle": airlineTitle,
        "type": "0",
        "domain": "",
        "ageType": ""
    }

    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析URL编码的结构化数据
    
    参数:
    content: str - URL编码后的结构化数据
    
    返回:
    Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]] - 解析后的数据和状态
    """
    try:
        # 处理特殊情况：空内容
        if not content:
            return [], {"status": "success", "message": ""}
        
        # 尝试解码
        try:
            decoded_bytes = unquote_to_bytes(content)
            content_str = decoded_bytes.decode('utf-8')
        except:
            content_str = content
        
        # 解析结构化数据
        result = parse_structured_data(content_str)
        
        return result, {"status": "success", "message": ""}
    except Exception as e:
        return None, {"status": "error", "message": str(e)}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化数据字符串
    
    参数:
    data_str: str - 结构化数据字符串，例如："field1:value1#*#field2:value2~~*~~field1:value3#*#field2:value4"
    
    返回:
    List[Dict[str, str]] - 解析后的数据列表
    """
    result = []
    # 以分隔符 ~~*~~ 分割数据项
    parts = data_str.split("~~*~~")
    # 解析每一项
    for part in parts:
        if part:  # 忽略空项
            parsed_fields = _parse_fields(part)
            if parsed_fields:  # 忽略空解析结果
                result.append(parsed_fields)
    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析单个数据项的字段
    
    参数:
    part_str: str - 单个数据项字符串，例如："field1:value1#*#field2:value2"
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    fields = {}
    # 以分隔符 #*# 分割字段
    field_parts = part_str.split("#*#")
    for field_part in field_parts:
        if field_part and ":" in field_part:  # 确保字段部分不为空且包含":"
            # 以第一个":"分割字段名和值
            idx = field_part.find(":")
            key = field_part[:idx].strip()
            value = field_part[idx+1:].strip()
            if key:  # 确保字段名不为空
                fields[key] = value
    return fields