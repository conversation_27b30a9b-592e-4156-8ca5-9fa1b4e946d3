from typing import Dict, List, Any, Union, Tuple
import numbers
import urllib.parse
import json


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def parse_and_validate_price_info(price_info: Any) -> Tuple[bool, str, Dict[str, Any]]:
    """
    校验价格信息的有效性
    Args:
        price_info: 价格信息字典
    Returns:
        校验后的价格信息字典
    """
    # 1. 整体校验
    if not price_info:
        return False, "价格信息为空", None
    if not isinstance(price_info, dict):
        return False, "价格信息类型错误", None

    result = {
        "userPriceType": price_info.get("userPriceType"),
        "precisePriceList": [],
        "durationPriceList": [],
    }

    # 2. 校验 precisePriceList
    precise_list = price_info.get("precisePriceList")
    if precise_list is not None:
        if isinstance(precise_list, list):
            for item in precise_list:
                if (
                    isinstance(item, dict)
                    and "price" in item
                    and is_valid_number(item.get("price"))
                ):
                    result["precisePriceList"].append(item)
        elif (
            isinstance(precise_list, dict)
            and "price" in precise_list
            and is_valid_number(precise_list.get("price"))
        ):
            result["precisePriceList"].append(precise_list)

    # 3. 校验 durationPriceList
    duration_list = price_info.get("durationPriceList")
    if duration_list is not None:
        if isinstance(duration_list, list):
            for item in duration_list:
                if isinstance(item, dict):
                    # 检查是否有 leftPrice 和 rightPrice
                    if "leftPrice" in item and "rightPrice" in item:
                        left_price = item.get("leftPrice")
                        right_price = item.get("rightPrice")
                        if (
                            is_valid_number(left_price)
                            and is_valid_number(right_price)
                            and left_price <= right_price
                        ):
                            result["durationPriceList"].append(item)
                    # 检查是否有 price
                    elif "price" in item and is_valid_number(item.get("price")):
                        result["precisePriceList"].append(item)
        elif isinstance(duration_list, dict):
            # 检查是否有 leftPrice 和 rightPrice
            if "leftPrice" in duration_list and "rightPrice" in duration_list:
                left_price = duration_list.get("leftPrice")
                right_price = duration_list.get("rightPrice")
                if (
                    is_valid_number(left_price)
                    and is_valid_number(right_price)
                    and left_price <= right_price
                ):
                    result["durationPriceList"].append(duration_list)
                # 检查是否有 price
            elif "price" in duration_list and is_valid_number(
                duration_list.get("price")
            ):
                result["precisePriceList"].append(duration_list)

    # 4. 检查是否有效
    if not result["precisePriceList"] and not result["durationPriceList"]:
        return False, "价格信息无效", None

    return True, "价格信息有效", result


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        price_info = param.get("priceInfo")
        parse_result = parse_and_validate_price_info(price_info)
        if parse_result[0]:
            data = {
                "status": 200,
                "errMsg": parse_result[1],
                "priceInfo": parse_result[2],
                "needExecAl": "是",
                "notExecAlReason": parse_result[1],
                "processStage": "价格信息解析",
                "priceInfoJson": urllib.parse.quote(
                    json.dumps(parse_result[2], indent=2, ensure_ascii=False)
                ),
            }
            return {"result": data}
        else:
            data = {
                "status": 404,
                "errMsg": parse_result[1],
                "priceInfo": {},
                "priceInfoJson": urllib.parse.quote(
                    json.dumps({}, indent=2, ensure_ascii=False)
                ),
                "needExecAl": "否",
                "notExecAlReason": f"价格信息解析失败: {parse_result[1]}",
                "processStage": "价格信息解析",
            }
            return {"result": data}
    except Exception as e:
        data = {
            "status": 404,
            "errMsg": f"系统异常: {str(e)}",
            "priceInfo": {},
            "priceInfoJson": urllib.parse.quote(
                json.dumps({}, indent=2, ensure_ascii=False)
            ),
            "needExecAl": "否",
            "notExecAlReason": f"价格信息解析异常！",
            "processStage": "价格信息解析",
        }
        return {"result": data}


# 测试用例
if __name__ == "__main__":
    test_cases = [
        # 正常情况
        {
            "userPriceType": "mixPreciseAndDiffPrice",
            "precisePriceList": [{"price": 850, "parseType": "精准"}],
            "durationPriceList": [
                {"leftPrice": 870, "rightPrice": 890, "parseType": "基于基准计算"}
            ],
        },
        # durationPriceList 为 dict 的情况
        {
            "userPriceType": "durationPrice",
            "precisePriceList": [],
            "durationPriceList": {
                "leftPrice": 870,
                "rightPrice": 890,
                "parseType": "基于基准计算",
            },
        },
        # 无效数据
        {
            "userPriceType": "mixPreciseAndDiffPrice",
            "precisePriceList": [{"price": -1, "parseType": "精准"}],
            "durationPriceList": [
                {"leftPrice": "invalid", "rightPrice": 890, "parseType": "基于基准计算"}
            ],
        },
        # 空数据
        None,
        # 非字典类型
        "invalid",
        {
            "userPriceType": "mixPreciseAndDiffPrice",
            "precisePriceList": [{"price": "-1", "parseType": "精准"}],
            "durationPriceList": [],
        },
    ]

    for case in test_cases:
        print(f"\n测试用例: {case}")
        param = {"priceInfo": case}

        result = main(param)
        print(json.dumps(result, indent=2, ensure_ascii=False))
