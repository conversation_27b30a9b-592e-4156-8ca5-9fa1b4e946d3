# 文档解析助手插件

## 功能特点
- 支持上传多个Word(.docx, .doc)和Excel(.xlsx, .xls)文件
- Word文件最多5个，Excel文件最多10个
- 可预览每个文档的内容
- **一键导出多种格式**：
  - **文本格式 (TXT)** - 默认选择，纯文本导出
  - **网页格式 (HTML)** - 保留完整格式和样式
  - **Markdown格式 (MD)** - 适合文档编写和版本控制
  - **数据格式 (JSON)** - 结构化数据，便于程序处理
- 支持个别删除已上传的文档
- 解析到多维表格功能，集成Dify.ai API

## 安装步骤

### 📦 第三方库下载
请下载以下库文件并放入 `lib` 目录：

**必需库**:
- [xlsx.full.min.js](https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js) - Excel文件处理
- [mammoth.browser.min.js](https://cdn.jsdelivr.net/npm/mammoth@1.4.0/mammoth.browser.min.js) - Word文档处理
- [turndown.min.js](https://cdn.jsdelivr.net/npm/turndown@7.1.2/dist/turndown.min.js) - HTML转Markdown

**新增库 (v2.0)**:
- [pako.min.js](https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js) - 数据压缩
- [lodash.min.js](https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js) - 工具库

**内置库**:
- table-formatter.js - 表格格式化
- table-structure-analyzer.js - 表格结构分析
- data-compressor.js - 数据压缩处理
- preview-manager.js - 预览管理
- cfb-lite.js - 复合文档二进制格式解析器
- doc-parser.js - 专门的.doc文件解析器

### 🚀 启动方式

**方式1: 独立应用**
- 确保所有第三方库已下载到 `lib` 目录
- 直接打开 `main.html` 文件

**方式2: 浏览器扩展**
- 打开Chrome浏览器，进入`chrome://extensions/`
- 启用"开发者模式"
- 点击"加载已解压的扩展程序"
- 选择本插件目录

## 使用说明

1. 点击插件图标打开界面
2. 点击上传区域或拖拽文件到指定区域
3. 点击文件名可预览内容
4. **选择导出格式**：
   - 在"导出格式"下拉菜单中选择所需格式
   - 默认选择"文本 (TXT)"格式
5. 点击"一键导出"按钮导出所有内容
6. 点击文件列表中的"×"按钮可删除单个文件
7. 点击"清空所有"按钮重置所有内容

## 导出格式说明

### 文本格式 (TXT)
- 纯文本导出，保留表格结构
- 适合快速查看和文本处理

### 网页格式 (HTML)
- 保留完整的格式和样式
- 包含表格、标题、段落等所有元素
- 可在浏览器中直接打开查看

### Markdown格式 (MD) - 🤖 简化AI友好格式
- **专注内容本身**，去除冗余信息，让AI更容易理解核心内容
- **清晰的文档结构**，使用标准Markdown层级组织内容
- 使用Turndown.js库进行高质量转换

**📝 简化特性**：
- **文档标题结构**：文件名作为二级标题，内容保持原有层级
- **纯净的表格格式**：标准Markdown表格，无额外分析信息
- **去除元数据冗余**：不包含文件属性、状态、统计等信息
- **保留核心内容**：完整保留Word文档正文和Excel表格数据
- **标准化格式**：符合通用Markdown规范，便于各种工具解析

**🤖 AI解析优势**：
- 结构简洁明了，减少解析干扰
- 标准Markdown格式，兼容性强
- 内容密度高，信息价值大
- 易于提取关键信息和数据
- 适合各种AI模型处理

### 数据格式 (JSON)
- 结构化数据导出
- 包含文件元信息和完整内容
- 便于程序处理和数据分析

## Markdown导出示例

以下是简化后的Markdown导出格式示例（专注内容，方便AI理解）：

```markdown
# 文档内容

## 项目计划.docx

# 项目计划

## 项目概述
本项目旨在开发一个文档解析工具...

| 任务 | 负责人 | 截止日期 |
| --- | --- | --- |
| 需求分析 | 张三 | 2024-01-20 |
| 系统设计 | 李四 | 2024-01-25 |

## 数据统计.xlsx

### 销售数据

| 产品名称 | 销售额 | 增长率 |
| --- | --- | --- |
| 产品A | 100000 | 15% |
| 产品B | 80000 | 8% |
| 产品C | 120000 | 22% |

### 成本分析

| 项目 | 成本 | 占比 |
| --- | --- | --- |
| 人力成本 | 50000 | 60% |
| 设备成本 | 20000 | 24% |
| 其他成本 | 13333 | 16% |
```

**简化特点**：
- 去除冗余的元数据和状态信息
- 保留清晰的文档结构（文件名作为二级标题）
- 保留完整的正文内容和表格
- 使用标准的Markdown格式，便于AI解析
- 专注内容本身，减少干扰信息

## 最新更新 v2.0

### ✅ 新增核心功能
- **🗜️ JSON数据压缩**：自动压缩大于1KB的JSON数据，支持gzip压缩，显著减少传输大小
- **📊 表格结构化分析**：智能识别表格数据类型（数字、货币、日期、邮箱等），提供统计信息
- **🤖 AI友好的Markdown**：增强版Markdown导出，包含表格结构说明和AI理解指南
- **📄 完整.doc文件支持**：使用专门的DOC解析器处理旧版Word文档(.doc)，支持多层回退机制
- **🔍 数据类型检测**：自动识别列数据类型，提供置信度评估

### 🚀 增强功能
- **预览功能**：支持所有导出格式的实时预览，包括结构化预览
- **环境选择**：在多维表格解析中支持Beta/Prod环境选择
- **高级预览管理器**：使用PreviewManager提供更好的预览体验
- **智能表格处理**：Word表格转换为结构化JSON，Excel数据智能分析
- **压缩传输**：JSON数据智能压缩，减少网络传输时间

### 🔧 技术特性
- **模块化设计**：DocumentProcessor、PreviewManager、TableStructureAnalyzer、DataCompressor分离
- **第三方库集成**：支持pako压缩、lodash工具库
- **错误处理**：完善的文件验证和错误提示
- **响应式界面**：适配不同屏幕尺寸
- **拖拽上传**：支持文件拖拽和点击上传
- **实时预览**：文件内容即时预览
- **批量处理**：支持多文件同时处理

## .doc文件支持详情

### 🔧 解析机制
插件使用多层回退机制确保.doc文件的最佳兼容性：

1. **专门的DOC解析器** (优先)
   - 使用CFBLite库解析OLE复合文档格式
   - 直接提取WordDocument流中的文本内容
   - 支持表格和格式化内容的识别

2. **Mammoth回退** (次选)
   - 当专门解析器失败时，尝试使用Mammoth库
   - 虽然主要为.docx设计，但对某些.doc文件有效

3. **基本文本提取** (最后回退)
   - 使用二进制分析直接提取可读文本
   - 支持UTF-16LE编码的文本内容
   - 确保即使在其他方法失败时也能提取基本内容

### 🧪 测试功能
- 提供专门的测试页面 `test-doc-support.html`
- 可以测试.doc文件解析功能
- 显示库加载状态和解析结果
- 支持拖拽上传和实时预览

### ⚠️ 已知限制
- 复杂格式的.doc文件可能无法完美解析
- 建议将重要的.doc文件转换为.docx格式以获得最佳效果
- 表格提取功能在.doc文件中可能有限

## 注意事项
- 大文件处理可能需要较长时间
- 导出文件内容格式:
  - Word文档按原顺序排列
  - Excel文档按工作簿和工作表顺序排列
  - 每个文档和工作表都有标题标记
- 如果Turndown.js库未加载，Markdown导出将回退到文本格式
- Markdown格式特别适合AI阅读和分析，结构清晰，层次分明
- 预览功能需要PreviewManager库支持，如未加载将使用简化预览
- .doc文件解析使用多层回退机制，确保最大兼容性