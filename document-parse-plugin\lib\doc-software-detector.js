/**
 * DOC软件检测器
 * 检测.doc文件是由WPS还是Microsoft Word创建的，并使用相应的解析策略
 */
class DocSoftwareDetector {
  constructor() {
    // WPS Office特征标识
    this.wpsSignatures = [
      'WPS Office', 'WPS Writer', 'KSOProductBuildVer', 'Kingsoft',
      'wps', 'kso', '金山', '珠海金山', 'WPS专业版', 'WPS个人版'
    ];
    
    // Microsoft Word特征标识
    this.wordSignatures = [
      'Microsoft Word', 'Microsoft Office', 'MSWordDoc', 'Word.Document',
      'Winword', 'MSWORD', 'Normal.dot', 'Normal.dotm'
    ];
    
    // 编码特征
    this.encodingPatterns = {
      wps: {
        // WPS可能使用的编码模式
        charPatterns: [
          /[\u4e00-\u9fff]{1,3}[\x00-\x1F]{1,3}[\u4e00-\u9fff]{1,3}/g, // 中文字符间有控制字符
          /[a-zA-Z]{2,}[\x00]{1,2}[a-zA-Z]{2,}/g, // ASCII字符间有空字节
        ],
        bytePatterns: [
          [0x57, 0x50, 0x53], // WPS
          [0x4B, 0x53, 0x4F], // KSO
        ]
      },
      word: {
        charPatterns: [
          /[\u4e00-\u9fff][\x00][\u4e00-\u9fff]/g, // 标准UTF-16LE模式
          /[a-zA-Z]+[\x00]+[a-zA-Z]+/g, // 标准ASCII模式
        ],
        bytePatterns: [
          [0x4D, 0x69, 0x63, 0x72, 0x6F, 0x73, 0x6F, 0x66, 0x74], // Microsoft
          [0x57, 0x6F, 0x72, 0x64], // Word
        ]
      }
    };
  }

  // 检测文档软件类型
  detectSoftware(arrayBuffer, rawText) {
    console.log('开始检测文档创建软件...');
    
    const results = {
      software: 'unknown',
      confidence: 0,
      features: [],
      recommendedStrategy: 'default'
    };
    
    // 检查文本特征
    const textAnalysis = this.analyzeTextFeatures(rawText);
    
    // 检查二进制特征
    const binaryAnalysis = this.analyzeBinaryFeatures(arrayBuffer);
    
    // 综合分析
    const wpsScore = this.calculateWPSScore(textAnalysis, binaryAnalysis);
    const wordScore = this.calculateWordScore(textAnalysis, binaryAnalysis);
    
    console.log(`WPS得分: ${wpsScore}, Word得分: ${wordScore}`);
    
    if (wpsScore > wordScore && wpsScore > 30) {
      results.software = 'wps';
      results.confidence = Math.min(wpsScore / 100, 1);
      results.recommendedStrategy = 'wps_optimized';
      results.features = textAnalysis.wpsFeatures.concat(binaryAnalysis.wpsFeatures);
    } else if (wordScore > wpsScore && wordScore > 30) {
      results.software = 'word';
      results.confidence = Math.min(wordScore / 100, 1);
      results.recommendedStrategy = 'word_optimized';
      results.features = textAnalysis.wordFeatures.concat(binaryAnalysis.wordFeatures);
    } else {
      results.software = 'unknown';
      results.confidence = 0;
      results.recommendedStrategy = 'hybrid';
      results.features = ['无法确定软件类型，使用混合策略'];
    }
    
    console.log(`检测结果: ${results.software} (置信度: ${(results.confidence * 100).toFixed(1)}%)`);
    return results;
  }

  // 分析文本特征
  analyzeTextFeatures(text) {
    const analysis = {
      wpsFeatures: [],
      wordFeatures: [],
      wpsScore: 0,
      wordScore: 0
    };
    
    // 检查WPS特征
    this.wpsSignatures.forEach(signature => {
      if (text.includes(signature)) {
        analysis.wpsFeatures.push(`发现WPS标识: ${signature}`);
        analysis.wpsScore += 20;
      }
    });
    
    // 检查Word特征
    this.wordSignatures.forEach(signature => {
      if (text.includes(signature)) {
        analysis.wordFeatures.push(`发现Word标识: ${signature}`);
        analysis.wordScore += 20;
      }
    });
    
    // 检查编码模式
    this.encodingPatterns.wps.charPatterns.forEach((pattern, index) => {
      const matches = text.match(pattern);
      if (matches && matches.length > 0) {
        analysis.wpsFeatures.push(`WPS编码模式${index + 1}: ${matches.length}个匹配`);
        analysis.wpsScore += matches.length * 2;
      }
    });
    
    this.encodingPatterns.word.charPatterns.forEach((pattern, index) => {
      const matches = text.match(pattern);
      if (matches && matches.length > 0) {
        analysis.wordFeatures.push(`Word编码模式${index + 1}: ${matches.length}个匹配`);
        analysis.wordScore += matches.length * 2;
      }
    });
    
    return analysis;
  }

  // 分析二进制特征
  analyzeBinaryFeatures(arrayBuffer) {
    const analysis = {
      wpsFeatures: [],
      wordFeatures: [],
      wpsScore: 0,
      wordScore: 0
    };
    
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 检查WPS二进制特征
    this.encodingPatterns.wps.bytePatterns.forEach((pattern, index) => {
      const count = this.findBytePattern(uint8Array, pattern);
      if (count > 0) {
        analysis.wpsFeatures.push(`WPS二进制模式${index + 1}: ${count}次出现`);
        analysis.wpsScore += count * 10;
      }
    });
    
    // 检查Word二进制特征
    this.encodingPatterns.word.bytePatterns.forEach((pattern, index) => {
      const count = this.findBytePattern(uint8Array, pattern);
      if (count > 0) {
        analysis.wordFeatures.push(`Word二进制模式${index + 1}: ${count}次出现`);
        analysis.wordScore += count * 10;
      }
    });
    
    return analysis;
  }

  // 查找字节模式
  findBytePattern(uint8Array, pattern) {
    let count = 0;
    for (let i = 0; i <= uint8Array.length - pattern.length; i++) {
      let match = true;
      for (let j = 0; j < pattern.length; j++) {
        if (uint8Array[i + j] !== pattern[j]) {
          match = false;
          break;
        }
      }
      if (match) {
        count++;
        i += pattern.length - 1; // 跳过已匹配的部分
      }
    }
    return count;
  }

  // 计算WPS得分
  calculateWPSScore(textAnalysis, binaryAnalysis) {
    let score = textAnalysis.wpsScore + binaryAnalysis.wpsScore;
    
    // 额外的WPS特征检查
    if (textAnalysis.wpsFeatures.some(f => f.includes('KSOProductBuildVer'))) {
      score += 30; // KSO是WPS的强特征
    }
    
    if (textAnalysis.wpsFeatures.some(f => f.includes('专业版'))) {
      score += 15; // WPS专业版标识
    }
    
    return score;
  }

  // 计算Word得分
  calculateWordScore(textAnalysis, binaryAnalysis) {
    let score = textAnalysis.wordScore + binaryAnalysis.wordScore;
    
    // 额外的Word特征检查
    if (textAnalysis.wordFeatures.some(f => f.includes('Microsoft Word'))) {
      score += 30; // Microsoft Word是强特征
    }
    
    if (textAnalysis.wordFeatures.some(f => f.includes('Normal.dot'))) {
      score += 15; // Word模板文件标识
    }
    
    return score;
  }

  // 获取推荐的解析策略
  getParsingStrategy(detectionResult) {
    const strategies = {
      wps_optimized: {
        name: 'WPS优化策略',
        encodings: ['gb2312', 'utf-16le', 'utf-8', 'windows-1252'],
        byteOffsets: [0x400, 0x200, 0x800, 0x1000],
        textPatterns: this.encodingPatterns.wps.charPatterns,
        specialHandling: 'wps_specific'
      },
      
      word_optimized: {
        name: 'Word优化策略',
        encodings: ['utf-16le', 'utf-8', 'windows-1252', 'iso-8859-1'],
        byteOffsets: [0x200, 0x400, 0x800, 0x1000],
        textPatterns: this.encodingPatterns.word.charPatterns,
        specialHandling: 'word_specific'
      },
      
      hybrid: {
        name: '混合策略',
        encodings: ['utf-16le', 'gb2312', 'utf-8', 'windows-1252', 'iso-8859-1'],
        byteOffsets: [0x200, 0x400, 0x800, 0x1000, 0x2000],
        textPatterns: [...this.encodingPatterns.wps.charPatterns, ...this.encodingPatterns.word.charPatterns],
        specialHandling: 'comprehensive'
      },
      
      default: {
        name: '默认策略',
        encodings: ['utf-16le', 'utf-8', 'windows-1252'],
        byteOffsets: [0x200, 0x400, 0x800],
        textPatterns: [],
        specialHandling: 'standard'
      }
    };
    
    return strategies[detectionResult.recommendedStrategy] || strategies.default;
  }

  // 生成检测报告
  generateDetectionReport(detectionResult, strategy) {
    let report = `DOC软件检测报告\n`;
    report += `==================\n\n`;
    report += `检测到的软件: ${detectionResult.software.toUpperCase()}\n`;
    report += `置信度: ${(detectionResult.confidence * 100).toFixed(1)}%\n`;
    report += `推荐策略: ${strategy.name}\n\n`;
    
    report += `检测到的特征:\n`;
    detectionResult.features.forEach((feature, index) => {
      report += `${index + 1}. ${feature}\n`;
    });
    
    report += `\n解析策略详情:\n`;
    report += `- 编码顺序: ${strategy.encodings.join(', ')}\n`;
    report += `- 字节偏移: ${strategy.byteOffsets.map(o => '0x' + o.toString(16)).join(', ')}\n`;
    report += `- 特殊处理: ${strategy.specialHandling}\n`;
    
    return report;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocSoftwareDetector;
} else if (typeof window !== 'undefined') {
  window.DocSoftwareDetector = DocSoftwareDetector;
}
