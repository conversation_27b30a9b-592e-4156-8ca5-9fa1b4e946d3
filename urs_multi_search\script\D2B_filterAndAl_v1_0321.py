from collections import defaultdict
from urllib.parse import unquote_to_bytes,quote
import json
from typing import Tuple, Optional, List, Dict, Any, Union
from datetime import datetime,date
from string import Formatter
import re
from typing import Union
import uuid



def generate_strategies(params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    根据输入参数动态生成策略配置列表

    参数映射规则：
    {
        "参数键": {
            "stra_name": 策略名称,
            "data_mapping": {
                "参数子键": "策略数据字段"  # 支持多字段映射
            }
        },
        ...
    }
    """
    # 策略配置映射表（可扩展）
    strategy_config = {
        # 价格差异策略
        "价差阈值": {
            "stra_name": "价差阈值",
            "data_mapping": {
                "priceMinDiffThreshold": "priceMinDiffThreshold"
            },  # 值直接映射
        },
        # 对比类型策略
        "对比类型": {
            "stra_name": "对比类型",
            "data_mapping": {
                "allowedComparetypes": "allowedComparetypes"
            },  # 值直接映射
        },
        # 时间差异策略
        "搜索时间差": {
            "stra_name": "搜索时间差",
            "data_mapping": {"listSearchDurationLimit": "listSearchDurationLimit"},
        },
         # 价差限制策略
        "价差限制": {
            "stra_name": "价差限制",
            "data_mapping": {"priceDiffSignedThreshold": "priceDiffSignedThreshold"},
        },
        # 对比类型策略
        "航班类型": {
            "stra_name": "航班类型",
            "data_mapping": {
                "allowedCompareFlightTypes": "allowedCompareFlightTypes"
            },  # 值直接映射
        },
    }

    strategies = []

    strategies.append({"straName": "航班日期不一致", "straData": {}})

    for strategy_key, config in strategy_config.items():
        # 构建策略数据
        stra_data = {}

        # 处理数据映射
        for paramIndexKey, data_field in config["data_mapping"].items():
            # 获取参数值
            if is_deep_empty(paramIndexKey):
                continue
            else:  # 处理嵌套参数
                paramValue = params.get(paramIndexKey)
                if paramValue is None:
                    continue
                else:
                    stra_data[data_field] = paramValue

        # 仅当有有效数据时添加策略
        if not is_deep_empty(stra_data):
            strategies.append({"straName": config["stra_name"], "straData": stra_data})
    return strategies


def apply_strategy(compare_data, stra_name, stra_data):
    """
    根据策略名称和数据判断对比数据是否符合条件
    """
    if stra_name == "对比类型":
        allowed_types = stra_data.get("allowedComparetypes", [])
        current_type = compare_data.get("compareType", "")
        if current_type not in allowed_types:
            return False, f"对比类型不满足 '{current_type}' ->  {allowed_types}"
        return True, ""

    elif stra_name == "价差阈值":
        min_diff = stra_data.get("priceMinDiffThreshold", 0)
        price_diff = compare_data.get("priceDiff", 0)
        # 确保price_diff是数值类型
        try:
            price_diff = int(price_diff) if isinstance(price_diff, str) else price_diff
        except ValueError:
            return True, ""
        if abs(price_diff) <= min_diff:
            return False, f"价差阈值绝对值 {price_diff} < {min_diff}"
        return True, ""

    elif stra_name == "搜索时间差":  # 新增时间差策略
        max_diff = stra_data.get("listSearchDurationLimit", 0)
        time_diff = compare_data.get("searchTimeDiff", 0)

        # 处理可能的类型异常
        try:
            time_diff = float(time_diff)
        except (TypeError, ValueError):
            return False, f"搜索时间差 '{time_diff}' 不是有效数值"
        if time_diff > max_diff:
            return False, f"搜索时间差 {time_diff} 秒超过阈值 {max_diff}"
        return True, ""
    elif stra_name == "航班日期不一致":
        flight_date = compare_data.get("preDepartureDate", "")
        if flight_date != compare_data.get("surDepartureDate", ""):
            return (
                False,
                f"航班日期不一致: {flight_date} != {compare_data.get('surDepartureDate', '')}",
            )
        return True, ""
    elif stra_name == "价差限制":
        min_diff = stra_data.get("priceDiffSignedThreshold", 0)
        price_diff = compare_data.get("priceDiff", 0)
        if price_diff < min_diff:
            return False, f"价差限制 {price_diff} < {min_diff}"
        return True, ""
    elif stra_name == "航班类型":
        allowed_types = stra_data.get("allowedCompareFlightTypes", [])
        if not allowed_types or ("single" in allowed_types and "connect" in allowed_types):
            return True, ""
        if allowed_types == ["single"]:
            flight_number = compare_data.get("preFlightNo", "")
            if flight_number and len(flight_number.split('/')) > 1:
                return False, f"航班类型不满足: 中转航班 '{flight_number}'"
        if allowed_types == ["connect"]:
            flight_number = compare_data.get("preFlightNo", "")
            if flight_number and len(flight_number.split('/')) <= 1:
                return False, f"航班类型不满足: 单程航班 '{flight_number}'"    
        return True, ""
    # 可扩展其他策略...
    else:
        return True, ""


def merge_reasons(reasons_list):
    seen = set()
    unique_reasons = []
    for reason in reasons_list:
        if reason not in seen:
            seen.add(reason)
            unique_reasons.append(reason)
    return "; ".join(unique_reasons)


def filterValiableTradeIds(compareDatas, filterStrategys):
    valiableTradeIds = set()
    allTradeIds = set()
    notValiableReasons = defaultdict(list)
    valiableDatas = []

    # 遍历每条对比数据
    for data in compareDatas:
        pre_tid = data["preTradeId"]
        sur_tid = data["surTradeId"]
        allTradeIds.update({pre_tid, sur_tid})

        is_valiable = True
        failure_reasons = []

        # 应用所有过滤策略
        for strategy in filterStrategys:
            stra_name = strategy["straName"]
            stra_data = strategy.get("straData", {})

            valid, reason = apply_strategy(data, stra_name, stra_data)
            if not valid:
                is_valiable = False
                failure_reasons.append(f"[{stra_name}]{reason}")

        # 记录结果
        if is_valiable:
            valiableTradeIds.add(pre_tid)
            valiableTradeIds.add(sur_tid)
            valiableDatas.append(data)
        else:
            not_exec_al_reason = "，".join(failure_reasons)
            data["needExecAl"] = "否"
            data["notExecAlReason"] = not_exec_al_reason
            for tid in [pre_tid, sur_tid]:
                notValiableReasons[tid].extend(failure_reasons)

    # 计算被过滤的tradeIds
    notValiableTradeIds = allTradeIds - valiableTradeIds

    # 合并原因说明
    mergedNotValiables = {}
    for tid in notValiableTradeIds:
        if tid in notValiableReasons:
            mergedNotValiables[tid] = merge_reasons(notValiableReasons[tid])
        else:
            mergedNotValiables[tid] = "关联对比数据被过滤"

    return  mergedNotValiables, valiableDatas


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result



def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将每个条目按照***** 分割为三部分：
    1. 公共字段
    2. preData
    3. surData

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表，每个字典包含commonFields、preData和surData三个子字典
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        
        # 按照***** 分割为三部分
        parts = entry.split("*****", 2)  # 最多分割2次，得到最多3个部分
        
        # 使用辅助函数解析三部分
        commonData = _parse_fields(parts[0].strip() if len(parts) > 0 else "")
        preData = _parse_fields(parts[1].strip() if len(parts) > 1 else "")
        surData = _parse_fields(parts[2].strip() if len(parts) > 2 else "")
        
        # 将三部分数据添加到结果中
        record["commonData"] = commonData
        record["preData"] = preData
        record["surData"] = surData
        
        # 注意：根据需求，仅在preData和surData都有数据时才添加记录
        if preData and surData:  # 俩部分有数据才添加到结果中
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:    
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False



def try_get_float(value):
    """尝试将值转换为浮点数，排除布尔类型"""
    if isinstance(value, bool):
        return None
    if isinstance(value, (int, float)):
        return float(value)
    elif isinstance(value, str):
        try:
            return float(value)
        except ValueError:
            return None
    else:
        return None


def fieldEquals(ota_value: Any, list_value: Any) -> bool:
    """严格字段值比对方法（不进行类型转换）"""
    # 处理空值逻辑
    if ota_value is None and list_value is None:
        return True
    if ota_value is None or list_value is None:
        return False

    # 处理字符串类型（去除首尾空格）
    if isinstance(ota_value, str) and isinstance(list_value, str):
        return ota_value.strip() == list_value.strip()

    # 尝试将两个参数都转为浮点数
    a_float = try_get_float(ota_value)
    b_float = try_get_float(list_value)

    # 两个都能转浮点数时比较数值
    if a_float is not None and b_float is not None:
        return a_float == b_float

    # 严格类型和值匹配
    return ota_value == list_value


def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue


def genPriceCompareRow(
    data: Dict,
    compareType: str,
    templatePrompt: str,
) -> Dict:
    """
    方法4: 生成单行价格对比记录

    """

    # 提取数据
    commonData = data.get("commonData", {})
    preData = data.get("preData", {})
    surData = data.get("surData", {}) 
    if not commonData or not preData or not surData:
        return None

    # 基础字段提取
    base_fields = {
        "compareId": commonData.get("compareId"),
        "preFlightNo": commonData.get("flightNo"),
        "surFlightNo": commonData.get("flightNo"),
        "departureCity": commonData.get("departureCity"),  # 假设城市信息相同
        "arrivalCity": commonData.get("arrivalCity"),
        "preTradeId": preData.get("tradeId"),
        "surTradeId": surData.get("tradeId"),
        "preCabin": preData.get("cabin"),
        "surCabin": surData.get("cabin"),
        
        "needExecAl": "是",
        "notExecAlReason": "",
    }

    time_diff = (
        parse_search_time(surData["searchDateTime"])
        - parse_search_time(preData["searchDateTime"])
    ).total_seconds()


    # 价格计算逻辑
    price_diff = convert_price_to_numeric(
        surData.get("price", 0)
    ) - convert_price_to_numeric(preData.get("price", 0))
  
    return {
        **base_fields,
        "searchDateTimeDiff": time_diff,
        "priceDiff": price_diff,
        "comparePrompt": genPriceComparePrompt(
            preData,
            surData,
            compare_type=compareType,
            templatePrompt=templatePrompt,
        ),
    }

# 需实现的提示生成函数（示例）
def genPriceComparePrompt(
    pre: Dict, sur: Dict, compare_type: str, templatePrompt: str
) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    preData = json.dumps(pre, ensure_ascii=False, indent=None)  # 紧凑格式
    surData = json.dumps(sur, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "preData": preData,
        "surData": surData,
        "compareType": compare_type,
    }

    try:
        formatter = Formatter()
        required_fields = [fn for _, fn, _, _ in formatter.parse(templatePrompt) if fn]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return templatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def genPrompt(soarTemplatePrompt, priceData) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    param = json.dumps(priceData, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "param": param,
    }

    try:
        formatter = Formatter()
        required_fields = [
            fn for _, fn, _, _ in formatter.parse(soarTemplatePrompt) if fn
        ]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return soarTemplatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e

def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None

def filterFlightGroupsDataByNotValiableTradeIds(data: List[Dict], valiableCompareDatas: List[Dict]) -> List[Dict]:
    """
    过滤并分组数据
    
    参数：
    data : List[Dict] - 从main方法入口解析出的原始数据列表，每个元素包含commonData、preData和surData
    valiableCompareDatas : List[Dict] - 有效的比较数据列表，包含compareId
    
    返回：
    List[Dict] - 分组后的数据列表，结构为：
    [
        {
            "groupKey": "航班号",
            "groupType": "flightNo",
            "groupValue": [
                {
                    "preData": {...},
                    "surData": {...}
                },
                ...
            ]
        },
        ...
    ]
    """
    # 提取有效的compareId集合
    valid_compare_ids = {item.get('compareId') for item in valiableCompareDatas if item.get('compareId')}
    
    # 用于存储按航班号分组的数据
    flight_groups = {}
    
    # 遍历原始数据进行过滤和分组
    for item in data:
        # 获取必要字段
        common_data = item.get('commonData', {})
        pre_data = item.get('preData', {})
        sur_data = item.get('surData', {})
        
        # 获取compareId和航班号
        compare_id = common_data.get('compareId')
        flight_no = common_data.get('flightNo')
        
        # 检查是否为有效数据
        if (compare_id in valid_compare_ids and 
            flight_no and 
            pre_data and 
            sur_data):
            
            # 创建当前记录
            current_record = {
                "preData": pre_data,
                "surData": sur_data
            }
            
            # 按航班号分组
            if flight_no not in flight_groups:
                flight_groups[flight_no] = []
            flight_groups[flight_no].append(current_record)
    
    # 直接返回分组列表
    return [
        {
            "groupKey": flight_no,
            "groupType": "flightNo",
            "groupValue": group_data
        }
        for flight_no, group_data in flight_groups.items()
    ]



def main(param: dict) -> dict:
    try:
        # 解析入参，data为列表,用户输入问题的unionKey维度，每个元素为字典，字典包含preData和surData两个子字典
        data, parseStatus = parse_urlencoded_structured_data(param, "param")
        compareType = "ota-booking"
        oneToOnetemplatePrompt = param.get("oneToOnetemplatePrompt")
        searchTimeDurationLimit = param.get("listSearchDurationLimit")
        listSearchDurationLimit = convert_to_seconds(searchTimeDurationLimit)

        if parseStatus["status"] != "success":
            return {
                "status": 404,
                "errMsg": parseStatus["message"],
                "prompt": "",
                "allCompareRows": [],
                "valiableCompareDatas": [],
                "notValiableTradeReasons": {},
                "needExecAl": "否",
                "notExecAlReason": "前置报价符合条件的报价数据为空，请确认用户输入是否有效！",
                "processStage": "变价数据分组和过滤",
            }
        
        if len(data) == 0:
            return {
                "status": 404,
                "errMsg": "无数据！",
                "prompt": "",
                "allCompareRows": [],
                "valiableCompareDatas": [],
                "notValiableTradeReasons": {},
                "needExecAl": "否",
                "notExecAlReason": "前置报价符合条件的报价数据为空，请确认用户输入是否有效！",
                "processStage": "变价数据分组和过滤",
            }
        
        # 生成所有对比记录，含俩俩对比维度的提示词，明细结果
        allCompareRows=[]
        # 生成所有对比记录，含俩俩对比维度的提示词，明细结果
        allCompareRows=[]
        for item in data:
            result = genPriceCompareRow(item, compareType, oneToOnetemplatePrompt)
            if result:
                allCompareRows.append(result)


        # 构建策略参数
        strageParams = {}

        # 添加搜索时间差限制
        if listSearchDurationLimit is not None and listSearchDurationLimit > 0:
            strageParams["listSearchDurationLimit"] = listSearchDurationLimit

        # 添加最小价差阈值
        priceMinDiffThreshold = param.get("priceMinDiffThreshold")
        minPriceDiff = convert_price_to_numeric(priceMinDiffThreshold)
        if minPriceDiff is not None and isinstance(minPriceDiff, (int, float)):
            strageParams["priceMinDiffThreshold"] = minPriceDiff

         # 添加价格差异阈值（值对比，不取绝对值）
        priceDiffSignedThreshold = param.get("priceDiffSignedThreshold")
        diffSignedThreshold = convert_price_to_numeric(priceDiffSignedThreshold)
        if diffSignedThreshold is not None and isinstance(diffSignedThreshold, (int, float)):
            strageParams["priceDiffSignedThreshold"] = diffSignedThreshold
        
        # 添加允许的航班类型
        allowedCompareFlightTypes = param.get("allowedCompareFlightTypes")
        if allowedCompareFlightTypes is not None and isinstance(allowedCompareFlightTypes, str):
            strageParams["allowedCompareFlightTypes"] = allowedCompareFlightTypes.strip().split(",")


        # 生成过滤策略
        filterStrategys = generate_strategies(strageParams)

        # 过滤可对比数据
        merged, valiableCompareDatas = filterValiableTradeIds(
            allCompareRows, filterStrategys
        )

        # 生成提示词
        prompt = ""
        if len(valiableCompareDatas) == 0:
            return {
                "status": 200,
                "errMsg": "执行过滤规则后无有效数据,请确认用户反馈是否有偏差!",
                "prompt": "",
                "allCompareRows": allCompareRows,
                "valiableCompareDatas": valiableCompareDatas,
                "notValiableTradeReasons": merged,
                "needExecAl": "否",
                "notExecAlReason": "执行过滤规则后无有效数据,请确认用户反馈是否有偏差",
                "processStage": "变价数据分组和过滤",
            }

        # 返回结果
        finalData = {
            "status": 200,
            "errMsg": "",
            "prompt": "",
            "allCompareRows": allCompareRows,
            "valiableCompareDatas": valiableCompareDatas,
            "notValiableTradeReasons": merged,
            "needExecAl": "是",
            "notExecAlReason": "",
            "processStage": "变价数据分组和过滤",
        }
        return finalData
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        result = {
            "status": 404,
            "errMsg": f"生成提示词异常！: {e}",
            "prompt": "",
            "allCompareRows": [],
            "valiableCompareDatas": [],
            "notValiableTradeReasons": {},
            "needExecAl": "否",
            "notExecAlReason": "入参缺失关键字段",
            "processStage": "变价数据分组和过滤",
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "status": 404,
            "errMsg": f"生成提示词异常！: {e}",
            "prompt": "",
            "allCompareRows": [],
            "valiableCompareDatas": [],
            "notValiableTradeReasons": {},
            "needExecAl": "否",
            "notExecAlReason": "处理过程发生异常",
            "processStage": "变价数据分组和过滤",
        }
        return result


def test_main():
    """
    测试main函数的功能
    
    可以在此处填充测试参数，例如：
    param = {
        "param": "这里填入URL编码后的结构化数据",
        "oneToOnetemplatePrompt": "模板提示词 {preData} {surData}",
        "listSearchDurationLimit": "120s",
        "priceMinDiffThreshold": "10",
        "priceDiffSignedThreshold": "-20",
        "allowedCompareFlightTypes": "single,connect"
    }
    
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))
    """
    # 在此处填充测试参数
    param = {
        "param": ("flightNo%3AMU3387%23%2A%23departureCity%3ACKG%23%2A%23arrivalCity%3ANNG%23%2A%23compareId%3Aa1ad1697-dfe3-43bf-bd55-69353e1d7f52%2A%2A%2A%2A%2AflightNo%3AMU3387%23%2A%23departureCity%3ACKG%23%2A%23arrivalCity%3ANNG%23%2A%23price%3A370%23%2A%23traceId%3Aops_slugger_250406.192659.10.90.5.105.1170235.2425451511_1%23%2A%23tag%3AJXL1%23%2A%23cabin%3AS%23%2A%23searchDateTime%3A2025-04-06%2019%3A26%3A59%23%2A%23viewPrice%3A410%23%2A%23basePrice%3A410%23%2A%23packagePrice%3A410%23%2A%23policyId%3A3483655444%23%2A%23zeroCard%3A0%23%2A%23expCut%3A0%23%2A%23coupon%3A0%23%2A%23activityCut%3A0preAdultnum%3A0preBabynum%3A0preChildnum%3A0%2A%2A%2A%2A%2AflightNo%3AMU3387%23%2A%23departureCity%3ACKG%23%2A%23arrivalCity%3ANNG%23%2A%23price%3A410%23%2A%23traceId%3Aops_slugger_250406.192659.10.90.5.105.1170235.2425451511_1%23%2A%23tag%3AJXL1%23%2A%23cabin%3AS%23%2A%23searchDateTime%3A2025-04-06%2019%3A27%3A03%23%2A%23viewPrice%3A410%23%2A%23basePrice%3A410%23%2A%23packagePrice%3A410%23%2A%23policyId%3A3483655444%23%2A%23zeroCard%3A0%23%2A%23expCut%3A0%23%2A%23coupon%3A0%23%2A%23activityCut%3A0surAdultnum%3A2surBabynum%3A0surChildnum%3A0%23%2A%23forceBindOrPackage%3A%5B%7B%22expCode%22%3A%20%22golFXTPMPC6shyTGJDJ%22%2C%20%22numLabel%22%3A%20%22x2%E4%BB%BD%22%2C%20%22priceLabel%22%3A%20%22%C2%A588%22%2C%20%22priceName%22%3A%20%22%E5%95%86%E6%97%85%E8%BF%94%E7%8E%B0%EF%BC%88%E8%B5%B7%E9%A3%9E%E5%90%8E%E5%8F%AF%E8%BF%94%C2%A540%EF%BC%89%22%2C%20%22type%22%3A%20%22PACKAGE%22%2C%20%22price%22%3A%20%2288%22%7D%5D%23%2A%23flightType%3A1%7E%7E%2A%7E%7E"),
        
        "oneToOnetemplatePrompt": (
            "【角色定义】：你是一名资深的机票行业运营，现在用户反馈机票搜索有变价，请结合用户反馈问题描述，"
            "搜索数据明细，搜索业务规则，变价场景和变价分析规则进行搜索变价分析  "
            "【任务目标】根据用户的反馈question，搜索数据明细，搜索业务规则，变价场景和变价分析规则分析前后"
            "（preData,surData）两次搜索价格（price）是否变价，是否发生用户反馈的变价（有可能是用户反馈的一个子集，"
            "如用户反馈变价从 A-B-C波动，本次是A-B的变价），并且进行变价归因和变价定责  "
            "【输入参数说明】 |字段名|说明|明细数据 "
            "compareType| 对比类型（list-list/list-ota/ota-booking）| {compareType} "
            "question | 用户问题| {question} "
            "preData | 前置搜索数据 | {preData} "
            "surData | 后置搜索数据 | {surData} "
            "preData,surData数据是用户前后两次搜索的数据， 其中list-list的数据是前后两次L页的搜索，"
            "list-ota的数据是L页到D页的搜索，ota-booking的数据是D页到B页的搜索  "
            "【明细字段说明】 "
            "searchDateTime:搜索时间，如果同航班两次搜索时间间隔过长，如搜索时间超过30分钟导致舱位，供应，tag, "
            "航司价格都可能不一样，如果用户搜索间隔时间在10min钟内，同航班-同起飞日期-同舱位变价，则可定责平台价格稳定性问题 "
            "departureDate:航班起飞日期，如果航班起飞日期不同，变价大概率是航班起飞日期导致（同时航班起飞日期不一样会导致舱位，"
            "供应，tag都可能不一样） "
            "tradeId：每次搜索事件唯一id "
            "adultnum:成人乘机人数 "
            "childnum:儿童乘机人数 "
            "babynum:婴儿乘机人数 "
            "price:展示价 "
            "coupon:代金券，影响展示价格，另外有些代金券跟乘机人相关，如果代金券变化可以联动check乘机人是否变化,乘机人数字段："
            "adultnum/babynum/childnum "
            "activityCut:营销立减金额，影响展示价格 "
            "expCut:膨胀金金额，影响展示价格 "
            "zeroCard:抹零卡金额，影响展示价 "
            "forceBindOrPackage:强绑/附加服务,只有surData中有值，影响展示价，根据下面枚举盘点，type为附加产品类型  "
            "INS(\"保险\"),  X(\"单品\"),  PACKAGE(\"服务号\")  "
            "cabin:舱位，舱位变化大概率价格变化 "
            "packagePrice:包装价 "
            "basePrice:政策价，是采购供应的价格 "
            "viewPrice:票面价 "
            "policyId:政策id，政策id不一致价格计算方式可能不一样 "
            "tag:tag "
            "flightType:报价类型，只有surData有值，枚举值一般为：（1、8），当flightType=8时，代表报价重推，可直接归因   "
            "【业务背景知识】 搜索流程 "
            "1、搜索分为list搜索（简称L页）和ota搜索（简称D页），用户搜索先进list，list展示用户选择的航线，起飞日期展示不同航班号的价格"
            "（一个航班号在list也只有一个航班号，通常是用户能看到的最低价） "
            "2、用户在list页点击具体的航班会进入ota搜索，ota展示同航班，不同tag的报价 "
            "3、用户在ota页点击具体的报价订购按钮会进入booking页面，booking页面展示同航班，同tag的报价明细信息，一般ota展示的价格消息为"
            "某成人维度的信息，如果用户在booking页面变更乘机人，可能会引起相关卡券、报价变化 "
            "4、用户可能进行多次list、ota搜索，也可以从同一个ota点同一报价或不同报价多次进入booking页面搜索 "
            "5、每一次搜索事件都对应有一个唯一的tradeId  "
            "【判断逻辑】 "
            "一、归因结果（attributeReason）：以计算顺序逆向进行变价判断，直到某个步骤没有变价终止，上一步为变价原因，输出结果 "
            "报价计算顺序是： 底价（basePrice） -> 包装价（packagePrice）-> 展示价（price） "
            "1、最终归因到展示价变化，核心影响因素是：代金券（coupon），营销立减（activityCut），膨胀金金额（expCut），"
            "抹零卡金额（zeroCard），强绑附加服务（ ） "
            "2、最终归因到底价变化，核心因素是：舱位（cabin），政策（policyId），票面价(viewPrice)，"
            "报价重推（surData.flightType=8）  "
            "二、定责结果判断逻辑（attributeResp） "
            "按顺序进行定责 "
            "1、用户操作导致的变价 "
            "a. 用户搜索时身份变化：乘机人不一致 "
            "b. 同航班搜索时间跨度大（目前天级维度的就可判断平台无责，小时级维度的需要进一步分析原因定责  "
            "2、航司调价 "
            "a、多次搜索舱位变化，一般是航班基础数据(cabin)变化 "
            "b、票面价变化，一般是航班基础数据变化（viewPrice）  "
            "3、其他因素导致 "
            "排除用户和航司原因后均为其他因素导致  "
            "【结果输出要求】 "
            "1、变价判定结果：hasPriceChange = 是|否    判断依据：hasPriceChangeDesc  "
            "2、归因结果：attributeReason "
            "3、定责结果：attributeResp"
        ),
        "listSearchDurationLimit": "30",
        "priceMinDiffThreshold": "0",
        "priceDiffSignedThreshold": "0",
        "allowedCompareFlightTypes": "single",
        "allowedComparetypes": "ota-booking"
    }
    
    # 执行main函数
    result = main(param)
    
    # 打印结果
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    test_main()

