import json
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List

import requests

# 环境配置
ENV_CONFIGS = {
    "beta": "https://tros-wbd.beta.qunar.com",
    "prod": "https://quan.corp.qunar.com"
}

def get_base_url(env: str = "beta") -> str:
    """
    Retrieves the base URL for the specified environment.
    
    Args:
        env (str): Environment type, options: "beta", "prod". Defaults to "beta".
    
    Returns:
        str: The base URL corresponding to the specified environment.
    """
    """
    获取基础URL

    Args:
        env (str): 环境类型，可选值: "beta", "prod"

    Returns:
        str: 对应环境的基础URL
    """
    return ENV_CONFIGS.get(env.lower(), ENV_CONFIGS["beta"])

def create_secondary_card_config(payload: Dict[str, Any], env: str = "beta") -> Dict[str, Any]:
    """
    Create secondary card configuration by making a POST request to the API.

    Args:
        payload (Dict[str, Any]): The request payload containing all attributes
        env (str): 环境类型，可选值: "beta", "prod"

    Returns:
        Dict[str, Any]: The API response
    """
    base_url = get_base_url(env)
    url = f"{base_url}/tpCommonConfig/opt/create"
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"success": False, "message": str(e), "data": {}}

def auto_create_airline(payload: Dict[str, Any], env: str = "beta") -> Dict[str, Any]:
    """
    Call the autoCreateAirline API to get data for the main API request.

    Args:
        payload (Dict[str, Any]): The request payload
        env (str): 环境类型，可选值: "beta", "prod"

    Returns:
        Dict[str, Any]: The API response or None if error
    """
    base_url = get_base_url(env)
    url = f"{base_url}/tpCommonConfig/opt/autoCreateAirline"
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return result
    except requests.exceptions.RequestException as e:
        return {"success": False, "message": str(e), "data": {}}

def validate_response(response: Dict[str, Any]) -> bool:
    """
    Validate the API response.

    Args:
        response (Dict[str, Any]): The API response

    Returns:
        bool: True if the request was successful, False otherwise
    """
    if not response:
        return False

    if response.get("success") is True and response.get("code") == 0:
        return True
    else:
        return False

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result

def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def main(params: Dict[str, Any], env: str = "beta") -> Dict[str, Any]:
    """
    Main entry point for the script.

    Args:
        params (Dict[str, Any]): Dictionary containing the request data with the following structure:
            - attributes: Dictionary containing all the configuration parameters
            - priceGroup: List of price group configurations
            - businessLine: Business line number (optional)
            - Other optional fields like id, startTime, endTime, etc.
        env (str): 环境类型，可选值: "beta", "prod"

    Returns:
        Dict[str, Any]: The API response containing:
            - message: str
            - code: int
            - execSuccess: str ("是" or "否")
    """
    try:
        price_group, parseStatus = parse_urlencoded_structured_data(params, "priceGroupStr")
        if parseStatus["status"] != "success":
            return {
                "message": "价格组成为空",
                "code": 404,
                "execSuccess": "否"
            }
        # Extract attributes from the input params
        attributes = params.get("attributes", {})

        # Get priceGroup from outer level and serialize it
        serialized_price_group = json.dumps(price_group)

        # Get required parameters with defaults for optional ones
        required_params = {
            "pmoId": attributes.get("pmoId"),
            "tpName": attributes.get("tpName"),
            "tpType": 1 if attributes.get("tpType") == "单程" else 2,
            "useNum": attributes.get("useNum"),
            "carrier": attributes.get("carrier"),
            "manager": attributes.get("manager"),
            "priceGroup": serialized_price_group,  # Use serialized priceGroup
            "carrierCode": attributes.get("carrierCode"),
            "availableNow": attributes.get("availableNow"),
            "mallTemplate": attributes.get("mallTemplate"),
            "useDateRange": attributes.get("useDateRange"),
            "mallBuyAgeEnd": attributes.get("mallBuyAgeEnd"),
            "activityDomain": attributes.get("activityDomain"),
            "mallUserBuyMax": attributes.get("mallUserBuyMax"),
            "mallBuyAgeBegin": attributes.get("mallBuyAgeBegin"),
            "mallProductDesc": attributes.get("mallProductDesc"),
            "activityTemplate": attributes.get("activityTemplate"),
            "mallRealNameAuth": 1 if attributes.get("mallRealNameAuth") == "true" else 0,
            "mallSupplierCode": attributes.get("mallSupplierCode", ""),
            "autoCreateAirline": attributes.get("autoCreateAirline", "true"),
            "mallSellDateRange": attributes.get("mallSellDateRange", ""),
            "activityFlightDate": attributes.get("activityFlightDate", "ALL"),
            "activityTotalPrice": attributes.get("activityTotalPrice", ""),
            "mallPassengerBuyMax": attributes.get("mallPassengerBuyMax", ""),
            "otherAfterMakeTagMsg": attributes.get("otherAfterMakeTagMsg", ""),
            "packageTicketEffDate": attributes.get("packageTicketEffDate", ""),
            "packageTicketEffType": attributes.get("packageTicketEffType", ""),
            "packageTicketExpDate": attributes.get("packageTicketExpDate", ""),
            "packageTicketExpType": attributes.get("packageTicketExpType", ""),
            "otherInterestsCardMsg": attributes.get("otherInterestsCardMsg", ""),
            "packageTicketTemplate": attributes.get("packageTicketTemplate", "")
        }

        # Prepare the initial payload
        initial_payload = {
            "attributes": required_params,
            "priceGroup": price_group,  # Keep original priceGroup at outer level
        }

        # Call the autoCreateAirline API first
        auto_airline_response = auto_create_airline(initial_payload, env)

        if not auto_airline_response.get("success"):
            return {
                "data": {},
                "message": auto_airline_response.get("message", "自动创建航线失败"),
                "code": auto_airline_response.get("code", 500),
                "execSuccess": "否"
            }

        # Get the result from the autoCreateAirline response to use as payload
        # Use result or data, whichever is available
        api_data = auto_airline_response.get("result") or auto_airline_response.get("data", {})

        if not api_data:
            return {
                "data": api_data,
                "message": "自动创建航线返回数据为空",
                "code": 500,
                "execSuccess": "否"
            }
        # Make the API request with the data from autoCreateAirline
        response = create_secondary_card_config(api_data, env)

        # Format response to only include required fields
        return {
            "data": api_data,
            "message": response.get("message", "操作成功"),
            "code": response.get("code", 0),
            "execSuccess": "是" if response.get("success") else "否"
        }

    except Exception as e:
        return {
            "data": {},
            "message": str(e),
            "code": 500,
            "execSuccess": "否"
        }


if __name__ == "__main__":
    # 构造测试数据
    test_json = {
        "priceGroupStr": "priceItem%3A800%23%2A%23priceMark%3A1540%23%2A%23priceCabin%3AI%23%2A%23priceStock%3A500%23%2A%23priceTagMoney%3ATPF%3A800%23%2A%23priceTerminalRoute%3ADLC_TAO%7E%7E%2A%7E%7E, priceItem%3A1000%23%2A%23priceMark%3A1543%23%2A%23priceCabin%3AI%23%2A%23priceStock%3A500%23%2A%23priceTagMoney%3ATPF%3A1000%23%2A%23priceTerminalRoute%3ADLC_TAO%7E%7E%2A%7E%7E, priceItem%3A1200%23%2A%23priceMark%3A1540%23%2A%23priceCabin%3AI%23%2A%23priceStock%3A500%23%2A%23priceTagMoney%3ATPF%3A1200%23%2A%23priceTerminalRoute%3ADLC_TAO%7E%7E%2A%7E%7E, priceItem%3A1400%23%2A%23priceMark%3A1540%23%2A%23priceCabin%3AI%23%2A%23priceStock%3A500%23%2A%23priceTagMoney%3ATPF%3A1400%23%2A%23priceTerminalRoute%3ADLC_TAO%7E%7E%2A%7E%7E",
        "attributes": {   
            "pmoId": "XXX", 
            "tpName": "测试1217-02",
            "tpType": "1",
            "useNum": "10",
            "carrier": "XX",
            "manager": "wwww",
            "carrierCode": "CA",
            "availableNow": "2",
            "mallTemplate": "001467691779",
            "useDateRange": "2024-12-01 00:00:00,2024-12-31 23:59:59",
            "mallBuyAgeEnd": "60",
            "activityDomain": "ALL",
            "mallUserBuyMax": "10",
            "mallBuyAgeBegin": "10",
            "mallProductDesc": "XX",
            "activityTemplate": "90029163",
            "mallRealNameAuth": "1",
            "mallSupplierCode": "",
            "autoCreateAirline": "true",
            "mallSellDateRange": "2024-12-01 00:00:00,2024-12-31 23:59:59",
            "activityFlightDate": "ALL",
            "activityTotalPrice": "1000",
            "mallPassengerBuyMax": "10",
            "otherAfterMakeTagMsg": "XXX",
            "packageTicketEffDate": "10",
            "packageTicketEffType": "0",
            "packageTicketExpDate": "2024-12-26",
            "packageTicketExpType": "1",
            "otherInterestsCardMsg": "XXX",
            "packageTicketTemplate": "90029220",
            "otherUploadAirlineData": "[{\"pid\":\"\",\"dep\":\"SJW\",\"depName\":\"石家庄\",\"depCapitalInitial\":\"S\",\"arr\":\"YSQ\",\"arrName\":\"松原\",\"arrCapitalInitial\":\"S\",\"price\":\"39900\",\"inventory\":\"0\",\"tpCode\":\"\",\"tpNeedDepArr\":\"FALSE\",\"cardType\":\"\",\"cardDesc\":\"\",\"purchasePrice\":\"0\",\"isHotAirline\":\"TRUE\",\"expireTime\":\"\"}]",
            "searchDate": "2024-12-01 00:00:00,2024-12-31 23:59:59"
        },
        "id": 90075848,
        "startTime": "1970-01-01 14:00:00",
        "endTime": "1970-01-01 14:00:00",
        "businessLine": 33,
        "activityStatus": 1,
        "createTime": "2025-03-31 21:37:44",
        "updateTime": "2025-03-31 21:37:44",
        "createUser": None,
        "updateUser": None,
        "xconfigKey": None,
        "uploadKey": ""
    }
    
    # 执行测试
    print("开始执行测试...")
    response = main(test_json)
    print("\n测试结果:")
    print(json.dumps(response, ensure_ascii=False, indent=2))
