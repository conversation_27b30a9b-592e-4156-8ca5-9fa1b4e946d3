/**
 * DOC文件编码修复器
 * 专门处理.doc文件的乱码问题
 */
class DocEncodingFixer {
  constructor() {
    this.chineseCharPattern = /[\u4e00-\u9fff]/g;
    this.asciiPattern = /[a-zA-Z0-9\s\.,!?;:'"()-]/g;
  }

  // 主要的修复方法
  fixEncoding(rawText) {
    console.log('开始修复编码，原始文本长度:', rawText.length);
    
    // 方法1: 提取已识别的中文字符
    const chineseText = this.extractChineseText(rawText);
    
    // 方法2: 修复UTF-16编码问题
    const utf16Fixed = this.fixUTF16Encoding(rawText);
    
    // 方法3: 提取可读的ASCII文本
    const asciiText = this.extractReadableASCII(rawText);
    
    // 方法4: 智能文本重构
    const reconstructed = this.reconstructText(rawText);
    
    // 选择最佳结果
    const results = [
      { text: chineseText, score: this.scoreText(chineseText), method: '中文提取' },
      { text: utf16Fixed, score: this.scoreText(utf16Fixed), method: 'UTF-16修复' },
      { text: asciiText, score: this.scoreText(asciiText), method: 'ASCII提取' },
      { text: reconstructed, score: this.scoreText(reconstructed), method: '智能重构' }
    ];
    
    const bestResult = results.reduce((best, current) => 
      current.score > best.score ? current : best
    );
    
    console.log(`最佳修复方法: ${bestResult.method}, 得分: ${bestResult.score}`);
    
    return {
      text: bestResult.text,
      method: bestResult.method,
      allResults: results
    };
  }

  // 提取中文文本
  extractChineseText(rawText) {
    const chineseMatches = rawText.match(this.chineseCharPattern) || [];
    const asciiMatches = rawText.match(/[a-zA-Z0-9\s]{2,}/g) || [];
    
    // 合并中文和有意义的ASCII文本
    const meaningfulText = [];
    
    // 添加中文字符
    if (chineseMatches.length > 0) {
      meaningfulText.push(chineseMatches.join(''));
    }
    
    // 添加有意义的ASCII文本
    asciiMatches.forEach(match => {
      if (match.trim().length > 2 && this.isMeaningfulText(match)) {
        meaningfulText.push(match.trim());
      }
    });
    
    return meaningfulText.join(' ');
  }

  // 修复UTF-16编码
  fixUTF16Encoding(rawText) {
    let fixedText = '';
    
    // 尝试重新解释字符编码
    for (let i = 0; i < rawText.length; i++) {
      const char = rawText[i];
      const charCode = char.charCodeAt(0);
      
      // 如果是正常的中文或ASCII字符，直接保留
      if ((charCode >= 0x4e00 && charCode <= 0x9fff) || 
          (charCode >= 0x20 && charCode <= 0x7E)) {
        fixedText += char;
      }
      // 如果是可能的编码错误，尝试修复
      else if (charCode < 0x20 || charCode > 0xFFFF) {
        // 跳过控制字符和无效字符
        continue;
      }
      // 其他字符，尝试重新编码
      else {
        try {
          // 尝试将字符重新解释为UTF-8
          const bytes = new TextEncoder().encode(char);
          const decoded = new TextDecoder('utf-8', { fatal: false }).decode(bytes);
          if (decoded && decoded !== char) {
            fixedText += decoded;
          }
        } catch (e) {
          // 如果重新编码失败，跳过
        }
      }
    }
    
    return this.cleanText(fixedText);
  }

  // 提取可读的ASCII文本
  extractReadableASCII(rawText) {
    const readableChunks = [];
    let currentChunk = '';
    
    for (let i = 0; i < rawText.length; i++) {
      const char = rawText[i];
      const charCode = char.charCodeAt(0);
      
      if (charCode >= 0x20 && charCode <= 0x7E) {
        currentChunk += char;
      } else if (charCode >= 0x4e00 && charCode <= 0x9fff) {
        currentChunk += char;
      } else {
        if (currentChunk.length > 3 && this.isMeaningfulText(currentChunk)) {
          readableChunks.push(currentChunk.trim());
        }
        currentChunk = '';
      }
    }
    
    // 处理最后一个块
    if (currentChunk.length > 3 && this.isMeaningfulText(currentChunk)) {
      readableChunks.push(currentChunk.trim());
    }
    
    return readableChunks.join(' ');
  }

  // 智能文本重构
  reconstructText(rawText) {
    // 查找已知的文档结构关键词
    const knownKeywords = [
      '正文', '默认段落字体', '页脚', '页眉', 'WPS Office', '专业版',
      'Times', 'Arial', 'Symbol', 'Courier', 'New Roman'
    ];
    
    const reconstructedParts = [];
    
    // 提取包含关键词的文本段落
    knownKeywords.forEach(keyword => {
      const index = rawText.indexOf(keyword);
      if (index !== -1) {
        // 提取关键词前后的文本
        const start = Math.max(0, index - 20);
        const end = Math.min(rawText.length, index + keyword.length + 20);
        const segment = rawText.substring(start, end);
        
        // 清理并添加到结果中
        const cleaned = this.cleanTextSegment(segment);
        if (cleaned.length > keyword.length) {
          reconstructedParts.push(cleaned);
        }
      }
    });
    
    // 如果没有找到关键词，尝试提取最长的有意义文本段
    if (reconstructedParts.length === 0) {
      const segments = this.findLongestMeaningfulSegments(rawText);
      reconstructedParts.push(...segments);
    }
    
    return reconstructedParts.join('\n');
  }

  // 查找最长的有意义文本段
  findLongestMeaningfulSegments(text) {
    const segments = [];
    const windowSize = 50;
    
    for (let i = 0; i < text.length - windowSize; i += 25) {
      const window = text.substring(i, i + windowSize);
      const meaningful = this.extractMeaningfulFromWindow(window);
      
      if (meaningful.length > 10) {
        segments.push(meaningful);
      }
    }
    
    // 去重并按长度排序
    const uniqueSegments = [...new Set(segments)];
    return uniqueSegments
      .sort((a, b) => b.length - a.length)
      .slice(0, 5); // 取前5个最长的段落
  }

  // 从窗口提取有意义的文本
  extractMeaningfulFromWindow(window) {
    let meaningful = '';
    
    for (let i = 0; i < window.length; i++) {
      const char = window[i];
      const charCode = char.charCodeAt(0);
      
      if ((charCode >= 0x4e00 && charCode <= 0x9fff) || // 中文
          (charCode >= 0x20 && charCode <= 0x7E)) {     // ASCII可打印
        meaningful += char;
      } else if (charCode === 0x0A || charCode === 0x0D) {
        meaningful += ' ';
      }
    }
    
    return this.cleanText(meaningful);
  }

  // 清理文本段落
  cleanTextSegment(segment) {
    return segment
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fff\u0020-\u007E\s]/g, '') // 只保留中文和ASCII
      .replace(/\s+/g, ' ') // 合并空格
      .trim();
  }

  // 判断是否为有意义的文本
  isMeaningfulText(text) {
    if (!text || text.length < 3) return false;
    
    // 检查是否包含有意义的字符
    const meaningfulChars = text.match(/[\u4e00-\u9fff\w]/g);
    if (!meaningfulChars) return false;
    
    // 有意义字符的比例
    const ratio = meaningfulChars.length / text.length;
    
    // 检查是否为常见的无意义字符串
    const meaninglessPatterns = [
      /^[0-9\s]+$/, // 纯数字
      /^[^\w\u4e00-\u9fff]+$/, // 纯符号
      /^(.)\1{5,}$/ // 重复字符
    ];
    
    for (const pattern of meaninglessPatterns) {
      if (pattern.test(text)) return false;
    }
    
    return ratio > 0.5;
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;
    
    let score = 0;
    
    // 基础长度分
    score += Math.min(text.length / 10, 50);
    
    // 中文字符加分
    const chineseChars = text.match(this.chineseCharPattern);
    if (chineseChars) {
      score += chineseChars.length * 3;
    }
    
    // 有意义的英文单词加分
    const englishWords = text.match(/\b[a-zA-Z]{3,}\b/g);
    if (englishWords) {
      score += englishWords.length * 2;
    }
    
    // 常见词汇加分
    const commonWords = ['正文', '页眉', '页脚', '字体', 'Office', 'WPS', '专业版'];
    commonWords.forEach(word => {
      if (text.includes(word)) {
        score += 10;
      }
    });
    
    // 文本连贯性加分
    if (text.length > 20 && this.hasCoherentStructure(text)) {
      score += 20;
    }
    
    return score;
  }

  // 检查文本连贯性
  hasCoherentStructure(text) {
    // 检查是否有合理的词汇分布
    const words = text.split(/\s+/).filter(word => word.length > 1);
    if (words.length < 3) return false;
    
    // 检查是否有重复的无意义字符
    const uniqueChars = new Set(text);
    const charVariety = uniqueChars.size / text.length;
    
    return charVariety > 0.1; // 字符多样性要求
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/\s{3,}/g, '  ') // 合并多余空格
      .replace(/\n{3,}/g, '\n\n') // 合并多余换行
      .trim();
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocEncodingFixer;
} else if (typeof window !== 'undefined') {
  window.DocEncodingFixer = DocEncodingFixer;
}
