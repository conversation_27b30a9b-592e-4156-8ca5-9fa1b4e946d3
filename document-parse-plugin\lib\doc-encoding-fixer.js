/**
 * DOC文件编码修复器
 * 专门处理.doc文件的乱码问题
 */
class DocEncodingFixer {
  constructor() {
    this.chineseCharPattern = /[\u4e00-\u9fff]/g;
    this.asciiPattern = /[a-zA-Z0-9\s\.,!?;:'"()-]/g;
    
    // GBK编码范围
    this.gbkRanges = [
      [0x8140, 0xFEFE], // GBK基本范围
      [0xA1A1, 0xFEFE]  // GB2312范围
    ];
    
    // 编码检测器配置
    this.encodings = ['utf-8', 'gbk', 'gb18030', 'utf-16le'];
    
    // 初始化编码检测器
    if (typeof TextDecoder !== 'undefined') {
      this.decoders = this.encodings.map(encoding => {
        try {
          return new TextDecoder(encoding, { fatal: true });
        } catch (e) {
          console.warn(`不支持 ${encoding} 编码`);
          return null;
        }
      }).filter(Boolean);
    } else {
      console.warn('当前环境不支持TextDecoder');
      this.decoders = [];
    }
  }

  // 主要的修复方法
  fixEncoding(rawText, arrayBuffer) {
    console.log('开始修复编码，原始文本长度:', rawText.length);
    
    // 方法0: 尝试自动检测编码并转换（如果提供了arrayBuffer）
    let autoDetectedText = '';
    if (arrayBuffer) {
      const detectionResult = this.detectAndConvertEncoding(arrayBuffer);
      if (detectionResult.success) {
        autoDetectedText = detectionResult.text;
        console.log(`自动检测到编码: ${detectionResult.encoding}`);
      }
    }
    
    // 方法1: 提取已识别的中文字符
    const chineseText = this.extractChineseText(rawText);
    
    // 方法2: 修复UTF-16编码问题
    const utf16Fixed = this.fixUTF16Encoding(rawText);
    
    // 方法3: 提取可读的ASCII文本
    const asciiText = this.extractReadableASCII(rawText);
    
    // 方法4: 智能文本重构
    const reconstructed = this.reconstructText(rawText);
    
    // 方法5: 尝试GBK/GB18030编码修复
    const gbkFixed = this.fixGBKEncoding(rawText);
    
    // 选择最佳结果
    const results = [
      { text: autoDetectedText, score: this.scoreText(autoDetectedText), method: '自动检测编码' },
      { text: chineseText, score: this.scoreText(chineseText), method: '中文提取' },
      { text: utf16Fixed, score: this.scoreText(utf16Fixed), method: 'UTF-16修复' },
      { text: asciiText, score: this.scoreText(asciiText), method: 'ASCII提取' },
      { text: reconstructed, score: this.scoreText(reconstructed), method: '智能重构' },
      { text: gbkFixed, score: this.scoreText(gbkFixed), method: 'GBK编码修复' }
    ];
    
    const bestResult = results.reduce((best, current) => 
      current.score > best.score ? current : best
    );
    
    console.log(`最佳修复方法: ${bestResult.method}, 得分: ${bestResult.score}`);
    
    return {
      text: bestResult.text,
      method: bestResult.method,
      allResults: results
    };
  }

  // 提取中文文本
  extractChineseText(rawText) {
    const chineseMatches = rawText.match(this.chineseCharPattern) || [];
    const asciiMatches = rawText.match(/[a-zA-Z0-9\s]{2,}/g) || [];
    
    // 合并中文和有意义的ASCII文本
    const meaningfulText = [];
    
    // 添加中文字符
    if (chineseMatches.length > 0) {
      meaningfulText.push(chineseMatches.join(''));
    }
    
    // 添加有意义的ASCII文本
    asciiMatches.forEach(match => {
      if (match.trim().length > 2 && this.isMeaningfulText(match)) {
        meaningfulText.push(match.trim());
      }
    });
    
    return meaningfulText.join(' ');
  }

  // 修复UTF-16编码
  fixUTF16Encoding(rawText) {
    let fixedText = '';
    
    // 尝试重新解释字符编码
    for (let i = 0; i < rawText.length; i++) {
      const char = rawText[i];
      const charCode = char.charCodeAt(0);
      
      // 如果是正常的中文或ASCII字符，直接保留
      if ((charCode >= 0x4e00 && charCode <= 0x9fff) || 
          (charCode >= 0x20 && charCode <= 0x7E)) {
        fixedText += char;
      }
      // 如果是可能的编码错误，尝试修复
      else if (charCode < 0x20 || charCode > 0xFFFF) {
        // 跳过控制字符和无效字符
        continue;
      }
      // 其他字符，尝试重新编码
      else {
        try {
          // 尝试将字符重新解释为UTF-8
          const bytes = new TextEncoder().encode(char);
          const decoded = new TextDecoder('utf-8', { fatal: false }).decode(bytes);
          if (decoded && decoded !== char) {
            fixedText += decoded;
          }
        } catch (e) {
          // 如果重新编码失败，跳过
        }
      }
    }
    
    return this.cleanText(fixedText);
  }

  // 提取可读的ASCII文本
  extractReadableASCII(rawText) {
    const readableChunks = [];
    let currentChunk = '';
    
    for (let i = 0; i < rawText.length; i++) {
      const char = rawText[i];
      const charCode = char.charCodeAt(0);
      
      if (charCode >= 0x20 && charCode <= 0x7E) {
        currentChunk += char;
      } else if (charCode >= 0x4e00 && charCode <= 0x9fff) {
        currentChunk += char;
      } else {
        if (currentChunk.length > 3 && this.isMeaningfulText(currentChunk)) {
          readableChunks.push(currentChunk.trim());
        }
        currentChunk = '';
      }
    }
    
    // 处理最后一个块
    if (currentChunk.length > 3 && this.isMeaningfulText(currentChunk)) {
      readableChunks.push(currentChunk.trim());
    }
    
    return readableChunks.join(' ');
  }

  // 智能文本重构
  reconstructText(rawText) {
    // 查找已知的文档结构关键词
    const knownKeywords = [
      '正文', '默认段落字体', '页脚', '页眉', 'WPS Office', '专业版',
      'Times', 'Arial', 'Symbol', 'Courier', 'New Roman'
    ];
    
    const reconstructedParts = [];
    
    // 提取包含关键词的文本段落
    knownKeywords.forEach(keyword => {
      const index = rawText.indexOf(keyword);
      if (index !== -1) {
        // 提取关键词前后的文本
        const start = Math.max(0, index - 20);
        const end = Math.min(rawText.length, index + keyword.length + 20);
        const segment = rawText.substring(start, end);
        
        // 清理并添加到结果中
        const cleaned = this.cleanTextSegment(segment);
        if (cleaned.length > keyword.length) {
          reconstructedParts.push(cleaned);
        }
      }
    });
    
    // 如果没有找到关键词，尝试提取最长的有意义文本段
    if (reconstructedParts.length === 0) {
      const segments = this.findLongestMeaningfulSegments(rawText);
      reconstructedParts.push(...segments);
    }
    
    return reconstructedParts.join('\n');
  }

  // 查找最长的有意义文本段
  findLongestMeaningfulSegments(text) {
    const segments = [];
    const windowSize = 50;
    
    for (let i = 0; i < text.length - windowSize; i += 25) {
      const window = text.substring(i, i + windowSize);
      const meaningful = this.extractMeaningfulFromWindow(window);
      
      if (meaningful.length > 10) {
        segments.push(meaningful);
      }
    }
    
    // 去重并按长度排序
    const uniqueSegments = [...new Set(segments)];
    return uniqueSegments
      .sort((a, b) => b.length - a.length)
      .slice(0, 5); // 取前5个最长的段落
  }

  // 从窗口提取有意义的文本
  extractMeaningfulFromWindow(window) {
    let meaningful = '';
    
    for (let i = 0; i < window.length; i++) {
      const char = window[i];
      const charCode = char.charCodeAt(0);
      
      if ((charCode >= 0x4e00 && charCode <= 0x9fff) || // 中文
          (charCode >= 0x20 && charCode <= 0x7E)) {     // ASCII可打印
        meaningful += char;
      } else if (charCode === 0x0A || charCode === 0x0D) {
        meaningful += ' ';
      }
    }
    
    return this.cleanText(meaningful);
  }

  // 清理文本段落
  cleanTextSegment(segment) {
    return segment
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fff\u0020-\u007E\s]/g, '') // 只保留中文和ASCII
      .replace(/\s+/g, ' ') // 合并空格
      .trim();
  }

  // 判断是否为有意义的文本
  isMeaningfulText(text) {
    if (!text || text.length < 3) return false;
    
    // 检查是否包含有意义的字符
    const meaningfulChars = text.match(/[\u4e00-\u9fff\w]/g);
    if (!meaningfulChars) return false;
    
    // 有意义字符的比例
    const ratio = meaningfulChars.length / text.length;
    
    // 检查是否为常见的无意义字符串
    const meaninglessPatterns = [
      /^[0-9\s]+$/, // 纯数字
      /^[^\w\u4e00-\u9fff]+$/, // 纯符号
      /^(.)\1{5,}$/ // 重复字符
    ];
    
    for (const pattern of meaninglessPatterns) {
      if (pattern.test(text)) return false;
    }
    
    return ratio > 0.5;
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;
    
    let score = 0;
    
    // 基础长度分
    score += Math.min(text.length / 10, 50);
    
    // 中文字符加分
    const chineseChars = text.match(this.chineseCharPattern);
    if (chineseChars) {
      score += chineseChars.length * 3;
    }
    
    // 有意义的英文单词加分
    const englishWords = text.match(/\b[a-zA-Z]{3,}\b/g);
    if (englishWords) {
      score += englishWords.length * 2;
    }
    
    // 常见词汇加分
    const commonWords = ['正文', '页眉', '页脚', '字体', 'Office', 'WPS', '专业版'];
    commonWords.forEach(word => {
      if (text.includes(word)) {
        score += 10;
      }
    });
    
    // 文本连贯性加分
    if (text.length > 20 && this.hasCoherentStructure(text)) {
      score += 20;
    }
    
    return score;
  }

  // 检查文本连贯性
  hasCoherentStructure(text) {
    // 检查是否有合理的词汇分布
    const words = text.split(/\s+/).filter(word => word.length > 1);
    if (words.length < 3) return false;
    
    // 检查是否有重复的无意义字符
    const uniqueChars = new Set(text);
    const charVariety = uniqueChars.size / text.length;
    
    return charVariety > 0.1; // 字符多样性要求
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/\s{3,}/g, '  ') // 合并多余空格
      .replace(/\n{3,}/g, '\n\n') // 合并多余换行
      .trim();
  }

  // 自动检测和转换编码
  detectAndConvertEncoding(arrayBuffer) {
    if (!arrayBuffer || !this.decoders.length) {
      return { success: false, text: '', encoding: null };
    }

    // 首先检查文件头部特征
    const header = new Uint8Array(arrayBuffer.slice(0, 4));
    
    // 检查UTF-8 BOM
    if (header[0] === 0xEF && header[1] === 0xBB && header[2] === 0xBF) {
      try {
        const decoder = new TextDecoder('utf-8');
        return {
          success: true,
          text: decoder.decode(arrayBuffer.slice(3)),
          encoding: 'utf-8'
        };
      } catch (e) {
        console.warn('UTF-8 BOM检测到但解码失败');
      }
    }
    
    // 检查UTF-16LE BOM
    if (header[0] === 0xFF && header[1] === 0xFE) {
      try {
        const decoder = new TextDecoder('utf-16le');
        return {
          success: true,
          text: decoder.decode(arrayBuffer.slice(2)),
          encoding: 'utf-16le'
        };
      } catch (e) {
        console.warn('UTF-16LE BOM检测到但解码失败');
      }
    }

    // 尝试所有支持的编码
    const results = [];
    for (const decoder of this.decoders) {
      try {
        const text = decoder.decode(arrayBuffer);
        const score = this.scoreText(text);
        
        results.push({
          text,
          score,
          encoding: decoder.encoding
        });
      } catch (e) {
        continue;
      }
    }

    // 按分数排序
    results.sort((a, b) => b.score - a.score);
    
    // 如果有足够高分的结果，返回最高分的
    if (results.length > 0 && results[0].score > 60) {
      return {
        success: true,
        text: results[0].text,
        encoding: results[0].encoding
      };
    }

    return { success: false, text: '', encoding: null };
  }

  // 修复GBK编码
  fixGBKEncoding(rawText) {
    try {
      // 尝试检测是否为GBK编码的二进制数据
      const bytes = new Uint8Array(rawText.length);
      for (let i = 0; i < rawText.length; i++) {
        bytes[i] = rawText.charCodeAt(i) & 0xFF;
      }
      
      // 检查是否符合GBK编码规则
      let result = '';
      for (let i = 0; i < bytes.length; i++) {
        if (bytes[i] <= 0x7F) {
          // ASCII字符
          result += String.fromCharCode(bytes[i]);
        } else if (i + 1 < bytes.length) {
          // 可能是GBK双字节字符
          const byte1 = bytes[i];
          const byte2 = bytes[i + 1];
          
          // 检查是否在GBK范围内
          const isInGBKRange = this.gbkRanges.some(([start, end]) => {
            const code = (byte1 << 8) | byte2;
            return code >= start && code <= end;
          });
          
          if (isInGBKRange) {
            try {
              // 尝试使用GB18030解码（包含GBK）
              const gbkBytes = new Uint8Array([byte1, byte2]);
              const decoder = new TextDecoder('gb18030');
              const char = decoder.decode(gbkBytes);
              result += char;
            } catch (e) {
              // 如果解码失败，保留原始字符
              result += rawText.charAt(i);
            }
            i++; // 跳过下一个字节
          } else {
            result += rawText.charAt(i);
          }
        } else {
          result += rawText.charAt(i);
        }
      }
      
      return this.cleanText(result);
    } catch (e) {
      console.warn('GBK编码修复失败:', e);
      return rawText;
    }
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocEncodingFixer;
} else if (typeof window !== 'undefined') {
  window.DocEncodingFixer = DocEncodingFixer;
}