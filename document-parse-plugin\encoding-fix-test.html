<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>编码修复测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .input-area {
      margin: 20px 0;
    }
    textarea {
      width: 100%;
      height: 200px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
    }
    .result-area {
      margin-top: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #f9f9f9;
      min-height: 200px;
    }
    .method-result {
      margin: 15px 0;
      padding: 15px;
      border-radius: 5px;
      border-left: 4px solid #007bff;
      background: white;
    }
    .method-title {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 10px;
    }
    .method-content {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 150px;
      overflow-y: auto;
    }
    .score {
      color: #28a745;
      font-weight: bold;
      float: right;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
      font-size: 16px;
    }
    button:hover {
      background: #0056b3;
    }
    .test-button {
      background: #28a745;
    }
    .test-button:hover {
      background: #1e7e34;
    }
    .clear-button {
      background: #6c757d;
    }
    .clear-button:hover {
      background: #545b62;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 DOC编码修复测试</h1>
    
    <div class="input-area">
      <h3>📝 输入乱码文本</h3>
      <textarea id="inputText" placeholder="请粘贴您的乱码文本...">Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\ WPS Office 专业版王如根@鞓D鞓耀 (\耀dlKSOProductBuildVer2052-9.1.0.39140澐C</textarea>
    </div>

    <div style="text-align: center;">
      <button class="test-button" onclick="testEncodingFix()">🔧 修复编码</button>
      <button onclick="testWithSampleText()">📄 测试示例文本</button>
      <button class="clear-button" onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <div class="result-area" id="resultArea">
      <p style="color: #666; text-align: center;">等待输入文本进行修复...</p>
    </div>
  </div>

  <!-- 加载编码修复器 -->
  <script src="lib/doc-encoding-fixer.js"></script>

  <script>
    // 初始化编码修复器
    let encodingFixer = null;
    try {
      encodingFixer = new DocEncodingFixer();
      console.log('编码修复器初始化成功');
      showStatus('success', '编码修复器加载成功');
    } catch (error) {
      console.error('编码修复器初始化失败:', error);
      showStatus('error', '编码修复器加载失败: ' + error.message);
    }

    function testEncodingFix() {
      const inputText = document.getElementById('inputText').value.trim();
      
      if (!inputText) {
        showStatus('error', '请输入要修复的文本');
        return;
      }

      if (!encodingFixer) {
        showStatus('error', '编码修复器未初始化');
        return;
      }

      showStatus('info', '开始修复编码...');

      try {
        const result = encodingFixer.fixEncoding(inputText);
        displayResults(result, inputText);
        showStatus('success', `修复完成，最佳方法: ${result.method}`);
      } catch (error) {
        showStatus('error', '修复失败: ' + error.message);
        console.error('修复错误:', error);
      }
    }

    function testWithSampleText() {
      const sampleText = `Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡\` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\\ WPS Office 专业版王如根@鞓D鞓耀 (\\耀dlKSOProductBuildVer2052-9.1.0.39140澐C`;
      
      document.getElementById('inputText').value = sampleText;
      testEncodingFix();
    }

    function displayResults(result, originalText) {
      const resultArea = document.getElementById('resultArea');
      
      let html = `
        <h3>🔧 编码修复结果</h3>
        
        <div class="method-result">
          <div class="method-title">
            最佳修复结果 <span class="score">得分: ${encodingFixer.scoreText(result.text)}</span>
          </div>
          <div><strong>使用方法:</strong> ${result.method}</div>
          <div><strong>修复后长度:</strong> ${result.text.length} 字符</div>
          <div class="method-content">${result.text || '无内容'}</div>
        </div>
      `;

      // 显示所有修复方法的结果
      if (result.allResults && result.allResults.length > 0) {
        html += '<h4>📊 所有修复方法对比</h4>';
        
        result.allResults
          .sort((a, b) => b.score - a.score)
          .forEach((methodResult, index) => {
            html += `
              <div class="method-result">
                <div class="method-title">
                  ${methodResult.method} <span class="score">得分: ${methodResult.score}</span>
                </div>
                <div class="method-content">${methodResult.text || '无内容'}</div>
              </div>
            `;
          });
      }

      // 显示原始文本分析
      html += `
        <h4>📋 原始文本分析</h4>
        <div class="method-result">
          <div class="method-title">原始输入</div>
          <div><strong>长度:</strong> ${originalText.length} 字符</div>
          <div><strong>中文字符:</strong> ${(originalText.match(/[\u4e00-\u9fff]/g) || []).length} 个</div>
          <div><strong>ASCII字符:</strong> ${(originalText.match(/[a-zA-Z0-9]/g) || []).length} 个</div>
          <div class="method-content">${originalText.substring(0, 500)}${originalText.length > 500 ? '...' : ''}</div>
        </div>
      `;

      resultArea.innerHTML = html;
    }

    function showStatus(type, message) {
      const resultArea = document.getElementById('resultArea');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      
      resultArea.insertBefore(statusDiv, resultArea.firstChild);
      
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function clearResults() {
      document.getElementById('resultArea').innerHTML = '<p style="color: #666; text-align: center;">等待输入文本进行修复...</p>';
      document.getElementById('inputText').value = '';
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof DocEncodingFixer !== 'undefined') {
        showStatus('success', '页面加载完成，编码修复器可用');
      } else {
        showStatus('error', '编码修复器未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
