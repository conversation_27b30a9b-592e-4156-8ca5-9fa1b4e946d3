import re
from collections import defaultdict
from urllib.parse import unquote


def parse_item(item_str: str) -> dict:
    """Parse a single item string into a dictionary."""
    # Decode URL encoding
    item_str = unquote(item_str)

    # Split by #*# and create a dictionary
    parts = item_str.split("#*#")
    item_dict = {}
    for part in parts:
        if ":" in part:
            key, value = part.split(":", 1)
            item_dict[key] = value
    return item_dict


def check_duplicate_tradeids(param_str: str):
    """Check for duplicate tradeIds in list data source for the same flight."""
    # Split by comma to get individual items
    items = [parse_item(item) for item in param_str.split(",")]

    # Create a dictionary to store flight -> tradeId -> items
    flight_tradeid_map = defaultdict(lambda: defaultdict(list))

    # Group items by flight and tradeId
    for item in items:
        flight_no = item.get("flightNo")
        trade_id = item.get("tradeId")
        t_source = item.get("tSource")

        if flight_no and trade_id and t_source == "list":
            flight_tradeid_map[flight_no][trade_id].append(item)

    # Check for duplicates
    print("\n=== 同航班下 list 数据源的 tradeId 重复情况 ===")
    found_duplicates = False

    for flight_no, tradeid_map in flight_tradeid_map.items():
        duplicates = {
            trade_id: items for trade_id, items in tradeid_map.items() if len(items) > 1
        }
        if duplicates:
            found_duplicates = True
            print(f"\n航班号: {flight_no}")
            for trade_id, items in duplicates.items():
                print(f"  TradeId: {trade_id}")
                print(f"  重复次数: {len(items)}")
                print("  详细信息:")
                for item in items:
                    print(
                        f"    - 价格: {item.get('price')}, 舱位: {item.get('cabin')}, 时间: {item.get('searchDateTime')}"
                    )

    if not found_duplicates:
        print("\n未发现同航班下 list 数据源的 tradeId 重复情况")


if __name__ == "__main__":
    # Test data
    test_input = {
        "priceMinDiffThreshold": 0,
        "allowedComparetypes": "list-list,list-ota",
        "param": "tradeId%3Aops_slugger_250303.120653.10.95.133.43.848208.6589504898_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A06%3A53%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A315%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740974803%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120653.10.95.133.43.848208.6589504898_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A06%3A53%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2105%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A275%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd02919%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A285%23%2A%23basePrice%3A284.5%23%2A%23viewPrice%3A300%23%2A%23policyId%3A2947024463%23%2A%23autoPriceDecreaseAmount%3A13.5%23%2A%23secondPrice%3A293%23%2A%23CPT%3A1740974726%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A11%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E",
    }

    # Check for duplicates
    check_duplicate_tradeids(test_input["param"])
