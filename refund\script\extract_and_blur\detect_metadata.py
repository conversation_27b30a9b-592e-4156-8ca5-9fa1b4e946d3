#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image
import piexif
from io import BytesIO
import requests
from pathlib import Path
import os
import sys

def check_metadata(image_data):
    """
    检查图像元数据中的PS痕迹
    
    参数:
        image_data: 图片数据（字节流）
    
    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 读取图像元数据
        img = Image.open(BytesIO(image_data))
        metadata_info = {}
        has_ps_metadata = False
        
        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])
            
            # 检查所有可能的元数据
            for ifd in ("0th", "Exif", "GPS", "1st"):
                if ifd in exif_dict:
                    for tag, value in exif_dict[ifd].items():
                        # 尝试解码值
                        try:
                            if isinstance(value, bytes):
                                decoded_value = value.decode('utf-8', errors='ignore')
                            else:
                                decoded_value = str(value)
                            
                            tag_name = f"{ifd}_tag_{tag}"
                            metadata_info[tag_name] = decoded_value
                            
                            # 检查是否包含Adobe/Photoshop相关信息
                            if isinstance(decoded_value, str) and ("photoshop" in decoded_value.lower() or "adobe" in decoded_value.lower()):
                                has_ps_metadata = True
                        except:
                            pass
        
        # 检查其他元数据
        for key, value in img.info.items():
            if key != "exif":
                try:
                    metadata_info[key] = str(value)
                    # 检查键名是否包含Adobe/Photoshop相关信息
                    if "photoshop" in key.lower() or "adobe" in key.lower():
                        has_ps_metadata = True
                        metadata_info["PS标记"] = f"图像包含'{key}'元数据键"
                    # 检查值是否包含Adobe/Photoshop相关信息
                    elif "photoshop" in str(value).lower() or "adobe" in str(value).lower():
                        has_ps_metadata = True
                        metadata_info["PS标记"] = f"图像的'{key}'元数据值中包含Photoshop/Adobe信息"
                except:
                    pass
        
        return has_ps_metadata, metadata_info
    
    except Exception as e:
        print(f"检查元数据时出错: {e}")
        return False, {"错误": str(e)}

def get_image_data(image_path_or_url):
    """
    获取图片数据，支持本地文件路径或URL
    
    参数:
        image_path_or_url: 图片本地路径或URL地址
    
    返回:
        image_data: 图片数据（字节流）
    """
    try:
        # 检查是否为URL（简单判断是否以http开头）
        if image_path_or_url.startswith(('http://', 'https://')):
            # 从URL获取图片
            response = requests.get(image_path_or_url, stream=True)
            response.raise_for_status()
            return response.content
        else:
            # 从本地文件读取图片
            with open(image_path_or_url, 'rb') as f:
                return f.read()
    except Exception as e:
        raise Exception(f"获取图片数据失败: {e}")

def detect_metadata(image_path_or_url):
    """
    检测图像元数据信息，支持本地文件路径或URL
    
    参数:
        image_path_or_url: 图片本地路径或URL地址
    
    返回:
        result: 检测结果字典
    """
    result = {
        "图片位置": image_path_or_url,
        "元数据信息": {}
    }
    
    # 获取图片数据
    try:
        image_data = get_image_data(image_path_or_url)
    except Exception as e:
        result["错误"] = str(e)
        return result
    
    # 检查元数据
    try:
        has_ps_metadata, metadata_info = check_metadata(image_data)
        result["元数据信息"] = metadata_info
        
        if has_ps_metadata:
            result["结论"] = "检测到Photoshop/Adobe相关元数据"
        else:
            result["结论"] = "未检测到Photoshop/Adobe相关元数据"
    
    except Exception as e:
        result["错误"] = f"元数据检查错误: {str(e)}"
    
    return result

def main():
    """
    主函数，处理命令行参数
    """
    # 默认图片URL
    default_url = "https://fuwu.qunar.com/orderview/upload/queryFile/mJYfb-bIdc7rXvUwcFn9KAlAqSsi-pm6fdg6TBD3rynV_J_3c1f7cZdmaWIgbA5U5pqG_t4y6BPOo9pM-_iEqkf-Xff3uzBq8MiCnevDO_d5m4rCJQI1UCHDXGSJF0-Yi3r9MfWxhQwCEmegPdawYSjfe3EJUQuvpvNxVEUEbzKg1Wb8cuOq3hSBu7Mciq9vGJJ71Yq2jmSEgVaO3xeyNyuCp07s2BIE-hYr9whivs24ENb8P9rbUN0DVQwLvfe7.jpeg"
    
    # 获取命令行参数
    if len(sys.argv) > 1:
        image_path_or_url = sys.argv[1]
    else:
        image_path_or_url = default_url
    
    # 检测图像元数据
    result = detect_metadata(image_path_or_url)
    
    # 打印结果
    print("\n===== 图像元数据检测结果 =====")
    print(f"图片: {result.get('图片位置', '未知')}")
    print(f"结论: {result.get('结论', '未知')}")
    print("\n详细元数据:")
    
    for key, value in result.get('元数据信息', {}).items():
        print(f"- {key}: {value}")
    
    if "错误" in result:
        print(f"\n错误: {result['错误']}")
    
    return result

if __name__ == "__main__":
    main() 