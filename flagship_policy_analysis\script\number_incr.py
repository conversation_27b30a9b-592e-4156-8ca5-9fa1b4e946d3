def increment_number(value, increment=1):
    """
    将输入的数字自增加指定值并返回
    
    参数:
        value: 数字或字符串类型的数字
        increment: 自增加的值，默认为1
        
    返回:
        自增加后的数字
        
    异常:
        ValueError: 当输入无法转换为数字时抛出
    """
    try:
        # 尝试将输入转换为浮点数
        num = float(value)
        # 自增加指定值
        result = num + increment
        # 如果输入是整数，返回整数结果
        if isinstance(value, int) or (isinstance(value, str) and value.isdigit()):
            return int(result)
        # 否则返回浮点数结果
        return result
    except (ValueError, TypeError):
        raise ValueError(f"无法将输入 '{value}' 转换为数字")

def main(param):
    """
    主函数入口，处理数字自增加请求
    
    参数:
        param: 包含输入数字的字典，键名为 'number'，可选键名为 'increment'
        
    返回:
        包含状态和结果的字典
        {
            "status": "success" 或 "error",
            "message": "状态描述",
            "data": 自增加后的数字（仅在成功时存在）
        }
    """
    try:
        # 检查输入参数
        if not isinstance(param, dict) or 'number' not in param:
            return {
                "status": "error",
                "message": "缺少必要的输入参数 'number'"
            }
        
        # 获取输入数字
        input_number = param['number']
        
        # 获取自增值，默认为1
        increment = param.get('increment', 1)
        
        # 确保自增值是数字
        try:
            increment = float(increment)
        except (ValueError, TypeError):
            return {
                "status": "error",
                "message": f"自增值 '{increment}' 不是有效的数字"
            }
        
        # 自增加
        result = increment_number(input_number, increment)
        
        # 返回成功结果
        return {
            "status": "success",
            "message": f"自增加 {increment} 成功",
            "data": result
        }
    
    except ValueError as e:
        # 处理无法转换为数字的错误
        return {
            "status": "error",
            "message": str(e)
        }
    except Exception as e:
        # 处理其他异常
        return {
            "status": "error",
            "message": f"处理异常: {str(e)}"
        }
