import json
import requests


def build_request_url(orderNo, domain):
    """
    构造请求的URL和参数
    """
    base_url = "http://fuwu.qunar.com/fuwuadmin/callcenter/order/serviceStandard"  # 移除末尾多余空格
    params = {"orderNo": orderNo, "domain": domain}
    return base_url, params


def query_data_from_api(url, params, headers):
    """
    发起HTTP GET请求并获取数据
    """
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": f"请求失败: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析失败: {str(e)}"}
    except Exception as e:
        return {"error": f"发生错误: {str(e)}"}


def main(param):
    """
    主入口函数
    """
    # 从参数中提取必要的字段
    orderNo = param.get("orderNo")
    domain = param.get("domain")

    # 使用单引号包裹Cookie字符串，避免双引号冲突
    cookie = 'QN271AC=register_pc; _RSG=6qKgdEnIzL0OJuN0bZcLm9; _RDG=286a3dfec6536c27230c697a8e59b84aff; _RGUID=057d4b13-a7a9-4d06-9c15-9a39405f84d8; ajs_anonymous_id=0c9f334a-b172-41eb-9a1b-a853acd1173f; UBT_VID=1694001468182.jqpofs; quinn=7f762589abeb3b5064f2c7ddc9deb343bae1e05f3a85b9f8ebdaa4fc336139c018d5d861c513041664171dc11a67c805; ctt_june=1683616182042##iK3wWSaNVuPwawPwa%3DPNEKgmasDNWs0IESDAWKjwVK2mVDWhEDXOEDj8aDkGiK3siK3saKgnVR3maKanWKgwahPwaUvt; QN601=39b558768b43147f9d0e5ebdd1480e50; fid=510174da-e450-420c-bfd5-35a1c91efcc9; QN1=00012a0011d8682a3360de2d; QN99=4820; QN48=0000f0002f10682a55d074b3; 11344=1722403391463##iK3waSD8WUPwawPwastmXsg%2BWSHRESP%2BasTDEKGhWRjmXstOaRTDERawEDDAiK3siK3saKgsas3nWKXwVRjnWUPwaUvt; 11536=1722403391463##iK3wWs2NWwPwawPwasaAWKfhaSGDEDj%3DXS3mXKvAXsg%3DWPWRWKX8EPkTasX%3DiK3siK3saKgsas3nWKXwVRjwauPwaUvt; QN621=1490067914133%2Ctestssong%3DDEFAULT%26fr%3Dflight_dom_search%261490067914133%3DDEFAULT%261490067914133%252Ctestssong%3DDEFAULT; QN668=51%2C57%2C53%2C53%2C52%2C51%2C55%2C54%2C53%2C55%2C53%2C58%2C54; _RF1=************; _bfa=1.1694001468182.jqpofs.1.1739850823164.1739882003841.22.1.10650153142; QN300=organic; _vi=QIkbk4wHQw4kRDwMuyLw3BDxjw4NztcnS0-Ae07ixfD0J4qzvHoanuG6hx-kQjCkV9k-vtQ8Xv-KzHaEa8VXOSAhE3UIEL1rki635WtF43Vsd0KFY_CXyv8QwZX7sQo4jNjAbIeEblzRnnkU1nWlDT-Q0IXyawRz71-EO6PVvmNk; QN271SL=512ae91d48ec1a758f61fbf7ee7c4c08; QN271RC=512ae91d48ec1a758f61fbf7ee7c4c08; _q=U.dyjamnb6468; csrfToken=yi2pgPbkacSjtzFSildRDJlvV6oUpbGv; _s=s_P5CMA7RXKEM5FIPM7EXFCO6HVQ; _t=29147275; _v=MS6HmrBllqFtdLoUq9FcOlUvfLzK-zbd_wuEpomIsjmxIQ_aWq8h6yN9igp4vMR-mknHrF9iIp9BJBh2CETxf5RAYPqYACBqHxOE7YWzgbvAoTthrTF8HvKw8G4bL1svYbS0c6yAIFOfmsK-fHBjqb2W6I_RNi17J4RKLLt-c4A8; QN43=""; _i=""; QN42=%E5%8E%BB%E5%93%AA%E5%84%BF%E7%94%A8%E6%88%B7; QN74=callcenter.qunar.com; QN29=bf99d0a85b784778b1d07da0d24f2c90; ctf_june=1683616182042##iK3wVRtsVhPwawPwa%3DDAasD8XKjnERjNXStmasawas2AERfhESHhW%3DWRXPPniK3siK3saKg%3DaS2AVKP8VK28WhPwaUvt; cs_june=fd28b6824058a65d6a181ce56387d533b59d9c5d115f9e2a3721b000dcc3beedd12b314f1b428b9009dc41ae2f056095fc8963c0e34840e7732bc03e50c7c198b17c80df7eee7c02a9c1a6a5b97c1179ce864755a666d095d891f55f15bb228f5a737ae180251ef5be23400b098dd8ca; _uf=hongyuan.cao; userId=hongyuan.cao; new_dubai_user="2|1:0|10:1743662319|14:new_dubai_user|212:eyJqb2JfY29kZSI6ICJSRCIsICJxX2RvbWFpbl9uYW1lIjogImhvbmd5dWFuLmNhbyIsICJxX2FjY291bnRfbmFtZSI6ICLmm7nlro/ov5wiLCAibWFuYWdlcl91c2VybmFtZSI6ICJsaWFueW9uZy5saSIsICJtYW5hZ2VyIjogIuadjui/nuWLhyIsICJpc19kYmEiOiBmYWxzZX0=|9089e9b158021a7b063639c1faa9fd864c538b4a45980870a45aefa2a7d717d0"; console_record="2|1:0|10:1743995025|14:console_record|92:W3siaXBfcG9ydCI6ICIxMC45NS4xMzYuMTEwXzMzMTgiLCAicGFzc3dvcmQiOiAicm50OTVVRngxbGVmRUhhayJ9XQ==|a1284bee12065340dc779070feafd660bac57f6b208ede903814ab204a555e87"; _mdp=F4743C05FA530475D61A0A8F76C6F26C; QN238=zh_cn; QN166=hongyuan.cao; currentId=65363039353165656137343034313438383539623438363261353461646333622C323032352D30342D30372031313A33303A33342C31302E37372E38362E392C686F6E677975616E2E63616F; JSESSIONID=67B87A76905BD7AB4236C7FFB8620FAE'

    # 检查参数是否完整
    if not orderNo or not domain:
        return {"error": "缺少必要的参数: orderNo or domain", "results": []}

    # 构造请求的URL和参数
    url, params = build_request_url(orderNo, domain)

    # 构造请求的headers，设置cookie
    headers = {
        "Cookie": cookie,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    }

    # 发起请求并获取数据
    response_data = query_data_from_api(url, params, headers)

    # 检查返回的数据是否包含错误信息
    if "error" in response_data:
        return {"error": response_data["error"], "results": []}

    # 返回标准化的结果结构
    return {"error": "", "results": response_data}


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


if __name__ == "__main__":
    result = main({"orderNo": "jkj250313165954035", "domain": "jkj.trade.qunar.com"})
    print(result)
    write_json_to_file(result, "quality_inspection/data/tgq_with_chd_result.json")
