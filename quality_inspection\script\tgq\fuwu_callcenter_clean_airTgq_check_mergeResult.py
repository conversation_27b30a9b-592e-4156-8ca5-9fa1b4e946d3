import traceback
import json
import re
from typing import Any, Dict, Union, Tuple, List, Optional


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def main(param: dict) -> dict:
    try:
        is_valid = param.get("is_valid", False)
        errorMsg = param.get("errorMsg", "")
        aiResponse = param.get("aiResponse", "")

        if not aiResponse:
            return {
                "is_valid": False,
                "errorMsg": "aiResponse不能为空",
                "aiResponse": {},
            }

        if isinstance(aiResponse, str):
            aiResponse = safe_json_parse(aiResponse)

        if not aiResponse:
            return {
                "is_valid": False,
                "errorMsg": "aiResponse转换为json失败",
                "aiResponse": {},
            }
        return {"is_valid": is_valid, "errorMsg": errorMsg, "aiResponse": aiResponse}
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg, "aiResponse": {}}


if __name__ == "__main__":
    param = {
        "is_valid": True,
        "errorMsg": "",
        "aiResponse": '{\n    "totalTgFeeDesc": "退改时间点: 2025-04-13 17:44:35, 退改类型：退票，乘机人类型：成人票， 舱位：V， 票面价：570， 费用规则说明：2025年04月13日 16:05后 ¥501/人 75%\\n退改时间点: 2025-04-13 17:45:32, 退改类型：退票，乘机人类型：成人票， 舱位：V， 票面价：570， 费用规则说明：2025年04月13日 16:05后 ¥501/人 75%"\n}',
    }
    reuslt = main(param)
    print(reuslt)
