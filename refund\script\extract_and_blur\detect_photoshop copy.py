#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image
import piexif
from pathlib import Path
from io import BytesIO
import requests

def check_metadata(image_data):
    """
    检查图像元数据中的PS痕迹
    
    参数:
        image_data: 图片数据（字节流）
    
    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 读取图像元数据
        img = Image.open(BytesIO(image_data))
        metadata_info = {}
        has_ps_metadata = False
        
        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])
            
            # 检查软件信息
            if piexif.ImageIFD.Software in exif_dict.get("0th", {}):
                software = exif_dict["0th"][piexif.ImageIFD.Software].decode('utf-8', errors='ignore')
                metadata_info["软件"] = software
                if "adobe" in software.lower():
                    has_ps_metadata = True
            
            # 检查修改历史
            if piexif.ExifIFD.MakerNote in exif_dict.get("Exif", {}):
                maker_note = exif_dict["Exif"][piexif.ExifIFD.MakerNote].decode('utf-8', errors='ignore')
                metadata_info["制造商备注"] = maker_note
                if "photoshop" in maker_note.lower() or "adobe" in maker_note.lower():
                    has_ps_metadata = True
        img_info = str(img.info).lower()
        print(img_info)
        # 检查XMP数据
        if "adobe" in img_info:
            has_ps_metadata = True
            metadata_info["其他元数据"] = "包含Adobe/Photoshop相关信息"
        
        return has_ps_metadata, metadata_info
    
    except Exception as e:
        print(f"检查元数据时出错: {e}")
        return False, {"错误": str(e)}

def get_image_data(image_url):
    """
    从URL获取图片数据
    
    参数:
        image_url: 图片URL地址
    
    返回:
        image_data: 图片数据（字节流）
    """
    try:
        response = requests.get(image_url, stream=True)
        response.raise_for_status()
        return response.content
    except Exception as e:
        raise Exception(f"获取图片数据失败: {e}")

def detect_photoshop(image_url):
    """
    仅通过元数据检测图像是否被PS过
    
    参数:
        image_url: 图片URL地址
    
    返回:
        result: 检测结果字典
    """
    result = {
        "图片地址": image_url,
        "PS可能性": 0.0,
        "分析结果": {}
    }
    
    # 获取图片数据
    try:
        image_data = get_image_data(image_url)
    except Exception as e:
        result["错误"] = str(e)
        return result
    
    # 检查元数据
    try:
        has_ps_metadata, metadata_info = check_metadata(image_data)
        result["分析结果"]["元数据"] = metadata_info
        
        if has_ps_metadata:
            result["分析结果"]["元数据解释"] = "检测到Photoshop/Adobe相关元数据"
            result["PS可能性"] = 1.0  # 元数据是确定性证据
            result["结论"] = "图像被PS过（检测到Adobe/Photoshop元数据）"
        else:
            result["分析结果"]["元数据解释"] = "未检测到Photoshop/Adobe相关元数据"
            result["PS可能性"] = 0.0
            result["结论"] = "未检测到PS痕迹（无Adobe/Photoshop元数据）"
    
    except Exception as e:
        result["分析结果"]["元数据检查错误"] = str(e)
        result["结论"] = "检测过程出错"
    
    return result

def main(params: dict) -> dict:
    image_url = params.get("image_url", "https://fuwu.qunar.com/qbcp/file/download?fileName=4.jpg&uniqueName=attach20250211141627753777")
    
    # 检测图像是否被PS过（仅基于元数据）
    result = detect_photoshop(image_url)
    
    # 打印结果
    print("\n===== 图像PS检测结果 =====")
    print(f"图片: {result.get('图片地址', '未知')}")
    print(f"结论: {result.get('结论', '未知')}")
    print("\n详细分析:")
    
    for key, value in result.get('分析结果', {}).items():
        if isinstance(value, dict):
            print(f"\n{key}:")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    
    return result

if __name__ == "__main__":
    main({})
