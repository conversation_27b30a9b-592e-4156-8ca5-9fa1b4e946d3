class FeishuApi {
  constructor() {
    this.token = null;
    this.baseUrl = 'https://open.feishu.cn/open-apis';
  }

  /**
   * 设置访问令牌
   * @param {string} token - 飞书访问令牌
   */
  setToken(token) {
    this.token = token;
  }

  /**
   * 获取访问令牌
   * @returns {string|null} 访问令牌
   */
  getToken() {
    return this.token;
  }

  /**
   * 检查是否已认证
   * @returns {boolean} 是否已认证
   */
  isAuthenticated() {
    return !!this.token;
  }

  /**
   * 获取应用访问令牌
   * @param {string} appId - 应用ID
   * @param {string} appSecret - 应用密钥
   * @returns {Promise<Object>} 认证结果
   */
  async getAppAccessToken(appId, appSecret) {
    try {
      const response = await fetch(`${this.baseUrl}/auth/v3/app_access_token/internal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          app_id: appId,
          app_secret: appSecret
        })
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '获取访问令牌失败');
      }

      this.token = data.app_access_token;
      return {
        success: true,
        token: this.token,
        expireIn: data.expire
      };
    } catch (error) {
      console.error('获取应用访问令牌失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 使用授权码获取用户访问令牌
   * @param {string} appId - 应用ID
   * @param {string} appSecret - 应用密钥
   * @param {string} code - 授权码
   * @returns {Promise<Object>} 认证结果
   */
  async getUserAccessToken(appId, appSecret, code) {
    try {
      // 先获取应用访问令牌
      const appTokenResult = await this.getAppAccessToken(appId, appSecret);
      if (!appTokenResult.success) {
        throw new Error(appTokenResult.error);
      }

      // 使用应用访问令牌获取用户访问令牌
      const response = await fetch(`${this.baseUrl}/authen/v1/access_token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${appTokenResult.token}`
        },
        body: JSON.stringify({
          grant_type: 'authorization_code',
          code
        })
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '获取用户访问令牌失败');
      }

      this.token = data.data.access_token;
      return {
        success: true,
        token: this.token,
        refreshToken: data.data.refresh_token,
        expireIn: data.data.expires_in
      };
    } catch (error) {
      console.error('获取用户访问令牌失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取表格列表
   * @returns {Promise<Object>} 表格列表
   */
  async getSpreadsheets() {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '获取表格列表失败');
      }

      return {
        success: true,
        spreadsheets: data.data.items
      };
    } catch (error) {
      console.error('获取表格列表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建表格
   * @param {string} title - 表格标题
   * @param {string} folderToken - 文件夹Token（可选）
   * @returns {Promise<Object>} 创建结果
   */
  async createSpreadsheet(title, folderToken = null) {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const body = {
        title
      };

      if (folderToken) {
        body.folder_token = folderToken;
      }

      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '创建表格失败');
      }

      return {
        success: true,
        spreadsheet: data.data
      };
    } catch (error) {
      console.error('创建表格失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建工作表
   * @param {string} spreadsheetToken - 表格Token
   * @param {string} title - 工作表标题
   * @returns {Promise<Object>} 创建结果
   */
  async createSheet(spreadsheetToken, title) {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets/${spreadsheetToken}/sheets`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title
        })
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '创建工作表失败');
      }

      return {
        success: true,
        sheet: data.data
      };
    } catch (error) {
      console.error('创建工作表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入表格数据
   * @param {string} spreadsheetToken - 表格Token
   * @param {string} range - 范围，如 'Sheet1!A1:D5'
   * @param {Array} values - 二维数组数据
   * @returns {Promise<Object>} 写入结果
   */
  async writeValues(spreadsheetToken, range, values) {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets/${spreadsheetToken}/values`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          valueRange: {
            range,
            values
          }
        })
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '写入数据失败');
      }

      return {
        success: true,
        updatedRows: data.data.updated_rows,
        updatedColumns: data.data.updated_columns
      };
    } catch (error) {
      console.error('写入表格数据失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取表格数据
   * @param {string} spreadsheetToken - 表格Token
   * @param {string} range - 范围，如 'Sheet1!A1:D5'
   * @returns {Promise<Object>} 读取结果
   */
  async readValues(spreadsheetToken, range) {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets/${spreadsheetToken}/values/${encodeURIComponent(range)}`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '读取数据失败');
      }

      return {
        success: true,
        values: data.data.valueRange.values
      };
    } catch (error) {
      console.error('读取表格数据失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取工作表列表
   * @param {string} spreadsheetToken - 表格Token
   * @returns {Promise<Object>} 工作表列表
   */
  async getSheets(spreadsheetToken) {
    if (!this.token) {
      return {
        success: false,
        error: '未认证'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/sheets/v2/spreadsheets/${spreadsheetToken}/sheets`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || '获取工作表列表失败');
      }

      return {
        success: true,
        sheets: data.data.sheets
      };
    } catch (error) {
      console.error('获取工作表列表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 导出API类
window.FeishuApi = FeishuApi;
