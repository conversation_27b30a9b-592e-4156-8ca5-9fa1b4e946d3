/**
 * DOC暴力解析器
 * 专门处理严重乱码的.doc文件
 */
class DocBruteForceParser {
  constructor() {
    // 已知的中文词汇模式
    this.knownWords = [
      '正文', '页眉', '页脚', '默认段落字体', '标题', '目录', '摘要',
      'WPS Office', '专业版', '微软', 'Microsoft', 'Word', '文档',
      '段落', '字体', '格式', '样式', '表格', '图片', '页面',
      '作者', '标题', '日期', '版本', '公司', '部门'
    ];
    
    // 常见的文档结构标识
    this.docStructures = [
      'Root Entry', 'WordDocument', 'Data', 'Table', 
      'SummaryInformation', 'DocumentSummaryInformation',
      'CompObj', '1Table', '0Table'
    ];
  }

  // 暴力解析方法
  bruteForceExtract(rawText) {
    console.log('开始暴力解析，原始长度:', rawText.length);
    
    const results = [];
    
    // 方法1: 直接提取已知词汇
    results.push({
      method: '词汇提取',
      text: this.extractKnownWords(rawText),
      score: 0
    });
    
    // 方法2: 字符重组
    results.push({
      method: '字符重组',
      text: this.reconstructCharacters(rawText),
      score: 0
    });
    
    // 方法3: 模式匹配
    results.push({
      method: '模式匹配',
      text: this.patternMatching(rawText),
      score: 0
    });
    
    // 方法4: 二进制重解释
    results.push({
      method: '二进制重解释',
      text: this.binaryReinterpret(rawText),
      score: 0
    });
    
    // 方法5: 上下文推断
    results.push({
      method: '上下文推断',
      text: this.contextInference(rawText),
      score: 0
    });
    
    // 计算每种方法的得分
    results.forEach(result => {
      result.score = this.scoreExtractedText(result.text);
    });
    
    // 选择最佳结果
    const bestResult = results.reduce((best, current) => 
      current.score > best.score ? current : best
    );
    
    console.log(`最佳方法: ${bestResult.method}, 得分: ${bestResult.score}`);
    
    return {
      bestResult: bestResult,
      allResults: results.sort((a, b) => b.score - a.score)
    };
  }

  // 提取已知词汇
  extractKnownWords(text) {
    const foundWords = [];
    const foundStructures = [];
    
    // 查找已知词汇
    this.knownWords.forEach(word => {
      if (text.includes(word)) {
        foundWords.push(word);
        
        // 尝试提取词汇周围的上下文
        const index = text.indexOf(word);
        const start = Math.max(0, index - 10);
        const end = Math.min(text.length, index + word.length + 10);
        const context = text.substring(start, end);
        
        // 清理上下文
        const cleanContext = this.cleanContext(context);
        if (cleanContext.length > word.length) {
          foundWords.push(cleanContext);
        }
      }
    });
    
    // 查找文档结构
    this.docStructures.forEach(structure => {
      if (text.includes(structure)) {
        foundStructures.push(structure);
      }
    });
    
    let result = '';
    if (foundStructures.length > 0) {
      result += '文档结构: ' + foundStructures.join(', ') + '\n\n';
    }
    if (foundWords.length > 0) {
      result += '提取内容: ' + foundWords.join(' | ');
    }
    
    return result;
  }

  // 字符重组
  reconstructCharacters(text) {
    const reconstructed = [];
    let currentWord = '';
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const charCode = char.charCodeAt(0);
      
      // 如果是中文字符，直接保留
      if (charCode >= 0x4e00 && charCode <= 0x9fff) {
        currentWord += char;
      }
      // 如果是ASCII字符
      else if (charCode >= 0x20 && charCode <= 0x7E) {
        currentWord += char;
      }
      // 遇到分隔符或特殊字符
      else {
        if (currentWord.length > 1) {
          // 检查是否为有意义的词
          if (this.isMeaningfulWord(currentWord)) {
            reconstructed.push(currentWord);
          }
        }
        currentWord = '';
      }
    }
    
    // 处理最后一个词
    if (currentWord.length > 1 && this.isMeaningfulWord(currentWord)) {
      reconstructed.push(currentWord);
    }
    
    return reconstructed.join(' ');
  }

  // 模式匹配
  patternMatching(text) {
    const patterns = [];
    
    // 查找中文句子模式
    const chinesePattern = /[\u4e00-\u9fff]{2,}/g;
    const chineseMatches = text.match(chinesePattern) || [];
    
    // 查找英文单词模式
    const englishPattern = /[a-zA-Z]{3,}/g;
    const englishMatches = text.match(englishPattern) || [];
    
    // 查找数字模式
    const numberPattern = /\d{4,}/g;
    const numberMatches = text.match(numberPattern) || [];
    
    // 查找版本信息模式
    const versionPattern = /\d+\.\d+\.\d+/g;
    const versionMatches = text.match(versionPattern) || [];
    
    let result = '';
    
    if (chineseMatches.length > 0) {
      result += '中文内容: ' + chineseMatches.join(' ') + '\n';
    }
    
    if (englishMatches.length > 0) {
      const meaningfulEnglish = englishMatches.filter(word => 
        word.length > 2 && !this.isGibberish(word)
      );
      if (meaningfulEnglish.length > 0) {
        result += '英文内容: ' + meaningfulEnglish.join(' ') + '\n';
      }
    }
    
    if (versionMatches.length > 0) {
      result += '版本信息: ' + versionMatches.join(' ') + '\n';
    }
    
    if (numberMatches.length > 0) {
      result += '数字信息: ' + numberMatches.join(' ') + '\n';
    }
    
    return result;
  }

  // 二进制重解释
  binaryReinterpret(text) {
    const results = [];
    
    // 尝试不同的字节偏移
    for (let offset = 0; offset < 4; offset++) {
      let reinterpreted = '';
      
      for (let i = offset; i < text.length - 1; i += 2) {
        try {
          // 重新组合字节
          const byte1 = text.charCodeAt(i) & 0xFF;
          const byte2 = text.charCodeAt(i + 1) & 0xFF;
          
          // 尝试UTF-16LE
          const char16 = byte1 | (byte2 << 8);
          if ((char16 >= 0x4e00 && char16 <= 0x9fff) || 
              (char16 >= 0x20 && char16 <= 0x7E)) {
            reinterpreted += String.fromCharCode(char16);
          }
          
          // 尝试UTF-16BE
          const char16be = (byte1 << 8) | byte2;
          if ((char16be >= 0x4e00 && char16be <= 0x9fff) || 
              (char16be >= 0x20 && char16be <= 0x7E)) {
            reinterpreted += String.fromCharCode(char16be);
          }
        } catch (e) {
          // 忽略错误
        }
      }
      
      if (reinterpreted.length > 10) {
        results.push(reinterpreted);
      }
    }
    
    // 返回最长的结果
    return results.reduce((longest, current) => 
      current.length > longest.length ? current : longest, ''
    );
  }

  // 上下文推断
  contextInference(text) {
    const inferences = [];
    
    // 查找作者信息
    const authorPattern = /[\u4e00-\u9fff]{2,4}(?=@|作者|Author)/g;
    const authorMatches = text.match(authorPattern) || [];
    
    // 查找软件信息
    if (text.includes('WPS') || text.includes('Office')) {
      inferences.push('软件: WPS Office');
    }
    if (text.includes('专业版')) {
      inferences.push('版本: 专业版');
    }
    
    // 查找文档类型信息
    if (text.includes('WordDocument')) {
      inferences.push('文档类型: Word文档');
    }
    
    // 查找编码信息
    if (text.includes('2052')) {
      inferences.push('语言: 中文简体 (2052)');
    }
    
    // 查找构建版本
    const buildPattern = /\d+\.\d+\.\d+\.\d+/g;
    const buildMatches = text.match(buildPattern) || [];
    if (buildMatches.length > 0) {
      inferences.push('构建版本: ' + buildMatches[0]);
    }
    
    if (authorMatches.length > 0) {
      inferences.push('可能的作者: ' + authorMatches.join(', '));
    }
    
    return inferences.join('\n');
  }

  // 清理上下文
  cleanContext(context) {
    return context
      .replace(/[^\u4e00-\u9fff\u0020-\u007E]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // 判断是否为有意义的词
  isMeaningfulWord(word) {
    if (word.length < 2) return false;
    
    // 检查是否包含中文
    if (/[\u4e00-\u9fff]/.test(word)) return true;
    
    // 检查是否为常见英文单词
    const commonWords = [
      'Office', 'Word', 'Document', 'Text', 'Font', 'Style',
      'Page', 'Header', 'Footer', 'Table', 'Image', 'WPS',
      'Microsoft', 'Times', 'Arial', 'Symbol', 'New', 'Roman'
    ];
    
    return commonWords.some(common => 
      word.toLowerCase().includes(common.toLowerCase())
    );
  }

  // 判断是否为乱码
  isGibberish(word) {
    // 检查是否包含过多的特殊字符
    const specialChars = word.match(/[^a-zA-Z0-9]/g) || [];
    if (specialChars.length > word.length * 0.5) return true;
    
    // 检查是否为重复字符
    if (/(.)\1{3,}/.test(word)) return true;
    
    // 检查是否为随机字符组合
    const vowels = word.match(/[aeiouAEIOU]/g) || [];
    if (word.length > 5 && vowels.length === 0) return true;
    
    return false;
  }

  // 评分提取的文本
  scoreExtractedText(text) {
    if (!text || text.length === 0) return 0;
    
    let score = 0;
    
    // 基础长度分
    score += Math.min(text.length / 5, 20);
    
    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fff]/g) || [];
    score += chineseChars.length * 5;
    
    // 已知词汇加分
    this.knownWords.forEach(word => {
      if (text.includes(word)) {
        score += 15;
      }
    });
    
    // 文档结构加分
    this.docStructures.forEach(structure => {
      if (text.includes(structure)) {
        score += 10;
      }
    });
    
    // 有意义的英文单词加分
    const englishWords = text.match(/\b[a-zA-Z]{3,}\b/g) || [];
    const meaningfulEnglish = englishWords.filter(word => 
      this.isMeaningfulWord(word) && !this.isGibberish(word)
    );
    score += meaningfulEnglish.length * 3;
    
    // 版本信息加分
    if (/\d+\.\d+\.\d+/.test(text)) {
      score += 10;
    }
    
    return score;
  }

  // 生成最终报告
  generateReport(rawText) {
    const extractResult = this.bruteForceExtract(rawText);
    
    let report = `DOC暴力解析报告\n`;
    report += `==================\n\n`;
    report += `原始文本长度: ${rawText.length} 字符\n`;
    report += `最佳解析方法: ${extractResult.bestResult.method}\n`;
    report += `最佳结果得分: ${extractResult.bestResult.score}\n\n`;
    
    report += `最佳解析结果:\n`;
    report += `${extractResult.bestResult.text}\n\n`;
    
    report += `所有方法结果对比:\n`;
    report += `==================\n`;
    extractResult.allResults.forEach((result, index) => {
      report += `${index + 1}. ${result.method} (得分: ${result.score})\n`;
      report += `${result.text.substring(0, 100)}${result.text.length > 100 ? '...' : ''}\n\n`;
    });
    
    return {
      report: report,
      bestText: extractResult.bestResult.text,
      allResults: extractResult.allResults
    };
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocBruteForceParser;
} else if (typeof window !== 'undefined') {
  window.DocBruteForceParser = DocBruteForceParser;
}
