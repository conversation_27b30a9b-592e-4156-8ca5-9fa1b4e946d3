import requests
import json
from typing import Dict, List, Any
import urllib.parse

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def query_user_labels(username: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    查询用户标签信息
    Args:
        username: 用户名
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        Dict: 包含用户标签信息的字典
    """
    url = f"http://labels.corp.qunar.com/test/getVirtualUserMarkInfo?username={username}&uid=&needCompulsoryCalculate=false&source=adr&invokerSource=f_twell_domestic&dep=PEK&arr=SHA&dptDate=2022-11-01%2001%3A00%3A00&arrDate=2021-12-02%2002%3A47%3A40&version=1145&passengerInfoList=%5B%5D"
    
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    }
    

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        # 解析响应JSON
        response_data = response.json()
        print("----------------response_data", response_data)
        # 获取body内容
        body = response_data.get("body", {})
        
        if not body:
            return {"error": "Response body is empty", "data": {}}
        
        return body

    except requests.exceptions.RequestException as e:
        return {
            "error": f"查询用户标签失败: {str(e)}",
            "status_code": getattr(e.response, "status_code", None) if hasattr(e, "response") else None,
            "data": {}
        }
    except json.JSONDecodeError as e:
        return {"error": f"解析响应JSON失败: {str(e)}", "data": {}}
    except Exception as e:
        return {"error": f"查询用户标签失败: {str(e)}", "data": {}}

def check_huangniu_labels(userMsg: Dict[str, Any]) -> str:
    """
    检查用户标签中是否包含黄牛相关标签
    Args:
        userMsg: 用户标签信息
    Returns:
        str: 是否为黄牛 "是"/"否"
    """
    huangniu_labels = ["SBHN", "ZFHN", "HNMX"]
    
    for label in huangniu_labels:
        if label in userMsg:
            return "是"
    
    return "否"

def query_user_by_phone(phone: str) -> Dict[str, Any]:
    """
    通过手机号查询用户信息
    Args:
        phone: 手机号
    Returns:
        Dict: 包含用户信息的字典
    """
    url = f"http://superman.corp.qunar.com/auto/userInfo?_t=1745478836262&phone={phone}"
    
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        response_data = response.json()
        
        if not response_data.get("phoneUserInfo"):
            return {"error": "未查询到用户信息", "data": {}}
            
        return response_data["phoneUserInfo"]

    except requests.exceptions.RequestException as e:
        return {
            "error": f"查询用户信息失败: {str(e)}",
            "status_code": getattr(e.response, "status_code", None) if hasattr(e, "response") else None,
            "data": {}
        }
    except json.JSONDecodeError as e:
        return {"error": f"解析响应JSON失败: {str(e)}", "data": {}}
    except Exception as e:
        return {"error": f"查询用户信息失败: {str(e)}", "data": {}}

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含username或phone的参数字典
    Returns:
        Dict: 处理结果
    """
    username = param.get("username", "")
    phone = param.get("phone", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    
    # 设置默认返回值
    results = {
        "huangniu": "",
        "username": ""
    }

    if not username and not phone:
        return {"error": "用户名和手机号不能同时为空", "results": results}  

    try:
        # 如果username为空，通过手机号查询用户信息
        if not username and phone:
            user_info = query_user_by_phone(phone)
            if "error" in user_info and user_info["error"]:
                return {"error": user_info["error"], "results": results}
            username = user_info.get("username", "")
            if not username:
                return {"error": "未查询到用户名", "results": results}

        # 查询用户标签信息
        user_labels_response = query_user_labels(username, appCode, appToken)
        
        if "error" in user_labels_response and user_labels_response["error"]:
            return {"error": user_labels_response["error"], "results": results}
        
        # 获取userMsg
        userMsg = user_labels_response.get("userMsg", {})
        if not userMsg:
            return {"error": "未查询到用户标签信息", "results": results}
        
        print("----------------userMsg", json.dumps(userMsg, indent=4,ensure_ascii=False))
        # 检查是否包含黄牛标签
        results["huangniu"] = check_huangniu_labels(userMsg)
        results["username"] = username
        
        return {
            "error": "",
            "results": results,
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "results": results}

def test():
    param = {
        "username": "",
        "phone": "18685416360",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 