import json
import requests
from datetime import datetime
from typing import Optional

from datetime import datetime
from typing import Union, List, Set, Any


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict, fields: List, date_format: str = None, case_sensitive: bool = False
) -> Set[Union[str, datetime.date]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt.date() if isinstance(dt, datetime) else dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def filter_flight_data(request_params: dict, target_data: List[dict]):
    """
    增强版多条件过滤（支持全量多值参数）
    """
    matched = []
    notMatched = []

    # 预处理所有多值参数（含智能类型转换）
    search_dates = parse_multi_values(
        request_params, ["searchDateTime", "searchDateList"], "%Y-%m-%d"
    )
    depart_dates = parse_multi_values(
        request_params, ["departureDate", "departureDateList"], "%Y-%m-%d"
    )
    carriers = parse_multi_values(request_params, ["carrier", "carrierList"])
    flight_numbers = parse_multi_values(
        request_params, ["flightNumber"]
    )  # 注意原始参数名拼写错误

    # 预处理城市代码（单值）
    dep_city = request_params.get("departureCity", "")
    arr_city = request_params.get("arrivalCity", "")

    for item in target_data:
        # 多条件检查
        checks = [
            _check_dates(item, search_dates, depart_dates),
            _check_cities(item, dep_city, arr_city),
            _check_carrier(item, carriers),
            _check_flight_number(item, flight_numbers),
        ]

        # 关键修复点：提取每个check的布尔状态
        is_all_match = all([check[0] for check in checks])

        # 收集所有错误信息
        error_msgs = [check[1] for check in checks if not check[0]]

        # 标注匹配状态及错误详情
        item.update(
            {
                "matchQeTag": "匹配" if is_all_match else "不匹配",
                "notMatchQeMsg": "; ".join(error_msgs) if error_msgs else "匹配",
            }
        )

        if is_all_match:
            matched.append(item)
        else:
            notMatched.append(item)

    return matched, notMatched


def _check_dates(item: dict, search_dates: set, depart_dates: set) -> tuple:
    """日期检查（返回匹配状态和带值的错误信息）"""
    errors = []

    # 搜索日期检查
    if search_dates:
        item_search_date = safe_parse_date(item.get("searchDateTime"), "%Y-%m-%d")
        req_dates_str = ",".join(sorted([d.strftime("%Y-%m-%d") for d in search_dates]))
        item_date_str = (
            item_search_date.strftime("%Y-%m-%d") if item_search_date else "无"
        )
        if not item_search_date or item_search_date not in search_dates:
            errors.append(
                f"搜索日期不匹配（请求值：{req_dates_str}，数据值：{item_date_str}）"
            )

    # 起飞日期检查
    if depart_dates:
        item_depart_date = safe_parse_date(item.get("departureDate"), "%Y-%m-%d")
        req_departs_str = ",".join(
            sorted([d.strftime("%Y-%m-%d") for d in depart_dates])
        )
        item_depart_str = (
            item_depart_date.strftime("%Y-%m-%d") if item_depart_date else "无"
        )
        if not item_depart_date or item_depart_date not in depart_dates:
            errors.append(
                f"起飞日期不匹配（请求值：{req_departs_str}，数据值：{item_depart_str}）"
            )

    return (len(errors) == 0, "; ".join(errors))


def _check_cities(item: dict, dep_city: str, arr_city: str) -> tuple:
    """城市代码检查（带值对比）"""
    errors = []
    item_dep = item.get("departureCity", "")
    item_arr = item.get("arrivalCity", "")

    if (
        not is_deep_empty(item_dep)
        and not is_deep_empty(dep_city)
        and item_dep != dep_city
    ):
        errors.append(f"出发城市不匹配（请求值：{dep_city}，数据值：{item_dep}）")
    if (
        not is_deep_empty(arr_city)
        and not is_deep_empty(arr_city)
        and item_arr != arr_city
    ):
        errors.append(f"到达城市不匹配（请求值：{arr_city}，数据值：{item_arr}）")

    return (len(errors) == 0, "; ".join(errors))


def _check_carrier(item: dict, carriers: set) -> tuple:
    """航司检查（带值对比）"""
    if carriers:
        item_flight_number = item.get("flightNumber")
        if (
            not is_deep_empty(carriers)
            and item_flight_number
            and len(item_flight_number) >= 2
        ):
            item_carrier = item_flight_number[:2]
            if item_carrier not in carriers:
                return (
                    False,
                    f"航司不匹配（请求值：{str(carriers)}，数据值：{item_carrier}）",
                )
    return (True, "")


def _check_flight_number(item: dict, flight_numbers: set) -> tuple:
    """航班号检查（带值对比）"""
    if flight_numbers:
        item_flight = item.get("flightNumber", "")
        req_flights_str = ",".join(sorted(flight_numbers))

        if (
            not is_deep_empty(flight_numbers)
            and item_flight
            and len(item_flight) > 2
            and item_flight not in flight_numbers
        ):
            return (
                False,
                f"航班号不匹配（请求值：{req_flights_str}，数据值：{item_flight}）",
            )
    return (True, "")


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def search_flight_case(username: str, searchDate: str, dptDate: str) -> dict:
    url = "http://paoding.corp.qunar.com/open/case/mainSearch"

    params = {
        "flightType": "SINGLE",
        "keywordType": "USERNAME",
        "keyword": username,
        "channelKey": "App",
        "searchDate": searchDate or "",
        "dptDate": dptDate or "",
        "qTracePrefixes": "ops_slugger,f_athena_gateway",
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    response = requests.get(url, params=params, headers=headers)

    return response.json()


def main(param: dict) -> dict:
    try:
        # 调用search_flight_case方法，并传入参数
        questionParam = param.get("questionParam")
        search_dates = parse_multi_values(
            questionParam, ["searchDateTime", "searchDateList"], "%Y-%m-%d"
        )
        depart_dates = parse_multi_values(
            questionParam, ["departureDate", "departureDateList"], "%Y-%m-%d"
        )

        searchDate = None
        dptDate = None
        if len(search_dates) == 1:
            searchDate = next(iter(search_dates))
        if len(depart_dates) == 1:
            dptDate = next(iter(depart_dates))
        result = search_flight_case(
            username=param["username"], searchDate=searchDate, dptDate=dptDate
        )
        if result is None:
            result = {"ret": False, "errmsg": f"    ", "data": [], "notMatchData": []}

        if result.get("data") is None:
            result = {
                "ret": False,
                "errmsg": f"调用庖丁接口返回数据为空，请确认用户名|搜索日期|航班出发日期是否描述正确！",
                "data": [],
                "notMatchData": [],
            }
        if (
            result.get("data")
            and isinstance(result.get("data"), list)
            and len(result.get("data")) > 0
        ):
            result["errmsg"] = "获取数据成功"
            matchData, notMatchData = filter_flight_data(
                questionParam, result.get("data")
            )
            result["data"] = matchData
            result["notMatchData"] = notMatchData
            if len(matchData) == 0:
                result["errmsg"] = "获取数据成功, 但未匹配到有效数据"
        return result
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        # print(f"参数缺失: {e}")
        result = {
            "ret": False,
            "errmsg": f"调用庖丁获取用户搜索事件发生错误: {e}",
            "data": [],
            "notMatchData": [],
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "ret": False,
            "errmsg": f"调用庖丁获取用户搜索事件发生错误: {e}",
            "data": [],
            "notMatchData": [],
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# 使用示例
if __name__ == "__main__":

    input = {
        "username": "iqieell1122",
        "question": "【多次搜索变价】用户（本机）于2025-03-07 中午12点左右 搜索航班，发现同一航班起飞时间为2025-03-22 07:00左右出发三亚-西安 航海旗下首航JD5175直达航班的L页价格从700多间隔几分钟上涨至900多。用户有观察其他平台，也有价格波动，未购票。",
        "questionParam": {
            "searchDateTime": "2025-03-07 12:00:00",
            "searchDateList": "2025-03-07 12:00:00",
            "departureCityName": "三亚",
            "departureCity": "SYX",
            "arrivalCityName": "西安",
            "arrivalCity": "XIY",
            "departureDate": "2025-03-22",
            "departureDateList": "2025-03-22",
            "flightNumber": "JD5175",
            "carrier": "JD",
            "carrierList": "JD",
        },
    }

    """paodingDatas = [
    {
        "id": "IbbZX5UBOtPFiNQxiGzq",
        "flightType": "SINGLE",
        "source": "LIST",
        "searchDateTime": "2025-03-04 14:29:02",
        "departureCity": "CTU",
        "arrivalCity": "SZX",
        "departureDate": "2025-03-04",
        "flightNumber": "MU5486",
        "channel": {
            "key": "App",
            "sourceSystem": [
                "wbd_oneway_bd_qunar_mobile.app.android"
            ],
            "id": 1
        },
        "qtraceId": "ops_slugger_250304.142902.10.95.140.67.618709.7136151101_1",
        "spanId": "1.3.1.24.1",
        "username": "qunar_lbs_902453984",
        "userLabel": "DXPD_CZ1+DXPD_CZ1",
        "userRequest": True,
        "listQTraceId": "",
        "matchQeTag": "不匹配",
        "notMatchQeMsg": "出发城市不匹配（请求值：SZX，数据值：CTU）; 到达城市不匹配（请求值：HET，数据值：SZX）"
    },
    {
        "id": "VyTXX5UBykVeEeKk7MZJ",
        "flightType": "SINGLE",
        "source": "OTA",
        "searchDateTime": "2025-03-04 14:28:58",
        "departureCity": "CTU",
        "arrivalCity": "SZX",
        "departureDate": "2025-03-04",
        "flightNumber": "MU5487",
        "channel": {
            "key": "App",
            "sourceSystem": [
                "wbd_oneway_bd_qunar_mobile.app.harmony"
            ],
            "id": 1
        },
        "qtraceId": "ops_slugger_250304.142858.10.95.140.67.618709.4560106972_1",
        "spanId": "1.1.1.22.1.6.1.14.1",
        "username": "qunar_lbs_902453984",
        "userLabel": "DXPD_CZ1+DXPD_CZ1;",
        "userRequest": False,
        "listQTraceId": "",
        "matchQeTag": "不匹配",
        "notMatchQeMsg": "出发城市不匹配（请求值：SZX，数据值：CTU）; 到达城市不匹配（请求值：HET，数据值：SZX）"
    }
]"""
    # matched, noMatched = filter_flight_data(input.get('questionParam'), paodingDatas)
    finalData = main(input)
    # print('执行结果匹配数据：' + json.dumps(matched, indent=2, ensure_ascii=False))
    # print('执行结果不匹配数据：' + json.dumps(noMatched, indent=2, ensure_ascii=False))
    write_json_to_file(
        obj=finalData.get("data"),
        file_path="D:/work/otherdoc/paodingSearchEventData.json",
    )
