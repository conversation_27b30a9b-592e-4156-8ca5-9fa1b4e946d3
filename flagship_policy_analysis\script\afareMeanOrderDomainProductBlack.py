# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import requests # type: ignore
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> ConfigError:
    """统一处理请求异常"""
    if isinstance(e, requests.exceptions.HTTPError):
        return ConfigError(1, f"http错误: {str(e)}")
    elif isinstance(e, requests.exceptions.ConnectionError):
        return ConfigError(1, "连接错误")
    elif isinstance(e, requests.exceptions.Timeout):
        return ConfigError(1, "超时错误")
    elif isinstance(e, requests.exceptions.RequestException):
        return ConfigError(1, "请求错误")
    else:
        return ConfigError(1, "未知错误")

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    # 检查必要参数
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }

    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }

    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取必要字段并检查
    domain = mapping_data.get("domain", "")
    if not domain:
        return {
            "status": "error",
            "message": "domain 不能为空",
            "data": None
        }
    
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    # 会员限制为空，则跳过配置
    membership_limit = analysis_data.get("membership_limit", "")
    if not membership_limit or membership_limit not in ["新会员", "老会员", "新老会员"]:
        return {
            "status": "ignore",
            "message": "未满足配置场景",
            "data": None
        }

    # 获取qconfig的配置，并处理domain和productMark
    config_result = process_qconfig_content(param, domain, product_mark)
    if config_result["status"] != "success":
        return {
            "status": config_result["status"],
            "message": config_result["message"],
            "data": config_result.get("data")
        }

    return {
        "status": "success",
        "message": "操作成功",
        "data": config_result.get("data")
    }

def process_qconfig_content(param: Dict[str, Any], domain: str, product_mark: str) -> Dict[str, Any]:
    """
    处理qconfig配置内容，更新afareMeanOrderDomainProductBlack配置
    
    参数:
    param: Dict[str, Any] - 请求参数
    domain: str - 域名
    product_mark: str - 产品标记
    
    返回:
    Dict[str, Any] - 处理结果
    """
    try:
        # 步骤一: GET请求获取当前配置
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': 'common.properties',
            'env': param['env'],
            "subenv": param.get('subenv', ''),
            'targetgroupid': 'f_tts_trade_core'
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        error = handle_request_exception(e)
        return {
            "status": "error",
            "message": error.message,
            "data": None
        }

    # 步骤二: 处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": "error",
            "message": get_result.get('message', '获取配置失败'),
            "data": None
        }

    config_data = get_result['data'].get('data', '')
    
    # 步骤三: 解析afareMeanOrderDomainProductBlack配置
    config_lines = config_data.split('\n')
    afareConfig = ""
    newConfig = ""
    configExists = False
    
    for line in config_lines:
        if line.startswith("afareMeanOrderDomainProductBlack="):
            configExists = True
            afareConfig = line[len("afareMeanOrderDomainProductBlack="):]
            # 检查domain是否存在于配置中
            domain_exists = False
            
            if afareConfig:
                domain_configs = afareConfig.split(';')
                for i, domain_config in enumerate(domain_configs):
                    if ":" in domain_config:
                        config_domain, product_marks = domain_config.split(':', 1)
                        if config_domain == domain:
                            domain_exists = True
                            # 检查productMark是否已经存在
                            product_marks_list = product_marks.split(',')
                            if product_mark in product_marks_list:
                                return {
                                    "status": "ignore",
                                    "message": "已配置",
                                    "data": None
                                }
                            # 追加productMark
                            if product_marks:
                                product_marks += f",{product_mark}"
                            else:
                                product_marks = product_mark
                            domain_configs[i] = f"{config_domain}:{product_marks}"
                
                # 如果domain不存在，追加domain和productMark
                if not domain_exists:
                    domain_configs.append(f"{domain}:{product_mark}")
                
                newConfig = "afareMeanOrderDomainProductBlack=" + ";".join(domain_configs) + "\n"
            
            break
    
    # 如果配置不存在，创建新配置
    if not configExists:
        newConfig = f"afareMeanOrderDomainProductBlack={domain}:{product_mark}\n"
    
    # 如果配置没有变化，直接返回成功
    if newConfig == "":
        newConfig = f"afareMeanOrderDomainProductBlack={domain}:{product_mark}\n"
    
    # 步骤四: 构建返回结果
    return {
        "status": "success",
        "message": "操作成功",
        "data": {
            "field_name": "afareMeanOrderDomainProductBlack",
            "field_value": newConfig.replace("afareMeanOrderDomainProductBlack=", "").rstrip("\n"),
            "split_char": "",
            "domain": domain,
            "product_mark": product_mark
        }
    }

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析URL编码的结构化数据
    
    参数:
    content: str - URL编码后的结构化数据
    
    返回:
    Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]] - 解析后的数据和状态
    """
    try:
        # 处理特殊情况：空内容
        if not content:
            return [], {"status": "success", "message": ""}
        
        # 尝试解码
        try:
            decoded_bytes = unquote_to_bytes(content)
            content_str = decoded_bytes.decode('utf-8')
        except:
            content_str = content
        
        # 解析结构化数据
        result = parse_structured_data(content_str)
        
        return result, {"status": "success", "message": ""}
    except Exception as e:
        return None, {"status": "error", "message": str(e)}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化数据字符串
    
    参数:
    data_str: str - 结构化数据字符串，例如："field1:value1#*#field2:value2~~*~~field1:value3#*#field2:value4"
    
    返回:
    List[Dict[str, str]] - 解析后的数据列表
    """
    result = []
    # 以分隔符 ~~*~~ 分割数据项
    parts = data_str.split("~~*~~")
    # 解析每一项
    for part in parts:
        if part:  # 忽略空项
            parsed_fields = _parse_fields(part)
            if parsed_fields:  # 忽略空解析结果
                result.append(parsed_fields)
    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析单个数据项的字段
    
    参数:
    part_str: str - 单个数据项字符串，例如："field1:value1#*#field2:value2"
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    fields = {}
    # 以分隔符 #*# 分割字段
    field_parts = part_str.split("#*#")
    for field_part in field_parts:
        if field_part and ":" in field_part:  # 确保字段部分不为空且包含":"
            # 以第一个":"分割字段名和值
            idx = field_part.find(":")
            key = field_part[:idx].strip()
            value = field_part[idx+1:].strip()
            if key:  # 确保字段名不为空
                fields[key] = value
    return fields