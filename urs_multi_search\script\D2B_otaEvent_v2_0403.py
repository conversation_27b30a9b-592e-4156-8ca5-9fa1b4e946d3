import json
import requests
from datetime import datetime, date
from typing import Optional, Union, List, Set, Any


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict,
    fields: List,
    date_format: str = None,
    case_sensitive: bool = False,
    supportTimePreciseMatch: bool = False,
) -> Set[Union[str, date, datetime]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :param supportTimePreciseMatch: 是否支持精确时间匹配（默认False，仅匹配日期）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            if supportTimePreciseMatch:
                # 尝试解析完整的时间格式
                dt = safe_parse_datetime(v)
                if dt:
                    parsed.add(dt)
                    continue
            # 如果精确时间解析失败或不需要精确时间，则按日期解析
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def filter_flight_data(
    target_data: List[dict]
):
    """
    增强版多条件过滤（支持全量多值参数）
    """
    matched = []
    notMatched = []

    for item in target_data:
        # 多条件检查
        checks = [
            _check_search_page(item),
            _check_flight_number(item),
        ]

        # 关键修复点：提取每个check的布尔状态
        is_all_match = all([check[0] for check in checks])

        # 收集所有错误信息
        error_msgs = [check[1] for check in checks if not check[0]]

        # 标注匹配状态及错误详情
        item.update(
            {
                "matchQeTag": "匹配" if is_all_match else "不匹配",
                "notMatchQeMsg": "; ".join(error_msgs) if error_msgs else "匹配",
            }
        )

        if is_all_match:
            matched.append(item)
        else:
            notMatched.append(item)

    return matched, notMatched


def _check_precise_search_time(
    item_search_datetime: datetime, search_dates: set
) -> tuple:
    """
    精准匹配搜索时间（精确到小时）
    :param item_search_datetime: 数据中的搜索时间
    :param search_dates: 请求的搜索时间集合
    :return: (是否匹配, 错误信息)
    """
    if not item_search_datetime:
        return False, "数据中搜索时间为空"

    # 将时间精确到小时
    item_search_hour = item_search_datetime.replace(minute=0, second=0, microsecond=0)
    req_dates_str = ",".join(
        sorted(
            [
                (
                    d.strftime("%Y-%m-%d %H:00")
                    if isinstance(d, datetime)
                    else d.strftime("%Y-%m-%d")
                )
                for d in search_dates
            ]
        )
    )

    # 检查是否有任何请求时间匹配（精确到小时）
    is_match = any(
        (
            isinstance(d, datetime)
            and d.replace(minute=0, second=0, microsecond=0) == item_search_hour
        )
        or (not isinstance(d, datetime) and d == item_search_datetime.date())
        for d in search_dates
    )

    if not is_match:
        return (
            False,
            f"搜索时间不匹配（请求值：{req_dates_str}，数据值：{item_search_datetime.strftime('%Y-%m-%d %H:00')}）",
        )
    return True, ""


def _check_precise_depart_time(
    item_depart_datetime: datetime, depart_dates: set
) -> tuple:
    """
    精准匹配起飞时间（精确到小时）
    :param item_depart_datetime: 数据中的起飞时间
    :param depart_dates: 请求的起飞时间集合
    :return: (是否匹配, 错误信息)
    """
    if not item_depart_datetime:
        return False, "数据中起飞时间为空"

    # 将时间精确到小时
    item_depart_hour = item_depart_datetime.replace(minute=0, second=0, microsecond=0)
    req_departs_str = ",".join(
        sorted(
            [
                (
                    d.strftime("%Y-%m-%d %H:00")
                    if isinstance(d, datetime)
                    else d.strftime("%Y-%m-%d")
                )
                for d in depart_dates
            ]
        )
    )

    # 检查是否有任何请求时间匹配（精确到小时）
    is_match = any(
        (
            isinstance(d, datetime)
            and d.replace(minute=0, second=0, microsecond=0) == item_depart_hour
        )
        or (not isinstance(d, datetime) and d == item_depart_datetime.date())
        for d in depart_dates
    )

    if not is_match:
        return (
            False,
            f"起飞时间不匹配（请求值：{req_departs_str}，数据值：{item_depart_datetime.strftime('%Y-%m-%d %H:00')}）",
        )
    return True, ""


def _check_dates(
    item: dict,
    search_dates: set,
    depart_dates: set,
    supportTimePreciseMatch: bool = False,
) -> tuple:
    """日期检查（返回匹配状态和带值的错误信息）"""
    errors = []

    # 搜索日期检查
    if search_dates:
        if supportTimePreciseMatch:
            # 尝试精确时间匹配
            item_search_datetime = safe_parse_datetime(item.get("searchDateTime"))
            is_match, error_msg = _check_precise_search_time(
                item_search_datetime, search_dates
            )
            if not is_match:
                errors.append(error_msg)
        else:
            # 日期匹配
            item_search_date = safe_parse_date(item.get("searchDateTime"), "%Y-%m-%d")
            req_dates_str = ",".join(
                sorted([d.strftime("%Y-%m-%d") for d in search_dates])
            )
            item_date_str = (
                item_search_date.strftime("%Y-%m-%d") if item_search_date else "无"
            )
            if not item_search_date or item_search_date not in search_dates:
                errors.append(
                    f"搜索日期不匹配（请求值：{req_dates_str}，数据值：{item_date_str}）"
                )

    # 起飞日期检查
    if depart_dates:
        # if supportTimePreciseMatch:
        # 尝试精确时间匹配
        #    item_depart_datetime = safe_parse_datetime(item.get("departureDate"))
        #    is_match, error_msg = _check_precise_depart_time(
        #        item_depart_datetime, depart_dates
        #    )
        #    if not is_match:
        #        errors.append(error_msg)
        # else:
        # 日期匹配
        item_depart_date = safe_parse_date(item.get("departureDate"), "%Y-%m-%d")
        req_departs_str = ",".join(
            sorted([d.strftime("%Y-%m-%d") for d in depart_dates])
        )
        item_depart_str = (
            item_depart_date.strftime("%Y-%m-%d") if item_depart_date else "无"
        )
        if not item_depart_date or item_depart_date not in depart_dates:
            errors.append(
                f"起飞日期不匹配（请求值：{req_departs_str}，数据值：{item_depart_str}）"
            )

    return (len(errors) == 0, "; ".join(errors))


def _check_cities(item: dict, dep_city: str, arr_city: str) -> tuple:
    """城市代码检查（带值对比）"""
    errors = []
    item_dep = item.get("departureCity", "")
    item_arr = item.get("arrivalCity", "")

    if (
        not is_deep_empty(item_dep)
        and not is_deep_empty(dep_city)
        and item_dep != dep_city
    ):
        errors.append(f"出发城市不匹配（请求值：{dep_city}，数据值：{item_dep}）")
    if (
        not is_deep_empty(arr_city)
        and not is_deep_empty(arr_city)
        and item_arr != arr_city
    ):
        errors.append(f"到达城市不匹配（请求值：{arr_city}，数据值：{item_arr}）")

    return (len(errors) == 0, "; ".join(errors))


def _check_carrier(item: dict, carriers: set) -> tuple:
    """航司检查（带值对比）"""
    if carriers:
        item_flight_number = item.get("flightNumber")
        if (
            not is_deep_empty(carriers)
            and item_flight_number
            and len(item_flight_number) >= 2
        ):
            item_carrier = item_flight_number[:2]
            if item_carrier not in carriers:
                return (
                    False,
                    f"航司不匹配（请求值：{str(carriers)}，数据值：{item_carrier}）",
                )
    return (True, "")


def _check_flight_number(item: dict) -> tuple:
    """航班号检查（带值对比）"""
    item_flight = item.get("flightNumber", "")

    if (item_flight is None or item_flight == ""):
        return (
            False,
            f"航班为空",
        )
    return (True, "")


def _check_search_page(item: dict) -> tuple:
    """搜索页面检查（检查source字段是否为ota，不区分大小写）"""
    item_source = item.get("source")
    if item_source is not None and item_source.lower() == "ota":
        return True, ""

    return False, f"搜索页面不匹配（数据值：{item_source}，要求：ota）"


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def safe_parse_datetime(
    datetime_str: str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime.datetime类型
    """
    if not datetime_str:
        return None

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except ValueError:
            continue

    return None


def search_flight_case(username: str, searchDate: str) -> dict:
    url = "http://paoding.corp.qunar.com/open/case/mainSearch"

    params = {
        "flightType": "SINGLE",
        "keywordType": "USERNAME",
        "keyword": username,
        "channelKey": "App",
        "searchDate": searchDate or "",
        "qTracePrefixes": "ops_slugger,f_athena_gateway",
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    response = requests.get(url, params=params, headers=headers)

    return response.json()


def main(param: dict) -> dict:
    try:

        searchDate = param.get("searchDate")
        if searchDate is None:
            result = {"ret": False, "errmsg": f"搜索日期为空", "data": [], "notMatchData": []}
            return result
        
        result = search_flight_case(
            username=param["username"], searchDate=searchDate
        )
        if result is None:
            result = {"ret": False, "errmsg": f"    ", "data": [], "notMatchData": []}

        if result.get("data") is None:
            result = {
                "ret": False,
                "errmsg": f"调用庖丁接口返回数据为空，请确认用户名|搜索日期|航班出发日期是否描述正确！",
                "data": [],
                "notMatchData": [],
            }
        if (
            result.get("data")
            and isinstance(result.get("data"), list)
            and len(result.get("data")) > 0
        ):
            result["errmsg"] = "获取数据成功"
            matchData, notMatchData = filter_flight_data(
                result.get("data"),
            )
            result["data"] = matchData
            result["notMatchData"] = notMatchData
            if len(matchData) == 0:
                result["errmsg"] = "获取数据成功, 但未匹配到有效数据"
        return result
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        # print(f"参数缺失: {e}")
        result = {
            "ret": False,
            "errmsg": f"调用庖丁获取用户搜索事件发生错误: {e}",
            "data": [],
            "notMatchData": [],
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "ret": False,
            "errmsg": f"调用庖丁获取用户搜索事件发生错误: {e}",
            "data": [],
            "notMatchData": [],
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


def test_main():
    """
    测试main方法，使用用户名ajsm9417，日期2025-04-06
    """
    # 准备测试参数
    test_params = {
        "username": "ajsm9417",
        "searchDate": "2025-04-06"
    }
    
    # 调用main方法
    result = main(test_params)
    
    # 打印结果
    print(f"结果状态: {result.get('ret')}")
    print(f"错误信息: {result.get('errmsg')}")
    print(f"匹配数据数量: {len(result.get('data', []))}")
    print(f"不匹配数据数量: {len(result.get('notMatchData', []))}")
    
    # 将结果写入文件以便查看详细内容
    output_file = f"test_result_{test_params['username']}_{test_params['searchDate']}.json"
    write_json_to_file(result, output_file)
    print(f"详细结果已写入文件: {output_file}")
    
    return result

if __name__ == "__main__":
    test_main()
