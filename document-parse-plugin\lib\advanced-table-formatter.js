/**
 * 高级表格格式化器 - 简洁但结构化的表格处理
 * 使用第三方算法优化表格显示效果
 */
class AdvancedTableFormatter {
  constructor() {
    this.options = {
      maxCellWidth: 40,
      minCellWidth: 6,
      alignment: 'auto',
      emptyCell: '-',
      numberFormat: true,
      compactMode: true // 简洁模式
    };
  }

  /**
   * 格式化表格为简洁的Markdown
   */
  formatTable(data, options = {}) {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    const opts = { ...this.options, ...options };
    const processedData = this.preprocessData(data);
    const analysis = this.analyzeTableStructure(processedData);
    
    return this.generateCleanTable(processedData, analysis, opts);
  }

  /**
   * 数据预处理 - 智能类型检测
   */
  preprocessData(data) {
    return data.map(row => {
      if (!Array.isArray(row)) return [];
      
      return row.map(cell => {
        const value = cell === null || cell === undefined ? '' : cell.toString().trim();
        const type = this.detectDataType(value);
        
        return {
          raw: cell,
          value: value,
          formatted: this.formatCellValue(value, type),
          type: type
        };
      });
    });
  }

  /**
   * 智能数据类型检测
   */
  detectDataType(value) {
    if (!value) return 'empty';
    
    // 数字检测
    if (/^\d+(\.\d+)?$/.test(value)) {
      return 'number';
    }
    
    // 百分比检测
    if (/^\d+(\.\d+)?%$/.test(value)) {
      return 'percentage';
    }
    
    // 货币检测
    if (/^[¥$€£]\d+(\.\d+)?$/.test(value) || /^\d+(\.\d+)?[¥$€£]$/.test(value)) {
      return 'currency';
    }
    
    // 日期检测
    if (this.isValidDate(value)) {
      return 'date';
    }
    
    return 'text';
  }

  /**
   * 格式化单元格值
   */
  formatCellValue(value, type) {
    if (!value) return this.options.emptyCell;
    
    switch (type) {
      case 'number':
        if (this.options.numberFormat) {
          const num = parseFloat(value);
          return num.toLocaleString('zh-CN');
        }
        return value;
      
      case 'percentage':
      case 'currency':
        return value;
      
      case 'date':
        try {
          const date = new Date(value);
          return date.toLocaleDateString('zh-CN');
        } catch (e) {
          return value;
        }
      
      default:
        // 处理长文本
        if (value.length > this.options.maxCellWidth) {
          return value.substring(0, this.options.maxCellWidth - 3) + '...';
        }
        return value;
    }
  }

  /**
   * 分析表格结构
   */
  analyzeTableStructure(data) {
    if (data.length === 0) return { isEmpty: true };
    
    const maxCols = Math.max(...data.map(row => row.length));
    const columnTypes = {};
    
    // 分析每列的主要数据类型
    for (let colIndex = 0; colIndex < maxCols; colIndex++) {
      const columnData = data.slice(1).map(row => row[colIndex]).filter(cell => cell && cell.type !== 'empty');
      
      if (columnData.length > 0) {
        const typeCount = {};
        columnData.forEach(cell => {
          typeCount[cell.type] = (typeCount[cell.type] || 0) + 1;
        });
        
        const mainType = Object.keys(typeCount).reduce((a, b) => typeCount[a] > typeCount[b] ? a : b);
        columnTypes[colIndex] = mainType;
      }
    }
    
    return {
      totalRows: data.length,
      totalCols: maxCols,
      columnTypes: columnTypes,
      hasHeader: data.length > 0,
      isEmpty: false
    };
  }

  /**
   * 生成简洁的表格
   */
  generateCleanTable(data, analysis, options) {
    if (analysis.isEmpty || data.length === 0) {
      return '';
    }
    
    let markdown = '';
    
    // 生成表头
    if (analysis.hasHeader && data.length > 0) {
      const headerRow = data[0];
      markdown += '| ' + headerRow.map(cell => cell.formatted || '列').join(' | ') + ' |\n';
      
      // 生成分隔线，根据列类型设置对齐
      markdown += '| ' + headerRow.map((cell, index) => {
        const colType = analysis.columnTypes[index] || 'text';
        return this.generateSeparator(colType);
      }).join(' | ') + ' |\n';
    }
    
    // 生成数据行
    const dataRows = analysis.hasHeader ? data.slice(1) : data;
    dataRows.forEach(row => {
      if (!Array.isArray(row)) return;
      
      // 检查是否为空行
      const hasData = row.some(cell => cell && cell.type !== 'empty');
      if (!hasData) return;
      
      // 确保行长度一致
      const paddedRow = [...row];
      while (paddedRow.length < analysis.totalCols) {
        paddedRow.push({ formatted: options.emptyCell, type: 'empty' });
      }
      
      markdown += '| ' + paddedRow.slice(0, analysis.totalCols).map(cell => 
        cell?.formatted || options.emptyCell
      ).join(' | ') + ' |\n';
    });
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 生成分隔线
   */
  generateSeparator(columnType) {
    switch (columnType) {
      case 'number':
      case 'percentage':
      case 'currency':
        return '---:'; // 右对齐
      case 'date':
        return ':---:'; // 居中对齐
      default:
        return '---'; // 左对齐
    }
  }

  /**
   * 检测是否为有效日期
   */
  isValidDate(value) {
    if (value.length < 4) return false;
    const date = new Date(value);
    return !isNaN(date.getTime()) && value.includes('-') || value.includes('/');
  }

  /**
   * 生成表格预览信息（简洁版）
   */
  generateTablePreview(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '*空表格*';
    }
    
    const rows = data.length;
    const cols = Math.max(...data.map(row => Array.isArray(row) ? row.length : 0));
    const dataRows = rows > 0 ? rows - 1 : 0;
    
    return `*${rows}行×${cols}列 (${dataRows}行数据)*`;
  }
}

// 表格美化工具类
class TableBeautifier {
  /**
   * 美化表格显示效果
   */
  static beautifyTable(markdown) {
    if (!markdown) return '';
    
    // 优化表格间距
    let beautified = markdown.replace(/\|\s+/g, '| ');
    beautified = beautified.replace(/\s+\|/g, ' |');
    
    // 确保表格前后有适当空行
    beautified = beautified.replace(/([^\n])\n\|/g, '$1\n\n|');
    beautified = beautified.replace(/\|\n([^\n|])/g, '|\n\n$1');
    
    return beautified;
  }

  /**
   * 压缩表格数据（用于JSON传输）
   */
  static compressTableData(data) {
    if (!Array.isArray(data)) return data;
    
    return data.map(row => {
      if (!Array.isArray(row)) return row;
      return row.map(cell => {
        if (cell === null || cell === undefined || cell === '') return null;
        return cell;
      });
    }).filter(row => row.some(cell => cell !== null));
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { AdvancedTableFormatter, TableBeautifier };
} else {
  window.AdvancedTableFormatter = AdvancedTableFormatter;
  window.TableBeautifier = TableBeautifier;
}
