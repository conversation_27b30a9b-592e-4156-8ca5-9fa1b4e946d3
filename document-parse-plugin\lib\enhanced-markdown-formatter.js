/**
 * 增强Markdown格式化器 - 专注于结构化和AI友好的输出
 * 特别优化Word和Excel表格的处理，提供详细的结构说明
 */
class EnhancedMarkdownFormatter {
  constructor() {
    this.options = {
      maxCellWidth: 50,
      minCellWidth: 8,
      tableAnalysis: true,
      structuredOutput: true,
      aiOptimized: true,
      includeMetadata: true,
      compactTables: false
    };

    // 数据类型标识（简化版）
    this.typeLabels = {
      'text': 'TEXT',
      'number': 'NUM',
      'percentage': 'PCT',
      'currency': 'CUR',
      'date': 'DATE',
      'email': 'EMAIL',
      'url': 'URL',
      'phone': 'PHONE',
      'empty': 'EMPTY'
    };

    // 表格类型识别
    this.tablePatterns = {
      'financial': ['金额', '价格', '费用', '成本', '收入', '支出', '预算'],
      'schedule': ['时间', '日期', '计划', '安排', '进度', '截止'],
      'contact': ['姓名', '电话', '邮箱', '地址', '联系'],
      'inventory': ['数量', '库存', '产品', '型号', '规格'],
      'performance': ['指标', '完成率', '达成', '目标', '实际']
    };
  }

  /**
   * 格式化表格为增强的Markdown
   */
  formatTable(data, options = {}) {
    if (!Array.isArray(data) || data.length === 0) {
      return this.generateEmptyTableMessage();
    }

    const opts = { ...this.options, ...options };
    const processedData = this.preprocessTableData(data);
    const analysis = this.analyzeTableStructure(processedData);

    let markdown = '';

    // 根据来源类型决定输出格式
    if (opts.source && opts.source.includes('.xlsx') || opts.source && opts.source.includes('.xls')) {
      // Excel表格：简化输出，只显示表格内容
      markdown += this.generateSimpleTable(processedData, analysis, opts);
    } else {
      // Word表格：详细输出，包含分析信息
      markdown += this.generateTableHeader(analysis, opts);
      markdown += this.generateStructuredTable(processedData, analysis, opts);

      if (opts.includeMetadata) {
        markdown += this.generateTableAnalysis(analysis, opts);
      }
    }

    return markdown;
  }

  /**
   * 预处理表格数据 - 智能类型检测和格式化
   */
  preprocessTableData(data) {
    return data.map((row, rowIndex) => {
      if (!Array.isArray(row)) return [];

      return row.map((cell, colIndex) => {
        const rawValue = cell;
        const stringValue = this.normalizeValue(cell);
        const dataType = this.detectAdvancedDataType(stringValue);
        const formattedValue = this.formatCellValue(stringValue, dataType);

        return {
          raw: rawValue,
          value: stringValue,
          formatted: formattedValue,
          type: dataType,
          position: { row: rowIndex, col: colIndex },
          isEmpty: this.isEmpty(stringValue),
          confidence: this.getTypeConfidence(stringValue, dataType)
        };
      });
    });
  }

  /**
   * 高级数据类型检测
   */
  detectAdvancedDataType(value) {
    if (this.isEmpty(value)) return 'empty';

    const patterns = {
      // 数字类型
      'integer': /^-?\d+$/,
      'decimal': /^-?\d+\.\d+$/,
      'scientific': /^-?\d+\.?\d*[eE][+-]?\d+$/,

      // 百分比和比率
      'percentage': /^\d+(\.\d+)?%$/,
      'ratio': /^\d+(\.\d+)?:\d+(\.\d+)?$/,

      // 货币
      'currency_cny': /^[¥￥]\s*\d+(\.\d+)?$/,
      'currency_usd': /^\$\s*\d+(\.\d+)?$/,
      'currency_eur': /^€\s*\d+(\.\d+)?$/,
      'currency_suffix': /^\d+(\.\d+)?\s*[¥￥$€£]$/,

      // 日期时间
      'date_iso': /^\d{4}-\d{2}-\d{2}$/,
      'date_slash': /^\d{1,2}\/\d{1,2}\/\d{4}$/,
      'date_chinese': /^\d{4}年\d{1,2}月\d{1,2}日$/,
      'datetime': /^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}(:\d{2})?$/,
      'time': /^\d{1,2}:\d{2}(:\d{2})?$/,

      // 联系信息
      'email': /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'phone_cn': /^1[3-9]\d{9}$/,
      'phone_intl': /^\+\d{1,3}\s?\d{4,14}$/,
      'phone_general': /^(\d{3,4}[-\s]?)?\d{7,8}$/,

      // 网络相关
      'url': /^https?:\/\/[^\s]+$/,
      'ip': /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,

      // 标识符
      'id_card': /^\d{15}|\d{18}$/,
      'code': /^[A-Z]{2,}\d+$/,
      'serial': /^[A-Z0-9]{8,}$/
    };

    // 检测具体类型
    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(value)) {
        return this.mapToMainType(type);
      }
    }

    // 数字检测（通用）
    if (!isNaN(parseFloat(value)) && isFinite(value)) {
      return 'number';
    }

    // 默认文本类型
    return 'text';
  }

  /**
   * 映射到主要类型
   */
  mapToMainType(detailedType) {
    const typeMap = {
      'integer': 'number',
      'decimal': 'number',
      'scientific': 'number',
      'percentage': 'percentage',
      'ratio': 'percentage',
      'currency_cny': 'currency',
      'currency_usd': 'currency',
      'currency_eur': 'currency',
      'currency_suffix': 'currency',
      'date_iso': 'date',
      'date_slash': 'date',
      'date_chinese': 'date',
      'datetime': 'date',
      'time': 'date',
      'email': 'email',
      'phone_cn': 'phone',
      'phone_intl': 'phone',
      'phone_general': 'phone',
      'url': 'url',
      'ip': 'text',
      'id_card': 'text',
      'code': 'text',
      'serial': 'text'
    };

    return typeMap[detailedType] || 'text';
  }

  /**
   * 格式化单元格值
   */
  formatCellValue(value, type) {
    if (this.isEmpty(value)) return '-';

    switch (type) {
      case 'number':
        const num = parseFloat(value);
        if (!isNaN(num)) {
          return num.toLocaleString('zh-CN', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          });
        }
        return value;

      case 'percentage':
        return value; // 保持原格式

      case 'currency':
        return value; // 保持原格式

      case 'date':
        try {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            return date.toLocaleDateString('zh-CN');
          }
        } catch (e) {
          // 忽略错误，返回原值
        }
        return value;

      case 'email':
        return `[${value}](mailto:${value})`;

      case 'url':
        const displayText = value.length > 30 ? value.substring(0, 30) + '...' : value;
        return `[${displayText}](${value})`;

      case 'phone':
        return value;

      default:
        // 处理长文本
        if (value.length > this.options.maxCellWidth) {
          return value.substring(0, this.options.maxCellWidth - 3) + '...';
        }
        return value;
    }
  }

  /**
   * 分析表格结构
   */
  analyzeTableStructure(data) {
    if (data.length === 0) {
      return { isEmpty: true, error: '表格为空' };
    }

    const maxCols = Math.max(...data.map(row => row.length));
    const totalCells = data.length * maxCols;

    // 分析列类型
    const columnAnalysis = this.analyzeColumns(data, maxCols);

    // 分析表格类型
    const tableType = this.identifyTableType(data);

    // 数据质量分析
    const qualityAnalysis = this.analyzeDataQuality(data);

    // 表头分析
    const headerAnalysis = this.analyzeHeader(data);

    return {
      isEmpty: false,
      totalRows: data.length,
      totalCols: maxCols,
      totalCells: totalCells,
      dataRows: headerAnalysis.hasHeader ? data.length - 1 : data.length,
      columns: columnAnalysis,
      tableType: tableType,
      quality: qualityAnalysis,
      header: headerAnalysis,
      structure: this.analyzeStructure(data)
    };
  }

  /**
   * 分析列数据
   */
  analyzeColumns(data, maxCols) {
    const columns = {};

    for (let colIndex = 0; colIndex < maxCols; colIndex++) {
      const columnData = data.map(row => row[colIndex]).filter(cell => cell && !cell.isEmpty);

      if (columnData.length > 0) {
        const types = columnData.map(cell => cell.type);
        const typeCount = {};
        types.forEach(type => {
          typeCount[type] = (typeCount[type] || 0) + 1;
        });

        const mainType = Object.keys(typeCount).reduce((a, b) => typeCount[a] > typeCount[b] ? a : b);
        const headerName = data[0] && data[0][colIndex] ? data[0][colIndex].formatted : `列${colIndex + 1}`;

        columns[colIndex] = {
          name: headerName,
          mainType: mainType,
          typeDistribution: typeCount,
          totalValues: columnData.length,
          emptyCount: data.length - columnData.length,
          completeness: (columnData.length / data.length * 100).toFixed(1),
          label: this.typeLabels[mainType] || 'TEXT',
          alignment: this.getColumnAlignment(mainType),
          stats: this.calculateColumnStats(columnData, mainType)
        };
      }
    }

    return columns;
  }

  /**
   * 识别表格类型
   */
  identifyTableType(data) {
    if (data.length === 0) return 'unknown';

    const headerRow = data[0];
    if (!headerRow) return 'unknown';

    const headerText = headerRow.map(cell => cell.value.toLowerCase()).join(' ');

    for (const [type, keywords] of Object.entries(this.tablePatterns)) {
      const matchCount = keywords.filter(keyword => headerText.includes(keyword)).length;
      if (matchCount >= 2) {
        return type;
      }
    }

    return 'general';
  }

  /**
   * 分析数据质量
   */
  analyzeDataQuality(data) {
    const totalCells = data.length * Math.max(...data.map(row => row.length));
    const emptyCells = data.flat().filter(cell => cell && cell.isEmpty).length;
    const completeness = ((totalCells - emptyCells) / totalCells * 100).toFixed(1);

    return {
      totalCells: totalCells,
      emptyCells: emptyCells,
      filledCells: totalCells - emptyCells,
      completeness: completeness,
      quality: completeness >= 90 ? 'excellent' : completeness >= 70 ? 'good' : completeness >= 50 ? 'fair' : 'poor'
    };
  }

  /**
   * 分析表头
   */
  analyzeHeader(data) {
    if (data.length === 0) return { hasHeader: false };

    const firstRow = data[0];
    const secondRow = data.length > 1 ? data[1] : null;

    // 简单的表头检测逻辑
    let hasHeader = true;

    if (secondRow) {
      // 比较第一行和第二行的数据类型
      const firstRowTypes = firstRow.map(cell => cell.type);
      const secondRowTypes = secondRow.map(cell => cell.type);

      const typeDifferences = firstRowTypes.filter((type, index) =>
        type !== secondRowTypes[index] && type === 'text'
      ).length;

      hasHeader = typeDifferences >= firstRowTypes.length * 0.5;
    }

    return {
      hasHeader: hasHeader,
      headerRow: hasHeader ? firstRow : null,
      confidence: hasHeader ? 0.8 : 0.2
    };
  }

  /**
   * 分析表格结构
   */
  analyzeStructure(data) {
    const structure = {
      isRegular: true,
      rowLengths: data.map(row => row.length),
      maxRowLength: Math.max(...data.map(row => row.length)),
      minRowLength: Math.min(...data.map(row => row.length)),
      hasConsistentStructure: true
    };

    // 检查结构一致性
    const lengthVariation = structure.maxRowLength - structure.minRowLength;
    structure.hasConsistentStructure = lengthVariation <= 1;
    structure.isRegular = lengthVariation === 0;

    return structure;
  }

  /**
   * 计算列统计信息
   */
  calculateColumnStats(columnData, type) {
    const stats = {};

    if (type === 'number') {
      const numbers = columnData
        .map(cell => parseFloat(cell.value))
        .filter(num => !isNaN(num));

      if (numbers.length > 0) {
        stats.min = Math.min(...numbers);
        stats.max = Math.max(...numbers);
        stats.sum = numbers.reduce((a, b) => a + b, 0);
        stats.avg = stats.sum / numbers.length;
        stats.count = numbers.length;
      }
    }

    return stats;
  }

  /**
   * 获取列对齐方式
   */
  getColumnAlignment(type) {
    switch (type) {
      case 'number':
      case 'percentage':
      case 'currency':
        return 'right';
      case 'date':
        return 'center';
      default:
        return 'left';
    }
  }

  /**
   * 工具方法
   */
  normalizeValue(value) {
    if (value === null || value === undefined) return '';
    return value.toString().trim();
  }

  isEmpty(value) {
    return !value || value === '' || value === '-' || value === 'null' || value === 'undefined';
  }

  getTypeConfidence(value, type) {
    // 简单的置信度计算
    if (this.isEmpty(value)) return 0;
    if (type === 'text') return 0.5;
    return 0.9; // 其他类型的置信度较高
  }

  /**
   * 生成空表格消息
   */
  generateEmptyTableMessage() {
    return '**表格为空** - 没有可显示的数据\n\n';
  }

  /**
   * 生成表格标题和描述
   */
  generateTableHeader(analysis, options) {
    if (analysis.isEmpty) return '';

    let markdown = '';

    const typeName = this.getTableTypeName(analysis.tableType);

    // 只显示表格类型，如果不是通用类型
    if (analysis.tableType !== 'general') {
      markdown += `**${typeName}表格**\n\n`;
    }

    return markdown;
  }

  /**
   * 生成结构化表格
   */
  generateStructuredTable(data, analysis, options) {
    if (analysis.isEmpty || data.length === 0) return '';

    let markdown = '';

    // 生成表头
    if (analysis.header.hasHeader && data.length > 0) {
      const headerRow = data[0];

      // 表头行 - 简洁的类型标识
      markdown += '| ' + headerRow.map((cell, index) => {
        const column = analysis.columns[index];
        const name = cell.formatted || `列${index + 1}`;
        // 只对非文本类型显示标识
        if (column && column.mainType !== 'text') {
          return `**${name}** (${column.mainType})`;
        }
        return `**${name}**`;
      }).join(' | ') + ' |\n';

      // 分隔线 - 根据数据类型设置对齐
      markdown += '| ' + headerRow.map((cell, index) => {
        const column = analysis.columns[index];
        const alignment = column ? column.alignment : 'left';
        return this.generateSeparator(alignment);
      }).join(' | ') + ' |\n';
    }

    // 生成数据行
    const dataRows = analysis.header.hasHeader ? data.slice(1) : data;
    let displayedRows = 0;
    const maxDisplayRows = options.maxDisplayRows || 50;

    dataRows.forEach((row, rowIndex) => {
      if (!Array.isArray(row) || displayedRows >= maxDisplayRows) return;

      // 检查是否为空行
      const hasData = row.some(cell => cell && !cell.isEmpty);
      if (!hasData) return;

      // 确保行长度一致
      const paddedRow = [...row];
      while (paddedRow.length < analysis.totalCols) {
        paddedRow.push({ formatted: '-', type: 'empty', isEmpty: true });
      }

      markdown += '| ' + paddedRow.slice(0, analysis.totalCols).map((cell, colIndex) => {
        return cell?.formatted || '-';
      }).join(' | ') + ' |\n';

      displayedRows++;
    });

    // 如果有更多行未显示
    if (dataRows.length > maxDisplayRows) {
      markdown += `\n**注意**: 表格共有 ${dataRows.length} 行数据，此处仅显示前 ${maxDisplayRows} 行\n`;
    }

    markdown += '\n';
    return markdown;
  }

  /**
   * 生成表格分析和说明
   */
  generateTableAnalysis(analysis, options) {
    if (analysis.isEmpty) return '';

    let markdown = '';

    // 只显示有数值统计的列信息
    const numericColumns = Object.entries(analysis.columns).filter(([colIndex, column]) =>
      column.stats && Object.keys(column.stats).length > 0 && column.mainType === 'number'
    );

    if (numericColumns.length > 0) {
      markdown += '**数值统计**:\n\n';
      numericColumns.forEach(([colIndex, column]) => {
        markdown += `- **${column.name}**: 总和 ${column.stats.sum.toLocaleString('zh-CN')}, 平均 ${column.stats.avg.toFixed(2)}\n`;
      });
      markdown += '\n';
    }

    // 简化的使用建议
    if (options.includeUsageTips && analysis.tableType !== 'general') {
      markdown += `**说明**: 这是一个${this.getTableTypeName(analysis.tableType)}类型的表格\n\n`;
    }

    return markdown;
  }

  /**
   * 生成分隔线
   */
  generateSeparator(alignment) {
    switch (alignment) {
      case 'right':
        return '---:';
      case 'center':
        return ':---:';
      default:
        return '---';
    }
  }

  /**
   * 获取表格类型名称
   */
  getTableTypeName(type) {
    const typeNames = {
      'financial': '财务',
      'schedule': '时间安排',
      'contact': '联系人',
      'inventory': '库存',
      'performance': '绩效',
      'general': '通用'
    };
    return typeNames[type] || '未知';
  }

  /**
   * 获取质量描述
   */
  getQualityDescription(quality) {
    const qualityDescriptions = {
      'excellent': '优秀',
      'good': '良好',
      'fair': '一般',
      'poor': '较差'
    };
    return qualityDescriptions[quality] || '未知';
  }

  /**
   * 获取数据密度描述
   */
  getDataDensityDescription(completeness) {
    const percentage = parseFloat(completeness);
    if (percentage >= 90) return '高密度 (数据充实)';
    if (percentage >= 70) return '中密度 (数据较好)';
    if (percentage >= 50) return '低密度 (数据稀疏)';
    return '极低密度 (数据缺失严重)';
  }

  /**
   * 生成使用建议
   */
  generateUsageTips(analysis) {
    let markdown = '\n**使用建议**:\n\n';

    if (analysis.quality.completeness < 70) {
      markdown += '- 数据完整度较低，建议检查和补充缺失数据\n';
    }

    if (!analysis.structure.isRegular) {
      markdown += '- 表格结构不规整，建议统一行列格式\n';
    }

    const numericColumns = Object.values(analysis.columns).filter(col => col.mainType === 'number');
    if (numericColumns.length > 0) {
      markdown += '- 包含数值数据，适合进行统计分析和图表制作\n';
    }

    if (analysis.tableType !== 'general') {
      markdown += `- 识别为${this.getTableTypeName(analysis.tableType)}类型表格，建议按相应业务场景使用\n`;
    }

    return markdown;
  }

  /**
   * 生成简化的表格（用于Excel）
   */
  generateSimpleTable(data, analysis, options) {
    if (analysis.isEmpty || data.length === 0) return '';

    let markdown = '';

    // 生成表头
    if (analysis.header.hasHeader && data.length > 0) {
      const headerRow = data[0];

      // 简单的表头行
      markdown += '| ' + headerRow.map((cell, index) => {
        const name = cell.formatted || `列${index + 1}`;
        return `**${name}**`;
      }).join(' | ') + ' |\n';

      // 分隔线
      markdown += '| ' + headerRow.map(() => '---').join(' | ') + ' |\n';
    }

    // 生成数据行
    const dataRows = analysis.header.hasHeader ? data.slice(1) : data;
    let displayedRows = 0;
    const maxDisplayRows = options.maxDisplayRows || 100; // Excel允许更多行

    dataRows.forEach((row, rowIndex) => {
      if (!Array.isArray(row) || displayedRows >= maxDisplayRows) return;

      // 检查是否为空行
      const hasData = row.some(cell => cell && !cell.isEmpty);
      if (!hasData) return;

      // 确保行长度一致
      const paddedRow = [...row];
      while (paddedRow.length < analysis.totalCols) {
        paddedRow.push({ formatted: '', type: 'empty', isEmpty: true });
      }

      markdown += '| ' + paddedRow.slice(0, analysis.totalCols).map((cell, colIndex) => {
        return cell?.formatted || '';
      }).join(' | ') + ' |\n';

      displayedRows++;
    });

    markdown += '\n';
    return markdown;
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedMarkdownFormatter;
} else {
  window.EnhancedMarkdownFormatter = EnhancedMarkdownFormatter;
}
