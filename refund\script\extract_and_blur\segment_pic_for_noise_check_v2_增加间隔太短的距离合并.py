import cv2
import numpy as np


def merge_close_bounding_boxes(boxes, proximity_threshold_x, proximity_threshold_y):
    """
    Merges bounding boxes that are close to each other horizontally.

    Args:
        boxes (list of tuples): A list of bounding boxes, where each box is (x, y, w, h).
        proximity_threshold_x (int): Maximum horizontal distance for merging.
        proximity_threshold_y (int): Not used in this version, kept for compatibility.

    Returns:
        list of tuples: A list of merged bounding boxes.
    """
    if not boxes:
        return []

    # Convert boxes to a list of lists for easier modification
    merged_boxes = [list(box) for box in boxes]

    while True:
        made_a_merge_in_this_pass = False
        i = 0
        while i < len(merged_boxes):
            j = i + 1
            while j < len(merged_boxes):
                box1 = merged_boxes[i]
                box2 = merged_boxes[j]

                x1, y1, w1, h1 = box1
                x2, y2, w2, h2 = box2

                # Check for horizontal proximity
                horizontal_dist = float("inf")
                if x1 + w1 < x2:  # box1 is to the left of box2
                    horizontal_dist = x2 - (x1 + w1)
                elif x2 + w2 < x1:  # box2 is to the left of box1
                    horizontal_dist = x1 - (x2 + w2)
                else:  # boxes overlap horizontally
                    horizontal_dist = 0

                # Calculate vertical overlap
                y_overlap = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))

                # Only merge if horizontal distance is small and there's some vertical overlap
                if horizontal_dist < proximity_threshold_x and y_overlap > 0:
                    # Merge box1 and box2
                    new_x = min(x1, x2)
                    new_y = min(y1, y2)
                    new_w = max(x1 + w1, x2 + w2) - new_x
                    new_h = max(y1 + h1, y2 + h2) - new_y

                    merged_boxes[i] = [new_x, new_y, new_w, new_h]
                    merged_boxes.pop(j)  # Remove box2
                    made_a_merge_in_this_pass = True
                    break
                else:
                    j += 1
            if made_a_merge_in_this_pass and i < len(merged_boxes):
                pass
            else:
                i += 1

        if not made_a_merge_in_this_pass:
            break

    return [tuple(box) for box in merged_boxes]


def segment_image_by_contours(
    image_path,
    min_contour_area=100,
    max_contour_area_ratio=0.5,
    merge_boxes=False,
    merge_prox_x=10,
    merge_prox_y=10,
):
    """
    Segments an image by detecting contours, optionally merging close bounding boxes,
    and drawing bounding boxes around them.

    Args:
        image_path (str): The path to the input image.
        min_contour_area (int): Minimum area for a contour to be considered.
        max_contour_area_ratio (float): Maximum area for a contour relative to image size.
        merge_boxes (bool): Whether to merge close bounding boxes.
        merge_prox_x (int): Horizontal proximity threshold for merging boxes.
        merge_prox_y (int): Vertical proximity threshold for merging boxes.

    Returns:
        tuple: (original_image, image_with_contours)
               Returns None, None if the image cannot be loaded.
    """
    # 1. 加载图像
    # 1. Load the image
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"错误：无法加载图像，请检查路径： {image_path}")
        print(f"Error: Could not load image. Please check the path: {image_path}")
        return None, None

    print(f"图像加载成功，尺寸: {original_image.shape}")
    print(f"Image loaded successfully, dimensions: {original_image.shape}")

    # 创建一个副本用于绘制轮廓
    # Create a copy to draw contours on
    image_with_contours = original_image.copy()

    # 2. 预处理
    # 2. Preprocessing
    gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
    blurred_image = cv2.GaussianBlur(gray_image, (5, 5), 0)

    # 3. 边缘检测 (使用 Canny 算法)
    # 3. Edge Detection (using Canny algorithm)
    edged_image = cv2.Canny(blurred_image, 50, 150)

    print("边缘检测完成。")
    print("Edge detection complete.")

    # 4. 查找轮廓
    # 4. Find Contours
    contours, hierarchy = cv2.findContours(
        edged_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )

    print(f"找到 {len(contours)} 个初始轮廓。")
    print(f"Found {len(contours)} initial contours.")

    # 5. 筛选轮廓并获取边界框
    # 5. Filter contours and get bounding boxes
    image_height, image_width = original_image.shape[:2]
    max_area = image_width * image_height * max_contour_area_ratio

    initial_bounding_boxes = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if min_contour_area < area < max_area:
            x, y, w, h = cv2.boundingRect(contour)
            initial_bounding_boxes.append((x, y, w, h))

    print(f"筛选后剩下 {len(initial_bounding_boxes)} 个初始边界框。")
    print(
        f"Found {len(initial_bounding_boxes)} initial bounding boxes after filtering."
    )

    # 6. (可选) 合并距离近的边界框
    # 6. (Optional) Merge close bounding boxes
    final_bounding_boxes = initial_bounding_boxes
    if merge_boxes and initial_bounding_boxes:
        print(f"开始合并边界框，水平阈值: {merge_prox_x}, 垂直阈值: {merge_prox_y}")
        print(
            f"Starting bounding box merging, horizontal threshold: {merge_prox_x}, vertical threshold: {merge_prox_y}"
        )
        final_bounding_boxes = merge_close_bounding_boxes(
            initial_bounding_boxes, merge_prox_x, merge_prox_y
        )
        print(f"合并后剩下 {len(final_bounding_boxes)} 个边界框。")
        print(f"Found {len(final_bounding_boxes)} bounding boxes after merging.")

    # 7. 绘制最终的边界框
    # 7. Draw final bounding boxes
    for x, y, w, h in final_bounding_boxes:
        cv2.rectangle(image_with_contours, (x, y), (x + w, y + h), (0, 255, 0), 2)

    return original_image, image_with_contours


# --- 主程序 ---
if __name__ == "__main__":
    image_file_path = (
        "D:\\work\\ps_check_data\\cdc250511204953254_1099894796_for_segment.jpeg"
    )

    # 示例：不合并边界框
    # original_no_merge, segmented_no_merge = segment_image_by_contours(
    #     image_file_path,
    #     min_contour_area=50,
    #     max_contour_area_ratio=0.8,
    #     merge_boxes=False
    # )

    # if original_no_merge is not None:
    #     cv2.namedWindow("Segmented Image (No Merge)", cv2.WINDOW_NORMAL)
    #     cv2.imshow("Segmented Image (No Merge)", segmented_no_merge)

    # 示例：合并边界框
    # 您需要根据您的图像和期望的效果调整 merge_prox_x 和 merge_prox_y
    # You will need to adjust merge_prox_x and merge_prox_y based on your image and desired outcome
    original_merged, segmented_merged = segment_image_by_contours(
        image_file_path,
        min_contour_area=50,  # 最小轮廓面积 (Minimum contour area)
        max_contour_area_ratio=0.8,  # 最大轮廓面积比例 (Maximum contour area ratio)
        merge_boxes=True,  # 启用合并 (Enable merging)
        merge_prox_x=20,  # 水平合并距离阈值 (Horizontal merge proximity threshold)
        merge_prox_y=10,  # 垂直合并距离阈值 (Vertical merge proximity threshold)
    )

    if original_merged is not None:
        cv2.namedWindow("Original Image", cv2.WINDOW_NORMAL)
        cv2.imshow("Original Image", original_merged)
        cv2.namedWindow("Segmented Image (Merged)", cv2.WINDOW_NORMAL)
        cv2.imshow("Segmented Image (Merged)", segmented_merged)

        print("请按任意键关闭图像窗口...")
        print("Press any key to close the image windows...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
