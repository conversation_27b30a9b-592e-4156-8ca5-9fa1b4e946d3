import requests
import time
import json
import base64
from datetime import datetime
from csv import DictReader, <PERSON><PERSON><PERSON> as CSVError
from io import StringIO
import uuid
from typing import List, Dict, Any

TAMIAS_RESULT_DOWNLOAD_URL = "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="

def generateId() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())

def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        return "tamias结果文件解析失败！", None

    return None, result

def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "yaowyw.wang",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False
    )
    print("----------------response:", response.text)
    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None

def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None

def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                item.update({"uuid": generateId()})
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


ORDER_EXTRA_INFO_SQL = """
select
  order_no,
  ext_value,ext_key,
  pay_time,
  source,
  carrier
from
  (
    select
      order_id,
      order_no,
      agent_domain,
      pay_time,
      source,
      carrier
    from
      flight.dwd_ord_wide_order_di_simple
    where
      dt = '{date}'
      and dom_inter = 0
      and is_fenxiao = 0
      and pay_ok = 1
  ) a
  left join (
    select
      cast(order_id as VARCHAR(20)) as border_id,
      etl_clientid,
      ext_value,ext_key
    from
      stage.sg_n_order_ext_domestictts
    where
      dt = '{vDate}'
      and ext_key in ('unFullInvoice','new_validate_info','extendmap')
  ) b on a.order_id = b.border_id
  and a.agent_domain = b.etl_clientid
where
  order_no = '{orderNo}'
"""

def extract_date_from_order(order_no):
    """
    Extract date from order number in format dvbYYMMDD...
    Example: dvb250321112040552 -> 2025-03-21
    """
    if not order_no or len(order_no) < 10:
        return None
    
    try:
        # Extract YYMMDD from order number (positions 3-8)
        date_str = order_no[3:9]
        year = "20" + date_str[:2]  # Add "20" prefix for full year
        month = date_str[2:4]
        day = date_str[4:6]
        return f"{year}-{month}-{day}"
    except Exception:
        return None

def main(param):
    try:
        order_no = param.get("orderNo")
        if not order_no:
            return {"error": "订单号不能为空", "results": []}
            
        # Extract date from order number
        order_date = extract_date_from_order(order_no)
        if not order_date:
            return {"error": "无法从订单号中提取日期", "results": []}
        
        # Query additional order information
        extraInfoSql = ORDER_EXTRA_INFO_SQL.format(
            vDate=order_date.replace("-", ""),
            date=order_date,
            orderNo=order_no
        )
        extraInfoResult = queryDataFromTamias(param.get("cookie"), extraInfoSql)
        
        # Initialize default values
        extraInfo = {
            "unFullInvoice": "否",
            "isNonMobile": "否",
            "payTime": "",
            "carrier": "",
            "notExecAlReason": "",
            "needExecAl": "是",
            "dis_flow": "否",
            "productLabels": ""
        }
        
        # Process extra info results
        if extraInfoResult.get("results") and isinstance(extraInfoResult["results"], list):
            # Get the first result for basic order info
            if len(extraInfoResult["results"]) > 0:
                first_result = extraInfoResult["results"][0]
                extraInfo.update({
                    "isNonMobile": "是" if first_result.get("source") not in ('mobile.app.iphone', 'mobile.app.android') else "否",
                    "payTime": first_result.get("pay_time", ""),
                    "carrier": first_result.get("carrier", "")
                })
            
            # Process all results for ext_key and ext_value
            for result in extraInfoResult["results"]:
                ext_key = result.get("ext_key")
                ext_value = result.get("ext_value")
                
                if ext_key == "unFullInvoice":
                    extraInfo["unFullInvoice"] = "是" if ext_value and ext_value.lower() == "true" else "否"
                    extraInfo["notExecAlReason"] = "旅行套餐" if extraInfo["unFullInvoice"] == "是" else ""
                    extraInfo["needExecAl"] = "否" if extraInfo["unFullInvoice"] == "是" else "是"
                
                elif ext_key == "new_validate_info" and ext_value:
                    try:
                        validate_info = json.loads(ext_value)
                        if validate_info.get("type") == "dis_flow_re":
                            extraInfo["dis_flow"] = "是"
                    except json.JSONDecodeError:
                        pass
                elif ext_key == "extendmap":
                    try:
                        extendmap = json.loads(ext_value)
                        extraInfo["productLabels"] = "是" if extendmap.get("productLabels", "")=="1" else "否"
                    except json.JSONDecodeError:
                        pass
            
            return {"error": "", "results": extraInfo}
        else:
            return {"error": "", "results": extraInfo}
            
    except Exception as e:
        return {"error": "订单信息查询失败", "results": []}

def test():
    param = {
        "vDate": "2025-04-16",
        "orderNo": "wxw250416152447096",
        "cookie": "QN1=0001088014346d6618281d38; QSSOFP=95e4b28e33d941c9be383c891f597885_1744190744162; cookie=yaowyw.wang&846331&520F3F96270CFD217F49D817D2084555"
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 