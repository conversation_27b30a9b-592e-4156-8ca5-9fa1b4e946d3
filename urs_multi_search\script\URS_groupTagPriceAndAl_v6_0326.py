from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from datetime import datetime
from string import Formatter
import re
from typing import Union
import uuid


def generate_compare_id() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())


def generate_strategies(params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    根据输入参数动态生成策略配置列表

    参数映射规则：
    {
        "参数键": {
            "stra_name": 策略名称,
            "data_mapping": {
                "参数子键": "策略数据字段"  # 支持多字段映射
            }
        },
        ...
    }
    """
    # 策略配置映射表（可扩展）
    strategy_config = {
        # 价格差异策略-范围限制
        "价差阈值": {
            "stra_name": "价差阈值",
            "data_mapping": {
                "priceMinDiffThreshold": "priceMinDiffThreshold"
            },  # 值直接映射
        },
        # 对比类型策略
        "对比类型": {
            "stra_name": "对比类型",
            "data_mapping": {
                "allowedComparetypes": "allowedComparetypes"
            },  # 值直接映射
        },
        # 时间差异策略
        "搜索时间差": {
            "stra_name": "搜索时间差",
            "data_mapping": {"listSearchDurationLimit": "listSearchDurationLimit"},
        },
        # 价差限制策略
        "价差限制": {
            "stra_name": "价差限制",
            "data_mapping": {"priceDiffSignedThreshold": "priceDiffSignedThreshold"},
        },
        # 对比类型策略
        "航班类型": {
            "stra_name": "航班类型",
            "data_mapping": {
                "allowedCompareFlightTypes": "allowedCompareFlightTypes"
            },  # 值直接映射
        },
    }

    strategies = []

    strategies.append({"straName": "航班日期不一致", "straData": {}})

    for strategy_key, config in strategy_config.items():
        # 构建策略数据
        stra_data = {}

        # 处理数据映射
        for paramIndexKey, data_field in config["data_mapping"].items():
            # 获取参数值
            if is_deep_empty(paramIndexKey):
                continue
            else:  # 处理嵌套参数
                paramValue = params.get(paramIndexKey)
                if paramValue is None:
                    continue
                else:
                    stra_data[data_field] = paramValue

        # 仅当有有效数据时添加策略
        if not is_deep_empty(stra_data):
            strategies.append({"straName": config["stra_name"], "straData": stra_data})
    return strategies


def apply_strategy(compare_data, stra_name, stra_data):
    """
    根据策略名称和数据判断对比数据是否符合条件
    """
    if stra_name == "对比类型":
        allowed_types = stra_data.get("allowedComparetypes", [])
        current_type = compare_data.get("compareType", "")
        if current_type not in allowed_types:
            return False, f"对比类型不满足 '{current_type}' ->  {allowed_types}"
        return True, ""

    elif stra_name == "价差阈值":
        min_diff = stra_data.get("priceMinDiffThreshold", 0)
        price_diff = compare_data.get("priceDiff", 0)
        # 确保price_diff是数值类型
        try:
            price_diff = int(price_diff) if isinstance(price_diff, str) else price_diff
        except ValueError:
            return True, ""
        if abs(price_diff) <= min_diff:
            return False, f"价差阈值绝对值 {abs(price_diff)} < {min_diff}"
        return True, ""

    elif stra_name == "搜索时间差":  # 新增时间差策略
        max_diff = stra_data.get("listSearchDurationLimit", 0)
        time_diff = compare_data.get("searchTimeDiff", 0)

        # 处理可能的类型异常
        try:
            time_diff = float(time_diff)
        except (TypeError, ValueError):
            return False, f"搜索时间差 '{time_diff}' 不是有效数值"
        if time_diff > max_diff:
            return False, f"搜索时间差 {time_diff} 秒超过阈值 {max_diff}"
        return True, ""
    elif stra_name == "航班日期不一致":
        flight_date = compare_data.get("preDepartureDate", "")
        if flight_date != compare_data.get("surDepartureDate", ""):
            return (
                False,
                f"航班日期不一致: {flight_date} != {compare_data.get('surDepartureDate', '')}",
            )
        return True, ""
    elif stra_name == "价差限制":
        min_diff = stra_data.get("priceDiffSignedThreshold", 0)
        price_diff = compare_data.get("priceDiff", 0)
        if price_diff < min_diff:
            return False, f"价差限制 {price_diff} < {min_diff}"
        return True, ""
    elif stra_name == "航班类型":
        allowed_types = stra_data.get("allowedCompareFlightTypes", [])
        if not allowed_types or ("single" in allowed_types and "connect" in allowed_types):
            return True, ""
        if allowed_types == ["single"]:
            flight_number = compare_data.get("preFlightNo", "")
            if flight_number and len(flight_number.split('/')) > 1:
                return False, f"航班类型不满足: 中转航班 '{flight_number}'"
        if allowed_types == ["connect"]:
            flight_number = compare_data.get("preFlightNo", "")
            if flight_number and len(flight_number.split('/')) <= 1:
                return False, f"航班类型不满足: 单程航班 '{flight_number}'"    
        return True, ""
    # 可扩展其他策略...
    else:
        return True, ""


def merge_reasons(reasons_list):
    seen = set()
    unique_reasons = []
    for reason in reasons_list:
        if reason not in seen:
            seen.add(reason)
            unique_reasons.append(reason)
    return "; ".join(unique_reasons)


def filterValiableTradeIds(compareDatas, filterStrategys):
    valiableTradeIds = set()
    allTradeIds = set()
    notValiableReasons = defaultdict(list)
    valiableDatas = []

    # 遍历每条对比数据
    for data in compareDatas:
        pre_tid = data["preTradeId"]
        sur_tid = data["surTradeId"]
        allTradeIds.update({pre_tid, sur_tid})

        is_valiable = True
        failure_reasons = []

        # 应用所有过滤策略
        for strategy in filterStrategys:
            stra_name = strategy["straName"]
            stra_data = strategy.get("straData", {})

            valid, reason = apply_strategy(data, stra_name, stra_data)
            if not valid:
                is_valiable = False
                failure_reasons.append(f"[{stra_name}]{reason}")

        # 记录结果
        if is_valiable:
            valiableTradeIds.add(pre_tid)
            valiableTradeIds.add(sur_tid)
            valiableDatas.append(data)
        else:
            not_exec_al_reason = "，".join(failure_reasons)
            data["needExecAl"] = "否"
            data["notExecAlReason"] = not_exec_al_reason
            for tid in [pre_tid, sur_tid]:
                notValiableReasons[tid].extend(failure_reasons)

    # 计算被过滤的tradeIds
    notValiableTradeIds = allTradeIds - valiableTradeIds

    # 合并原因说明
    mergedNotValiables = {}
    for tid in notValiableTradeIds:
        if tid in notValiableReasons:
            mergedNotValiables[tid] = merge_reasons(notValiableReasons[tid])
        else:
            mergedNotValiables[tid] = "关联对比数据被过滤"

    return valiableTradeIds, mergedNotValiables, valiableDatas


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "Missing required parameter: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def split_price_changes(groups: dict, group_type: str) -> tuple:
    """拆分价格变化的分组（增强版）"""
    price_change = {}
    no_change = {}

    for key, items in groups.items():
        # 处理航司/航班号分组
        if group_type in ("airline", "flight"):
            # 获取所有唯一价格（字符串形式）
            prices = {str(item["price"]) for item in items}

            if len(prices) > 1:
                price_change[key] = items
            else:
                no_change[key] = {
                    "group_type": group_type,
                    "group_value": key,
                    "flight_nos": list(
                        {item["flightNo"] for item in items}
                    ),  # 去重后的航班号列表
                    "prices": list(prices),  # 保证单值
                }

        # 处理OTA关联数据
        elif group_type == "list2ota":
            ota_price = items["otaPrice"]["price"]
            list_price = items["listPrice"]["price"]

            if ota_price != list_price:
                price_change[key] = items
            else:
                no_change[key] = {
                    "group_type": group_type,
                    "group_value": key,
                    "flight_nos": [items["otaPrice"]["flightNo"]],
                    "prices": [ota_price],
                }

    return price_change, no_change


def generate_no_change_report(no_change_groups: dict) -> str:
    """生成无价格变动报告字符串"""
    if not no_change_groups:
        return ""

    report = []
    for key, group in no_change_groups.items():
        report.append(
            f"结论：无价格变动 | "
            f"分组类型: {group['group_type']} | "
            f"分组值: {group['group_value']}\n"
            f"  涉及航班: {', '.join(group['flight_nos'])}\n"
            f"  稳定价格: {group['prices'][0]}\n"
            f"{'-'*40}"
        )

    return "\n".join(report) + "\n"


def get_airline(flight_no: str) -> str:
    if "/" in flight_no:
        parts = flight_no.split("/")
        return "/".join([p[:2] for p in parts])
    return flight_no[:2]


def group_by_airline(data: List[dict]) -> List[dict]:
    # 第一层分组：按航司
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}

    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")

        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        airline = get_airline(flight_no=flight_no)
        # 初始化航班分组
        if airline not in flight_groups:
            flight_groups[airline] = {}

        # 初始化追踪ID分组
        if tracer_id not in flight_groups[airline]:
            flight_groups[airline][tracer_id] = []

        # 添加原始数据（保留完整字段）
        flight_groups[airline][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append(
                {
                    "groupKey": tracer_key,
                    "groupType": "tradeId",
                    "groupValue": items.copy(),  # 包含完整的原始数据
                }
            )

        result.append(
            {"groupKey": flight_key, "groupType": "airline", "groupValue": tracer_list}
        )

    return result


def group_by_flightno(data: List[dict]) -> List[dict]:
    # 第一层分组：按航班号
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}

    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")

        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]

        # 初始化航班分组
        if flight_no not in flight_groups:
            flight_groups[flight_no] = {}

        # 初始化追踪ID分组
        if tracer_id not in flight_groups[flight_no]:
            flight_groups[flight_no][tracer_id] = []

        # 添加原始数据（保留完整字段）
        flight_groups[flight_no][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append(
                {
                    "groupKey": tracer_key,
                    "groupType": "tradeId",
                    "groupValue": items.copy(),  # 包含完整的原始数据
                }
            )

        result.append(
            {"groupKey": flight_key, "groupType": "flightNo", "groupValue": tracer_list}
        )

    return result


# 方法二：判断是否统一航班号
def is_unified_flight(data: list) -> bool:
    flights = {item["flightNo"] for item in data}
    return len(flights) == 1


# 方法四：处理OTA数据关联
def process_ota_data(data: list) -> dict:
    # 建立list数据索引
    list_data = {item["tradeId"]: item for item in data if item["tSource"] == "list"}

    # 处理OTA数据
    result = {}
    for item in data:
        listTradeId = item.get("listTradeId")
        if item["tSource"] == "ota" and listTradeId is not None:
            list_item = list_data.get(item["listTradeId"])
            result[item["tradeId"]] = {"otaPrice": item, "listPrice": list_item}
    return flatten_grouped_data(result, "list2ota")


def flatten_grouped_data(grouped_data: Dict[str, any], group_type: str) -> List[dict]:
    result = []

    for group_key, groupData in grouped_data.items():
        # 合并所有trace分组下的数据
        merged_data = []
        result.append(
            {"groupKey": group_key, "groupType": group_type, "groupValue": groupData}
        )

    return result


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False


from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


def getFlightGroupListPrices(flightPriceGroup: List[Dict]) -> Dict[str, List[Dict]]:
    """
    方法1: 获取每个航班号下所有list类型价格数据，并按时间排序

    参数:
    flightPriceGroup: 原始航班价格分组数据

    返回:
    {
        "MU9807": [sorted list prices...],
        "MU4890": [sorted list prices...]
    }
    """
    result = {}

    # 遍历航班分组
    for flight_group in flightPriceGroup:
        flight_no = flight_group["groupKey"]
        list_prices = []

        # 遍历tradeId分组
        for trade_group in flight_group["groupValue"]:
            # 筛选tSource=list的数据
            for price in trade_group["groupValue"]:
                if price.get("tSource") == "list":
                    list_prices.append(price)

        # 按searchDateTime排序
        if list_prices:
            list_prices.sort(
                key=lambda x: datetime.strptime(
                    x["searchDateTime"], "%Y-%m-%d %H:%M:%S"
                )
            )
            result[flight_no] = list_prices

    return result


def try_get_float(value):
    """尝试将值转换为浮点数，排除布尔类型"""
    if isinstance(value, bool):
        return None
    if isinstance(value, (int, float)):
        return float(value)
    elif isinstance(value, str):
        try:
            return float(value)
        except ValueError:
            return None
    else:
        return None


def fieldEquals(ota_value: Any, list_value: Any) -> bool:
    """严格字段值比对方法（不进行类型转换）"""
    # 处理空值逻辑
    if ota_value is None and list_value is None:
        return True
    if ota_value is None or list_value is None:
        return False

    # 处理字符串类型（去除首尾空格）
    if isinstance(ota_value, str) and isinstance(list_value, str):
        return ota_value.strip() == list_value.strip()

    # 尝试将两个参数都转为浮点数
    a_float = try_get_float(ota_value)
    b_float = try_get_float(list_value)

    # 两个都能转浮点数时比较数值
    if a_float is not None and b_float is not None:
        return a_float == b_float

    # 严格类型和值匹配
    return ota_value == list_value


def filterOtaPricesByFiledValue(
    ota_prices: List[Dict], list_price: Dict, field: str
) -> List[Dict]:
    """按字段值过滤（严格模式）"""
    target_value = list_price.get(field)

    # 精确匹配分组
    exact_matches = []
    non_exact = []

    for ota in ota_prices:
        ota_value = ota.get(field)
        if fieldEquals(ota_value, target_value):
            exact_matches.append(ota)
        else:
            non_exact.append(ota)

    # 优先返回精确匹配，否则保持原顺序
    return exact_matches if exact_matches else non_exact


def findBestMatchOtaPrice(flightPriceGroup: List[Dict], listPrice: Dict) -> List[Dict]:
    """
    查找与list价格最匹配的ota价格，确保每个listTradeId只选择一个最合适的OTA报价

    Args:
        flightPriceGroup: 原始数据
        listPrice: 要匹配的list价格条目

    Returns:
        Dict: 最匹配的OTA价格数据，如果没有匹配则返回None
    """
    # 需要过滤的特定tag列表
    filtered_tags = {
        "TOU1",
        "FXD1",
        "PTJ1",
        "WYY1",
        "SLN1",
        "SLP1",
        "CZB1",
        "SLD1",
        "SLG1",
        "BYJ1",
        "GPP1",
        "STU1",
        "YHU1",
        "CLP1",
        "LHO1",
        "LHS1",
        "LHN1",
        "LHO1",
        "LHT1",
        "LHX1",
        "LHM1",
        "LHE1",
        "HUJ1",
        "JTP1",
        "SOA1",
        "AGE1",
        "OLD1",
        "OKR1",
        "OKK1",
        "HUB1",
        "ZZM1",
        "HUL1",
        "XFQ1",
        "XFL1",
        "XFZ1",
        "XFA1",
        "XLE1",
        "QHY1",
        "GSX1",
        "HET1",
        "LNF1",
        "JCY1",
        "JCB1",
        "ZHZ1",
        "KFW1",
        "HYC1",
        "NQX1",
        "XFF1",
        "TTD1",
        "SLB1",
    }

    # 步骤1: 获取目标航班分组
    target_flight = next(
        (fg for fg in flightPriceGroup if fg["groupKey"] == listPrice["flightNo"]), None
    )
    if not target_flight:
        return None

    # 步骤2: 按traceId分组筛选符合条件的ota数据
    trace_ota_groups = defaultdict(list)
    for trade_group in target_flight["groupValue"]:
        for price in trade_group["groupValue"]:
            # 筛选条件：必须是OTA数据，且listTradeId匹配，且tag不在过滤列表中
            if (
                price.get("tSource") == "ota"
                and price.get("listTradeId")
                and price["listTradeId"] == listPrice.get("tradeId")
                and price.get("tag") not in filtered_tags  # 添加tag过滤条件
            ):
                trace_ota_groups[price.get("tradeId")].append(price)

    if not trace_ota_groups:
        return None

    # 步骤3: 对每个traceId组选择最佳报价
    best_ota = None
    best_priority = float("inf")
    list_price = float(listPrice.get("price", 0))

    bestMatchOtaPrices = []

    for trace_otas in trace_ota_groups.values():
        # 对当前traceId组内的报价按优先级排序
        def match_priority(ota_item: Dict) -> int:
            """定义匹配优先级"""
            # 第一优先级: tag精确匹配且价格相同
            if ota_item["tag"] == listPrice["tag"] and fieldEquals(
                ota_item.get("price"), listPrice.get("price")
            ):
                return 1
            # 第二优先级: tag精确匹配但价格不同
            elif ota_item["tag"] == listPrice["tag"]:
                return 2
            # 第三优先级: 交叉匹配且价格相同
            elif (
                ota_item["tag"] == listPrice.get("oriTag")
                or ota_item.get("oriTag") == listPrice["tag"]
                or ota_item.get("oriTag") == listPrice.get("oriTag")
            ) and fieldEquals(ota_item.get("price"), listPrice.get("price")):
                return 3
            # 第四优先级: 交叉匹配但价格不同
            elif (
                ota_item["tag"] == listPrice.get("oriTag")
                or ota_item.get("oriTag") == listPrice["tag"]
                or ota_item.get("oriTag") == listPrice.get("oriTag")
            ):
                return 4
            # 第五优先级: 其他情况
            else:
                return 5

        # 按优先级排序
        trace_otas.sort(key=match_priority)

        # 在相同优先级组内，按价格接近度排序
        current_best = trace_otas[0]
        bestMatchOtaPrices.append(current_best)
        # current_priority = match_priority(current_best)

        # if current_priority < best_priority:
        #    best_priority = current_priority
        #    best_ota = current_best
        # elif current_priority == best_priority:
        # 如果优先级相同，选择价格更接近的
        # current_price_diff = abs(float(current_best.get("price", 0)) - list_price)
        # best_price_diff = abs(float(best_ota.get("price", 0)) - list_price)
        # if current_price_diff < best_price_diff:
        #    best_ota = current_best

    return bestMatchOtaPrices


def getFlightAllListOtaPairs(
    listPrices: Dict[str, List[Dict]], flightPriceGroup: List[Dict]
) -> Dict[str, List[Tuple[Dict, Dict]]]:
    """
    方法3: 获取所有航班list-ota配对，确保每个list的traceId只选择一个最佳OTA报价

    参数:
    listPrices: 方法1的返回结果
    flightPriceGroup: 原始数据

    返回:
    {
        "MU9807": [
            (listPrice1, otaPrice1),
            (listPrice2, otaPrice2),
            ...
        ]
    }
    """
    result = {}
    # 用于记录已处理的list traceId
    processed_list_trace_ids = set()
    # write_json_to_file(
    #     listPrices,
    #     file_path="urs_multi_search/data/listPrices.json",
    # )

    for flight_no, prices in listPrices.items():
        pairs = []
        for list_price in prices:
            # 获取list的traceId
            list_trace_id = list_price.get("tradeId")

            # 如果该traceId已经处理过，跳过
            if list_trace_id in processed_list_trace_ids:
                continue

            # 在原始数据中寻找最佳匹配的OTA价格

            matchOtaPrices = findBestMatchOtaPrice(flightPriceGroup, list_price)
            if matchOtaPrices is not None and len(matchOtaPrices) > 0:
                # 当找到匹配时，组成配对元组
                for matchOtaPrice in matchOtaPrices:
                    pairs.append((list_price, matchOtaPrice))
                # 记录已处理的traceId
                processed_list_trace_ids.add(list_trace_id)

        # 即使没有匹配项也保留空列表
        if len(pairs) > 0:
            result[flight_no] = pairs
    # write_json_to_file(
    #     result,
    #     file_path="urs_multi_search/data/listOtaPairPrices.json",
    # )
    return result


from datetime import datetime
from typing import List, Dict, Tuple


def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue


def genPriceCompareRow(
    preSearchPrice: Dict,
    surSearchPrice: Dict,
    compareType: str,
    oriQe: str,
    templatePrompt: str,
) -> Dict:
    """
    方法4: 生成单行价格对比记录

    参数示例：
    preSearchPrice = {
        'flightNo': 'MU9807', 'departureDate': '2023-03-20',
        'price': 800, 'cabin': '经济舱', 'searchTime': '2023-03-20T10:00:00',
        'tag': '促销', 'oriTag': '早鸟票'
    }
    compareType = 'list-ota'
    """
    # 基础字段提取
    base_fields = {
        "compareId": generate_compare_id(),
        "preFlightNo": preSearchPrice.get("flightNo"),
        "surFlightNo": surSearchPrice.get("flightNo"),
        "preDepartureDate": preSearchPrice.get("departureDate"),
        "surDepartureDate": surSearchPrice.get("departureDate"),
        "preDepTime": preSearchPrice.get("depTime"),
        "surDepTime": surSearchPrice.get("depTime"),
        "departureCity": preSearchPrice.get("departureCity"),  # 假设城市信息相同
        "arrivalCity": preSearchPrice.get("arrivalCity"),
        "compareType": compareType,
        "preTradeId": preSearchPrice.get("tradeId"),
        "surTradeId": surSearchPrice.get("tradeId"),
        "preCabin": preSearchPrice.get("cabin"),
        "surCabin": surSearchPrice.get("cabin"),
        "needExecAl": "是",
        "notExecAlReason": "",
    }

    time_diff = (
        parse_search_time(surSearchPrice["searchDateTime"])
        - parse_search_time(preSearchPrice["searchDateTime"])
    ).total_seconds()

    # 价格计算逻辑
    price_diff = convert_price_to_numeric(
        surSearchPrice.get("price", 0)
    ) - convert_price_to_numeric(preSearchPrice.get("price", 0))

    # Tag交叉匹配逻辑
    tag_fields = {
        "preTag": preSearchPrice.get("tag"),
        "preOriTag": preSearchPrice.get("oriTag"),
        "surTag": surSearchPrice.get("tag"),
        "surOriTag": surSearchPrice.get("oriTag"),
    }
    tag_is_same = any(
        [
            tag_fields["preTag"] == tag_fields["surTag"],
            tag_fields["preTag"] == tag_fields["surOriTag"],
            tag_fields["preOriTag"] == tag_fields["surTag"],
            tag_fields["preOriTag"] == tag_fields["surOriTag"],
        ]
    )

    return {
        **base_fields,
        "preSearchTime": preSearchPrice["searchDateTime"],
        "surSearchTime": surSearchPrice["searchDateTime"],
        "searchTimeDiff": time_diff,
        "prePrice": preSearchPrice.get("price"),
        "surPrice": surSearchPrice.get("price"),
        "priceDiff": price_diff,
        **tag_fields,
        "tagIsSame": str(tag_is_same),
        "comparePrompt": genPriceComparePrompt(
            preSearchPrice,
            surSearchPrice,
            oriQe=oriQe,
            compare_type=compareType,
            templatePrompt=templatePrompt,
        ),
    }


def genListPriceCompareRows(
    listprices: List[Dict],
    oriQe: str,
    templatePrompt: str,
    searchTimeDurationLimit: int = None,
) -> List[Dict]:
    """
    方法5: 生成列表价格对比记录

    示例输入：
    [
        {'searchDateTime': '2023-03-20 09:00:00', 'price': 800},
        {'searchDateTime': '2023-03-20 09:05:00', 'price': 790},
        {'searchDateTime': '2023-03-20 10:00:00', 'price': 810}
    ]
    """
    if len(listprices) <= 1:
        return []

    results = []
    for i in range(len(listprices) - 1):
        pre = listprices[i]
        sur = listprices[i + 1]

        # 计算时间间隔（秒）
        delta = (
            parse_search_time(sur["searchDateTime"])
            - parse_search_time(pre["searchDateTime"])
        ).total_seconds()

        # 时间剪枝逻辑
        if searchTimeDurationLimit and delta > searchTimeDurationLimit:
            continue

        results.append(genPriceCompareRow(pre, sur, "list-list", oriQe, templatePrompt))

    return results


def genListOtaPriceCompareRows(
    listOtaPairs: List[Tuple[Dict, Dict]], oriQe: str, templatePrompt: str
) -> List[Dict]:
    """
    方法6: 生成List-OTA价格对比记录

    示例输入：
    [
        (list_price1, ota_price1),
        (list_price2, ota_price2)
    ]
    """
    return [
        genPriceCompareRow(list_p, ota_p, "list-ota", oriQe, templatePrompt)
        for list_p, ota_p in listOtaPairs
    ]


def genAllPriceCompareRows(
    list_prices: Dict[str, List[Dict]],
    listOtaPricesPairs: Dict[str, List[Tuple[Dict, Dict]]],
    oriQe: str,
    templatePrompt: str,
    searchTimeDurationLimit: int = None,
) -> List[Dict]:
    all_rows = []

    list_prices = list_prices or {}
    listOtaPricesPairs = listOtaPricesPairs or {}

    # 使用union方法替代|操作符
    valid_flight_nos = set(list_prices.keys()).union(listOtaPricesPairs.keys())

    # 按航班号分组处理
    for flight_no in valid_flight_nos:
        flight_rows = []

        # 处理list-list对比（同航班价格波动）
        if flight_no in list_prices:
            sorted_prices = list_prices[flight_no]
            list_compare = genListPriceCompareRows(
                sorted_prices, oriQe, templatePrompt, searchTimeDurationLimit
            )
            flight_rows.extend(list_compare)

        # 处理list-ota对比（同航班渠道差异）
        if flight_no in listOtaPricesPairs:
            pairs = listOtaPricesPairs[flight_no]
            ota_compare = genListOtaPriceCompareRows(
                pairs, oriQe=oriQe, templatePrompt=templatePrompt
            )
            flight_rows.extend(ota_compare)

        all_rows.extend(flight_rows)

    return all_rows


# 需实现的提示生成函数（示例）
def genPriceComparePrompt(
    pre: Dict, sur: Dict, oriQe: str, compare_type: str, templatePrompt: str
) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    preData = json.dumps(pre, ensure_ascii=False, indent=None)  # 紧凑格式
    surData = json.dumps(sur, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "preData": preData,
        "surData": surData,
        "compareType": compare_type,
        "question": oriQe,
    }

    try:
        formatter = Formatter()
        required_fields = [fn for _, fn, _, _ in formatter.parse(templatePrompt) if fn]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return templatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def genPrompt(soarTemplatePrompt, oriQe, priceData) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    param = json.dumps(priceData, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "param": param,
        "question": oriQe,
    }

    try:
        formatter = Formatter()
        required_fields = [
            fn for _, fn, _, _ in formatter.parse(soarTemplatePrompt) if fn
        ]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return soarTemplatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None


def filterFlightTracedIdSizeLessThanAssignCount(
    flightGroupPrices: List,
    merged: Dict[str, str],
    assignCount,
    whiteFlightNumberList: List,
):
    filtered_flight_groups = []
    filtered_flight_numbers = []
    filtered_price_count = 0
    allPriceCount = 0

    white_flight_set = set(whiteFlightNumberList or [])

    for flight_group in flightGroupPrices:
        flight_number = flight_group["groupKey"]
        trade_groups = flight_group["groupValue"]
        trade_group_count = len(trade_groups)
        total_prices = sum(len(tg["groupValue"]) for tg in trade_groups)

        allPriceCount += total_prices
        # 判断过滤条件
        if (trade_group_count <= assignCount) and (
            flight_number not in white_flight_set
        ):
            filtered_flight_numbers.append(flight_number)
            filtered_price_count += total_prices
            # 如果航班号下只有一个tradeId，将其添加到merged字典中
            trade_id = trade_groups[0]["groupKey"]
            if trade_id not in merged:
                merged[trade_id] = "该航班报价数据量不够" + str(assignCount)
        else:
            filtered_flight_groups.append(flight_group)

    return (
        filtered_flight_groups,
        filtered_flight_numbers,
        allPriceCount,
        filtered_price_count,
    )


def filterFlightGroupsDataByNotValiableTradeIds(
    flight_groups: List[Dict], valiableTradeIds: List[str], merged: Dict[str, str]
) -> List[Dict]:
    """
    Filter flight groups to only keep those that have valid trade IDs from the merged list.

    Args:
        flight_groups: List of flight group dictionaries
        merged: List of valid trade IDs to keep

    Returns:
        List of filtered flight group dictionaries
    """
    filtered_groups = []

    for group in flight_groups:
        # Get all trade IDs in this group
        trade_groups = group.get("groupValue", [])
        valid_trade_groups = []

        for trade_group in trade_groups:
            trade_id = trade_group.get("groupKey")
            if trade_id in valiableTradeIds:
                valid_trade_groups.append(trade_group)
            elif trade_id not in merged:
                merged[trade_id] = "该航班报价无可对比报价"

        # Only keep groups that have at least one valid trade ID
        if valid_trade_groups:
            filtered_group = group.copy()
            filtered_group["groupValue"] = valid_trade_groups
            filtered_groups.append(filtered_group)

    return filtered_groups


def main(param: dict) -> dict:
    try:
        data, parseStatus = parse_urlencoded_structured_data(param, "param")
        oriQe = param.get("question")
        oneToOnetemplatePrompt = param.get("oneToOnetemplatePrompt")
        searchTimeDurationLimit = param.get("listSearchDurationLimit")
        listSearchDurationLimit = convert_to_seconds(searchTimeDurationLimit)
        soarTemplatePrompt = param.get("soarTemplatePrompt")

        if parseStatus["status"] != "success":
            return {
                "status": 404,
                "errMsg": parseStatus["message"],
                "prompt": "",
                "allCompareRows": [],
                "valiableCompareDatas": [],
                "notValiableTradeReasons": {},
                "needExecAl": "否",
                "notExecAlReason": "入参解析失败，未获取到有效数据",
                "processStage": "变价数据分组和过滤",
            }

        # 方法三调用
        flight_groups = group_by_flightno(data)
        if len(flight_groups) == 0:
            return {
                "status": 200,
                "errMsg": "无航班维度价格分组数据！",
                "prompt": "无航班维度价格分组数据，无需生成提示词！",
                "allCompareRows": [],
                "valiableCompareDatas": [],
                "notValiableTradeReasons": {},
                "needExecAl": "否",
                "notExecAlReason": "入参数据为空，无航班维度价格分组数据",
                "processStage": "变价数据分组和过滤",
            }

        # 执行方法1
        list_prices = getFlightGroupListPrices(flight_groups)

        # 执行方法3
        listOtaPricesPairs = getFlightAllListOtaPairs(list_prices, flight_groups)

        allCompareRows = genAllPriceCompareRows(
            list_prices,
            listOtaPricesPairs,
            oriQe,
            oneToOnetemplatePrompt,
            listSearchDurationLimit,
        )

        # 构建策略参数
        strageParams = {}

        # 添加搜索时间差限制
        if listSearchDurationLimit is not None and listSearchDurationLimit > 0:
            strageParams["listSearchDurationLimit"] = listSearchDurationLimit

        # 添加允许的对比类型
        allowedComparetypes = param.get("allowedComparetypes")
        if allowedComparetypes is not None and isinstance(allowedComparetypes, str):
            strageParams["allowedComparetypes"] = allowedComparetypes.strip().split(",")

        # 添加最小价差阈值
        priceMinDiffThreshold = param.get("priceMinDiffThreshold")
        minPriceDiff = convert_price_to_numeric(priceMinDiffThreshold)
        if minPriceDiff is not None and isinstance(minPriceDiff, (int, float)):
            strageParams["priceMinDiffThreshold"] = minPriceDiff
        
        # 添加价格差异阈值（值对比，不取绝对值）
        priceDiffSignedThreshold = param.get("priceDiffSignedThreshold")
        diffSignedThreshold = convert_price_to_numeric(priceDiffSignedThreshold)
        if diffSignedThreshold is not None and isinstance(diffSignedThreshold, (int, float)):
            strageParams["priceDiffSignedThreshold"] = diffSignedThreshold
        
        # 添加允许的航班类型
        allowedCompareFlightTypes = param.get("allowedCompareFlightTypes")
        if allowedCompareFlightTypes is not None and isinstance(allowedCompareFlightTypes, str):
            strageParams["allowedCompareFlightTypes"] = allowedCompareFlightTypes.strip().split(",")


        filterStrategys = generate_strategies(strageParams)

        valiableTradeIds, merged, valiableCompareDatas = filterValiableTradeIds(
            allCompareRows, filterStrategys
        )

        filtered_flight_groups = filterFlightGroupsDataByNotValiableTradeIds(
            flight_groups, valiableTradeIds, merged
        )

        (
            filtered_flight_groups,
            filtered_flight_numbers,
            allPriceCount,
            filtered_price_count,
        ) = filterFlightTracedIdSizeLessThanAssignCount(
            filtered_flight_groups, merged, 1, []
        )
        prompt = ""
        if len(valiableCompareDatas) == 0:
            return {
                "status": 200,
                "errMsg": "执行过滤规则后无有效数据,请确认用户反馈是否有偏差!",
                "prompt": "",
                "allCompareRows": allCompareRows,
                "valiableCompareDatas": valiableCompareDatas,
                "notValiableTradeReasons": merged,
                "needExecAl": "否",
                "notExecAlReason": "执行过滤规则后无有效数据,请确认用户反馈是否有偏差",
                "processStage": "变价数据分组和过滤",
            }
        prompt = genPrompt(soarTemplatePrompt, oriQe, filtered_flight_groups)
        finalData = {
            "status": 200,
            "errMsg": "",
            "prompt": prompt,
            "allCompareRows": allCompareRows,
            "valiableCompareDatas": valiableCompareDatas,
            "notValiableTradeReasons": merged,
            "needExecAl": "是",
            "notExecAlReason": "",
            "processStage": "变价数据分组和过滤",
        }
        return finalData
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        result = {
            "status": 404,
            "errMsg": f"生成提示词异常！: {e}",
            "prompt": "",
            "allCompareRows": [],
            "valiableCompareDatas": [],
            "notValiableTradeReasons": {},
            "needExecAl": "否",
            "notExecAlReason": "入参缺失关键字段",
            "processStage": "变价数据分组和过滤",
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "status": 404,
            "errMsg": f"生成提示词异常！: {e}",
            "prompt": "",
            "allCompareRows": [],
            "valiableCompareDatas": [],
            "notValiableTradeReasons": {},
            "needExecAl": "否",
            "notExecAlReason": "处理过程发生异常",
            "processStage": "变价数据分组和过滤",
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# 使用示例
if __name__ == "__main__":

    input = {
        "priceMinDiffThreshold": 0,
        "priceDiffSignedThreshold": 0,
        "allowedComparetypes": "list-list,list-ota",
        "allowedCompareFlightTypes":"single",
        "param": "tradeId%3Aops_slugger_250311.201345.10.95.136.64.1237529.502715504_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A13%3A45%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3A%23%2A%23productMark%3A%23%2A%23cabin%3A%23%2A%23packagePrice%3Anull%23%2A%23basePrice%3Anull%23%2A%23viewPrice%3Anull%23%2A%23policyId%3Anull%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A%23%2A%23allGoodItemPrice%3Anull%23%2A%23listTradeId%3A%23%2A%23depTime%3A%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.201236.10.95.136.63.3818336.7739754874_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A12%3A36%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741695077%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.201236.10.95.136.63.3818336.7739754874_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A12%3A36%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741695077%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.201147.10.95.140.64.979845.6169679901_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A11%3A47%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741695077%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.201147.10.95.140.64.979845.6169679901_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A11%3A47%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741695077%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.201043.10.95.133.43.3972441.9377865309_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A10%3A43%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741694912%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200858.10.95.133.55.3498385.888557073_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A08%3A58%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741694912%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200855.10.95.136.59.3945914.2240802216_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200858.10.95.133.55.3498385.888557073_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3ATSA1%23%2A%23searchDateTime%3A2025-03-11%2020%3A08%3A58%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A386%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741694912%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200855.10.95.136.59.3945914.2240802216_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200855.10.95.136.59.3945914.2240802216_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A08%3A55%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A381%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgnd09000%23%2A%23productMark%3A201%23%2A%23cabin%3AL%23%2A%23packagePrice%3A386%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5172774584%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A398%23%2A%23CPT%3A1741694912%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200812.10.95.133.43.111900.8614205445_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A08%3A12%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200752.10.95.133.43.111900.8008372240_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A52%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200749.10.95.133.35.3535687.2901482416_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200747.10.95.133.35.3535687.549247466_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200749.10.95.133.35.3535687.2901482416_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A390%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200747.10.95.133.35.3535687.549247466_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200747.10.95.133.35.3535687.549247466_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A47%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200746.10.95.133.35.3535687.1598949789_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A46%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694850%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200729.10.95.133.35.3535687.342958298_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694844%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200729.10.95.133.35.3535687.6946784843_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694844%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200724.10.95.133.35.3535687.9208248389_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A07%3A24%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741694844%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200632.10.95.133.35.758261.6201045488_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741693972%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200632.10.95.133.35.758261.6201045488_1%23%2A%23oriTag%3ATTL1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A384%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd04074%23%2A%23productMark%3A500%23%2A%23cabin%3AL%23%2A%23packagePrice%3A399%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A96551850%23%2A%23autoPriceDecreaseAmount%3A18.0%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1741694750%23%2A%23allGoodItemPrice%3A13%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200632.10.95.133.35.758261.6201045488_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ATTL1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A384%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd04074%23%2A%23productMark%3A500%23%2A%23cabin%3AL%23%2A%23packagePrice%3A399%23%2A%23basePrice%3A386%23%2A%23viewPrice%3A400%23%2A%23policyId%3A96551850%23%2A%23autoPriceDecreaseAmount%3A18.0%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1741694750%23%2A%23allGoodItemPrice%3A13%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200632.10.95.133.35.758261.6201045488_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ACLV1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A383%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd04074%23%2A%23productMark%3A-1%23%2A%23cabin%3AL%23%2A%23packagePrice%3A398%23%2A%23basePrice%3A387.9%23%2A%23viewPrice%3A400%23%2A%23policyId%3A96551850%23%2A%23autoPriceDecreaseAmount%3A16.1%23%2A%23secondPrice%3A388%23%2A%23CPT%3A1741694750%23%2A%23allGoodItemPrice%3A10%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200632.10.95.133.35.758261.6201045488_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ATYL1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A100%23%2A%23cabin%3AL%23%2A%23packagePrice%3A400%23%2A%23basePrice%3A400%23%2A%23viewPrice%3A400%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741693972%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200628.10.95.136.62.1853574.6056147646_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A28%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741693972%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200620.10.95.136.62.1853574.8894963091_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A20%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741693972%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.200604.10.95.140.64.1946550.7623069261_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2020%3A06%3A04%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741693972%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193747.10.90.5.105.1651693.6706300844_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A37%3A47%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193747.10.90.5.105.1651693.6706300844_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A37%3A47%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193740.10.90.5.84.1390030.8996969568_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A37%3A40%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193456.10.95.133.56.313792.846685798_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A34%3A56%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193431.10.90.5.81.122327.2547668832_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A34%3A31%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3Aname%3D%E5%88%98%E4%BA%91%E5%96%9C%26ageType%3D0%26cardNo%3D220622197109062517%3Bname%3D%E5%AD%9F%E5%87%A1%E9%A6%99%26ageType%3D0%26cardNo%3D220622197006052527%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.193422.10.90.75.73.4127904.9988362770_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2019%3A34%3A22%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A370%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741692004%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.171634.10.95.133.41.2006503.9911182626_1%23%2A%23oriTag%3ATTL1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-11%2017%3A16%3A34%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A384%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E7%BA%AA%E5%90%9B%26ageType%3D0%26cardNo%3D220622199304102528%23%2A%23wrapperId%3Attsgnd03064%23%2A%23productMark%3A500%23%2A%23cabin%3AL%23%2A%23packagePrice%3A399%23%2A%23basePrice%3A387.1%23%2A%23viewPrice%3A400%23%2A%23policyId%3A574649365%23%2A%23autoPriceDecreaseAmount%3A10.9%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1741684354%23%2A%23allGoodItemPrice%3A11.9%23%2A%23listTradeId%3Aops_slugger_250311.171632.10.95.133.41.2006503.9446778484_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.171634.10.95.133.41.2006503.9911182626_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ATTL1%23%2A%23searchDateTime%3A2025-03-11%2017%3A16%3A34%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A384%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E7%BA%AA%E5%90%9B%26ageType%3D0%26cardNo%3D220622199304102528%23%2A%23wrapperId%3Attsgnd03064%23%2A%23productMark%3A500%23%2A%23cabin%3AL%23%2A%23packagePrice%3A399%23%2A%23basePrice%3A387.1%23%2A%23viewPrice%3A400%23%2A%23policyId%3A574649365%23%2A%23autoPriceDecreaseAmount%3A10.9%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1741684354%23%2A%23allGoodItemPrice%3A11.9%23%2A%23listTradeId%3Aops_slugger_250311.171632.10.95.133.41.2006503.9446778484_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.171634.10.95.133.41.2006503.9911182626_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ACLV1%23%2A%23searchDateTime%3A2025-03-11%2017%3A16%3A34%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A383%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E7%BA%AA%E5%90%9B%26ageType%3D0%26cardNo%3D220622199304102528%23%2A%23wrapperId%3Attsgnd00060%23%2A%23productMark%3A-1%23%2A%23cabin%3AL%23%2A%23packagePrice%3A398%23%2A%23basePrice%3A388%23%2A%23viewPrice%3A400%23%2A%23policyId%3A4408708204%23%2A%23autoPriceDecreaseAmount%3A12.0%23%2A%23secondPrice%3A389%23%2A%23CPT%3A1741684455%23%2A%23allGoodItemPrice%3A10%23%2A%23listTradeId%3Aops_slugger_250311.171632.10.95.133.41.2006503.9446778484_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.171634.10.95.133.41.2006503.9911182626_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3A15%23%2A%23tag%3ATYL1%23%2A%23searchDateTime%3A2025-03-11%2017%3A16%3A34%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E5%88%98%E7%BA%AA%E5%90%9B%26ageType%3D0%26cardNo%3D220622199304102528%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A100%23%2A%23cabin%3AL%23%2A%23packagePrice%3A400%23%2A%23basePrice%3A400%23%2A%23viewPrice%3A400%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741682871%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.171632.10.95.133.41.2006503.9446778484_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.105143.10.95.133.55.198118.994061278_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2010%3A51%3A43%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741660955%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250311.105135.10.95.133.37.3233379.7002620827_1%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.105135.10.95.133.37.3233379.7002620827_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2010%3A51%3A35%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3A%23%2A%23productMark%3A%23%2A%23cabin%3A%23%2A%23packagePrice%3Anull%23%2A%23basePrice%3Anull%23%2A%23viewPrice%3Anull%23%2A%23policyId%3Anull%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A%23%2A%23allGoodItemPrice%3Anull%23%2A%23listTradeId%3A%23%2A%23depTime%3A%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.104126.10.95.136.64.787720.2370849897_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2010%3A41%3A26%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3A%23%2A%23productMark%3A%23%2A%23cabin%3A%23%2A%23packagePrice%3Anull%23%2A%23basePrice%3Anull%23%2A%23viewPrice%3Anull%23%2A%23policyId%3Anull%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A%23%2A%23allGoodItemPrice%3Anull%23%2A%23listTradeId%3A%23%2A%23depTime%3A%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.104033.10.95.133.39.1286630.3013423457_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2010%3A40%3A33%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741660586%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250311.102932.10.90.5.105.1170262.97582128_1%23%2A%23oriTag%3AHUJ1%23%2A%23departureDate%3A2025-03-17%23%2A%23coupon%3Anull%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-11%2010%3A29%3A32%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ATYN%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC2268%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3ACGQ%23%2A%23price%3A385%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CPRICE_ASC%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndysc01%23%2A%23productMark%3A1211%23%2A%23cabin%3AL%23%2A%23packagePrice%3A390%23%2A%23basePrice%3A390%23%2A%23viewPrice%3A390%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1741659642%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A10%23%2A%23username%3A0929mengfei%7E%7E%2A%7E%7E",
        "question": "【多次搜索变价】用户（本机）于2025-03-11 20:07:29搜索CGQ到TYN航班， 起飞时间为2025-03-17 航班号为：SC2268，L-D页报价由370上涨到380多",
        "listSearchDurationLimit": "30m",
        "oneToOnetemplatePrompt": "帮我对比新老数据价格是否变化，如果变化请进行变化归因\n对比类型：{compareType}\n用户问题：{question}\n原数据：\n{preData}\n新数据：\n{surData}\n",
        "soarTemplatePrompt": "你是一名资深的机票行业运营，现在用户反馈机票搜索有变价，请结合用户反馈问题描述，搜索数据明细，搜索业务规则，变价场景和变价分析规则进行搜索变价分析\n# 变价分析目标\n搜索变价分析目标：根据用户的反馈question，搜索数据明细，搜索业务规则，变价场景和变价分析规则分析是否发生用户反馈的变价，并且进行变价归因和变价定责\n#入参\n##：入参说明\nquestion参数： 用户反馈的信息\nparam 参数：结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组\n##：入参明细\nquestion: {question}\nparam: {param}\n",
    }
    # print(json.dumps(input, indent=2, ensure_ascii=False))

    finalData = main(input)
    print("执行结果：" + json.dumps(finalData, indent=2, ensure_ascii=False))
    write_json_to_file(
        finalData,
        file_path="urs_multi_search/data/allCompareRows.json",
    )
