import json
import requests
import sys
from urllib.parse import urlparse

def download_file_from_url(url):
    """Download file from URL and return the binary content and filename"""
    try:
        response = requests.get(url)
        if response.status_code == 200:
            # Get filename from URL
            filename = urlparse(url).path.split('/')[-1]
            if not filename:
                filename = "downloaded_file.png"
            return response.content, filename
        else:
            raise Exception(f"Failed to download file from {url}. Status code: {response.status_code}")
    except Exception as e:
        return {
            'error': str(e),
            'status_code': 500
        }

def upload_file_to_endpoint(file_content, filename):
    """Upload file to the Java endpoint"""
    url = "http://pangunew.corp.qunar.com/maintenace/qa/fileOpr/bpmFileUpload"
    
    # Simplify filename to avoid issues (use a simple name)
    simple_filename = "test_image.png"
    
    # Content type for PNG
    content_type = 'image/png'
    
    # Create a proper multipart form data request
    files = {
        'file': (simple_filename, file_content, content_type)
    }

    # 使用单引号包裹Cookie字符串，避免双引号冲突
    cookie = 'QN1=0001080017506e3629306da6; login_name=yaowyw.wang'

    # 构造请求的headers，设置cookie
    headers = {
        "Cookie": cookie,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        
        response = requests.post(url, files=files, headers=headers)
        
        
        if response.status_code != 200:
            return {
                'status_code': response.status_code,
                'text': response.text,
                'headers': dict(response.headers),
                'error': 'Upload failed'
            }
            
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError:
            return {
                'status_code': response.status_code,
                'text': response.text,
                'headers': dict(response.headers)
            }
    except Exception as e:
        return {
            'error': str(e),
            'status_code': 500
        }

def process_url_and_upload(image_url):
    """Download image from URL and upload it to the endpoint"""
    try:
        # Extract actual URL if it starts with @
        if image_url.startswith('@'):
            image_url = image_url[1:]
            
        # Download the file
        file_content, filename = download_file_from_url(image_url)
        
        # Upload the file
        result = upload_file_to_endpoint(file_content, filename)
        
        # Print results with original URL for reference
        return result
    except Exception as e:
        return {
            'error': str(e),
            'status_code': 500
        }

# 修改主函数，使其总是尝试使用提供的URL，如果没有提供URL则使用默认URL
if __name__ == "__main__":
    # 默认使用一个真实图片的URL
    default_url = "https://fuwu.qunar.com/orderview/upload/queryFile/VYVYGHgN1yA828T4gQ8qiInT8VmRUglkBwQ39m7__ZDV_J_3c1f7cZdmaWIgbA5U5pqG_t4y6BPOo9pM-_iEqkf-Xff3uzBq8MiCnevDO_d5m4rCJQI1UCHDXGSJF0-Y7BsyAUN2ILgdWlr8Bm9mjfIhmuYfp63Gc37cM4D9d4C1enRtbdvPJNLFo6h4SMWxzUbIozRmKBh4JduutpobWHp4vJOE0vNiu4MaLCFNvQE.png"
    
    # Check if a URL is provided as a command-line argument
    if len(sys.argv) > 1:
        image_url = sys.argv[1]
        result = process_url_and_upload(image_url)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        # Use default URL
        result = process_url_and_upload(default_url)
        print(json.dumps(result, ensure_ascii=False, indent=2))
