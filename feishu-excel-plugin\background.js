// 后台脚本，处理插件的后台任务

// 存储认证信息
let authInfo = {
  token: null,
  expireTime: null
};

// 初始化时从存储中加载认证信息
chrome.storage.local.get(['feishuToken', 'feishuTokenExpire'], (result) => {
  if (result.feishuToken) {
    authInfo.token = result.feishuToken;
    authInfo.expireTime = result.feishuTokenExpire;

    // 检查令牌是否过期
    if (authInfo.expireTime && Date.now() > authInfo.expireTime) {
      // 令牌已过期，清除存储
      chrome.storage.local.remove(['feishuToken', 'feishuTokenExpire']);
      authInfo.token = null;
      authInfo.expireTime = null;
    }
  }
});

// 监听来自内容脚本或弹出窗口的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 处理认证请求
  if (request.action === 'authenticate') {
    handleAuthentication(request.appId, request.appSecret)
      .then(sendResponse)
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放
  }

  // 处理获取认证状态请求
  else if (request.action === 'getAuthStatus') {
    sendResponse({
      isAuthenticated: !!authInfo.token,
      token: authInfo.token
    });
  }

  // 处理注销请求
  else if (request.action === 'logout') {
    chrome.storage.local.remove(['feishuToken', 'feishuTokenExpire']);
    authInfo.token = null;
    authInfo.expireTime = null;
    sendResponse({ success: true });
  }

  // 处理飞书页面检测通知
  else if (request.action === 'onFeishuSheet') {
    // 更新插件图标状态，表示当前在飞书表格页面
    chrome.action.setBadgeText({ text: '表格' });
    chrome.action.setBadgeBackgroundColor({ color: '#3370ff' });

    // 存储当前表格信息
    chrome.storage.local.set({
      currentFeishuSheet: {
        url: request.data.url,
        title: request.data.title
      }
    });

    sendResponse({ success: true });
  }
});

// 处理认证流程
async function handleAuthentication(appId, appSecret) {
  try {
    // 构建认证URL
    const authUrl = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${appId}&redirect_uri=${encodeURIComponent(chrome.identity.getRedirectURL())}`;

    // 启动Web认证流程
    const responseUrl = await chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    });

    // 从URL中提取授权码
    const code = new URL(responseUrl).searchParams.get('code');
    if (!code) {
      throw new Error('未能获取授权码');
    }

    // 使用授权码获取访问令牌
    const response = await fetch('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_id: appId,
        app_secret: appSecret
      })
    });

    const data = await response.json();
    if (data.code !== 0) {
      throw new Error(data.msg || '获取访问令牌失败');
    }

    // 存储访问令牌
    const expireTime = Date.now() + (data.expire * 1000);
    authInfo.token = data.tenant_access_token;
    authInfo.expireTime = expireTime;

    await chrome.storage.local.set({
      feishuToken: authInfo.token,
      feishuTokenExpire: authInfo.expireTime
    });

    return {
      success: true,
      token: authInfo.token
    };
  } catch (error) {
    console.error('认证失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 监听标签页更新事件，检测是否离开飞书表格页面
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.url) {
    const isFeishuSheet = changeInfo.url.includes('feishu.cn/sheets/') ||
                          changeInfo.url.includes('feishu.cn/base/');

    if (!isFeishuSheet) {
      // 不在飞书表格页面，清除徽章
      chrome.action.setBadgeText({ text: '' });
    }
  }
});

// 监听标签页关闭事件
chrome.tabs.onRemoved.addListener((tabId) => {
  // 检查关闭的标签页是否是飞书表格页面
  chrome.storage.local.get('currentFeishuSheet', (result) => {
    if (result.currentFeishuSheet) {
      // 清除当前表格信息
      chrome.storage.local.remove('currentFeishuSheet');
      // 清除徽章
      chrome.action.setBadgeText({ text: '' });
    }
  });
});
