# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import re

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }
    
    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }
    
    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    # 判断是否需要构建蓝标配置
    limit_types = should_build_blue_sign(analysis_data)
    if not limit_types:
        return {
            "status": "ignore",
            "message": "无需构建蓝标配置",
            "data": []
        }

    # 构建蓝标配置字段
    blue_sign_fields = build_blue_sign_fields(analysis_data, mapping_data, limit_types)
    if not blue_sign_fields:
        blue_sign_fields = {
            "labelShowName": "待人工确认",
            "layerTitle": "待人工确认",
            "layerDesc": "待人工确认",
            "layerViceDesc": "待人工确认",
            "bubbleLabel": "待人工确认"
        }
    
    # 构建结果对象
    result_obj = {
        "productMark": product_mark,
        "labelShowName": blue_sign_fields["labelShowName"],
        "layerTitle": blue_sign_fields["layerTitle"],
        "layerDesc": blue_sign_fields["layerDesc"],
        "layerViceDesc": blue_sign_fields["layerViceDesc"],
        "isRightsLabel": "false",
        "bookingPriceName": "",
        "bubbleLabel": blue_sign_fields["bubbleLabel"],
        "strategyKey": "",
        "qualityProduct": "false"
    }
    
    # 创建第二个结果对象，设置strategyKey为"new_ota_label_2024"，用于区分新老主站
    result_obj2 = result_obj.copy()
    result_obj2["strategyKey"] = "new_ota_label_2024"
    
    # 返回包含两个结果对象的列表
    return {
        "data": [result_obj, result_obj2],
        "status": "success",
        "message": "操作成功"
    }

def should_build_blue_sign(analysis_data: Dict[str, Any]) -> List[str]:
    """
    判断需要构建蓝标配置的限制类型
    
    返回所有满足以下条件的限制类型：
    1. 人数限制 (capacity_limit)
    2. 年龄限制 (age_limit)
    3. 身份证开头限制 (regional_limit)
    4. 赠送权益类（溢价产品）
    5. 会员（新会员，老会员）(membership_limit)
    6. 性别 (gender_restriction)
    7. 证件限制回乡证 (document_restrictions)
    
    参数:
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[str] - 满足条件的限制类型列表
    """
    result = []
    # 1. 人数限制
    capacity_limit = analysis_data.get("capacity_limit", "")
    if capacity_limit and "-" in capacity_limit:
        result.append("capacity_limit")
    
    # 2. 年龄限制
    if analysis_data.get("age_limit"):
        result.append("age_limit")
    
    # 3. 身份证开头限制（地区限制）
    if analysis_data.get("regional_limit"):
        result.append("regional_limit")
    
    # 5. 会员
    membership_limit = analysis_data.get("membership_limit", "")
    if membership_limit and membership_limit in ["新会员", "老会员"]:
        result.append("membership_limit")
    
    # 6. 性别
    gender_restriction = analysis_data.get("gender_restriction")
    if gender_restriction and gender_restriction in ["男", "女"]:
        result.append("gender_restriction")
    
    # 7. 证件限制回乡证
    document_restrictions = analysis_data.get("document_restrictions", "")
    if "2" in document_restrictions:
        result.append("document_restrictions")
    
    return result

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    

def build_blue_sign_fields(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any], limit_types: List[str]) -> Dict[str, str]:
    """
    根据限制类型构建蓝标配置的字段内容
    """
    # 多限制条件时，文案暂时不处理
    if len(limit_types) > 1:
        return None
    
    result = {
        "labelShowName": "",
        "layerTitle": "",
        "layerDesc": "",
        "layerViceDesc": "",
        "bubbleLabel": ""
    }
    # 调用各自的子方法，后出现的限制类型会覆盖前面的字段
    for limit_type in limit_types:
        # 人数限制
        if limit_type == "capacity_limit":
            fields = _build_capacity_limit_fields(analysis_data)
        # 年龄限制
        elif limit_type == "age_limit":
            fields = _build_age_limit_fields(analysis_data)
        # 身份证开头限制
        elif limit_type == "regional_limit":
            fields = _build_regional_limit_fields(analysis_data)
        # 会员限制
        elif limit_type == "membership_limit":
            fields = _build_membership_limit_fields(analysis_data, mapping_data)
        # 性别限制
        elif limit_type == "gender_restriction":
            fields = _build_gender_restriction_fields(analysis_data)
        # 证件限制回乡证
        elif limit_type == "document_restrictions":
            fields = _build_document_restrictions_fields(analysis_data)
        else:
            fields = None
        if fields:
            result.update(fields)
    return result


def _build_capacity_limit_fields(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    capacity_limit = analysis_data.get("capacity_limit", "")
    if not capacity_limit:
        return None
    return {
        "labelShowName": f"限{capacity_limit}位旅客购买",
        "layerTitle": f"限{capacity_limit}位旅客购买",
        "layerDesc": f"仅限订单中包含{capacity_limit}位旅客时可享受此优惠产品，感谢您的理解与支持。",
        "layerViceDesc": f"仅限订单中包含{capacity_limit}位旅客时可享受此优惠产品，感谢您的理解与支持。",
        "bubbleLabel": "多人特惠"
    }


def _build_age_limit_fields(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    age_limit = analysis_data.get("age_limit", "")
    if not age_limit:
        return None
    has_young = False
    has_old = False
    YOUNG_MIN = 12
    YOUNG_MAX = 28
    OLD_MIN = 50
    age_ranges = []
    for age_range in age_limit.split(","):
        age_range = age_range.strip()
        if not age_range:
            continue
        if "-" not in age_range:
            continue
        try:
            min_age, max_age = map(int, age_range.split("-"))
            if min_age < 0 or max_age < 0 or max_age > 150 or min_age > max_age:
                continue
            if (min_age >= YOUNG_MIN and max_age <= YOUNG_MAX):
                has_young = True
            if min_age >= OLD_MIN:
                has_old = True
            age_ranges.append((min_age, max_age))
        except (ValueError, TypeError):
            continue
    if not age_ranges:
        return None
    
    if len(age_ranges) == 1:
        min_age, max_age = age_ranges[0]
        if max_age > 90:
            layerTitle = f"限{min_age}周岁(含)以上用户买"
            layerDesc = f"此优惠产品仅限{min_age}周岁（含）及以上旅客购买，购票和乘机须使用中国大陆居民身份证，感谢的理解与配合。"
        else:
            layerTitle = f"限{min_age}-{max_age}周岁(含)旅客购买"
            layerDesc = f"此优惠产品仅限{min_age}-{max_age}周岁（含）旅客预订，购票和乘机须使用中国大陆居民身份证，感谢的理解与配合。"
    else:
        title_text_parts = []
        for min_age, max_age in age_ranges:
            if max_age > 90:
                title_text_parts.append(f"{min_age}岁(含)以上")
            else:
                title_text_parts.append(f"{min_age}-{max_age}(含)")
        layerTitle = f"限{' 或 '.join(title_text_parts)}"
        layerDesc = f"本产品适用于年龄在{' 或 '.join(title_text_parts)}的旅客购买，购票和乘机须使用中国大陆居民身份证，感谢的理解与配合。"
    labelShowName = layerTitle
    layerViceDesc = layerDesc
    if has_young and has_old:
        bubbleLabel = "家庭套票"
    elif has_young:
        bubbleLabel = "青年特惠"
    elif has_old:
        bubbleLabel = "长者特惠"
    else:
        bubbleLabel = ""
    return {
        "labelShowName": labelShowName,
        "layerTitle": layerTitle,
        "layerDesc": layerDesc,
        "layerViceDesc": layerViceDesc,
        "bubbleLabel": bubbleLabel
    }


def _build_regional_limit_fields(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    regional_limit = analysis_data.get("regional_limit", "")
    import re
    id_prefixes = re.findall(r'\d{2,}', regional_limit)
    if not id_prefixes:
        return None
    
    prefix_str = ",".join(id_prefixes)
    if len(id_prefixes) > 3:
        labelShowName = "限部分身份证开头的旅客购买"
        layerTitle = "限部分身份证开头的旅客购买"
        layerDesc = f"此优惠产品仅限身份证号以{prefix_str}开头的旅客购买，感谢您的理解与支持。"
        layerViceDesc = layerDesc
    else:
        labelShowName = f"限身份证开头为{prefix_str}的旅客购买"
        layerTitle = f"限身份证开头为{prefix_str}的旅客购买"
        layerDesc = f"此优惠产品仅限身份证号以{prefix_str}开头的旅客购买，感谢您的理解与支持。"
        layerViceDesc = layerDesc
    return {
        "labelShowName": labelShowName,
        "layerTitle": layerTitle,
        "layerDesc": layerDesc,
        "layerViceDesc": layerViceDesc,
        "bubbleLabel": "地区专享"
    }


def _build_membership_limit_fields(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> Dict[str, str]:
    membership_limit = analysis_data.get("membership_limit", "")
    member_name = mapping_data.get("memberName", "航空会员")
    if membership_limit == "新会员":
        # 在"会员"前插入"新"
        if "会员" in member_name:
            new_member_name = member_name.replace("会员", "新会员")
        else:
            new_member_name = "新会员"
        return {
            "labelShowName": "新会员专享",
            "layerTitle": "新会员专享",
            "layerDesc": f"尊敬的用户，此产品仅限{new_member_name}专享(儿童不适用)，如非{member_name}将自动注册为会员，如您已为{member_name}将无法享受此优惠，感谢您的理解与支持。",
            "layerViceDesc": f"尊敬的用户，此产品仅限{new_member_name}专享(儿童不适用)，如非{member_name}将自动注册为会员，如您已为{member_name}将无法享受此优惠，感谢您的理解与支持。",
            "bubbleLabel": "新会员专享"
        }
    return None


def _build_gender_restriction_fields(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    gender = analysis_data.get("gender_restriction", "")
    if gender in ["男", "女"]:
        return {
            "labelShowName": f"限{gender}性旅客购买",
            "layerTitle": f"限{gender}性旅客购买",
            "layerDesc": f"此优惠产品仅限{gender}性旅客购买，感谢您的理解与支持。",
            "layerViceDesc": f"此优惠产品仅限{gender}性旅客购买，感谢您的理解与支持。",
            "bubbleLabel": ""
        }
    return None


def _build_document_restrictions_fields(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    return {
        "labelShowName": "港澳专享",
        "layerTitle": "港澳专享",
        "layerDesc": "尊敬的用户，该权益限港澳旅客购买且乘机人证件须为回乡证才可享受低价权益，感谢您的理解与支持。",
        "layerViceDesc": "尊敬的用户，该权益限港澳旅客购买且乘机人证件须为回乡证才可享受低价权益，感谢您的理解与支持。",
        "bubbleLabel": "港澳专享"
    }
