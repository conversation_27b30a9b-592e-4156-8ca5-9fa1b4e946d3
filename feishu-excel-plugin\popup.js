// 全局变量和实例
let currentSheet = null;
const excelProcessor = new ExcelProcessor();
const feishuApi = new FeishuApi();

// DOM 元素
const elements = {
  authStatus: document.querySelector('.auth-status'),
  authButton: document.getElementById('authButton'),
  uploadArea: document.getElementById('uploadArea'),
  fileInput: document.getElementById('fileInput'),
  sheetList: document.getElementById('sheetList'),
  previewArea: document.getElementById('previewArea'),
  spreadsheetSelect: document.getElementById('spreadsheetSelect'),
  previewButton: document.getElementById('previewButton'),
  importButton: document.getElementById('importButton'),
  exportButton: document.getElementById('exportButton'),
  loading: document.getElementById('loading'),
  statusMessage: document.getElementById('statusMessage')
};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  await checkAuthStatus();
  setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
  // 文件上传相关
  elements.uploadArea.addEventListener('click', () => elements.fileInput.click());
  elements.fileInput.addEventListener('change', handleFileSelect);
  elements.uploadArea.addEventListener('dragover', handleDragOver);
  elements.uploadArea.addEventListener('dragleave', handleDragLeave);
  elements.uploadArea.addEventListener('drop', handleFileDrop);

  // 按钮点击事件
  elements.authButton.addEventListener('click', handleAuth);
  elements.previewButton.addEventListener('click', handlePreview);
  elements.importButton.addEventListener('click', handleImport);
  elements.exportButton.addEventListener('click', handleExport);
}

// 检查认证状态
async function checkAuthStatus() {
  try {
    // 从后台脚本获取认证状态
    const response = await chrome.runtime.sendMessage({ action: 'getAuthStatus' });
    
    if (response.isAuthenticated) {
      feishuApi.setToken(response.token);
      updateAuthStatus(true);
      await loadFeishuSpreadsheets();
    } else {
      updateAuthStatus(false);
    }
  } catch (error) {
    console.error('检查认证状态失败:', error);
    updateAuthStatus(false);
  }
}

// 更新认证状态UI
function updateAuthStatus(isLoggedIn) {
  elements.authStatus.className = `auth-status ${isLoggedIn ? 'logged-in' : 'logged-out'}`;
  elements.authStatus.querySelector('.status-text').textContent = isLoggedIn ? '已登录飞书' : '未登录飞书';
  elements.authButton.textContent = isLoggedIn ? '退出' : '登录';
}

// 处理认证
async function handleAuth() {
  if (feishuApi.isAuthenticated()) {
    // 登出
    const response = await chrome.runtime.sendMessage({ action: 'logout' });
    if (response.success) {
      feishuApi.setToken(null);
      updateAuthStatus(false);
      elements.spreadsheetSelect.innerHTML = '<option value="">选择表格...</option>';
    }
  } else {
    // 登录
    try {
      showLoading(true);
      
      // 获取应用ID和密钥（实际应用中应从配置中获取）
      const appId = prompt('请输入飞书应用ID (App ID)');
      const appSecret = prompt('请输入飞书应用密钥 (App Secret)');
      
      if (!appId || !appSecret) {
        showStatus('请提供有效的应用ID和密钥', true);
        showLoading(false);
        return;
      }
      
      // 发送认证请求到后台脚本
      const response = await chrome.runtime.sendMessage({
        action: 'authenticate',
        appId,
        appSecret
      });
      
      if (response.success) {
        feishuApi.setToken(response.token);
        updateAuthStatus(true);
        await loadFeishuSpreadsheets();
        showStatus('飞书登录成功');
      } else {
        showStatus('认证失败: ' + (response.error || '未知错误'), true);
      }
    } catch (error) {
      console.error('认证失败:', error);
      showStatus('认证失败，请重试', true);
    } finally {
      showLoading(false);
    }
  }
}

// 加载飞书表格列表
async function loadFeishuSpreadsheets() {
  if (!feishuApi.isAuthenticated()) return;

  try {
    showLoading(true);
    const result = await feishuApi.getSpreadsheets();
    
    if (result.success) {
      elements.spreadsheetSelect.innerHTML = `
        <option value="">选择表格...</option>
        ${result.spreadsheets.map(sheet => 
          `<option value="${sheet.spreadsheet_token}">${sheet.properties.title}</option>`
        ).join('')}
      `;
    } else {
      showStatus('加载表格列表失败: ' + result.error, true);
    }
  } catch (error) {
    console.error('加载表格列表失败:', error);
    showStatus('加载表格列表失败', true);
  } finally {
    showLoading(false);
  }
}

// 处理文件选择
function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    parseExcelFile(file);
  }
}

// 处理拖拽
function handleDragOver(event) {
  event.preventDefault();
  event.stopPropagation();
  elements.uploadArea.classList.add('drag-over');
}

function handleDragLeave(event) {
  event.preventDefault();
  event.stopPropagation();
  elements.uploadArea.classList.remove('drag-over');
}

function handleFileDrop(event) {
  event.preventDefault();
  event.stopPropagation();
  elements.uploadArea.classList.remove('drag-over');
  
  const file = event.dataTransfer.files[0];
  if (file) {
    parseExcelFile(file);
  }
}

// 检查SheetJS库是否加载
function isSheetJSAvailable() {
  try {
    if (typeof XLSX === 'undefined' || !XLSX.read || !XLSX.utils) {
      return false;
    }
    return true;
  } catch (e) {
    return false;
  }
}

// 解析Excel文件
function parseExcelFile(file) {
  if (!isSheetJSAvailable()) {
    showStatus('SheetJS库未正确加载，请确保已按照README说明下载xlsx.full.min.js文件', true);
    return;
  }

  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      showLoading(true);
      
      // 再次检查库是否可用
      if (!isSheetJSAvailable()) {
        throw new Error('SheetJS库加载失败');
      }
      
      const result = excelProcessor.parseExcel(e.target.result);
      
      if (result.success) {
        updateSheetList(result.sheets);
        showStatus('Excel文件解析成功');
      } else {
        showStatus('解析Excel文件失败: ' + result.error, true);
      }
    } catch (error) {
      console.error('解析Excel文件失败:', error);
      showStatus('解析Excel文件失败: ' + error.message, true);
      
      // 如果是库加载问题，提供详细指引
      if (error.message.includes('SheetJS') || error.message.includes('XLSX')) {
        showStatus('请确保已正确下载xlsx.full.min.js文件到lib目录', true);
      }
    } finally {
      showLoading(false);
    }
  };
  
  reader.onerror = () => {
    showStatus('文件读取失败，请重试', true);
  };
  
  reader.readAsArrayBuffer(file);
}

// 更新Sheet列表
function updateSheetList(sheets) {
  if (!sheets || !sheets.length) return;

  elements.sheetList.innerHTML = sheets.map(name => 
    `<div class="sheet-item" data-sheet="${name}">${name}</div>`
  ).join('');

  // 添加点击事件
  document.querySelectorAll('.sheet-item').forEach(item => {
    item.addEventListener('click', () => {
      document.querySelectorAll('.sheet-item').forEach(s => s.classList.remove('active'));
      item.classList.add('active');
      currentSheet = item.dataset.sheet;
      previewSheetData(currentSheet);
    });
  });

  // 默认选择第一个sheet
  if (sheets.length > 0) {
    const firstSheet = document.querySelector('.sheet-item');
    firstSheet.click();
  }
}

// 预览Sheet数据
function previewSheetData(sheetName) {
  if (!sheetName) return;

  console.log('预览工作表:', sheetName);
  const data = excelProcessor.getSheetData(sheetName);
  if (!data) {
    showStatus('获取工作表数据失败', true);
    return;
  }

  console.log('原始数据行数:', data.length);

  // 过滤空行
  const filteredData = data.filter((row, index) => {
    // 保留表头
    if (index === 0) return true;
    
    // 检查行是否有非空值
    return row && row.some(cell => 
      cell !== null && cell !== undefined && cell !== '' && 
      (typeof cell !== 'string' || cell.trim() !== '')
    );
  });

  console.log('过滤后数据行数:', filteredData.length);

  if (filteredData.length <= 1) {
    elements.previewArea.innerHTML = '<div style="text-align:center;padding:20px;">此工作表没有有效数据</div>';
    return;
  }

  // 创建预览表格
  const table = document.createElement('table');
  table.style.width = '100%';
  table.style.borderCollapse = 'collapse';

  // 限制显示前10行，避免过多数据
  const displayData = filteredData.slice(0, 10);
  
  displayData.forEach((row, i) => {
    const tr = document.createElement('tr');
    
    // 处理每个单元格
    row.forEach(cell => {
      const td = document.createElement(i === 0 ? 'th' : 'td');
      td.style.border = '1px solid #ddd';
      td.style.padding = '4px';
      td.textContent = cell || '';
      tr.appendChild(td);
    });
    
    table.appendChild(tr);
  });

  // 如果数据超过10行，添加提示
  if (filteredData.length > 10) {
    const tr = document.createElement('tr');
    const td = document.createElement('td');
    td.colSpan = displayData[0]?.length || 1;
    td.style.textAlign = 'center';
    td.style.padding = '4px';
    td.textContent = `... 共 ${filteredData.length} 行有效数据，仅显示前 10 行 ...`;
    tr.appendChild(td);
    table.appendChild(tr);
  }

  elements.previewArea.innerHTML = '';
  elements.previewArea.appendChild(table);
  
  // 生成并显示AI友好的文本格式
  console.log('AI友好的文本格式:');
  console.log(excelProcessor.convertToAIText(filteredData));
}

// 处理预览按钮点击
function handlePreview() {
  if (!currentSheet) {
    showStatus('请先选择一个工作表', true);
    return;
  }
  previewSheetData(currentSheet);
}

// 处理导入按钮点击
async function handleImport() {
  if (!feishuApi.isAuthenticated()) {
    showStatus('请先登录飞书', true);
    return;
  }

  if (!currentSheet) {
    showStatus('请先选择要导入的工作表', true);
    return;
  }

  const spreadsheetToken = elements.spreadsheetSelect.value;
  if (!spreadsheetToken) {
    showStatus('请选择目标飞书表格', true);
    return;
  }

  try {
    showLoading(true);
    
    // 获取当前工作表数据
    const data = excelProcessor.getSheetData(currentSheet);
    if (!data) {
      throw new Error('获取工作表数据失败');
    }
    
    // 验证数据有效性
    const validation = excelProcessor.validateData(data);
    if (!validation.valid) {
      throw new Error('数据验证失败: ' + validation.errors.join(', '));
    }
    
    // 创建新的工作表
    const sheetResult = await feishuApi.createSheet(spreadsheetToken, currentSheet);
    if (!sheetResult.success) {
      throw new Error('创建工作表失败: ' + sheetResult.error);
    }
    
    // 写入数据
    const writeResult = await feishuApi.writeValues(
      spreadsheetToken, 
      `${sheetResult.sheet.title}!A1`, 
      data
    );
    
    if (!writeResult.success) {
      throw new Error('写入数据失败: ' + writeResult.error);
    }
    
    showStatus(`数据导入成功，更新了 ${writeResult.updatedRows} 行 ${writeResult.updatedColumns} 列`);
  } catch (error) {
    console.error('导入数据失败:', error);
    showStatus('导入数据失败: ' + error.message, true);
  } finally {
    showLoading(false);
  }
}

// 显示/隐藏加载状态
function showLoading(show) {
  elements.loading.className = `loading ${show ? 'active' : ''}`;
}

// 导出为文本文件
function handleExport() {
  if (!currentSheet) {
    showStatus('请先选择一个工作表', true);
    return;
  }

  try {
    showLoading(true);
    console.log('开始导出工作表:', currentSheet);
    
    // 获取当前sheet数据
    const data = excelProcessor.getSheetData(currentSheet);
    console.log('获取到原始数据行数:', data?.length || 0);
    
    if (!data || !data.length) {
      throw new Error('没有可导出的数据');
    }

    // 添加sheet名称作为第一行
    const sheetNameLine = `【${currentSheet}】\n`;
    console.log('添加sheet名称行:', sheetNameLine);

    // 转换为文本格式
    let textContent = excelProcessor.convertToTextFile(data, currentSheet);
    console.log('转换后的文本内容长度:', textContent.length);
    
    if (!textContent) {
      throw new Error('转换后的文本内容为空');
    }

    // 在文本内容前添加sheet名称
    textContent = sheetNameLine + textContent;
    
    // 创建下载链接
    const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
    console.log('创建Blob对象，大小:', blob.size);
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentSheet}.txt`;
    
    // 触发下载
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('导出成功，文件名:', `${currentSheet}.txt`);
    showStatus('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    showStatus(`导出失败: ${error.message}`, true);
  } finally {
    showLoading(false);
  }
}

// 显示状态消息
function showStatus(message, isError = false) {
  elements.statusMessage.className = `status-message ${isError ? 'error' : 'success'}`;
  elements.statusMessage.textContent = message;
  elements.statusMessage.style.display = 'block';

  setTimeout(() => {
    elements.statusMessage.style.display = 'none';
  }, 3000);
}
