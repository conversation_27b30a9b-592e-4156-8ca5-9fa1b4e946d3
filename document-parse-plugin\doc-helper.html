<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>DOC文件处理助手</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 40px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 10px;
      font-size: 2.5em;
    }
    .subtitle {
      text-align: center;
      color: #666;
      margin-bottom: 40px;
      font-size: 1.1em;
    }
    .upload-section {
      background: #f8f9fa;
      padding: 30px;
      border-radius: 10px;
      margin-bottom: 30px;
      border: 2px dashed #dee2e6;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .upload-section:hover {
      border-color: #007bff;
      background: #e3f2fd;
    }
    .upload-section.dragover {
      border-color: #007bff;
      background: #e3f2fd;
      transform: scale(1.02);
    }
    .file-input {
      display: none;
    }
    .conversion-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .option-card {
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 25px;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .option-card:hover {
      border-color: #007bff;
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,123,255,0.2);
    }
    .option-card.recommended {
      border-color: #28a745;
      background: #f8fff9;
    }
    .option-icon {
      font-size: 3em;
      margin-bottom: 15px;
    }
    .option-title {
      font-size: 1.3em;
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
    }
    .option-description {
      color: #666;
      line-height: 1.5;
    }
    .result-area {
      margin-top: 30px;
      padding: 25px;
      border: 1px solid #ddd;
      border-radius: 10px;
      background: #f9f9f9;
      min-height: 200px;
      display: none;
    }
    .result-area.show {
      display: block;
    }
    .status {
      padding: 15px;
      margin: 15px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    .content-preview {
      margin-top: 20px;
      padding: 20px;
      background: white;
      border-radius: 8px;
      border: 1px solid #ddd;
      max-height: 400px;
      overflow-y: auto;
    }
    .btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1em;
      margin: 8px;
      transition: background 0.3s ease;
    }
    .btn:hover {
      background: #0056b3;
    }
    .btn.success {
      background: #28a745;
    }
    .btn.success:hover {
      background: #1e7e34;
    }
    .progress-bar {
      width: 100%;
      height: 6px;
      background: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
      margin: 15px 0;
    }
    .progress-fill {
      height: 100%;
      background: #007bff;
      width: 0%;
      transition: width 0.3s ease;
    }
    .tips {
      background: #e3f2fd;
      padding: 20px;
      border-radius: 8px;
      margin-top: 20px;
      border-left: 4px solid #2196f3;
    }
    .tips h4 {
      margin-top: 0;
      color: #1976d2;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>📄 DOC文件处理助手</h1>
    <p class="subtitle">智能解析.doc文件，提供多种处理方案</p>

    <!-- 文件上传区域 -->
    <div class="upload-section" id="uploadArea">
      <div class="option-icon">📁</div>
      <h3>拖拽或点击上传.doc文件</h3>
      <p>支持Microsoft Word 97-2003格式(.doc)</p>
      <input type="file" id="fileInput" class="file-input" accept=".doc">
    </div>

    <!-- 转换选项 -->
    <div class="conversion-options">
      <div class="option-card recommended" onclick="showConversionGuide()">
        <div class="option-icon">🔄</div>
        <div class="option-title">推荐：格式转换</div>
        <div class="option-description">
          将.doc文件转换为.docx格式，获得最佳兼容性和解析效果
        </div>
      </div>

      <div class="option-card" onclick="tryDirectParsing()">
        <div class="option-icon">⚡</div>
        <div class="option-title">直接解析</div>
        <div class="option-description">
          尝试直接解析.doc文件，可能适用于简单的文档
        </div>
      </div>

      <div class="option-card" onclick="showManualOptions()">
        <div class="option-icon">✋</div>
        <div class="option-title">手动处理</div>
        <div class="option-description">
          提供手动复制粘贴等替代方案
        </div>
      </div>
    </div>

    <!-- 结果显示区域 -->
    <div class="result-area" id="resultArea">
      <h3>处理结果</h3>
      <div id="resultContent"></div>
    </div>

    <!-- 使用提示 -->
    <div class="tips">
      <h4>💡 使用提示</h4>
      <ul>
        <li><strong>最佳方案：</strong>使用Microsoft Word将.doc文件另存为.docx格式</li>
        <li><strong>在线转换：</strong>可以使用在线转换工具，如Convertio、Zamzar等</li>
        <li><strong>兼容性：</strong>.docx格式具有更好的跨平台兼容性</li>
        <li><strong>功能完整：</strong>转换后的文件支持所有解析功能，包括表格提取</li>
      </ul>
    </div>
  </div>

  <!-- 加载必要的库 -->
  <script src="lib/doc-conversion-helper.js"></script>

  <script>
    let currentFile = null;
    let conversionHelper = null;

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof DocConversionHelper !== 'undefined') {
        conversionHelper = new DocConversionHelper();
        console.log('DOC转换助手初始化成功');
      } else {
        showStatus('error', 'DOC转换助手未加载，请刷新页面重试');
      }

      setupFileUpload();
    });

    // 设置文件上传
    function setupFileUpload() {
      const uploadArea = document.getElementById('uploadArea');
      const fileInput = document.getElementById('fileInput');

      uploadArea.addEventListener('click', () => fileInput.click());
      fileInput.addEventListener('change', handleFileSelect);

      // 拖拽支持
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleFile(files[0]);
        }
      });
    }

    function handleFileSelect(e) {
      const file = e.target.files[0];
      if (file) {
        handleFile(file);
      }
    }

    function handleFile(file) {
      currentFile = file;
      
      if (!file.name.toLowerCase().endsWith('.doc')) {
        showStatus('error', '请选择.doc格式的文件');
        return;
      }

      showStatus('info', `文件已选择: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
      showResultArea();
    }

    // 显示转换指南
    function showConversionGuide() {
      const content = `
        <div style="background: #e8f5e8; padding: 25px; border-radius: 10px; border: 2px solid #28a745;">
          <h3 style="color: #155724; margin-top: 0;">🔄 推荐：文件格式转换</h3>
          
          <div style="margin: 20px 0;">
            <h4>方法一：使用Microsoft Word</h4>
            <ol>
              <li>用Microsoft Word打开您的.doc文件</li>
              <li>点击"文件" → "另存为"</li>
              <li>在"保存类型"中选择"Word文档(*.docx)"</li>
              <li>点击"保存"</li>
              <li>重新上传转换后的.docx文件</li>
            </ol>
          </div>

          <div style="margin: 20px 0;">
            <h4>方法二：使用在线转换工具</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
              <a href="https://convertio.co/doc-docx/" target="_blank" style="background: white; padding: 15px; border-radius: 8px; text-decoration: none; color: #333; border: 1px solid #ddd; text-align: center;">
                <strong>Convertio</strong><br>
                <small>免费在线转换</small>
              </a>
              <a href="https://www.zamzar.com/convert/doc-to-docx/" target="_blank" style="background: white; padding: 15px; border-radius: 8px; text-decoration: none; color: #333; border: 1px solid #ddd; text-align: center;">
                <strong>Zamzar</strong><br>
                <small>专业文档转换</small>
              </a>
              <a href="https://www.ilovepdf.com/word_to_pdf" target="_blank" style="background: white; padding: 15px; border-radius: 8px; text-decoration: none; color: #333; border: 1px solid #ddd; text-align: center;">
                <strong>ILovePDF</strong><br>
                <small>多格式支持</small>
              </a>
            </div>
          </div>

          <div style="background: #fff; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <strong>✅ 转换后的优势：</strong>
            <ul style="margin: 10px 0;">
              <li>完美的文本提取</li>
              <li>表格结构保持</li>
              <li>格式信息完整</li>
              <li>支持所有导出功能</li>
            </ul>
          </div>
        </div>
      `;
      
      showResult(content);
    }

    // 尝试直接解析
    async function tryDirectParsing() {
      if (!currentFile) {
        showStatus('warning', '请先选择一个.doc文件');
        return;
      }

      if (!conversionHelper) {
        showStatus('error', 'DOC转换助手未初始化');
        return;
      }

      showStatus('info', '正在尝试解析.doc文件...');
      showProgress(20);

      try {
        const result = await conversionHelper.handleDocFile(currentFile);
        showProgress(100);

        if (result.success) {
          showStatus('success', `解析成功！使用方法: ${result.method}`);
          showResult(`
            <h4>📄 解析结果</h4>
            <div class="content-preview">
              ${result.html}
            </div>
            <div style="margin-top: 15px;">
              <button class="btn success" onclick="copyToClipboard('${result.text.replace(/'/g, "\\'")}')">📋 复制文本</button>
              <button class="btn" onclick="downloadAsText('${currentFile.name}', '${result.text.replace(/'/g, "\\'")}')">💾 下载文本</button>
            </div>
          `);
        } else {
          showStatus('warning', '直接解析失败，建议使用格式转换方案');
          showResult(result.html);
        }
      } catch (error) {
        showProgress(0);
        showStatus('error', `解析失败: ${error.message}`);
      }
    }

    // 显示手动选项
    function showManualOptions() {
      const content = `
        <div style="background: #fff3cd; padding: 25px; border-radius: 10px; border: 2px solid #ffc107;">
          <h3 style="color: #856404; margin-top: 0;">✋ 手动处理方案</h3>
          
          <div style="margin: 20px 0;">
            <h4>方案一：复制粘贴</h4>
            <ol>
              <li>用任意文字处理软件打开.doc文件</li>
              <li>选择所有内容 (Ctrl+A)</li>
              <li>复制内容 (Ctrl+C)</li>
              <li>粘贴到记事本或其他文本编辑器</li>
              <li>保存为.txt文件并上传</li>
            </ol>
          </div>

          <div style="margin: 20px 0;">
            <h4>方案二：另存为文本</h4>
            <ol>
              <li>用Word或WPS打开.doc文件</li>
              <li>点击"文件" → "另存为"</li>
              <li>选择"纯文本(*.txt)"格式</li>
              <li>保存并上传txt文件</li>
            </ol>
          </div>

          <div style="margin: 20px 0;">
            <h4>方案三：使用免费软件</h4>
            <ul>
              <li><strong>LibreOffice Writer:</strong> 免费开源，支持.doc格式</li>
              <li><strong>WPS Office:</strong> 免费版本，兼容性好</li>
              <li><strong>Google Docs:</strong> 在线编辑，可上传.doc文件</li>
            </ul>
          </div>

          <div style="background: #fff; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <strong>💡 提示：</strong> 虽然这些方案可以获取文本内容，但可能会丢失格式和表格结构。
          </div>
        </div>
      `;
      
      showResult(content);
    }

    // 显示状态
    function showStatus(type, message) {
      const resultContent = document.getElementById('resultContent');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      
      resultContent.insertBefore(statusDiv, resultContent.firstChild);
      
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    // 显示结果
    function showResult(content) {
      document.getElementById('resultContent').innerHTML = content;
      showResultArea();
    }

    // 显示结果区域
    function showResultArea() {
      document.getElementById('resultArea').classList.add('show');
    }

    // 显示进度
    function showProgress(percent) {
      let progressBar = document.querySelector('.progress-bar');
      if (!progressBar) {
        progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        progressBar.innerHTML = '<div class="progress-fill"></div>';
        document.getElementById('resultContent').appendChild(progressBar);
      }
      
      const fill = progressBar.querySelector('.progress-fill');
      fill.style.width = percent + '%';
      
      if (percent === 100) {
        setTimeout(() => {
          if (progressBar.parentNode) {
            progressBar.parentNode.removeChild(progressBar);
          }
        }, 1000);
      }
    }

    // 复制到剪贴板
    function copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        showStatus('success', '文本已复制到剪贴板');
      }).catch(() => {
        showStatus('error', '复制失败，请手动选择文本复制');
      });
    }

    // 下载为文本文件
    function downloadAsText(originalName, text) {
      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = originalName.replace('.doc', '.txt');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      showStatus('success', '文件下载已开始');
    }
  </script>
</body>
</html>
