import hashlib
import uuid
import requests
import json
from typing import Dict, Any
import cv2
import numpy as np
import io
from PIL import Image, ImageChops, ImageEnhance
import piexif
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def get_order_domain(orderNo: str, appCode: str, appToken: str) -> str:
    """
    获取订单的domain值
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        str: 订单的domain值
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/gongdan/order/search?orderNo={orderNo}&domain=callcenter.qunar.com",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return ""

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            return ""

        # 获取list中的第一个订单的rawDomain
        order_list = response_data.get("data", {}).get("list", [])
        if not order_list:
            return ""
            
        return order_list[0].get("rawDomain", "")

    except Exception as e:
        return ""


def perform_ela(image_data, quality=90, scale=10):
    """
    执行错误等级分析(Error Level Analysis)，增强对局部PS的检测能力
    
    参数:
        image_data: 图片数据（bytes）
        quality: JPEG压缩质量
        scale: 差异放大倍数
    
    返回:
        ela_image: ELA结果图像
        score: ELA分数（差异程度）
        region_analysis: 区域分析结果，检测可能被编辑的区域
    """
    # 从bytes创建PIL图像
    original = Image.open(io.BytesIO(image_data)).convert('RGB')
    
    # 降采样处理大图像以提高效率
    max_size = 1500
    if max(original.size) > max_size:
        scale_factor = max_size / max(original.size)
        new_size = (int(original.size[0] * scale_factor), int(original.size[1] * scale_factor))
        original = original.resize(new_size, Image.LANCZOS)
    
    # 创建内存中的JPEG
    temp_buffer = io.BytesIO()
    original.save(temp_buffer, 'JPEG', quality=quality)
    temp_buffer.seek(0)
    
    # 读取重新保存的图像
    resaved = Image.open(temp_buffer).convert('RGB')
    
    # 计算原始图像和重新保存的图像之间的差异
    ela_image = ImageChops.difference(original, resaved)
    
    # 使用numpy进行更高效的差异计算
    ela_array = np.array(ela_image)
    
    # 计算全局差异指标
    extrema = ela_image.getextrema()
    max_diff = max([ex[1] for ex in extrema])
    if max_diff == 0:
        max_diff = 1
    
    # 使用更鲁棒的指标计算全局分数
    diff_values = ela_array.flatten()
    # 排除零值（未变化的像素）以获得更准确的统计
    non_zero_diff = diff_values[diff_values > 0]
    
    if len(non_zero_diff) > 0:
        # 使用百分位数而不是中位数，减少异常值影响
        median_diff = np.percentile(non_zero_diff, 75)
        global_score = median_diff / 255.0  # 归一化分数
    else:
        global_score = 0.0
    
    # 区域分析 - 将图像分割成网格，检测局部异常
    region_analysis = {}
    grid_size = 4  # 分割为4x4网格
    
    height, width = ela_array.shape[:2]
    region_height = height // grid_size
    region_width = width // grid_size
    
    # 存储每个区域的差异分数
    region_scores = np.zeros((grid_size, grid_size))
    suspicious_regions = []
    
    for i in range(grid_size):
        for j in range(grid_size):
            # 计算区域边界
            top = i * region_height
            left = j * region_width
            bottom = min((i + 1) * region_height, height)
            right = min((j + 1) * region_width, width)
            
            # 提取区域
            region = ela_array[top:bottom, left:right]
            
            # 计算区域差异分数
            region_flat = region.flatten()
            region_non_zero = region_flat[region_flat > 0]
            
            if len(region_non_zero) > 0:
                # 使用更高的百分位数检测异常区域
                region_score = np.percentile(region_non_zero, 90) / 255.0
                region_scores[i, j] = region_score
                
                # 检测可疑区域（差异显著高于全局）
                if region_score > global_score * 1.5 and region_score > 0.05:
                    suspicious_regions.append({
                        "grid": (i, j),
                        "score": float(region_score),
                        "bounds": (left, top, right, bottom)
                    })
    
    # 分析区域差异的方差 - 高方差可能表示局部编辑
    region_variance = np.var(region_scores)
    
    # 检测是否存在明显的差异异常区域 - 局部PS特征
    has_local_edits = len(suspicious_regions) > 0 and region_variance > 0.0005
    
    region_analysis = {
        "region_variance": float(region_variance),
        "has_local_edits": has_local_edits,
        "suspicious_regions": suspicious_regions,
        "region_description": "检测到明显的局部编辑区域" if has_local_edits else "未检测到明显的局部编辑区域"
    }
    
    # 综合评分 - 结合全局分数和区域分析
    final_score = global_score
    if has_local_edits:
        # 如果检测到局部编辑，增加最终分数
        max_region_score = max(region_scores.flatten())
        final_score = max(final_score, max_region_score * 0.8)
    
    # 放大差异以便更容易看到
    ela_image = ImageEnhance.Brightness(ela_image).enhance(scale / max_diff)
    
    return ela_image, final_score, region_analysis

def noise_analysis(image_data):
    """
    执行噪声分析，检测图像中噪声的不一致性
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        noise_image: 噪声图像
        noise_score: 噪声不一致性分数
    """
    # 将bytes转换为numpy数组
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("无法解码图像数据")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 计算噪声
    noise = cv2.absdiff(gray, blurred)
    
    # 计算噪声的标准差作为分数
    noise_score = np.std(noise) / 255.0  # 归一化
    
    # 创建热图以显示噪声分布
    noise_image = cv2.applyColorMap(noise, cv2.COLORMAP_JET)
    
    return noise_image, noise_score

def noise_analysis_optimized(image_data):
    """
    优化的噪声分析版本，提高检测精度和减少误判
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        noise_image: 噪声图像
        noise_score: 噪声不一致性分数
        noise_regions: 可疑的噪声区域信息
    """
    # 将bytes转换为numpy数组
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("无法解码图像数据")
    
    # 降采样图像以减少计算量
    height, width = image.shape[:2]
    max_dim = 1024
    if max(height, width) > max_dim:
        scale = max_dim / max(height, width)
        image = cv2.resize(image, (int(width * scale), int(height * scale)))
    
    # 转换为YUV颜色空间，更适合分析噪声
    yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
    y_channel = yuv[:,:,0]  # Y通道包含亮度信息
    
    # 应用高斯模糊来消除噪声
    blurred = cv2.GaussianBlur(y_channel, (5, 5), 0)
    
    # 计算噪声 - 使用绝对差异
    noise = cv2.absdiff(y_channel, blurred)
    
    # 应用中值滤波器来获得平滑的噪声估计
    smooth_noise_estimate = cv2.medianBlur(noise, 5)
    
    # 检测异常噪声区域 - 先使用阈值分割
    _, noise_mask = cv2.threshold(noise, 15, 255, cv2.THRESH_BINARY)
    
    # 计算各区域的噪声统计特征
    grid_size = 8  # 将图像分为8x8网格
    h, w = noise.shape
    cell_h, cell_w = h // grid_size, w // grid_size
    grid_stats = np.zeros((grid_size, grid_size, 3))  # 均值、标准差、峰度
    
    suspicious_regions = []
    
    for i in range(grid_size):
        for j in range(grid_size):
            # 计算网格边界
            top = i * cell_h
            left = j * cell_w
            bottom = min((i + 1) * cell_h, h)
            right = min((j + 1) * cell_w, w)
            
            # 提取网格区域
            cell = noise[top:bottom, left:right]
            if cell.size == 0:
                continue
                
            # 计算噪声统计特征
            mean = np.mean(cell)
            std = np.std(cell)
            # 计算峰度 - 正常图像的噪声通常呈高斯分布
            cell_flat = cell.flatten() - mean
            kurt = np.mean((cell_flat/std)**4) - 3 if std > 0 else 0
            
            grid_stats[i, j] = [mean, std, kurt]
    
    # 计算全局噪声统计
    global_mean = np.mean(noise)
    global_std = np.std(noise)
    
    # 使用Z分数检测异常区域
    for i in range(grid_size):
        for j in range(grid_size):
            mean, std, kurt = grid_stats[i, j]
            
            # 计算不同特征的Z分数
            z_mean = abs(mean - global_mean) / (global_std / 3 + 1e-6)
            z_std = abs(std - global_std) / (global_std / 2 + 1e-6)
            
            # 检测异常值 - PS区域通常有不同的噪声特征
            is_suspicious = False
            suspicious_reasons = []
            
            # 比正常噪声低50%以上
            if mean < global_mean * 0.5 and z_mean > 2:
                is_suspicious = True
                suspicious_reasons.append(f"噪声显著低于平均 (均值Z分数: {z_mean:.2f})")
            
            # 比正常噪声高80%以上
            if mean > global_mean * 1.8 and z_mean > 2:
                is_suspicious = True
                suspicious_reasons.append(f"噪声显著高于平均 (均值Z分数: {z_mean:.2f})")
            
            # 标准差异常 - 过于平滑或过于嘈杂
            if (std < global_std * 0.4 or std > global_std * 2.0) and z_std > 2:
                is_suspicious = True
                if std < global_std * 0.4:
                    suspicious_reasons.append(f"噪声过于均匀 (标准差Z分数: {z_std:.2f})")
                else:
                    suspicious_reasons.append(f"噪声过于不规则 (标准差Z分数: {z_std:.2f})")
            
            # 峰度异常 - 指示非自然的噪声分布
            if abs(kurt) > 3:
                is_suspicious = True
                suspicious_reasons.append(f"噪声分布异常 (峰度: {kurt:.2f})")
            
            if is_suspicious:
                # 计算区域边界
                top = i * cell_h
                left = j * cell_w
                bottom = min((i + 1) * cell_h, h)
                right = min((j + 1) * cell_w, w)
                
                suspicious_regions.append({
                    "grid": (i, j),
                    "bounds": (left, top, right, bottom),
                    "mean": float(mean),
                    "std": float(std),
                    "kurt": float(kurt),
                    "reasons": suspicious_reasons
                })
    
    # 计算噪声一致性分数
    noise_consistency = 1.0
    
    # 区域方差分析 - 检查噪声均值和标准差的方差
    mean_variance = np.var([stats[0] for stats in grid_stats.reshape(-1, 3)])
    std_variance = np.var([stats[1] for stats in grid_stats.reshape(-1, 3)])
    
    # 归一化方差 - 使用sigmoid函数将值映射到0-1之间
    norm_mean_var = min(1.0, mean_variance / (global_mean * 0.5 + 1e-6))
    norm_std_var = min(1.0, std_variance / (global_std * 0.5 + 1e-6))
    
    # 计算一致性分数 - 越低表示越不一致
    region_inconsistency = np.mean([norm_mean_var, norm_std_var])
    
    # 处理异常区域情况
    if len(suspicious_regions) > 0:
        # 可疑区域的比例
        suspicious_ratio = min(1.0, len(suspicious_regions) / (grid_size * grid_size * 0.25))
        region_descriptions = [f"区域({r['grid']}): {', '.join(r['reasons'])}" for r in suspicious_regions[:3]]
        
        noise_consistency -= suspicious_ratio * 0.6
        noise_regions = {
            "has_suspicious_regions": True,
            "suspicious_count": len(suspicious_regions),
            "region_descriptions": region_descriptions,
            "suspicious_details": suspicious_regions,
            "inconsistency_score": float(region_inconsistency)
        }
    else:
        noise_regions = {
            "has_suspicious_regions": False,
            "suspicious_count": 0,
            "region_descriptions": [],
            "inconsistency_score": float(region_inconsistency)
        }
    
    # 最终噪声分数 - 1表示一致(可能未PS)，0表示不一致(可能PS)
    noise_consistency = max(0.0, min(1.0, noise_consistency))
    noise_score = 1.0 - noise_consistency  # 转换为PS可能性分数
    
    # 创建可视化结果
    # 使用热图显示噪声分布
    noise_image = cv2.applyColorMap(noise.astype(np.uint8), cv2.COLORMAP_JET)
    
    # 在噪声图上标记可疑区域
    for region in suspicious_regions:
        left, top, right, bottom = region["bounds"]
        cv2.rectangle(noise_image, (left, top), (right, bottom), (0, 255, 255), 2)
    
    return noise_image, noise_score, noise_regions

def check_metadata(image_data):
    """
    检查图像元数据中的PS痕迹，增强检测精度
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        has_ps_metadata: 是否包含PS相关元数据
        metadata_info: 元数据信息
    """
    try:
        # 从bytes创建PIL图像
        img = Image.open(io.BytesIO(image_data))
        metadata_info = {}
        has_ps_metadata = False
        
        # 定义精确的Photoshop关键词列表，减少误判
        ps_specific_keywords = [
            #"photoshop", 
            "adobe"
        ]
        
        # 常见的相机和手机制造商列表，避免误判
        common_camera_keywords = [
            "canon", "nikon", "sony", "fujifilm", "olympus", "panasonic", 
            "samsung", "huawei", "xiaomi", "apple", "iphone", "oppo", "vivo",
            "google", "pixel", "leica", "hasselblad", "dji"
        ]
        
        # Adobe XMP命名空间标识符列表，更精确地识别Adobe产品
        adobe_namespaces = [
            "xmp.did:", "xmp.iid:", "xmpmm:", "xmp:CreatorTool",
            "photoshop:", "xmp.crs:", "stEvt:", "photoshop:DocumentAncestors"
        ]
        
        # 检查EXIF数据
        if "exif" in img.info:
            exif_dict = piexif.load(img.info["exif"])
            
            # 检查软件信息
            if piexif.ImageIFD.Software in exif_dict["0th"]:
                software = exif_dict["0th"][piexif.ImageIFD.Software].decode('utf-8', errors='ignore')
                metadata_info["软件"] = software

                
                # 检查是否为常见相机软件
                software_lower = software.lower()
                print(software_lower)
                is_camera_software = any(keyword in software_lower for keyword in common_camera_keywords)
                
                # 仅当明确匹配Photoshop且不是相机软件时判定为PS
                if any(keyword in software_lower for keyword in ps_specific_keywords) and not is_camera_software:
                    has_ps_metadata = True
                    metadata_info["ps_match"] = "明确匹配Photoshop软件标识"
            
            # 检查修改历史
            if piexif.ExifIFD.MakerNote in exif_dict["Exif"]:
                maker_note = exif_dict["Exif"][piexif.ExifIFD.MakerNote].decode('utf-8', errors='ignore')
                metadata_info["制造商备注"] = maker_note
                
                # 仅当明确匹配Photoshop关键词时才判定为PS
                if any(keyword in maker_note.lower() for keyword in ps_specific_keywords):
                    has_ps_metadata = True
                    metadata_info["ps_match"] = "制造商备注中包含Photoshop信息"
            
            # 检查创建和修改日期的差异 - PS编辑通常会导致修改日期更新
            if (piexif.ImageIFD.DateTime in exif_dict["0th"] and
                piexif.ExifIFD.DateTimeOriginal in exif_dict["Exif"]):
                try:
                    modified_date = exif_dict["0th"][piexif.ImageIFD.DateTime].decode('utf-8', errors='ignore')
                    original_date = exif_dict["Exif"][piexif.ExifIFD.DateTimeOriginal].decode('utf-8', errors='ignore')
                    
                    metadata_info["修改日期"] = modified_date
                    metadata_info["原始日期"] = original_date
                    
                    if modified_date != original_date:
                        metadata_info["日期差异"] = "原始日期与修改日期不同，可能表明图像被编辑"
                except Exception as e:
                    metadata_info["日期解析错误"] = str(e)
        
        # 检查XMP数据 - 更精确匹配
        img_info_str = str(img.info).lower()
        
        # 检查Adobe特定的XMP命名空间标识符
        for namespace in adobe_namespaces:
            if namespace.lower() in img_info_str:
                metadata_info["adobe_namespace"] = f"检测到Adobe命名空间: {namespace}"
                # 仍然需要排除相机软件误判
                if not any(keyword in img_info_str for keyword in common_camera_keywords):
                    has_ps_metadata = True
                    metadata_info["ps_match"] = f"包含Adobe特定命名空间: {namespace}"
                break
        
        # 更严格的XMP检测 - 必须明确匹配Photoshop关键词
        if any(keyword in img_info_str for keyword in ps_specific_keywords):
            # 再次排除相机软件误判
            if not any(keyword in img_info_str for keyword in common_camera_keywords):
                has_ps_metadata = True
                metadata_info["ps_match"] = "包含明确的Adobe Photoshop信息"
        
        # 检查是否包含"history"相关信息，这常常表明有编辑历史
        if "xmp:history" in img_info_str or "photoshop:history" in img_info_str:
            metadata_info["edit_history"] = "检测到编辑历史记录"
            if not has_ps_metadata:  # 避免重复标记
                has_ps_metadata = True
                metadata_info["ps_match"] = "包含编辑历史记录"
        
        return has_ps_metadata, metadata_info
    
    except Exception as e:
        return False, {"错误": str(e)}

def detect_frequency_artifacts(image_data):
    """
    检测频域人工痕迹，AI生成图像常有特定频率特征
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        freq_score: 频域人工痕迹分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 转为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 应用FFT
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift) + 1)
        
        # 分析频谱特征
        # 检查规则网格模式，常见于GAN生成图像
        max_val = np.max(magnitude_spectrum)
        
        # 优化：更细致地分析频谱
        # 将频谱分为低频、中频和高频区域
        rows, cols = magnitude_spectrum.shape
        center_row, center_col = rows // 2, cols // 2
        
        # 创建距离矩阵 - 每个点到中心的距离
        Y, X = np.ogrid[:rows, :cols]
        dist_from_center = np.sqrt((X - center_col)**2 + (Y - center_row)**2)
        
        # 定义低、中、高频区域
        low_freq_mask = dist_from_center <= min(rows, cols) * 0.1
        high_freq_mask = dist_from_center >= min(rows, cols) * 0.4
        mid_freq_mask = ~low_freq_mask & ~high_freq_mask
        
        # 计算各频率区域的能量
        low_freq_energy = np.mean(magnitude_spectrum[low_freq_mask])
        mid_freq_energy = np.mean(magnitude_spectrum[mid_freq_mask])
        high_freq_energy = np.mean(magnitude_spectrum[high_freq_mask])
        
        # 计算频率能量比率 - 正常图像通常中频能量较高
        freq_ratio1 = high_freq_energy / mid_freq_energy if mid_freq_energy > 0 else 0
        freq_ratio2 = low_freq_energy / mid_freq_energy if mid_freq_energy > 0 else 0
        
        # 根据频率比率得出分数
        freq_score = 0
        explanation = ""
        
        # 更新判断逻辑 - 更宽松的阈值，减少误判
        if freq_ratio1 > 0.8 or freq_ratio2 > 3.0:
            # 高频或低频过强
            freq_score = 0.6
            explanation = f"检测到可疑的频域模式，可能是AI合成图像 (高/中频比: {freq_ratio1:.2f}, 低/中频比: {freq_ratio2:.2f})"
        elif freq_ratio1 > 0.5 or freq_ratio2 > 2.0:
            freq_score = 0.3
            explanation = f"频域特征有轻微异常，可能经过处理 (高/中频比: {freq_ratio1:.2f}, 低/中频比: {freq_ratio2:.2f})"
        else:
            explanation = f"频域特征正常 (高/中频比: {freq_ratio1:.2f}, 低/中频比: {freq_ratio2:.2f})"
        
        return freq_score, explanation
    
    except Exception as e:
        return 0, f"检测频域痕迹时出错: {e}"

def analyze_texture_consistency(image_data):
    """
    分析纹理一致性，AI生成图像常有纹理异常
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        texture_score: 纹理不一致性分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 降采样图像以减少计算量
        height, width = image.shape[:2]
        max_dim = 512  # 限制最大尺寸
        if max(height, width) > max_dim:
            scale = max_dim / max(height, width)
            image = cv2.resize(image, (int(width * scale), int(height * scale)))
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用skimage的LBP实现 (如果可用)
        try:
            from skimage.feature import local_binary_pattern # type: ignore
            lbp = local_binary_pattern(gray, P=8, R=1, method='uniform')
        except ImportError:
            # 如果skimage不可用，使用优化的LBP实现
            def calculate_lbp(image, P=8, R=1):
                rows, cols = image.shape
                # 减少计算量 - 只计算部分点的LBP
                stride = 2  # 计算步长
                result = np.zeros((rows, cols), dtype=np.uint8)
                
                # 预计算角度点
                angles = [(R * np.cos(2 * np.pi * k / P), R * np.sin(2 * np.pi * k / P)) for k in range(P)]
                
                for i in range(R, rows - R, stride):
                    for j in range(R, cols - R, stride):
                        center = image[i, j]
                        binary = [1 if image[int(i + x)][int(j + y)] >= center else 0 for x, y in angles]
                        decimal = sum([binary[k] * (2 ** k) for k in range(P)])
                        result[i, j] = decimal
                return result
            
            lbp = calculate_lbp(gray)
        
        # 计算局部区域LBP直方图标准差，检测不一致性
        block_size = 32
        rows, cols = gray.shape
        stds = []
        
        # 增加采样步长减少计算量
        sample_stride = 2
        
        for i in range(0, rows - block_size + 1, block_size // sample_stride):
            for j in range(0, cols - block_size + 1, block_size // sample_stride):
                block = lbp[i:i+block_size, j:j+block_size]
                hist, _ = np.histogram(block, bins=256, range=(0, 256))
                hist = hist / np.sum(hist)  # 归一化
                stds.append(np.std(hist))
        
        # 分析标准差的变异
        texture_variation = np.var(stds) if stds else 0
        
        # 设置阈值判断
        texture_score = 0
        explanation = ""
        
        if texture_variation < 0.0001:  # 过于一致
            texture_score = 0.6
            explanation = f"检测到异常的纹理一致性，可能是AI合成图像 (纹理变异: {texture_variation:.6f})"
        elif texture_variation > 0.01:  # 过于不一致
            texture_score = 0.7
            explanation = f"检测到异常的纹理不一致性，可能是拼接或AI合成图像 (纹理变异: {texture_variation:.6f})"
        else:
            explanation = f"纹理一致性正常 (纹理变异: {texture_variation:.6f})"
        
        return texture_score, explanation
    
    except Exception as e:
        return 0, f"分析纹理一致性时出错: {e}"

def check_facial_anomalies(image_data):
    """
    检查面部异常，AI生成的人脸常有细节问题
    
    参数:
        image_data: 图片数据（bytes）
    
    返回:
        face_score: 面部异常分数
        explanation: 解释
    """
    try:
        # 将bytes转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError("无法解码图像数据")
        
        # 使用人脸检测器
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)
        
        if len(faces) == 0:
            return 0, "未检测到人脸"
        
        face_score = 0
        anomalies = []
        
        for (x, y, w, h) in faces:
            face_region = gray[y:y+h, x:x+w]
            
            # 检测眼睛
            eyes = eye_cascade.detectMultiScale(face_region)
            
            # 检查眼睛对称性和位置
            if len(eyes) == 2:
                eye1, eye2 = eyes[0], eyes[1]
                eye_distance = abs((eye1[0] + eye1[2]//2) - (eye2[0] + eye2[2]//2))
                eye_height_diff = abs((eye1[1] + eye1[3]//2) - (eye2[1] + eye2[3]//2))
                
                if eye_height_diff > 0.15 * h:
                    anomalies.append("眼睛高度不对称")
                    face_score = max(face_score, 0.6)
            
            # 检查面部纹理
            face_texture = cv2.calcHist([face_region], [0], None, [256], [0, 256])
            face_texture = cv2.normalize(face_texture, face_texture).flatten()
            texture_entropy = -np.sum(face_texture * np.log2(face_texture + 1e-10))
            
            if texture_entropy < 3.0:  # 低熵可能表示AI生成的过于平滑的皮肤
                anomalies.append("面部纹理异常平滑")
                face_score = max(face_score, 0.7)
        
        if anomalies:
            explanation = f"检测到面部异常: {', '.join(anomalies)}"
        else:
            explanation = "未检测到明显的面部异常"
        
        return face_score, explanation
    
    except Exception as e:
        return 0, f"检查面部异常时出错: {e}"

def detect_photoshop_and_ai(image_data, ela_quality=90, ela_scale=10, use_parallel=True, early_stop=True):
    """
    综合检测图像是否被PS过或AI合成，返回简化的关键结果
    
    参数:
        image_data: 图片数据（bytes）
        ela_quality: ELA分析的JPEG质量
        ela_scale: 差异放大倍数
        use_parallel: 是否使用并行处理
        early_stop: 是否启用早期停止逻辑
    
    返回:
        result: 检测结果字典，只包含关键信息
    """
    result = {
        "ps_probability": 0.0,
        "ai_probability": 0.0,
        "analysis_summary": {},  # 替换详细分析结果，只保留摘要
        "suspicious_count": 0,   # 可疑区域数量，不保存详细信息
        "votes": {
            "ps_positive": 0,
            "ps_negative": 0,
            "ai_positive": 0,
            "ai_negative": 0
        },
        "confidence_level": "high" # 默认高置信度
    }

    # 临时存储详细分析结果，最后不返回
    temp_analysis_results = {}

    # 分析图像尺寸和质量 - 低质量图像可能会导致误判
    try:
        img = Image.open(io.BytesIO(image_data))
        width, height = img.size
        filesize = len(image_data)
        
        # 计算每像素的平均字节数，判断图像质量
        bytes_per_pixel = filesize / (width * height)
        
        if bytes_per_pixel < 0.5:  # 低质量图像
            result["confidence_level"] = "low"
            temp_analysis_results["image_quality"] = f"低质量图像，检测可能不准确 ({bytes_per_pixel:.2f} bytes/pixel)"
        elif width < 300 or height < 300:
            result["confidence_level"] = "medium"
            temp_analysis_results["image_quality"] = f"低分辨率图像 ({width}x{height})，检测可能不准确"
    except Exception as e:
        temp_analysis_results["image_quality_error"] = str(e)

    # 元数据检查 - 先执行这个最快的检查
    try:
        has_ps_metadata, metadata_info = check_metadata(image_data)
        temp_analysis_results["metadata"] = metadata_info
        
        # 简化元数据摘要
        if has_ps_metadata and "ps_match" in metadata_info:
            result["analysis_summary"]["metadata"] = metadata_info["ps_match"]
        
        if has_ps_metadata:
            temp_analysis_results["metadata_explanation"] = "检测到Photoshop/Adobe相关元数据"
            result["ps_probability"] = max(result["ps_probability"], 0.9)  # 元数据是很强的证据
            result["votes"]["ps_positive"] += 3  # 元数据很有说服力，给予3票
            
            # 早期停止 - 仅当元数据检测结果非常明确时才触发
            if early_stop and result["ps_probability"] > 0.9 and result["confidence_level"] == "high":
                # 添加额外的检查：必须有"ps_match"字段明确指出匹配结果
                if "ps_match" in metadata_info:
                    result["ps_conclusion"] = f"图像很可能被PS过 (可能性: {result['ps_probability']:.2f}, 明确匹配: {metadata_info['ps_match']})"
                    result["ai_conclusion"] = f"无法确定AI合成可能性"
                    result["final_conclusion"] = "不通过"
                    return result
        else:
            temp_analysis_results["metadata_explanation"] = "未检测到Photoshop/Adobe相关元数据"
            result["votes"]["ps_negative"] += 2  # 增加未检测到PS元数据的负票权重
    
    except Exception as e:
        temp_analysis_results["metadata_check_error"] = str(e)
    
    # 定义各个分析任务函数，使其接受预解码的图像数据以避免重复解码
    def task_ela():
        try:
            ela_image, ela_score, region_analysis = perform_ela(image_data, quality=ela_quality, scale=ela_scale)
            
            # 根据图像质量调整ELA阈值，提高阈值减少误判
            ela_threshold = 0.15 if result["confidence_level"] == "low" else 0.12
            
            # 基于区域分析调整PS概率和投票
            ps_probability = 0.0
            votes = 0
            explanation = ""
            
            # 如果检测到局部编辑
            if region_analysis.get("has_local_edits", False):
                suspicious_count = len(region_analysis.get("suspicious_regions", []))
                # 局部编辑是PS的强烈迹象
                ps_probability = min(0.75, ela_score * 2.5 + 0.1 * suspicious_count)
                votes = 2 if suspicious_count > 2 else 1  # 多个可疑区域增加票数
                explanation = f"检测到局部编辑区域，可能被PS过 (分数: {ela_score:.4f}, 可疑区域: {suspicious_count})"
                
                # 更新可疑区域计数
                result["suspicious_count"] += suspicious_count
                
                # 添加ELA分析摘要到结果
                result["analysis_summary"]["ela"] = f"检测到{suspicious_count}个可疑编辑区域"
            
            # 全局ELA得分高但没有明显局部编辑
            elif ela_score > ela_threshold:
                ps_probability = min(0.65, ela_score * 2.2)  # 降低全局得分的权重
                votes = 1 if ps_probability > 0.4 else 0
                explanation = f"检测到较高的错误等级差异，可能被PS过 (分数: {ela_score:.4f})"
                result["analysis_summary"]["ela"] = f"错误等级差异较高 ({ela_score:.2f})"
            else:
                explanation = f"错误等级差异较低，可能未被PS过 (分数: {ela_score:.4f})"
                votes = -1  # 负票，表明支持图像是真实的
                result["analysis_summary"]["ela"] = "无明显编辑迹象"
            
            return {
                "ela_score": ela_score,
                "ela_explanation": explanation,
                "ps_probability": ps_probability,
                "ps_votes": votes,
                "region_analysis": region_analysis
            }
        except Exception as e:
            return {"ela_error": str(e), "ps_probability": 0.0, "ps_votes": 0, "region_analysis": {}}
    
    def task_noise():
        try:
            # 使用优化版的噪声分析
            noise_image, noise_score, noise_regions = noise_analysis_optimized(image_data)
            
            # 噪声分数解释
            ps_probability = 0.0
            ai_probability = 0.0
            ps_votes = 0
            ai_votes = 0
            
            # 基于区域分析和全局分数调整概率
            has_suspicious = noise_regions.get("has_suspicious_regions", False)
            suspicious_count = noise_regions.get("suspicious_count", 0)
            inconsistency_score = noise_regions.get("inconsistency_score", 0.0)
            
            explanation = ""
            
            if has_suspicious and suspicious_count > 0:
                # 噪声不一致性是PS的较强指标
                ps_probability = min(0.7, noise_score * 0.6 + inconsistency_score * 0.4)
                ps_votes = 2 if suspicious_count > 3 else 1
                
                # 更新可疑区域计数
                result["suspicious_count"] += suspicious_count
                
                # 添加噪声分析摘要
                region_descriptions = noise_regions.get("region_descriptions", [])
                explanation = f"检测到噪声不一致区域 ({suspicious_count}个)"
                result["analysis_summary"]["noise"] = f"检测到{suspicious_count}个噪声异常区域"
            
            # 全局噪声得分但无明显异常区域
            elif noise_score > 0.4:
                ps_probability = min(0.5, noise_score * 0.6)  # 降低全局得分权重
                ps_votes = 1 if noise_score > 0.6 else 0
                explanation = f"检测到异常的噪声分布，可能被PS过 (分数: {noise_score:.4f})"
                result["analysis_summary"]["noise"] = f"噪声分布异常 ({noise_score:.2f})"
            else:
                explanation = f"噪声水平正常 (分数: {noise_score:.4f})"
                ps_votes = -1  # 负票，支持图像是真实的
                ai_votes = -1
                result["analysis_summary"]["noise"] = "噪声水平正常"
            
            # 低置信度时降低噪声分析权重
            if result["confidence_level"] != "high":
                ps_probability *= 0.6  # 进一步降低权重
                ai_probability *= 0.6
            
            return {
                "noise_score": noise_score,
                "noise_explanation": explanation,
                "ps_probability": ps_probability,
                "ai_probability": ai_probability,
                "ps_votes": ps_votes,
                "ai_votes": ai_votes,
                "noise_regions": noise_regions
            }
        except Exception as e:
            return {"noise_analysis_error": str(e), "ps_probability": 0.0, "ai_probability": 0.0, "ps_votes": 0, "ai_votes": 0}
    
    def task_frequency():
        try:
            freq_score, freq_explanation = detect_frequency_artifacts(image_data)
            
            # 低置信度时降低频域分析权重
            if result["confidence_level"] != "high":
                freq_score *= 0.7
            
            # 添加频域分析摘要
            if freq_score > 0.5:
                result["analysis_summary"]["frequency"] = f"检测到频域异常 ({freq_score:.2f})"
            elif freq_score > 0.25:
                result["analysis_summary"]["frequency"] = f"频域特征轻微异常 ({freq_score:.2f})"
            
            ai_votes = 1 if freq_score > 0.5 else (-1 if freq_score < 0.25 else 0)
            return {
                "frequency_score": freq_score,
                "frequency_explanation": freq_explanation,
                "ai_probability": freq_score,
                "ai_votes": ai_votes
            }
        except Exception as e:
            return {"frequency_analysis_error": str(e), "ai_probability": 0.0, "ai_votes": 0}
    
    def task_texture():
        try:
            texture_score, texture_explanation = analyze_texture_consistency(image_data)
            # 减小纹理分析权重，它对真实图像也可能给出高分
            adjusted_score = texture_score * 0.6  # 进一步降低权重
            
            # 低置信度时进一步降低纹理分析权重
            if result["confidence_level"] != "high":
                adjusted_score *= 0.7
            
            # 添加纹理分析摘要
            if adjusted_score > 0.5:
                result["analysis_summary"]["texture"] = f"检测到纹理异常 ({texture_score:.2f})"
                
            ai_votes = 1 if adjusted_score > 0.5 else (-1 if adjusted_score < 0.25 else 0)
            return {
                "texture_score": texture_score,
                "texture_explanation": texture_explanation,
                "ai_probability": adjusted_score,
                "ai_votes": ai_votes
            }
        except Exception as e:
            return {"texture_analysis_error": str(e), "ai_probability": 0.0, "ai_votes": 0}
    
    def task_face():
        try:
            face_score, face_explanation = check_facial_anomalies(image_data)
            
            # 低置信度时降低人脸分析权重
            if result["confidence_level"] != "high":
                face_score *= 0.7
            
            # 添加人脸分析摘要
            if face_score > 0.5:
                result["analysis_summary"]["face"] = f"检测到面部异常 ({face_score:.2f})"
                
            ai_votes = 1 if face_score > 0.6 else (-1 if face_score == 0 else 0)
            return {
                "face_score": face_score,
                "face_explanation": face_explanation,
                "ai_probability": face_score,
                "ai_votes": ai_votes
            }
        except Exception as e:
            return {"face_analysis_error": str(e), "ai_probability": 0.0, "ai_votes": 0}
    
    # 定义任务列表，按照重要性和耗时从小到大排序
    tasks = [
        task_ela,       # 错误等级分析 - 最重要
        task_noise,     # 噪声分析 - 次重要
        task_frequency, # 频域分析
        task_texture,   # 纹理分析
        task_face       # 面部分析
    ]
    
    if use_parallel:
        # 并行执行所有任务
        with ThreadPoolExecutor(max_workers=min(5, multiprocessing.cpu_count())) as executor:
            future_to_task = {executor.submit(task): task.__name__ for task in tasks}
            
            for future in as_completed(future_to_task):
                task_name = future_to_task[future]
                try:
                    task_result = future.result()
                    
                    # 更新临时分析结果
                    temp_analysis_results.update(task_result)
                    
                    # 更新总体概率 - 使用加权平均
                    if "ps_probability" in task_result and task_result["ps_probability"] > 0:
                        # 使用加权平均，保持最终结果不超过0.85（除非有元数据证据）
                        current_prob = result["ps_probability"]
                        task_prob = task_result.get("ps_probability", 0.0)
                        # 如果当前概率来自元数据检测(0.9)，则保留它
                        if current_prob < 0.9:
                            # 根据检测方法的重要性设置权重
                            if task_name == "task_ela":
                                weight = 0.5  # ELA是最重要的检测方法
                            elif task_name == "task_noise":
                                weight = 0.3  # 噪声分析次之
                            else:
                                weight = 0.2  # 其他方法权重较低
                                
                            result["ps_probability"] = min(0.85, (current_prob * (1-weight) + task_prob * weight))
                    
                    if "ai_probability" in task_result and task_result["ai_probability"] > 0:
                        current_prob = result["ai_probability"]
                        task_prob = task_result.get("ai_probability", 0.0)
                        result["ai_probability"] = min(0.85, (current_prob * 0.7 + task_prob * 0.3))
                    
                    # 更新投票
                    if "ps_votes" in task_result:
                        if task_result["ps_votes"] > 0:
                            result["votes"]["ps_positive"] += task_result["ps_votes"]
                        elif task_result["ps_votes"] < 0:
                            result["votes"]["ps_negative"] += abs(task_result["ps_votes"])
                    
                    if "ai_votes" in task_result:
                        if task_result["ai_votes"] > 0:
                            result["votes"]["ai_positive"] += task_result["ai_votes"]
                        elif task_result["ai_votes"] < 0:
                            result["votes"]["ai_negative"] += abs(task_result["ai_votes"])
                    
                    # 早期停止逻辑 - 判断非常明确时才早期停止
                    if early_stop:
                        # 元数据检测 + ELA/噪声都确认为PS
                        if has_ps_metadata and task_name in ["task_ela", "task_noise"] and task_result.get("ps_probability", 0) > 0.7:
                            if "region_analysis" in task_result and task_result["region_analysis"].get("has_local_edits", False):
                                # 有明确的局部编辑证据
                                if result["votes"]["ps_positive"] > result["votes"]["ps_negative"] + 2:
                                    for f in future_to_task:
                                        if not f.done():
                                            f.cancel()
                                    break
                    
                except Exception as e:
                    temp_analysis_results[f"{task_name}_error"] = str(e)
    else:
        # 串行执行所有任务
        for task in tasks:
            try:
                task_result = task()
                
                # 更新临时分析结果
                temp_analysis_results.update(task_result)
                
                # 更新总体概率 - 使用加权平均
                if "ps_probability" in task_result and task_result["ps_probability"] > 0:
                    # 使用加权平均，保持最终结果不超过0.85（除非有元数据证据）
                    current_prob = result["ps_probability"]
                    task_prob = task_result.get("ps_probability", 0.0)
                    # 如果当前概率来自元数据检测(0.9)，则保留它
                    if current_prob < 0.9:
                        # 根据检测方法的重要性设置权重
                        if task.__name__ == "task_ela":
                            weight = 0.5  # ELA是最重要的检测方法
                        elif task.__name__ == "task_noise":
                            weight = 0.3  # 噪声分析次之
                        else:
                            weight = 0.2  # 其他方法权重较低
                            
                        result["ps_probability"] = min(0.85, (current_prob * (1-weight) + task_prob * weight))
                
                if "ai_probability" in task_result and task_result["ai_probability"] > 0:
                    current_prob = result["ai_probability"]
                    task_prob = task_result.get("ai_probability", 0.0)
                    result["ai_probability"] = min(0.85, (current_prob * 0.7 + task_prob * 0.3))
                
                # 更新投票
                if "ps_votes" in task_result:
                    if task_result["ps_votes"] > 0:
                        result["votes"]["ps_positive"] += task_result["ps_votes"]
                    elif task_result["ps_votes"] < 0:
                        result["votes"]["ps_negative"] += abs(task_result["ps_votes"])
                
                if "ai_votes" in task_result:
                    if task_result["ai_votes"] > 0:
                        result["votes"]["ai_positive"] += task_result["ai_votes"]
                    elif task_result["ai_votes"] < 0:
                        result["votes"]["ai_negative"] += abs(task_result["ai_votes"])
                
                # 早期停止逻辑 - 判断非常明确时才早期停止
                if early_stop:
                    # 元数据检测 + ELA/噪声都确认为PS
                    if has_ps_metadata and task.__name__ in ["task_ela", "task_noise"] and task_result.get("ps_probability", 0) > 0.7:
                        if "region_analysis" in task_result and task_result["region_analysis"].get("has_local_edits", False):
                            # 有明确的局部编辑证据
                            if result["votes"]["ps_positive"] > result["votes"]["ps_negative"] + 2:
                                break
                
            except Exception as e:
                temp_analysis_results[f"{task.__name__}_error"] = str(e)
    
    # 综合评估 - 使用投票系统作为主要决策依据，概率作为次要依据
    ps_vote_result = result["votes"]["ps_positive"] - result["votes"]["ps_negative"]
    ai_vote_result = result["votes"]["ai_positive"] - result["votes"]["ai_negative"]
    
    # 考虑可疑区域数量作为额外判断依据
    suspicious_region_count = result["suspicious_count"]
    has_strong_local_evidence = suspicious_region_count >= 3
    
    # PS结论逻辑 - 简化输出
    if ps_vote_result > 3 or (ps_vote_result > 1 and has_strong_local_evidence):
        result["ps_conclusion"] = "图像很可能被PS过"
    elif ps_vote_result > 1 or (ps_vote_result > 0 and has_strong_local_evidence):
        result["ps_conclusion"] = "图像可能被PS过"
    else:
        # 处理低投票得分的情况
        # 特殊情况：有元数据但其他测试显示未PS
        if result["ps_probability"] > 0.5 and "metadata" in result["analysis_summary"]:
            result["ps_conclusion"] = "图像可能被PS过 (仅基于元数据证据)"
        else:
            # 普通情况
            result["ps_conclusion"] = "图像可能未被PS过"
    
    # AI检测结论逻辑 - 简化输出
    if ai_vote_result > 2:
        result["ai_conclusion"] = "图像很可能是AI合成的"
    elif ai_vote_result > 1:
        result["ai_conclusion"] = "图像可能是AI合成的"
    else:
        result["ai_conclusion"] = "图像可能不是AI合成的"
    
    # 最终结论判断逻辑
    ps_detected = "可能被PS过" in result["ps_conclusion"] or "很可能被PS过" in result["ps_conclusion"]
    ai_detected = "可能是AI合成" in result["ai_conclusion"] or "很可能是AI合成" in result["ai_conclusion"]
    
    # 如果检测到局部编辑但投票不足，仍然可能判定为PS
    if has_strong_local_evidence and result["ps_probability"] > 0.6 and not ps_detected:
        ps_detected = True
        result["ps_conclusion"] = "图像可能被PS过 (检测到多个可疑编辑区域)"
    
    # 如果检测到PS或AI合成，返回"不通过"，否则返回"通过"
    if ps_detected or ai_detected:
        result["final_conclusion"] = "不通过"
    else:
        result["final_conclusion"] = "通过"
    
    return result

def get_material_by_md5(orderNo: str, domain: str, appCode: str, appToken: str, uniqKey: str, imgPathMd5: str) -> Dict[str, Any]:
    """
    根据图片MD5获取单条材料记录并处理
    Args:
        orderNo: 订单号
        domain: 域名
        appCode: 应用代码
        appToken: 应用令牌
        uniqKey: 唯一标识
        imgPathMd5: 图片MD5值
    Returns:
        Dict[str, Any]: 简化的单条材料记录
    """
    if not imgPathMd5:
        return {}

    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/refund/refundconfirm/proveimg/showRecordMaterialList?orderNo={orderNo}&recordId=&crowdsourcingAuditResult=3&agentAuditResult=3&domain={domain}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        if "error" in result:
            return {}

        response_data = json.loads(result.get("data", "{}"))

        if not response_data or response_data.get("ret") is False:
            return {}

        material_list = response_data.get("data", {}).get("aduitModel", [])

        # 查找匹配的材料
        for material in material_list:
            img_path = material.get("imgPath", "")
            current_img_path_md5 = ""
            if img_path:
                # 对图片地址进行MD5加密
                current_img_path_md5 = hashlib.md5(img_path.encode('utf-8')).hexdigest()
            # 如果没有匹配的MD5，继续下一个
            if current_img_path_md5 != imgPathMd5:
                continue
                
            # 找到匹配的材料，处理并返回
            processed_material = {
                "uniqKey": uniqKey,
                "orderNo": material.get("orderNo", ""),
                "materialType": material.get("materialType", ""),
                "materialStringType": material.get("materialStringType", ""),
                "imgName": material.get("imgName", ""),
                "imgPathMd5": current_img_path_md5,
                "uploadTime": material.get("uploadTime", ""),
                "refundApplyTime": material.get("refundApplyTime", ""),
                "agentAuditResult": material.get("agentAuditResult", ""),
                "agentAuditResultChosen": material.get("agentAuditResultChosen", ""),
                "crowdsourcingAuditResult": material.get("crowdsourcingAuditResult", ""),
                "crowdsourcingAuditResultChosen": material.get("crowdsourcingAuditResultChosen", ""),
                "traceId": str(uuid.uuid4())
            }

            # 如果存在图片路径，进行图片识别
            if img_path:
                try:
                    # 下载图片数据
                    response = requests.get(img_path, stream=True)
                    response.raise_for_status()
                    image_data = response.content
                    # 执行图像处理检测 - 使用简化的返回值
                    ps_check_result = detect_photoshop_and_ai(image_data, 90, 10, use_parallel=True, early_stop=True)
                    
                    # 只获取关键结论字段
                    processed_material["ps_conclusion"] = ps_check_result.get("ps_conclusion", "")
                    processed_material["ai_conclusion"] = ps_check_result.get("ai_conclusion", "")
                    processed_material["final_conclusion"] = ps_check_result.get("final_conclusion", "")
                    
                    # 提取最关键的分析结果
                    processed_material["detection_summary"] = {
                        "ps_probability": round(ps_check_result.get("ps_probability", 0), 2),
                        "ai_probability": round(ps_check_result.get("ai_probability", 0), 2),
                        "suspicious_count": ps_check_result.get("suspicious_count", 0),
                        "confidence_level": ps_check_result.get("confidence_level", "medium"),
                        "analysis_summary": ps_check_result.get("analysis_summary", {})
                    }
                                
                except Exception as e:
                    processed_material["ps_conclusion"] = ""
                    processed_material["ai_conclusion"] = ""
                    processed_material["final_conclusion"] = ""
                    processed_material["detection_error"] = f"PS检测失败: {str(e)}"
            
            # 匹配到了材料并处理完成，返回结果
            return processed_material

        # 没有找到匹配的材料
        return {}

    except Exception as e:
        return {}

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数 - 根据图片MD5获取材料并检测是否PS
    Args:
        param: 包含orderNo、invokeAppCode、invokeToken和imgPathMd5的参数字典
    Returns:
        Dict: 简化的处理结果，只包含关键信息
    """
    orderNo = param.get("orderNo", "")
    uniqKey = param.get("uniqKey", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    imgPathMd5 = param.get("imgPathMd5", "")
    domain = param.get("domain", {})
    
    if not orderNo:
        return {"error": "订单号不能为空", "result": {}}
    
    if not imgPathMd5:
        return {"error": "图片MD5不能为空", "result": {}}

    try:
        # 获取单个材料
        material = get_material_by_md5(orderNo, domain, appCode, appToken, uniqKey, imgPathMd5)
        
        if not material:
            return {"error": f"未找到匹配的图片记录: {imgPathMd5}", "result": {}}
            
        # 返回处理后的单个材料，只保留关键信息
        return {
            "error": "",
            "result": material
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "result": {}}

def test():
    """
    测试函数 - 运行PS检测算法并显示简化结果
    """
    # 测试参数
    param = {
        "orderNo": "sgz250115104349320",
        "uniqKey": "1234567890",
        "imgPathMd5":"277d3788234bd5d371d314cded1a8193",
        "domain": "xep.trade.qunar.com",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    
    import time
    print("===== 运行PS检测算法测试 =====")
    print(f"订单号: {param['orderNo']}")
    print(f"图片MD5: {param['imgPathMd5']}")
    print("===========================")
    
    start_time = time.time()
    result = main(param)
    end_time = time.time()
    
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 
