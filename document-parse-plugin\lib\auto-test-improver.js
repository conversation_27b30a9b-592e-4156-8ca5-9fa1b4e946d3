/**
 * 自动测试改进器
 * 自动测试不同的解析策略，直到找到最佳方案
 */
class AutoTestImprover {
  constructor() {
    this.testCases = [
      {
        name: 'WPS测试用例',
        originalText: 'LAsSASASsSS',
        garbledText: `Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_\`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡\` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\\ WPS Office 专业版王如根@鞓D鞓耀 (\\耀dlKSOProductBuildVer2052-9.1.0.39140澐C`,
        expectedKeywords: ['LAsSASASsSS', 'WPS Office', '专业版', '王如根'],
        software: 'wps'
      },
      {
        name: 'Word测试用例',
        originalText: '你好傻逼傻逼',
        garbledText: `>/1.勰橢橢謯謯柩柩鈖爁$6耝或0鈖$,鈖戌瀓你好傻逼傻逼譨梺漀0週倲舮連逤匃逌踓槸葛码頀鸀瘂栁瀂嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄堁嘂縂适耂 佊偊兊彈浈湈獈瑈J怀J正文$愤 彈慊浈湈獈瑈$$默认段落字体BiB普通表格4l愀 欠 无列表偋!孃湯整瑮呟灹獥崮浸沬釋仃菥鲲苇軇粢餤喧呂氖鸻狣飃鲧嵇伷襖脚憥頏肇襉劚箥烦杌邎爟邤瘦能耎痍凜醉堽飹邴丶莳鋀覡姍哂敞谒褌忏朠鸻觬汙躲粎帶仌怔孿缂偋!牟汥猯爮汥葳迏櫃蟯薽菑兽瘯邥綣缨栢俛萈议铧骪棡瘽羿苉薤嬈灸蚣篛徵傼锒蠏俙劼擑戴缤醧影麘刖悮辨侞翁渄鐷楌拤厽邨斪倀!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸氌綡遷掻抲C鰚鿛菣鯕夬鰇訍旍蠮簬泡砘贓珈絑薐庹樤謽均偋!励桴浥支桴浥支桴浥攱砮汭姬毝缐缸撱鄶菵銑鋇閴宺毯巙寑鹈偈緩義譃髾鴑鏝目喥鯷駟賍滶糜儘獁屘诳螾逛侮黻逨拊峳堧逡躰鐿奘紑塆灋貆嘏茝裞閕鄈戔臚郴糿彇鿀悢阨成箔衇糖惇巼僺骍忐贕襲匿惀淲鞠闊嚦弃岨鋩仃匮櫪瞳峚娫硃铪磑毂詐竓肯骥荟傮暻鶷犩嵝鴪减逍癙旋倀詀褤餡忿癈昨嘂皅愍鋟訃萲阖俴操絚爃韣輧缽秲叛镚跢铀篻埃翤矋齯鵾麚漓淍鱶篢鯴徾紸鬟鼺澴燔崒懡揯謮荠嫨膫換迷吹堒蟆愻搁眙疣鷆庸沣渙軮鱭轍巜躄岶倧襢敋訃襢揩箩葬掱眏鰉鐶焑穋嘶芸屌雞癯篯畆嶻滢嵌襣霢誨蓷屍璈耩嫳葋齦练趠僜憜鏈牆鴮蠻擙袢藄郭龱訑璘矁駽蒨衫訃蚗囁髂铲億矆寋妘駐劮滄燢軌劰笇詣軑挀甆墖沪坣拝癆暱仮愐沥氉鿝峉駡籂駦蠽図迃轌腉綔賤庪噄匃鶯渓螗贖猼霠茯藝禔澧袺娆蓲蠭壡獔拕控桾烀操觏耚鿲缷熀獇熟唗琜雕鋭昙繮榨C夛堬圣驇族泜籤駩擲痞鄞齃棩駏傐釚訓葷晵蛐辧焽瘆蒘凰怰躴資駼賈蜎誾划袩砉瀓暤鶗躞嶣輺譅疬皙腖撾帨柫灌匥璥騟旟邋谰綨下遳笯倭奪哠毒纹燇鬰吏鳒像菹嚙耽胋諫皩蹈嬤璌棏葏驸檝洊刏冽霖贵鬚芕燍锡旺辆儂埔砃霼締匯夒搮褓田椺蠵裄櫹姻栘殬裦屖芅宒螲莑諆瓲稉嫼惲仮競瓌犵市潓鹙獿榍癚矍儺蟒畄錐妊双紝闹髮檸據笈檁厭驮赵铃箶郲农榞唕嗝欰猃鍜堷峍槰黩娷嵤黹痏樌骋押虘嗍麮掶鶨唘医柍服玥畾溾曖額狇吊楸撓敃溋偋!龐'桴浥支桴浥支牟汥猯桴浥敍湡条牥砮汭爮汥葳轍蓷瞂漈濓鄦裝蓤儤蜮憾榙鞻鷉挓栱闩驱淁蹀刖襎撰艠澎朕酋刨犘鍊鳐咊胹辣囤痷綠鯱簁抽笐阙驐褚术巾允珙蔅鮪寊倀!嬀潃瑮湥彴祔数嵳砮汭偋-!牟汥猯爮汥偳!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸偬!勪琀敨敭琯敨敭琯敨敭浸偬!郑龶'琀敨敭琯敨敭弯敲獬琯敨敭慍慮敧爮浸氮敲獬偋崁浸氠敶獲潩渽湥潣楤杮呕猠慴摮污湯攽礢獥愺汣牍灡砠汭獮愺瑨灴猯档浥獡漮数确汭潦浲瑡献牯术牤睡湩浧氯洯楡渢戠朱瑬琠砱此戠朲瑬琠砲此愠捣湥琱捡散瑮愠捣湥琲捡散瑮愠捣湥琳捡散瑮愠捣湥琴捡散瑮愠捣湥琵捡散瑮愠捣湥琶捡散瑮栠楬歮汨湩欢映汯汈湩欽昢汯汈湩欢"8@肀耀鋰匀?3億(朗謀老Unknown送硛Times New Roman送耀Symbol送硛Arial送蘃 糺等线DengXian蘀 糺等线 Light送Cambria Math 耘栁逃?!%),.:;>?]}嫾峾廾峿巿廿$([{姾対巾寿鰀肂2茑偀値翿2xx王如根王如根椉跰娧馵悼路娧馵藠鿲俹栐醫0氁退頀吁封搁潎浲污搮瑯m2楍牣獯景琠晏楦散圠牯d@@蛾@蛾鰮鞓D鰮鞓簁頀 \` ?G卋偏潲畤瑣畂汩噤牥2 !"#$%'()*+,-0Root EntryF耝2耀Data1TableWordDocumentSummaryInformation(DocumentSummaryInformation8&CompObjnF楍牣獯景琠潗摲卍潗摲潄c潗摲捯浵湥琮8熲`,
        expectedKeywords: ['你好傻逼傻逼', '王如根', 'Microsoft', 'Word'],
        software: 'word'
      }
    ];
    
    this.strategies = [
      {
        name: '精确文本提取',
        method: this.preciseTextExtraction.bind(this)
      },
      {
        name: '关键词周围提取',
        method: this.keywordContextExtraction.bind(this)
      },
      {
        name: '智能分段过滤',
        method: this.intelligentSegmentFilter.bind(this)
      },
      {
        name: '模式识别清理',
        method: this.patternRecognitionClean.bind(this)
      },
      {
        name: '统计学过滤',
        method: this.statisticalFilter.bind(this)
      }
    ];
    
    this.bestStrategy = null;
    this.testResults = [];
  }

  // 自动测试所有策略
  async autoTest() {
    console.log('🚀 开始自动测试改进...');
    
    for (const strategy of this.strategies) {
      console.log(`\n📊 测试策略: ${strategy.name}`);
      
      const strategyResults = [];
      
      for (const testCase of this.testCases) {
        console.log(`  测试用例: ${testCase.name}`);
        
        try {
          const result = await strategy.method(testCase.garbledText, testCase);
          const score = this.evaluateResult(result, testCase);
          
          strategyResults.push({
            testCase: testCase.name,
            result: result,
            score: score,
            success: score > 80
          });
          
          console.log(`    结果: ${result.substring(0, 50)}...`);
          console.log(`    得分: ${score}`);
          
        } catch (error) {
          console.error(`    错误: ${error.message}`);
          strategyResults.push({
            testCase: testCase.name,
            result: '',
            score: 0,
            success: false,
            error: error.message
          });
        }
      }
      
      const avgScore = strategyResults.reduce((sum, r) => sum + r.score, 0) / strategyResults.length;
      
      this.testResults.push({
        strategy: strategy.name,
        results: strategyResults,
        averageScore: avgScore,
        successRate: strategyResults.filter(r => r.success).length / strategyResults.length
      });
      
      console.log(`  平均得分: ${avgScore.toFixed(1)}`);
      console.log(`  成功率: ${(strategyResults.filter(r => r.success).length / strategyResults.length * 100).toFixed(1)}%`);
    }
    
    // 选择最佳策略
    this.bestStrategy = this.testResults.reduce((best, current) => 
      current.averageScore > best.averageScore ? current : best
    );
    
    console.log(`\n🏆 最佳策略: ${this.bestStrategy.strategy}`);
    console.log(`   平均得分: ${this.bestStrategy.averageScore.toFixed(1)}`);
    console.log(`   成功率: ${(this.bestStrategy.successRate * 100).toFixed(1)}%`);
    
    return this.bestStrategy;
  }

  // 策略1: 精确文本提取
  async preciseTextExtraction(garbledText, testCase) {
    // 直接查找原文
    const originalText = testCase.originalText;
    const index = garbledText.indexOf(originalText);
    
    if (index !== -1) {
      // 找到原文，提取周围的上下文
      const start = Math.max(0, index - 20);
      const end = Math.min(garbledText.length, index + originalText.length + 20);
      let context = garbledText.substring(start, end);
      
      // 清理上下文
      context = this.cleanText(context);
      
      // 如果上下文太短，只返回原文
      if (context.length < originalText.length * 1.5) {
        return originalText;
      }
      
      return context;
    }
    
    return '';
  }

  // 策略2: 关键词周围提取
  async keywordContextExtraction(garbledText, testCase) {
    const keywords = testCase.expectedKeywords;
    const extractedParts = [];
    
    for (const keyword of keywords) {
      const index = garbledText.indexOf(keyword);
      if (index !== -1) {
        // 提取关键词周围的文本
        const start = Math.max(0, index - 30);
        const end = Math.min(garbledText.length, index + keyword.length + 30);
        const context = garbledText.substring(start, end);
        
        // 清理并添加
        const cleaned = this.cleanText(context);
        if (cleaned.length > keyword.length) {
          extractedParts.push(cleaned);
        }
      }
    }
    
    // 去重并合并
    const uniqueParts = [...new Set(extractedParts)];
    return uniqueParts.join(' ');
  }

  // 策略3: 智能分段过滤
  async intelligentSegmentFilter(garbledText, testCase) {
    // 按不同分隔符分段
    const segments = this.segmentText(garbledText);
    const validSegments = [];
    
    for (const segment of segments) {
      const score = this.scoreSegment(segment, testCase);
      if (score > 30) { // 阈值可调整
        validSegments.push({
          text: segment,
          score: score
        });
      }
    }
    
    // 按得分排序，取前几个
    validSegments.sort((a, b) => b.score - a.score);
    const topSegments = validSegments.slice(0, 3);
    
    return topSegments.map(s => this.cleanText(s.text)).join(' ');
  }

  // 策略4: 模式识别清理
  async patternRecognitionClean(garbledText, testCase) {
    let cleaned = garbledText;
    
    // 移除明显的乱码模式
    const garbagePatterns = [
      /[^\u4e00-\u9fff\u0020-\u007E\s]{10,}/g, // 连续非中文非ASCII字符
      /(.)\1{5,}/g, // 重复字符
      /[!@#$%^&*()]{3,}/g, // 连续特殊字符
      /\d{10,}/g, // 长数字串
      /[A-Za-z]{20,}/g // 长英文串（可能是乱码）
    ];
    
    for (const pattern of garbagePatterns) {
      cleaned = cleaned.replace(pattern, ' ');
    }
    
    // 提取有意义的部分
    const meaningfulParts = [];
    const words = cleaned.split(/\s+/);
    
    for (const word of words) {
      if (this.isMeaningfulWord(word, testCase)) {
        meaningfulParts.push(word);
      }
    }
    
    return meaningfulParts.join(' ');
  }

  // 策略5: 统计学过滤
  async statisticalFilter(garbledText, testCase) {
    // 字符频率分析
    const charFreq = this.analyzeCharFrequency(garbledText);
    
    // 过滤低频异常字符
    let filtered = '';
    for (let i = 0; i < garbledText.length; i++) {
      const char = garbledText[i];
      const freq = charFreq[char] || 0;
      
      // 保留高频字符或已知有意义的字符
      if (freq > 2 || this.isKnownGoodChar(char, testCase)) {
        filtered += char;
      }
    }
    
    // 进一步清理
    return this.cleanText(filtered);
  }

  // 文本分段
  segmentText(text) {
    const separators = ['\n', '  ', '!', '@', '#', '$', '%', '^', '&', '*'];
    let segments = [text];
    
    for (const sep of separators) {
      const newSegments = [];
      for (const segment of segments) {
        newSegments.push(...segment.split(sep));
      }
      segments = newSegments;
    }
    
    return segments.filter(s => s.trim().length > 3);
  }

  // 段落评分
  scoreSegment(segment, testCase) {
    let score = 0;
    
    // 包含原文加分
    if (segment.includes(testCase.originalText)) {
      score += 50;
    }
    
    // 包含关键词加分
    for (const keyword of testCase.expectedKeywords) {
      if (segment.includes(keyword)) {
        score += 20;
      }
    }
    
    // 中文字符比例加分
    const chineseChars = segment.match(/[\u4e00-\u9fff]/g) || [];
    const chineseRatio = chineseChars.length / segment.length;
    score += chineseRatio * 30;
    
    // 可读字符比例加分
    const readableChars = segment.match(/[\u4e00-\u9fff\u0020-\u007E]/g) || [];
    const readableRatio = readableChars.length / segment.length;
    score += readableRatio * 20;
    
    // 长度适中加分
    if (segment.length >= 5 && segment.length <= 100) {
      score += 10;
    }
    
    return score;
  }

  // 检查是否为有意义的词
  isMeaningfulWord(word, testCase) {
    if (word.length < 2) return false;
    
    // 包含原文或关键词
    if (testCase.expectedKeywords.some(k => word.includes(k))) {
      return true;
    }
    
    // 纯中文词
    if (/^[\u4e00-\u9fff]+$/.test(word)) {
      return true;
    }
    
    // 常见英文词
    const commonWords = ['Microsoft', 'Word', 'Office', 'WPS', 'Document', 'Root', 'Entry'];
    if (commonWords.some(w => word.includes(w))) {
      return true;
    }
    
    return false;
  }

  // 字符频率分析
  analyzeCharFrequency(text) {
    const freq = {};
    for (const char of text) {
      freq[char] = (freq[char] || 0) + 1;
    }
    return freq;
  }

  // 检查是否为已知好字符
  isKnownGoodChar(char, testCase) {
    const charCode = char.charCodeAt(0);
    
    // 中文字符
    if (charCode >= 0x4e00 && charCode <= 0x9fff) return true;
    
    // ASCII可打印字符
    if (charCode >= 0x20 && charCode <= 0x7E) return true;
    
    // 原文中的字符
    if (testCase.originalText.includes(char)) return true;
    
    return false;
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/\s{3,}/g, '  ') // 合并空格
      .replace(/(.)\1{4,}/g, '$1') // 移除重复字符
      .trim();
  }

  // 评估结果
  evaluateResult(result, testCase) {
    let score = 0;
    
    // 包含原文 (最重要)
    if (result.includes(testCase.originalText)) {
      score += 60;
    }
    
    // 包含关键词
    for (const keyword of testCase.expectedKeywords) {
      if (result.includes(keyword)) {
        score += 10;
      }
    }
    
    // 文本质量
    const cleanRatio = this.calculateCleanRatio(result);
    score += cleanRatio * 20;
    
    // 长度合理性
    if (result.length >= testCase.originalText.length && result.length <= testCase.originalText.length * 3) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }

  // 计算清洁度比例
  calculateCleanRatio(text) {
    if (!text) return 0;
    
    const cleanChars = text.match(/[\u4e00-\u9fff\u0020-\u007E]/g) || [];
    return cleanChars.length / text.length;
  }

  // 生成测试报告
  generateReport() {
    let report = `自动测试改进报告\n`;
    report += `==================\n\n`;
    
    this.testResults.forEach((strategyResult, index) => {
      report += `${index + 1}. ${strategyResult.strategy}\n`;
      report += `   平均得分: ${strategyResult.averageScore.toFixed(1)}\n`;
      report += `   成功率: ${(strategyResult.successRate * 100).toFixed(1)}%\n`;
      
      strategyResult.results.forEach(result => {
        report += `   - ${result.testCase}: ${result.success ? '✅' : '❌'} (${result.score}分)\n`;
        if (result.result) {
          report += `     结果: ${result.result.substring(0, 50)}...\n`;
        }
      });
      report += `\n`;
    });
    
    if (this.bestStrategy) {
      report += `🏆 推荐策略: ${this.bestStrategy.strategy}\n`;
      report += `   这是表现最好的策略，建议在实际应用中使用。\n`;
    }
    
    return report;
  }

  // 应用最佳策略
  async applyBestStrategy(garbledText, testCase) {
    if (!this.bestStrategy) {
      await this.autoTest();
    }
    
    const strategyMethod = this.strategies.find(s => s.name === this.bestStrategy.strategy)?.method;
    if (strategyMethod) {
      return await strategyMethod(garbledText, testCase);
    }
    
    return garbledText;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AutoTestImprover;
} else if (typeof window !== 'undefined') {
  window.AutoTestImprover = AutoTestImprover;
}
