/**
 * 数据压缩处理器
 * 支持JSON数据压缩和解压缩
 */
class DataCompressor {
  constructor() {
    this.compressionEnabled = typeof pako !== 'undefined';
    if (!this.compressionEnabled) {
      console.warn('Pako库未加载，将使用原始数据传输');
    }
  }

  /**
   * 压缩JSON数据
   * @param {Object} data - 要压缩的数据对象
   * @returns {string} 压缩后的base64字符串或原始JSON字符串
   */
  compressJSON(data) {
    try {
      const jsonString = JSON.stringify(data);

      if (!this.compressionEnabled) {
        return jsonString;
      }

      // 使用pako进行gzip压缩
      const compressed = pako.gzip(jsonString);

      // 转换为base64字符串
      const base64 = btoa(String.fromCharCode.apply(null, compressed));

      console.log(`数据压缩: ${jsonString.length} bytes -> ${base64.length} bytes (压缩率: ${((1 - base64.length / jsonString.length) * 100).toFixed(1)}%)`);

      return base64;
    } catch (error) {
      console.error('数据压缩失败:', error);
      return JSON.stringify(data);
    }
  }

  /**
   * 解压缩数据
   * @param {string} compressedData - 压缩的base64字符串
   * @returns {Object} 解压缩后的数据对象
   */
  decompressJSON(compressedData) {
    try {
      if (!this.compressionEnabled) {
        return JSON.parse(compressedData);
      }

      // 从base64解码
      const binaryString = atob(compressedData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // 使用pako解压缩
      const decompressed = pako.ungzip(bytes, { to: 'string' });

      return JSON.parse(decompressed);
    } catch (error) {
      console.error('数据解压缩失败:', error);
      // 尝试直接解析JSON
      try {
        return JSON.parse(compressedData);
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        return null;
      }
    }
  }

  /**
   * 检查是否应该压缩数据
   * @param {string} jsonString - JSON字符串
   * @returns {boolean} 是否应该压缩
   */
  shouldCompress(jsonString) {
    // 对于API传输，更积极地压缩（超过500字节就压缩）
    return this.compressionEnabled && jsonString.length > 500;
  }

  /**
   * 智能压缩 - 根据数据大小决定是否压缩
   * @param {Object} data - 要处理的数据对象
   * @returns {Object} 包含数据和压缩信息的对象
   */
  smartCompress(data) {
    const jsonString = JSON.stringify(data);

    if (this.shouldCompress(jsonString)) {
      return {
        data: this.compressJSON(data),
        compressed: true,
        originalSize: jsonString.length,
        compressedSize: this.compressJSON(data).length
      };
    } else {
      return {
        data: jsonString,
        compressed: false,
        originalSize: jsonString.length,
        compressedSize: jsonString.length
      };
    }
  }
}
