/**
 * DOC文件转换助手
 * 提供多种.doc文件处理方案
 */
class DocConversionHelper {
  constructor() {
    this.conversionMethods = [
      'fileReader',
      'binaryExtraction',
      'userGuidance'
    ];
  }

  // 主要的处理方法
  async handleDocFile(file) {
    console.log('开始处理.doc文件:', file.name);
    
    // 方法1: 尝试FileReader API的不同方式
    try {
      const result1 = await this.tryFileReaderMethods(file);
      if (result1.success) {
        return result1;
      }
    } catch (error) {
      console.warn('FileReader方法失败:', error.message);
    }

    // 方法2: 尝试二进制数据提取
    try {
      const result2 = await this.tryBinaryExtraction(file);
      if (result2.success) {
        return result2;
      }
    } catch (error) {
      console.warn('二进制提取失败:', error.message);
    }

    // 方法3: 提供用户指导和转换建议
    return this.provideUserGuidance(file);
  }

  // FileReader多种方法尝试
  async tryFileReaderMethods(file) {
    const methods = [
      { name: 'readAsText', encoding: 'UTF-8' },
      { name: 'readAsText', encoding: 'UTF-16LE' },
      { name: 'readAsText', encoding: 'GBK' },
      { name: 'readAsText', encoding: 'GB18030' },
      { name: 'readAsText', encoding: 'Windows-1252' },
      { name: 'readAsArrayBuffer' }
    ];

    for (const method of methods) {
      try {
        console.log(`尝试方法: ${method.name} ${method.encoding || ''}`);
        const result = await this.readFileWithMethod(file, method);
        
        if (result && result.length > 50) {
          const cleanedText = this.cleanExtractedText(result);
          if (cleanedText.length > 20) {
            return {
              success: true,
              text: cleanedText,
              html: this.textToHtml(cleanedText),
              method: `${method.name} ${method.encoding || ''}`
            };
          }
        }
      } catch (error) {
        console.warn(`方法 ${method.name} 失败:`, error.message);
      }
    }

    return { success: false };
  }

  // 使用指定方法读取文件
  readFileWithMethod(file, method) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          let result = e.target.result;
          
          if (method.name === 'readAsArrayBuffer') {
            // 从ArrayBuffer提取文本
            result = this.extractTextFromArrayBuffer(result);
          }
          
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('文件读取失败'));
      
      if (method.name === 'readAsText') {
        reader.readAsText(file, method.encoding);
      } else if (method.name === 'readAsArrayBuffer') {
        reader.readAsArrayBuffer(file);
      }
    });
  }

  // 从ArrayBuffer提取文本
  extractTextFromArrayBuffer(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';
    let bestScore = 0;
    
    // 方法1: UTF-16LE提取
    const utf16Text = this.extractUTF16LE(uint8Array);
    const utf16Score = this.scoreExtractedText(utf16Text);
    if (utf16Score > bestScore) {
      text = utf16Text;
      bestScore = utf16Score;
    }
    
    // 方法2: ASCII提取
    const asciiText = this.extractASCII(uint8Array);
    const asciiScore = this.scoreExtractedText(asciiText);
    if (asciiScore > bestScore) {
      text = asciiText;
      bestScore = asciiScore;
    }
    
    // 方法3: GBK提取
    const gbkText = this.extractGBK(uint8Array);
    const gbkScore = this.scoreExtractedText(gbkText);
    if (gbkScore > bestScore) {
      text = gbkText;
      bestScore = gbkScore;
    }
    
    // 方法4: 智能扫描
    const smartText = this.smartExtraction(uint8Array);
    const smartScore = this.scoreExtractedText(smartText);
    if (smartScore > bestScore) {
      text = smartText;
      bestScore = smartScore;
    }
    
    return text;
  }

  // GBK编码提取
  extractGBK(uint8Array) {
    let text = '';
    let i = 0;
    
    // GBK编码范围
    const isGBKFirstByte = byte => (byte >= 0x81 && byte <= 0xFE);
    const isGBKSecondByte = byte => (byte >= 0x40 && byte <= 0xFE && byte !== 0x7F);
    
    try {
      while (i < uint8Array.length) {
        const byte1 = uint8Array[i];
        
        // ASCII字符
        if (byte1 <= 0x7F) {
          if (byte1 >= 0x20) { // 可打印ASCII字符
            text += String.fromCharCode(byte1);
          } else if (byte1 === 0x0D || byte1 === 0x0A) {
            text += '\n';
          } else if (byte1 === 0x09) {
            text += '\t';
          }
          i++;
          continue;
        }
        
        // 可能是GBK字符
        if (i + 1 < uint8Array.length && isGBKFirstByte(byte1)) {
          const byte2 = uint8Array[i + 1];
          if (isGBKSecondByte(byte2)) {
            try {
              // 尝试使用TextDecoder解码GBK
              const decoder = new TextDecoder('gb18030');
              const char = decoder.decode(new Uint8Array([byte1, byte2]));
              text += char;
              i += 2;
              continue;
            } catch (e) {
              // 解码失败，跳过这个字节
              i++;
              continue;
            }
          }
        }
        
        // 不是有效的GBK字符，跳过
        i++;
      }
    } catch (e) {
      console.warn('GBK提取过程出错:', e);
    }
    
    return text;
  }

  // 评分提取的文本质量
  scoreExtractedText(text) {
    if (!text) return 0;
    let score = 0;
    
    // 检查中文字符
    const chineseChars = text.match(/[\u4e00-\u9fff]/g) || [];
    score += chineseChars.length * 2;
    
    // 检查常见中文标点
    const chinesePunct = text.match(/[。，、；：？！""''（）【】《》]/g) || [];
    score += chinesePunct.length;
    
    // 检查ASCII字符
    const asciiChars = text.match(/[a-zA-Z0-9]/g) || [];
    score += asciiChars.length;
    
    // 检查文本长度
    score += Math.min(text.length / 10, 100);
    
    // 减分项：无效字符
    const invalidChars = text.match(/[\ufffd\u0000-\u0008\u000b-\u000c\u000e-\u001f]/g) || [];
    score -= invalidChars.length * 10;
    
    return score;
  }

  // UTF-16LE提取
  extractUTF16LE(uint8Array) {
    let text = '';
    const startOffsets = [0x200, 0x400, 0x800, 0x1000, 0];
    
    for (const offset of startOffsets) {
      let currentText = '';
      let validChars = 0;
      
      for (let i = offset; i < uint8Array.length - 1; i += 2) {
        const char = uint8Array[i] | (uint8Array[i + 1] << 8);
        
        if ((char >= 0x20 && char <= 0x7E) || (char >= 0x4e00 && char <= 0x9fff)) {
          currentText += String.fromCharCode(char);
          validChars++;
        } else if (char === 0x0D || char === 0x0A) {
          currentText += '\n';
        } else if (char === 0x09) {
          currentText += '\t';
        } else if (char === 0x20) {
          currentText += ' ';
        }
        
        if (currentText.length > 1000) break;
      }
      
      if (currentText.length > text.length) {
        text = currentText;
      }
    }
    
    return text;
  }

  // ASCII提取
  extractASCII(uint8Array) {
    let text = '';
    
    for (let i = 0; i < uint8Array.length; i++) {
      const char = uint8Array[i];
      
      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      }
    }
    
    return text;
  }

  // 智能提取
  smartExtraction(uint8Array) {
    // 查找可能的文本区域
    const textRegions = [];
    let currentRegion = null;
    
    for (let i = 0; i < uint8Array.length; i++) {
      const char = uint8Array[i];
      
      if (char >= 0x20 && char <= 0x7E) {
        if (!currentRegion) {
          currentRegion = { start: i, end: i, text: String.fromCharCode(char) };
        } else {
          currentRegion.end = i;
          currentRegion.text += String.fromCharCode(char);
        }
      } else {
        if (currentRegion && currentRegion.text.length > 10) {
          textRegions.push(currentRegion);
        }
        currentRegion = null;
      }
    }
    
    if (currentRegion && currentRegion.text.length > 10) {
      textRegions.push(currentRegion);
    }
    
    // 返回最长的文本区域
    const longestRegion = textRegions.reduce((longest, current) => 
      current.text.length > longest.text.length ? current : longest, 
      { text: '' }
    );
    
    return longestRegion.text;
  }

  // 二进制提取尝试
  async tryBinaryExtraction(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const text = this.extractTextFromArrayBuffer(arrayBuffer);
      
      if (text && text.length > 20) {
        const cleanedText = this.cleanExtractedText(text);
        return {
          success: true,
          text: cleanedText,
          html: this.textToHtml(cleanedText),
          method: 'Binary Extraction'
        };
      }
    } catch (error) {
      console.error('二进制提取失败:', error);
    }
    
    return { success: false };
  }

  // 提供用户指导
  provideUserGuidance(file) {
    const guidance = `
无法自动解析 ${file.name}

建议的解决方案：

1. 📄 转换为.docx格式：
   - 用Microsoft Word打开文件
   - 点击"文件" → "另存为"
   - 选择"Word文档(.docx)"格式
   - 重新上传转换后的文件

2. 🔧 使用在线转换工具：
   - 访问 https://convertio.co/doc-docx/
   - 或 https://www.zamzar.com/convert/doc-to-docx/
   - 上传.doc文件并转换为.docx

3. 📋 手动复制粘贴：
   - 用Word或其他编辑器打开文件
   - 选择所有内容并复制
   - 粘贴到文本编辑器中
   - 保存为.txt文件后上传

4. 🔄 尝试其他格式：
   - 将文件另存为RTF格式
   - 或保存为纯文本(.txt)格式

如果文件包含重要格式或表格，建议使用方案1或2。
如果只需要文本内容，可以使用方案3或4。
    `;

    return {
      success: false,
      text: guidance,
      html: this.guidanceToHtml(guidance),
      method: 'User Guidance',
      isGuidance: true
    };
  }

  // 清理提取的文本
  cleanExtractedText(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/\s{3,}/g, '  ') // 移除过多空格
      .replace(/\n{3,}/g, '\n\n') // 移除过多换行
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .trim();
  }

  // 文本转HTML
  textToHtml(text) {
    if (!text) return '<p>无内容</p>';
    
    const paragraphs = text.split('\n\n');
    let html = '';
    
    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        html += `<p>${this.escapeHtml(paragraph.trim())}</p>`;
      }
    });
    
    return html || '<p>无法提取内容</p>';
  }

  // 指导信息转HTML
  guidanceToHtml(guidance) {
    return `
      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
        <h3 style="color: #007bff; margin-top: 0;">📄 .doc文件处理指南</h3>
        <pre style="white-space: pre-wrap; font-family: inherit; background: white; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;">${this.escapeHtml(guidance)}</pre>
        <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 4px;">
          <strong>💡 提示：</strong> .docx格式的兼容性更好，建议优先转换为.docx格式。
        </div>
      </div>
    `;
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocConversionHelper;
} else if (typeof window !== 'undefined') {
  window.DocConversionHelper = DocConversionHelper;
}