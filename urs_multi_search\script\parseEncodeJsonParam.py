from urllib.parse import unquote_to_bytes
import json
from datetime import datetime
from typing import Optional

def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None

def filter_flight_data(request_params, target_data):
    """
    根据请求参数过滤航班数据
    :param request_params: 用户请求参数字典
    :param target_data: 目标数据列表
    :return: 过滤后的数据列表
    """
    filtered = []
    
    req_departure_date = None
    if request_params.get('departureDate'):
        req_departure_date = safe_parse_date(request_params.get('departureDate'), "%Y-%m-%d")

    # 遍历所有目标数据
    for item in target_data:
        match = True

        # 条件2：departureDate 匹配（精确到天）
        if req_departure_date and match:
            item_departure_date = safe_parse_date(item.get('departureDate'), "%Y-%m-%d")
            if item_departure_date != req_departure_date:
                match = False

        # 条件3：departureCity 匹配
        if request_params.get('departureCity') and match:
            if item['departureCity'] != request_params['departureCity']:
                match = False

        # 条件4：arrivalCity 匹配
        if request_params.get('arrivalCity') and match:
            if item['arrivalCity'] != request_params['arrivalCity']:
                match = False

        # 条件5：flightNumber 匹配（双方都不为空时）
        req_flight_number = request_params.get('flightNumber')  # 注意参数名拼写错误
        item_flight_number = item.get('flightNumber')
        if req_flight_number and len(req_flight_number) > 2 and item_flight_number and match:
            if item_flight_number != req_flight_number:
                match = False

        req_carrier = request_params.get('carrier')  # 注意参数名拼写错误
        if req_carrier and item_flight_number and len(req_carrier) >= 2 and len(item_flight_number) >= 2 and match:
            item_carrier = item_flight_number[:2]
            carrier = req_carrier[:2]
            if carrier != item_carrier:
                match = False

        if match:
            filtered.append(item)

    return filtered

def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}

# 测试用例
if __name__ == "__main__":
    test_cases = [
        ({'param': '%7B%22name%22%3A%20%22%E5%BC%A0%E4%B8%89%22%7D'},  # 正常编码
         {"name": "张三"}),
         
        ({'param': '{"age": 25}'},  # 未编码合法JSON
         {"age": 25}),
         
        ({'param': 'name%3Dtest%26age%3D30'},  # 普通URL参数
         None),
         
        ({'param': ''},  # 空值
         None),
         
        ({'param': b'%7B%22error%22%3A%20false%7D'},  # bytes类型参数
         {"error": False}),
         
        ({'param': None},  # 非法类型
         None)
    ]

    for data, expected in test_cases:
        result, status = parse_urlencoded_json(data, 'param')
        print(f"输入: {data.get('param')}")
        print(f"状态: {status['status']}")
        if status['status'] == 'success':
            print(f"结果: {result}\n")
        else:
            print(f"错误: {status['message']}\n")
