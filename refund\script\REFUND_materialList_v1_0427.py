import requests
import json
import hashlib
from typing import Dict, List, Any, <PERSON><PERSON>

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def get_material_list(orderNo: str, domain: str, appCode: str, appToken: str, uniqKey: str) -> List[Dict[str, Any]]:
    """
    获取材料列表
    Args:
        orderNo: 订单号
        domain: 域名
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        List[Dict[str, Any]]: 材料列表
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/refund/refundconfirm/proveimg/showRecordMaterialList?orderNo={orderNo}&recordId=&crowdsourcingAuditResult=3&agentAuditResult=3&domain={domain}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return []

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            return []

        # 获取材料列表
        material_list = response_data.get("data", {}).get("aduitModel", [])
        
        # 处理材料列表，只保留需要的字段
        processed_list = []
        for material in material_list:
            img_path = material.get("imgPath", "")
            img_path_md5 = ""
            if img_path:
                # 对图片地址进行MD5加密
                img_path_md5 = hashlib.md5(img_path.encode('utf-8')).hexdigest()
                
            processed_material = {
                "uniqKey": uniqKey,
                "orderNo": material.get("orderNo", ""),
                "materialType": material.get("materialType", ""),
                "materialStringType": material.get("materialStringType", ""),
                "imgName": material.get("imgName", ""),
                #图片地址md5加密后返回
                "imgPathMd5": img_path_md5,
                "uploadTime": material.get("uploadTime", ""),
                "refundApplyTime": material.get("refundApplyTime", ""),
                "agentAuditResult": material.get("agentAuditResult", ""),
                "agentAuditResultChosen": material.get("agentAuditResultChosen", ""),
                "crowdsourcingAuditResult": material.get("crowdsourcingAuditResult", ""),
                "crowdsourcingAuditResultChosen": material.get("crowdsourcingAuditResultChosen", "")
            }
            processed_list.append(processed_material)
            
        return processed_list

    except Exception as e:
        return []

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含orderNo、invokeAppCode和invokeToken的参数字典
    Returns:
        Dict: 处理结果
    """
    orderNo = param.get("orderNo", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    uniqKey = param.get("uniqKey", "")
    domain = param.get("domain", {})
    
    if not orderNo:
        return {"error": "订单号不能为空", "results": [], "orderInfo": {}}

    try:
        # 获取订单信息
        if not domain:
            return {"error": "域名不能为空", "results": [], "orderInfo": {}}
            
        # 获取材料列表
        material_list = get_material_list(orderNo, domain, appCode, appToken, uniqKey)
        
        return {
            "error": "",
            "results": material_list,
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "results": [], "orderInfo": {}}

def test():
    param = {
        "orderNo": "xep250507193110180",
        "uniqKey": "1234567890",
        "invokeAppCode": "f_pangu",
        "domain": "xep.trade.qunar.com",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 