import json
import re
from typing import Any, Dict, Optional, Union
import requests


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        HEADER_SERVER_TOKEN = "Q-Server-Token"
        HEADER_APP_CODE = "Q-App-Code"
        # Make POST request with proxyData as JSON
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        response = requests.post(
            proxy, json=proxyData, headers=headers
        )

        response.raise_for_status() 
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def mask_sensitive_info(data: Any) -> Any:
    """
    递归遍历数据，对敏感信息进行脱敏处理。
    敏感信息包括：身份证号、手机号、银行卡号。
    保留前三位数字，其余用*替换。

    Args:
        data (Any): 需要处理的数据，可以是字典、列表或基本类型

    Returns:
        Any: 处理后的数据
    """
    # 定义敏感信息的正则表达式模式
    patterns = {
        'id_card': r'^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$',
        'phone': r'^1[3-9]\d{9}$',
        'bank_card': r'^\d{16,19}$'
    }

    if isinstance(data, dict):
        # 处理字典
        result = {}
        for key, value in data.items():
            # 检查值是否为字符串且匹配敏感信息模式
            if isinstance(value, str):
                for pattern_name, pattern in patterns.items():
                    if re.match(pattern, value):
                        # 保留前三位，其余用*替换
                        value = value[:3] + '*' * (len(value) - 3)
                        break
            # 递归处理嵌套数据
            result[key] = mask_sensitive_info(value)
        return result
    elif isinstance(data, list):
        # 处理列表
        return [mask_sensitive_info(item) for item in data]
    else:
        # 基本类型直接返回
        return data


def main(param: Dict[str, str]) -> Dict[str, Any]:
    """
    通用的HTTP代理调用方法

    Args:
        param (Dict[str, str]): 包含以下字段的字典:
            - method: HTTP方法，如 'get', 'post' 等
            - url: 请求的URL
            - data: 请求数据 (可选)
            - qssAuthTarget: 认证目标，如 'fuwu' 或 'paoding'

    Returns:
        Dict[str, Any]: 包含响应数据的字典，格式为:
        {
            "success": bool,
            "error": str,
            "data": {
                "response": Any  # JSON格式的响应数据，已脱敏
            }
        }
    """
    
    method = param.get("method")
    url = param.get("url")
    data = param.get("data")
    qssAuthTarget = param.get("qssAuthTarget")
    appCode = param.get("invokeAppCode")
    invokeToken = param.get("invokeToken")
    if not qssAuthTarget or not url or not method:
        return {"success": False, "error": "qssAuthTarget or url or method is required", "data": {}}

    if(qssAuthTarget == "fuwu"):
        qssAuthTarget = "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso"
        authCookies = ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"]
    elif(qssAuthTarget == "paoding"):
        qssAuthTarget = "https://paoding.corp.qunar.com/login"
        authCookies = ["user_id", "JSESSIONID"]
    else:
        return {"success": False, "error": "qssAuthTarget is invalid", "data": {}}

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    proxyData = {
        "method": method,
        "url": url,
        "data": data,
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": qssAuthTarget,
            "authCookies": authCookies,
        },
    }

    try:
        result = invoke_http_by_proxy(appCode, invokeToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": {}}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": {}}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": {}}
        
        # 确保inner_data是JSON格式
        if isinstance(inner_data, str):
            try:
                inner_data = json.loads(inner_data)
            except json.JSONDecodeError:
                return {"success": False, "error": "Failed to parse inner data as JSON", "data": {}}
        
        # 处理flowLogList长度限制
        if isinstance(inner_data, dict) and "flowLogList" in inner_data:
            flow_log_list = inner_data["flowLogList"]
            if isinstance(flow_log_list, list):
                # 将当前数据转为字符串检查长度
                current_length = len(json.dumps(inner_data))
                if current_length > 5000:
                    # 如果超过5000，则删除部分flowLogList内容
                    while current_length > 5000 and len(flow_log_list) > 0:
                        # 每次删除最后一个元素
                        flow_log_list.pop()
                        current_length = len(json.dumps(inner_data))
        # 对敏感信息进行脱敏处理
        masked_data = mask_sensitive_info(inner_data)
        masked_data["test"] = "inner_data"
        
        result = {
            "response": masked_data,
        }
        return {"success": True, "error": "", "data": result}

    except Exception as e:
        return {"success": False, "error": str(e), "data": {}}


def test():
    # 测试示例
    param = {
        "method": "get",
        "url": "https://paoding.corp.qunar.com/visual/mainSearch?orderNo=yyw250421153835516&qTraceId=",
        "qssAuthTarget": "paoding",
        "invokeAppCode": "f_pangu",
        "invokeToken": "Hnu88YsOdF2FekK3qbEBhpPzK8ix8OhdGuwok9RaQsFd54/2hkM8VXaUTyAp/qJR9KtgLQH8J+OoP6KsnxKBEom/ju5QamxJIgzeIyxsSC0mzQ3m7T6ZCW2d5cdSR+rAbsg5cqXlCwqM5KxElKz6wKcm5CM35atOjQDM9Whing4="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False))


if __name__ == "__main__":
    test() 