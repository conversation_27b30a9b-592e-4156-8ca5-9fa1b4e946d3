from pathlib import Path
import re
import time
import traceback
from urllib.parse import unquote_to_bytes
import requests
from typing import Callable, Dict, Any, List, Optional, Union
import json



def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        return False
    except PermissionError:
        return False
    except Exception as e:
        return False



def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            return default
    return default

def needUnlockBeforeOpr(qualityCheckModel: Any) -> bool:
    operationTodo = qualityCheckModel.get("operationTodo")
    if not operationTodo:
        return False
    if (
        isinstance(operationTodo, list)
        and len(operationTodo) > 0
        and "UNLOCK" in operationTodo
    ):
        return True
    return False

def needProcessBeforeOpr(qualityCheckModel: Any) -> bool:
    operationTodo = qualityCheckModel.get("operationTodo")
    if not operationTodo:
        return False
    if (
        isinstance(operationTodo, list)
        and len(operationTodo) > 0
        and "PROCESSOR" in operationTodo
    ):
        return True
    return False

def queryQualityCheckByNo(
    appCode: str, appToken: str, qualityCheckNo: str
) -> Dict[str, Any]:
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/qualityCheck/handle/handleInfoQuery?domain=callcenter.qunar.com&qualityCheckNo={qualityCheckNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        print("----------------------proxyData", json.dumps(proxyData, ensure_ascii=False,indent=2))
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        print("----------------------result", json.dumps(result, ensure_ascii=False,indent=2))
        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        # 获取内层data
        inner_data = response_data.get("data")
        if not inner_data:
            return {"success": False, "error": "Inner data is empty", "data": []}

        return {"success": True, "error": "", "data": inner_data}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}

def unlockQualityCheckByNo(
    appCode: str, appToken: str, qualityCheckNo: str
) -> Dict[str, Any]:
    proxyData = {
        "method": "get",
        "url": f"http://fuwu.qunar.com/qualityCheck/QCprocessor/unlock?domain=callcenter.qunar.com&qualityCheckNo={qualityCheckNo}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        return {"success": True, "error": "", "data": []}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}

def toAgent(appCode: str, appToken: str, toAgentParams: dict) -> Dict[str, Any]:
    """
    调用质检转客服接口
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        toAgentParams: 请求参数
        
    Returns:
        Dict: 包含接口返回结果的字典
    """
    # 构建URL参数
    params = [
        "domain=callcenter.qunar.com",
        f"agent={toAgentParams.get('agent', '')}",
        f"orderNo={toAgentParams.get('orderNo', '')}",
        f"compensateType={toAgentParams.get('compensateType', '')}",
        f"addSummary={toAgentParams.get('addSummary', '')}",
        f"isValid={toAgentParams.get('isValid', '')}",
        f"problemTypeLevel2={toAgentParams.get('problemTypeLevel2', '')}",
        f"responsibleExt={toAgentParams.get('responsibleExt', '')}",
        f"transferTo={toAgentParams.get('transferTo', '')}",
        f"points={toAgentParams.get('points', '')}",
        f"qualityCheckNo={toAgentParams.get('qualityCheckNo', '')}",
        f"compensateMoney={toAgentParams.get('compensateMoney', '')}",
        f"responsible={toAgentParams.get('responsible', '')}",
        f"name={toAgentParams.get('name', '')}",
        f"comment={toAgentParams.get('comment', '')}",
        f"penaltyMoney={toAgentParams.get('penaltyMoney', '')}",
        f"problemType={toAgentParams.get('problemType', '')}"
    ]
    url = f"https://fuwu.qunar.com/qualityCheck/QCprocessor/toAgent?{'&'.join(params)}"

    proxyData = {
        "method": "get",
        "url": url,
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)

        # 检查是否有错误
        if "error" in result:
            return {"success": False, "error": result["error"], "data": []}

        # 尝试解析data字段为JSON
        response_data = safe_json_parse(result.get("data"))
        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"success": False, "error": error_msg, "data": []}

        return {"success": True, "error": "", "data": []}

    except Exception as e:
        return {"success": False, "error": str(e), "data": []}

def queryQualityCheckInfo(orderNo: str, createDate: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    查询质检信息
    Args:
        orderNo: 订单号
        createTimeStart: 创建时间开始
        createTimeEnd: 创建时间结束
    Returns:
        Dict: 包含质检信息的字典
    """
    results = []
    page_index = 1
    start = 0
    last_index = 1

    while True:
        # 构建请求URL
        url = (
            f"https://fuwu.qunar.com/qualityCheck/taskManage/QCInfoQuery?"
            f"domain=callcenter.qunar.com&businessType=1&orderNo={orderNo}&"
            f"qualityCheckNo=&curtStep=-1&createTimeStart={createDate}&"
            f"createTimeEnd={createDate}&agent=&problemTypeLevel2=-1&"
            f"endTimeStart=&endTimeEnd=&operatorName=&orderSource=-1&"
            f"responsible=-1&compensateFlag=-1&confirmPenalty=-1&"
            f"paymentTimeStart=&paymentTimeEnd=&paymentStatus=-1&"
            f"paymentType=-1&paymentNo=&creatorName=&curtStatus=-1&"
            f"forcePenalty=-1&fenxiao=-1&_v={int(time.time() * 1000)}&limit=20&"
            f"pageIndex={page_index}&start={start}&lastIndex={last_index}"
        )

        proxyData = {
            "method": "get",
            "url": url,
            "data": "",
            "dataType": "form-data",
            "authType": "qsso",
            "qssAuthParam": {
                "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
                "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
            },
        }

        proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

        try:
            print("----------------------proxyData", json.dumps(proxyData, ensure_ascii=False,indent=2))    
            result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
            print("----------------------result", json.dumps(result, ensure_ascii=False,indent=2))
            # 检查是否有错误
            if "error" in result:
                return {"error": result["error"], "results": []}

            # 尝试解析data字段为JSON
            response_data = safe_json_parse(result.get("data"))

            # 检查ret字段和data字段
            if not response_data or response_data.get("ret") is False:
                error_msg = (
                    response_data.get("errmsg")
                    if response_data
                    else "Response data is empty or ret is false"
                )
                return {"error": error_msg, "results": []}

            # 获取内层data
            inner_data = response_data.get("data")
            if not inner_data:
                return {"error": "Inner data is empty", "results": []}

            total_count = inner_data.get("totalCount", 0)
            items = inner_data.get("list", [])
            # Filter items with problemTypeStr='不认可退改规则'
            results.extend(items)

            # Check if we've processed all items
            if start + 20 >= total_count:
                break

            # Update pagination parameters
            page_index += 1
            start += 20
            last_index = page_index - 1

            # Add 1 second delay between requests
            time.sleep(1)

        except Exception as e:
            return {"error": str(e), "results": []}

    return {"error": "", "results": results}

def processUnlockAndToAgent(
    appCode: str, 
    appToken: str, 
    qualityCheckNo: str, 
    qualityCheckModel: Dict[str, Any]
) -> Dict[str, Any]:
    """
    处理解锁和转客服逻辑
    
    Args:
        appCode: 应用代码
        appToken: 应用token
        qualityCheckNo: 原质检单号
        qualityCheckModel: 质检单模型
        
    Returns:
        Dict: 包含处理结果的字典
    """
    # 1. 调用queryQualityCheckInfo，日期为空，订单号为qualityCheckModel中"orderNo"
    orderNo = qualityCheckModel.get("orderNo")
    if not orderNo:
        return {
            "isSuccess": "false",
            "errorMsg": "质检单中未找到订单号",
        }
    
    checkInfoResult = queryQualityCheckInfo(orderNo, "", appCode, appToken)
    if checkInfoResult.get("error"):
        return {
            "isSuccess": "false",
            "errorMsg": f"查询质检信息失败: {checkInfoResult.get('error')}",
        }

    # 2. 循环遍历结果，查找备注中包含原质检单号的记录
    results = checkInfoResult.get("results", [])
    if not results:
        return {
            "isSuccess": "false",
            "errorMsg": "未找到相关质检单",
        }

    # 记录是否找到匹配的记录
    foundMatch = False
    lastError = None

    print("----------------------results", json.dumps(results, indent=2))
    for result in results:
        newQualityCheckNo = result.get("qualityCheckNo")
        if not newQualityCheckNo:
            continue

        # 调用queryQualityCheckByNo方法
        newQueryResult = queryQualityCheckByNo(appCode, appToken, newQualityCheckNo)
        print("----------------------newQueryResult", json.dumps(newQueryResult, indent=2))
        if not newQueryResult["success"]:
            lastError = f"查询新质检单失败: {newQueryResult.get('error')}"
            continue

        # 检查remark是否包含原质检单号
        newQualityCheckModel = newQueryResult.get("data")
        remark = str(newQualityCheckModel.get("remark", ""))
        if qualityCheckNo not in remark:
            continue

        # 检查是否需要解锁
        if needUnlockBeforeOpr(newQualityCheckModel):
            unlockResult = unlockQualityCheckByNo(appCode, appToken, newQualityCheckNo)
            if not unlockResult["success"]:
                lastError = f"解锁新质检单失败: {unlockResult.get('error')}"
                continue
        elif not needProcessBeforeOpr(newQualityCheckModel):
            lastError = f"新质检单不需要处理:状态不是PROCESSOR"
            continue

        newQualityCheck = newQualityCheckModel.get("qualityCheck")
        if not newQualityCheck:
            lastError = f"新质检单中没有qualityCheck"
            continue
        agent = newQualityCheckModel.get("agent", "")
        compensateType = newQualityCheck.get("compensateType", "1")
        points = newQualityCheck.get("points", "0")
        compensateMoney = newQualityCheck.get("compensateMoney", "0")
        responsible = newQualityCheck.get("responsible", "2")
        penaltyMoney = newQualityCheck.get("penalty", "0")
        problemType = newQualityCheck.get("problemType", "")

        # 准备toAgent参数
        toAgentParams = {
            "agent": agent,
            "orderNo": orderNo,
            "compensateType": compensateType,
            "addSummary": "",
            "isValid": "1",
            "problemTypeLevel2": "p_2_13",
            "responsibleExt": "1",
            "transferTo": "",
            "points": points,
            "qualityCheckNo": newQualityCheckNo,
            "compensateMoney": compensateMoney,
            "domain": "callcenter.qunar.com",
            "responsible": responsible,
            "name": "1",
            "comment": "用户投诉退票手续费/改签手续费与航司不一致，协商赔付，损失需贵司承担",
            "penaltyMoney": penaltyMoney,
            "problemType": problemType
        }

        # 3. 调用toAgent接口
        toAgentResult = toAgent(appCode, appToken, toAgentParams)
        if not toAgentResult["success"]:
            lastError = f"转客服失败: {toAgentResult.get('error')}"
            continue

        # 标记找到匹配记录
        foundMatch = True
        break

    # 根据是否找到匹配记录返回结果
    if foundMatch:
        return {
            "isSuccess": "true",
            "errorMsg": "",
            "data": {},
        }
    else:
        return {
            "isSuccess": "false",
            "errorMsg": lastError or "未找到备注中包含原质检单号的新质检单",
        }

def main(param: dict) -> dict:
    try:
        # Example parameters
        qualityCheckNo = param.get("qualityCheckNo")
        appCode = param.get("invokeAppCode")
        invokeToken = param.get("invokeToken")

        queryResult = queryQualityCheckByNo(
            appCode, invokeToken, qualityCheckNo=qualityCheckNo
        )
        print("----------------------queryResult", json.dumps(queryResult, indent=2))
        if not queryResult["success"]:
            result = {
                "isSuccess": "false",
                "errorMsg": f"查询质检单失败: {queryResult.get('error')}",
                "data": {}
            }
            return result
        

        qualityCheckModel = queryResult.get("data")      
    
        # Call the processUnlockAndToAgent function
        unlockAndToAgentResult = processUnlockAndToAgent(
            appCode=appCode,
            appToken=invokeToken,
            qualityCheckNo=qualityCheckNo,
            qualityCheckModel=qualityCheckModel
        )
    
        if unlockAndToAgentResult["isSuccess"] == "false":
            return unlockAndToAgentResult

        result = {
            "isSuccess": "true",
            "errorMsg": "成功",
            "data": {}
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"质检结果录入: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {"isSuccess": "false", "errorMsg": error_msg}
        return result



# Example usage
if __name__ == "__main__":
    input = {
        "qualityCheckNo": "JF250415222004380125906",
        "invokeAppCode": "f_pangu",
        "invokeToken": "YEXo4RKDc0kqNeesFc/MuF6UPmcIPawg7O4WQYSu2ZSX2L6WDZUsf6jfqutujEb8eYyHX2USkAy8IW2XUrYZuRFZxXG9Di4fBGFwXC4XlRAFnv6e+wvU0TJiJRYma/o/WJfNSRBJsGwmpn8eoZF1H9CtVbaSk2t/jNpdVQrPJDM=",
    }
    result = main(input)
    print(result)
    write_json_to_file(result, "quality_inspection/data/tg_check_unlock_toAgent_result.json")

