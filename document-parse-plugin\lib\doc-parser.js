/**
 * DOC文件解析器
 * 支持旧版.doc文件的文本提取和表格解析
 * 使用OLE复合文档格式解析
 */
class DocParser {
  constructor() {
    this.initialized = false;
    this.cfbLibrary = null;
    this.advancedExtractor = null;
    this.initializeLibraries();
  }

  // 初始化必要的库
  async initializeLibraries() {
    try {
      // 尝试加载CFB库（如果可用）
      if (typeof CFB !== 'undefined') {
        this.cfbLibrary = CFB;
        this.initialized = true;
        console.log('CFB library loaded successfully');
      } else if (typeof CFBLite !== 'undefined') {
        this.cfbLibrary = new CFBLite();
        this.initialized = true;
        console.log('CFBLite library loaded successfully');
      } else {
        console.warn('No CFB library available, using fallback parser');
        this.initialized = true;
      }

      // 初始化高级提取器
      if (typeof AdvancedDocExtractor !== 'undefined') {
        this.advancedExtractor = new AdvancedDocExtractor();
        console.log('AdvancedDocExtractor loaded successfully');
      } else {
        console.warn('AdvancedDocExtractor not available');
      }
    } catch (error) {
      console.error('Failed to initialize DOC parser libraries:', error);
      this.initialized = true; // 仍然允许使用回退方案
    }
  }

  // 检查是否为有效的.doc文件
  isValidDocFile(arrayBuffer) {
    if (!arrayBuffer || arrayBuffer.byteLength < 8) return false;

    const header = new Uint8Array(arrayBuffer, 0, 8);
    // DOC文件头特征 - Microsoft Office文档的OLE复合文档格式
    const docHeader = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
    return header.every((val, i) => val === docHeader[i]);
  }

  // 解析.doc文件
  async parseDocFile(arrayBuffer) {
    if (!this.initialized) {
      await this.initializeLibraries();
    }

    if (!this.isValidDocFile(arrayBuffer)) {
      throw new Error('不是有效的.doc文件格式');
    }

    try {
      // 方法1: 使用CFB库解析（如果可用）
      if (this.cfbLibrary) {
        return await this.parseWithCFB(arrayBuffer);
      }

      // 方法2: 使用基本的二进制解析
      return await this.parseWithBasicParser(arrayBuffer);

    } catch (error) {
      console.error('DOC parsing failed:', error);

      // 方法3: 回退到简单的文本提取
      return await this.extractBasicText(arrayBuffer);
    }
  }

  // 使用CFB库解析
  async parseWithCFB(arrayBuffer) {
    try {
      const cfb = this.cfbLibrary.read(arrayBuffer);

      // 查找WordDocument流
      const wordDocStream = this.findStream(cfb, 'WordDocument');
      if (!wordDocStream) {
        console.warn('找不到WordDocument流，尝试查找其他流');
        // 尝试查找其他可能的流
        const streams = ['1Table', '0Table', 'Data'];
        let foundStream = null;
        for (const streamName of streams) {
          foundStream = this.findStream(cfb, streamName);
          if (foundStream) {
            console.log(`找到流: ${streamName}`);
            break;
          }
        }
        if (!foundStream) {
          throw new Error('找不到可用的文档流');
        }
      }

      // 提取文本内容
      const text = this.extractTextFromStream(wordDocStream || foundStream);

      // 尝试提取表格
      const tables = this.extractTablesFromStream(wordDocStream || foundStream);

      return {
        text: text || '',
        html: this.convertTextToHtml(text, tables),
        tables: tables || []
      };
    } catch (error) {
      console.error('CFB parsing failed:', error);
      throw error;
    }
  }

  // 查找指定名称的流
  findStream(cfb, streamName) {
    if (!cfb || !cfb.FileIndex) return null;

    for (let i = 0; i < cfb.FileIndex.length; i++) {
      const entry = cfb.FileIndex[i];
      if (entry && entry.name === streamName) {
        return cfb.FileIndex[i];
      }
    }
    return null;
  }

  // 从流中提取文本
  extractTextFromStream(stream) {
    if (!stream || !stream.content) return '';

    try {
      const content = stream.content;
      let text = '';

      // 尝试多种文本提取方法

      // 方法1: UTF-16LE编码提取（Word文档常用）
      text = this.extractUTF16Text(content);
      if (text && text.length > 10) {
        return this.cleanExtractedText(text);
      }

      // 方法2: ASCII文本提取
      text = this.extractASCIIText(content);
      if (text && text.length > 10) {
        return this.cleanExtractedText(text);
      }

      // 方法3: 扫描整个内容寻找文本模式
      text = this.scanForTextPatterns(content);
      if (text && text.length > 10) {
        return this.cleanExtractedText(text);
      }

      return '';
    } catch (error) {
      console.error('Text extraction failed:', error);
      return '';
    }
  }

  // UTF-16LE文本提取
  extractUTF16Text(content) {
    let text = '';

    // 从不同偏移量开始尝试
    const offsets = [0x200, 0x400, 0x800, 0x1000, 0];

    for (const startOffset of offsets) {
      let currentText = '';
      let validChars = 0;

      for (let i = startOffset; i < content.length - 1; i += 2) {
        const char = content[i] | (content[i + 1] << 8);

        if (char >= 0x20 && char <= 0x7E) {
          // ASCII可打印字符
          currentText += String.fromCharCode(char);
          validChars++;
        } else if (char >= 0x4e00 && char <= 0x9fff) {
          // 中文字符
          currentText += String.fromCharCode(char);
          validChars++;
        } else if (char === 0x0D || char === 0x0A) {
          // 换行符
          currentText += '\n';
        } else if (char === 0x09) {
          // 制表符
          currentText += '\t';
        } else if (char === 0x20) {
          // 空格
          currentText += ' ';
        } else if (char === 0x00) {
          // 空字符，可能是填充
          if (currentText.length > 0 && validChars > 5) {
            // 如果已经有一些文本，继续
            continue;
          }
        } else {
          // 其他字符，如果已经有足够的有效字符，继续
          if (validChars < 5) {
            break;
          }
        }

        // 如果找到足够的文本，返回
        if (currentText.length > 50 && validChars > 20) {
          text = currentText;
          break;
        }
      }

      if (text.length > 10) {
        break;
      }
    }

    return text;
  }

  // ASCII文本提取
  extractASCIIText(content) {
    let text = '';

    for (let i = 0; i < content.length; i++) {
      const char = content[i];

      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
    }

    return text;
  }

  // 扫描文本模式
  scanForTextPatterns(content) {
    let text = '';
    let bestText = '';
    let maxScore = 0;

    // 尝试不同的扫描窗口
    const windowSizes = [1, 2];

    for (const windowSize of windowSizes) {
      text = '';
      let score = 0;

      for (let i = 0; i < content.length - windowSize + 1; i += windowSize) {
        let char;

        if (windowSize === 1) {
          char = content[i];
        } else {
          char = content[i] | (content[i + 1] << 8);
        }

        if (char >= 0x20 && char <= 0x7E) {
          text += String.fromCharCode(char);
          score++;
        } else if (char >= 0x4e00 && char <= 0x9fff) {
          text += String.fromCharCode(char);
          score += 2; // 中文字符权重更高
        } else if (char === 0x0D || char === 0x0A) {
          text += '\n';
        } else if (char === 0x09) {
          text += '\t';
        } else if (char === 0x20) {
          text += ' ';
        }
      }

      if (score > maxScore) {
        maxScore = score;
        bestText = text;
      }
    }

    return bestText;
  }

  // 从流中提取表格
  extractTablesFromStream(stream) {
    // 简化的表格提取逻辑
    // 在实际实现中，这里需要解析Word的表格结构
    return [];
  }

  // 使用基本解析器
  async parseWithBasicParser(arrayBuffer) {
    try {
      const text = this.extractTextFromBinary(arrayBuffer);
      return {
        text: text || '',
        html: this.convertTextToHtml(text),
        tables: []
      };
    } catch (error) {
      console.error('Basic parsing failed:', error);
      throw error;
    }
  }

  // 从二进制数据中提取文本
  extractTextFromBinary(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);

    // 尝试多种提取方法
    let text = '';

    // 方法1: UTF-16LE扫描
    text = this.extractUTF16FromBinary(uint8Array);
    if (text && text.length > 20) {
      return this.cleanExtractedText(text);
    }

    // 方法2: ASCII扫描
    text = this.extractASCIIFromBinary(uint8Array);
    if (text && text.length > 20) {
      return this.cleanExtractedText(text);
    }

    // 方法3: 智能模式识别
    text = this.smartTextExtraction(uint8Array);
    if (text && text.length > 10) {
      return this.cleanExtractedText(text);
    }

    return this.cleanExtractedText(text || '');
  }

  // UTF-16LE二进制提取
  extractUTF16FromBinary(uint8Array) {
    let text = '';
    let bestText = '';
    let maxScore = 0;

    // 尝试不同的起始偏移量
    const offsets = [0x200, 0x400, 0x800, 0x1000, 0x2000, 0];

    for (const offset of offsets) {
      text = '';
      let score = 0;
      let consecutiveValid = 0;

      for (let i = offset; i < uint8Array.length - 1; i += 2) {
        const char = uint8Array[i] | (uint8Array[i + 1] << 8);

        if (char >= 0x20 && char <= 0x7E) {
          // ASCII可打印字符
          text += String.fromCharCode(char);
          score++;
          consecutiveValid++;
        } else if (char >= 0x4e00 && char <= 0x9fff) {
          // 中文字符
          text += String.fromCharCode(char);
          score += 2;
          consecutiveValid++;
        } else if (char === 0x0D || char === 0x0A) {
          text += '\n';
          consecutiveValid = 0;
        } else if (char === 0x09) {
          text += '\t';
          consecutiveValid = 0;
        } else if (char === 0x20) {
          text += ' ';
          consecutiveValid = 0;
        } else {
          consecutiveValid = 0;
          // 如果遇到太多无效字符，可能需要跳过
          if (text.length > 100 && score < text.length * 0.3) {
            break;
          }
        }

        // 如果找到足够好的文本，提前结束
        if (score > 100 && text.length > 200) {
          break;
        }
      }

      if (score > maxScore) {
        maxScore = score;
        bestText = text;
      }
    }

    return bestText;
  }

  // ASCII二进制提取
  extractASCIIFromBinary(uint8Array) {
    let text = '';
    let score = 0;

    for (let i = 0; i < uint8Array.length; i++) {
      const char = uint8Array[i];

      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
        score++;
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
    }

    // 只有当ASCII文本质量足够好时才返回
    if (score > text.length * 0.3) {
      return text;
    }

    return '';
  }

  // 智能文本提取
  smartTextExtraction(uint8Array) {
    let bestText = '';
    let maxScore = 0;

    // 查找文本块
    const textBlocks = this.findTextBlocks(uint8Array);

    for (const block of textBlocks) {
      const text = this.extractTextFromBlock(uint8Array, block.start, block.end);
      const score = this.scoreText(text);

      if (score > maxScore) {
        maxScore = score;
        bestText = text;
      }
    }

    return bestText;
  }

  // 查找可能的文本块
  findTextBlocks(uint8Array) {
    const blocks = [];
    let currentBlock = null;
    let validChars = 0;

    for (let i = 0; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);

      if ((char >= 0x20 && char <= 0x7E) || (char >= 0x4e00 && char <= 0x9fff)) {
        if (!currentBlock) {
          currentBlock = { start: i, end: i };
          validChars = 1;
        } else {
          currentBlock.end = i;
          validChars++;
        }
      } else {
        if (currentBlock && validChars > 10) {
          blocks.push(currentBlock);
        }
        currentBlock = null;
        validChars = 0;
      }
    }

    if (currentBlock && validChars > 10) {
      blocks.push(currentBlock);
    }

    return blocks;
  }

  // 从指定块提取文本
  extractTextFromBlock(uint8Array, start, end) {
    let text = '';

    for (let i = start; i <= end && i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);

      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
      } else if (char >= 0x4e00 && char <= 0x9fff) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
    }

    return text;
  }

  // 基本文本提取（最后的回退方案）
  async extractBasicText(arrayBuffer) {
    try {
      console.log('使用基本文本提取方案');

      let text = '';

      // 优先使用高级提取器
      if (this.advancedExtractor) {
        console.log('使用高级文本提取器');
        text = this.advancedExtractor.extractText(arrayBuffer);

        if (text && text.length > 20) {
          console.log(`高级提取器成功，文本长度: ${text.length}`);
          const cleanedText = this.cleanExtractedText(text);
          return {
            text: cleanedText,
            html: this.convertTextToHtml(cleanedText),
            tables: []
          };
        }
      }

      // 回退到原有的提取方法
      console.log('高级提取器失败，使用传统方法');

      // 首先尝试二进制提取
      text = this.extractTextFromBinary(arrayBuffer);

      if (!text || text.length < 10) {
        // 如果二进制提取失败，尝试编码解码
        const encodings = ['utf-16le', 'utf-8', 'windows-1252', 'iso-8859-1'];
        let bestText = '';
        let maxScore = 0;

        for (const encoding of encodings) {
          try {
            const decoder = new TextDecoder(encoding, {fatal: false});
            const decodedText = decoder.decode(arrayBuffer);
            const score = this.scoreText(decodedText);

            console.log(`编码 ${encoding} 得分: ${score}, 长度: ${decodedText.length}`);

            if (score > maxScore) {
              maxScore = score;
              bestText = decodedText;
            }
          } catch (e) {
            console.warn(`编码 ${encoding} 解码失败:`, e.message);
          }
        }

        if (bestText && bestText.length > text.length) {
          text = bestText;
        }
      }

      // 如果还是没有文本，尝试原始字节扫描
      if (!text || text.length < 10) {
        console.log('尝试原始字节扫描');
        text = this.rawByteTextExtraction(arrayBuffer);
      }

      const cleanedText = this.cleanExtractedText(text);

      console.log(`最终提取的文本长度: ${cleanedText.length}`);

      return {
        text: cleanedText || '无法从此.doc文件中提取文本内容。建议：\n1. 尝试将文件转换为.docx格式\n2. 检查文件是否损坏\n3. 使用其他文档处理工具',
        html: this.convertTextToHtml(cleanedText || '无法提取文本内容'),
        tables: []
      };
    } catch (error) {
      console.error('Basic text extraction failed:', error);
      return {
        text: '文件解析失败，请尝试将.doc文件转换为.docx格式后重新上传',
        html: '<p>文件解析失败，请尝试将.doc文件转换为.docx格式后重新上传</p>',
        tables: []
      };
    }
  }

  // 原始字节文本提取
  rawByteTextExtraction(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';
    let consecutiveText = '';
    let bestSegment = '';
    let maxLength = 0;

    // 扫描整个文件，寻找连续的可读文本
    for (let i = 0; i < uint8Array.length; i++) {
      const byte = uint8Array[i];

      // 检查是否为可打印字符
      if ((byte >= 0x20 && byte <= 0x7E) || byte === 0x09 || byte === 0x0A || byte === 0x0D) {
        consecutiveText += String.fromCharCode(byte);
      } else {
        // 遇到非文本字符，检查当前段落
        if (consecutiveText.length > maxLength && consecutiveText.length > 10) {
          maxLength = consecutiveText.length;
          bestSegment = consecutiveText;
        }
        consecutiveText = '';
      }
    }

    // 检查最后一段
    if (consecutiveText.length > maxLength && consecutiveText.length > 10) {
      bestSegment = consecutiveText;
    }

    return bestSegment;
  }

  // 评估文本质量
  scoreText(text) {
    if (!text) return 0;

    let score = 0;
    const printableChars = text.match(/[\x20-\x7E\u4e00-\u9fa5]/g);
    const totalChars = text.length;

    if (totalChars === 0) return 0;

    // 可打印字符比例
    const printableRatio = printableChars ? printableChars.length / totalChars : 0;
    score += printableRatio * 100;

    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g);
    if (chineseChars) {
      score += (chineseChars.length / totalChars) * 50;
    }

    // 常见单词加分
    const commonWords = text.match(/\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/gi);
    if (commonWords) {
      score += commonWords.length * 2;
    }

    return score;
  }

  // 清理提取的文本
  cleanExtractedText(text) {
    if (!text) return '';

    return text
      // 移除控制字符（除了换行和制表符）
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // 移除过多的空白字符
      .replace(/\s{3,}/g, '  ')
      // 移除过多的换行
      .replace(/\n{3,}/g, '\n\n')
      // 移除行首行尾空白
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .trim();
  }

  // 将文本转换为HTML
  convertTextToHtml(text, tables = []) {
    if (!text) return '<p>无内容</p>';

    let html = '';

    // 处理表格
    if (tables && tables.length > 0) {
      tables.forEach((table, index) => {
        html += `<h3>表格 ${index + 1}</h3>`;
        html += this.tableToHtml(table);
      });
    }

    // 处理文本内容
    const paragraphs = text.split('\n\n');
    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        html += `<p>${this.escapeHtml(paragraph.trim())}</p>`;
      }
    });

    return html || '<p>无法提取内容</p>';
  }

  // 表格转HTML
  tableToHtml(table) {
    if (!table || !Array.isArray(table) || table.length === 0) {
      return '<p>空表格</p>';
    }

    let html = '<table border="1">';

    table.forEach((row, rowIndex) => {
      html += '<tr>';
      if (Array.isArray(row)) {
        row.forEach(cell => {
          const tag = rowIndex === 0 ? 'th' : 'td';
          html += `<${tag}>${this.escapeHtml(cell || '')}</${tag}>`;
        });
      }
      html += '</tr>';
    });

    html += '</table>';
    return html;
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocParser;
} else if (typeof window !== 'undefined') {
  window.DocParser = DocParser;
}
