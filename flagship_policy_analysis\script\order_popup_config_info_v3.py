# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import requests # type: ignore
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> ConfigError:
    """统一处理请求异常"""
    if isinstance(e, requests.exceptions.HTTPError):
        return ConfigError(1, f"http错误: {str(e)}")
    elif isinstance(e, requests.exceptions.ConnectionError):
        return ConfigError(1, "连接错误")
    elif isinstance(e, requests.exceptions.Timeout):
        return ConfigError(1, "超时错误")
    elif isinstance(e, requests.exceptions.RequestException):
        return ConfigError(1, "请求错误")
    else:
        return ConfigError(1, "未知错误")

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }

    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }

    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }

    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取必要字段并检查
    carrier = mapping_data.get("carrier", "")
    if not carrier:
        return {
            "status": "error",
            "message": "carrier 不能为空",
            "data": None
        }
    
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    
    flagship_name = mapping_data.get("flagshipName", "")
    if not flagship_name:
        return {
            "status": "error",
            "message": "flagshipName 不能为空",
            "data": None
        }
    
    # 会员限制为空，则跳过配置
    membership_limit = analysis_data.get("membership_limit", "")
    if not membership_limit or membership_limit not in ["新会员", "老会员", "新老会员"]:
        return {
            "status": "ignore",
            "message": "未满足配置场景",
            "data": None
        }
    
    # 优先复用已有的航司配置，后续配置提交时追加mark
    newData = process_qconfig_content(analysis_data, mapping_data, param)
    if newData["status"] != "success":
        return newData
    if newData.get("data") is not None:
        return newData

    # 如果不存在可复用的mark，则新建
    # 顶部标题
    member_name = mapping_data.get("memberName", "航空会员")
    if member_name == "金鹏会员":
        # 注册首都航空金鹏会员
        topTitle = f"注册{flagship_name}{member_name}"
    else:
        topTitle = f"注册{member_name}"
    # 处理顶部副标题
    topSubtitle = f"该票价为{member_name}专属，免费注册立享优惠"

    # 构建结果对象
    result_obj = {
        "airline": carrier,
        "tag": "",
        "type": "10",
        "topTitleIcon": "https://s.qunarzz.com/f_cms/2021/1610526224690_346858849.png",
        "topTitle": topTitle,
        "topSubtitle": topSubtitle,
        "imgUrl": "",
        "bottomTitle": "",
        "bottomSubtitle": "",
        "cancelButtonText": "",
        "confirmButtonText": "",
        "stopOverFlagForCancelButton": "false",
        "toastFlag": "false",
        "jumpUrl": "",
        "mark": product_mark,
        "realNameVerify": "false",
        "depApCode": "",
        "arrApCode": "",
        "topTitleAfterIcon": "",
        "topTitleContentStart": "",
        "topTitleContentMiddle": "",
        "topTitleContentMiddleUrl": "",
        "topTitleContentEnd": "",
        "titleAfterText": "",
        "titleAfterTextColor": "",
        "titleAfterBorderColor": "",
        "flightType": 1,
        "tipType": 0,
        "bottomText": "",
        "agreementTextList": "",
        "agreementUrlList": "",
        "domain": "ALL",
        "topTitleUrl": "",
        "flightIndexMerge": "false",
        "selected": "false",
        "unSelectedPass": "false"
    }

    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }


def process_qconfig_content(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any], param: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理qconfig配置内容，选择最优tag

    参数:
    param: Dict[str, Any] - 请求参数
    analysis_data: Dict[str, Any] - 分析数据
    mapping_data: Dict[str, Any] - 映射数据

    返回:
    Dict[str, Any] - 包含选择的最优tag的结果
    """
    # 步骤一和二：GET请求获取当前配置
    try:
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': 'order_popup_config_info_v3.t',
            'env': param['env'],
            "subenv": param.get('subenv', ''),
            'targetgroupid': 'f_athena_domestic_tts'
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        error = handle_request_exception(e)
        return {
            "status": "error",
            "message": error.message,
            "data": None
        }

    # 步骤三：处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": "error",
            "message": get_result.get('message', '获取配置失败'),
            "data": None
        }

    config_data = get_result['data'].get('data', '')
    config_list = json.loads(config_data) if config_data else []

    carrier = mapping_data.get("carrier", "")
    product_mark = mapping_data.get("productMark", "")

    # 1. 判断product_mark是否已存在于任意一条columns["mark"]中
    for item in config_list:
        columns = item.get('columns', {})
        mark_str = columns.get('mark', '')
        if mark_str:
            mark_list = [m.strip() for m in mark_str.split(',') if m.strip()]
            if product_mark in mark_list:
                return {
                    "status": "ignore",
                    "message": "已存在配置",
                    "data": None
                }

    # 2. 筛选满足条件的多行
    candidates = []
    for item in config_list:
        columns = item.get('columns', {})
        if not isinstance(columns, dict):
            continue
        if columns.get('airline', '') != carrier:
            continue
        if columns.get('tag', ''):
            continue
        if columns.get('type', '') != '10':
            continue
        flight_type = columns.get('flightType', '')
        if '1' not in flight_type.split(','):
            continue
        if columns.get('tipType', '') != '0':
            continue
        if columns.get('domain', '') != 'ALL':
            continue
        candidates.append(item)

    # 3. 选择所有字段不为空最多的一条
    def count_non_empty_fields(columns: dict) -> int:
        return sum(1 for v in columns.values() if str(v).strip() != '')

    best_item = None
    max_non_empty = -1
    for item in candidates:
        columns = item.get('columns', {})
        non_empty_count = count_non_empty_fields(columns)
        if non_empty_count > max_non_empty:
            max_non_empty = non_empty_count
            best_item = item

    if best_item:
        return {
            "status": "success",
            "message": "操作成功",
            "data": {
                "rowKey": best_item.get('row', ''),
                "airline": carrier,
                "mark": product_mark,
                "reuse_record": "true"
            }
        }
    return {
            "status": "success",
            "message": "流程无误就是没数据，需要新增",
            "data": None
        }


def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析URL编码的结构化数据
    
    参数:
    content: str - URL编码后的结构化数据
    
    返回:
    Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]] - 解析后的数据和状态
    """
    try:
        # 处理特殊情况：空内容
        if not content:
            return [], {"status": "success", "message": ""}
        
        # 尝试解码
        try:
            decoded_bytes = unquote_to_bytes(content)
            content_str = decoded_bytes.decode('utf-8')
        except:
            content_str = content
        
        # 解析结构化数据
        result = parse_structured_data(content_str)
        
        return result, {"status": "success", "message": ""}
    except Exception as e:
        return None, {"status": "error", "message": str(e)}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化数据字符串
    
    参数:
    data_str: str - 结构化数据字符串，例如："field1:value1#*#field2:value2~~*~~field1:value3#*#field2:value4"
    
    返回:
    List[Dict[str, str]] - 解析后的数据列表
    """
    result = []
    # 以分隔符 ~~*~~ 分割数据项
    parts = data_str.split("~~*~~")
    # 解析每一项
    for part in parts:
        if part:  # 忽略空项
            parsed_fields = _parse_fields(part)
            if parsed_fields:  # 忽略空解析结果
                result.append(parsed_fields)
    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析单个数据项的字段
    
    参数:
    part_str: str - 单个数据项字符串，例如："field1:value1#*#field2:value2"
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    fields = {}
    # 以分隔符 #*# 分割字段
    field_parts = part_str.split("#*#")
    for field_part in field_parts:
        if field_part and ":" in field_part:  # 确保字段部分不为空且包含":"
            # 以第一个":"分割字段名和值
            idx = field_part.find(":")
            key = field_part[:idx].strip()
            value = field_part[idx+1:].strip()
            if key:  # 确保字段名不为空
                fields[key] = value
    return fields
