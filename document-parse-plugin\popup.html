<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>文档解析助手</title>
  <style>
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      overflow-y: auto;
    }
    body {
      padding: 15px;
      box-sizing: border-box;
    }
    .main-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      min-height: 100%;
    }
    .content-area {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    .upload-area {
      border: 2px dashed #ccc;
      padding: 15px;
      text-align: center;
      margin-bottom: 10px;
      cursor: pointer;
      border-radius: 5px;
    }
    .upload-area:hover {
      border-color: #4CAF50;
    }
    .upload-area p {
      margin: 5px 0;
    }
    .file-list {
      margin-bottom: 10px;
      max-height: 150px;
      overflow-y: auto;
    }
    .file-list ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .file-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0;
      border: 1px solid #eee;
      margin-bottom: 5px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    .file-list .file-item {
      flex-grow: 1;
      padding: 8px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .file-list li:hover {
      background-color: #f5f5f5;
    }
    .file-list .file-icon {
      margin-right: 8px;
    }
    .file-list .file-name {
      flex-grow: 1;
    }
    .file-list .delete-btn {
      background-color: transparent;
      color: #f44336;
      border: none;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 0 4px 4px 0;
      transition: background-color 0.2s;
    }
    .file-list .delete-btn:hover {
      background-color: #ffebee;
    }
    .preview-area {
      border: 1px solid #ddd;
      padding: 10px;
      height: 150px;
      overflow-y: auto;
      margin-bottom: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 10px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 6px 12px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #45a049;
    }
    #clearBtn {
      background-color: #f44336;
    }
    #clearBtn:hover {
      background-color: #d32f2f;
    }
    .loading {
      display: none;
      text-align: center;
      margin: 5px 0;
      padding: 8px;
      background-color: #f8f8f8;
      border-radius: 4px;
      font-size: 14px;
    }
    .status-message {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .status-message.error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
    .status-message.success {
      background-color: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    /* Word 文档预览样式 */
    .word-preview {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .word-preview h3 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 8px;
    }
    .word-content {
      line-height: 1.6;
    }
    .word-content table, .word-table {
      border-collapse: collapse;
      width: 100%;
      margin: 15px 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .word-content table, .word-content th, .word-content td,
    .word-table, .word-table th, .word-table td {
      border: 1px solid #ddd;
    }
    .word-content th, .word-content td,
    .word-table th, .word-table td {
      padding: 8px;
      text-align: left;
    }
    .word-content th, .word-table th,
    .word-content .header-row td, .word-table .header-row td {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .word-content tr:nth-child(even), .word-table tr:nth-child(even),
    .word-content .even-row, .word-table .even-row {
      background-color: #f9f9f9;
    }
    .word-content tr:hover, .word-table tr:hover {
      background-color: #f5f5f5;
    }
    .word-text {
      white-space: pre-wrap;
      font-family: Consolas, monospace;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }

    /* Excel 表格预览样式 */
    .excel-preview {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .excel-preview h3 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 8px;
    }
    .sheet-selector {
      margin: 10px 0;
    }
    .sheet-selector select {
      padding: 5px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    .sheet-content {
      margin-top: 15px;
    }
    .sheet-content h4 {
      color: #555;
      margin-bottom: 10px;
    }
    .excel-table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 15px;
    }
    .excel-table, .excel-table th, .excel-table td {
      border: 1px solid #ddd;
    }
    .excel-table th, .excel-table td {
      padding: 8px;
      text-align: left;
    }
    .excel-table .header-row td {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .excel-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .excel-table tr:hover {
      background-color: #f5f5f5;
    }
    .note {
      font-size: 12px;
      color: #777;
      font-style: italic;
    }

    /* 解析到多维表格相关样式 */
    .parse-section {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .parse-section h3 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #333;
      font-size: 16px;
    }
    .input-group {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
    .input-group label {
      width: 80px;
      font-weight: bold;
      flex-shrink: 0;
    }
    .input-group input {
      flex: 1;
      min-width: 150px;
      padding: 6px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .primary-btn {
      background-color: #2196F3;
      margin-top: 10px;
    }
    .primary-btn:hover {
      background-color: #0b7dda;
    }
    .parse-result {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #fff;
      height: 150px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .parse-result h3 {
      margin-top: 0;
      margin-bottom: 10px;
      flex-shrink: 0;
    }
    .result-content {
      white-space: pre-wrap;
      font-family: Consolas, monospace;
      background-color: #1e1e1e;
      color: #f8f8f8;
      padding: 8px;
      border-radius: 4px;
      height: 120px;
      overflow-y: auto;
      line-height: 1.4;
      font-size: 13px;
    }
    .event-node {
      color: #88c0d0;
      margin-bottom: 5px;
    }
    .event-ping {
      color: #a3be8c;
      font-style: italic;
    }
    .event-message {
      color: #ebcb8b;
    }
    .event-error {
      color: #bf616a;
    }
    .event-done {
      color: #a3be8c;
      font-weight: bold;
    }
    .initial-message {
      color: #888;
      text-align: center;
      padding: 20px;
      font-style: italic;
    }

    /* 自定义弹窗样式 */
    .custom-alert {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .alert-content {
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      max-width: 400px;
      width: 90%;
      margin: 0 auto;
    }
    .alert-content h3 {
      margin-top: 0;
      color: #2196F3;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .alert-content p {
      margin: 15px 0;
      line-height: 1.5;
    }
    .alert-content button {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      float: right;
    }
    .alert-content button:hover {
      background-color: #0b7dda;
    }

    /* 预览弹窗样式 */
    .preview-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .preview-modal-content {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      width: 80%;
      height: 80%;
      max-width: 1000px;
      max-height: 800px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      resize: both;
      min-width: 400px;
      min-height: 300px;
    }
    .preview-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-bottom: 1px solid #ddd;
      background-color: #f8f8f8;
      cursor: move;
    }
    .preview-modal-header h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
    }
    .preview-modal-close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: #666;
      cursor: pointer;
      padding: 0 5px;
    }
    .preview-modal-close-btn:hover {
      color: #f44336;
    }
    .preview-modal-body {
      flex: 1;
      padding: 15px;
      overflow: auto;
    }
    /* 预览内容样式 */
    .preview-modal .word-preview,
    .preview-modal .excel-preview {
      height: 100%;
      overflow: auto;
    }
    .preview-modal .word-content,
    .preview-modal .excel-table {
      margin-top: 10px;
    }

    /* 预览按钮样式 */
    .word-content-preview,
    .excel-content-preview {
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 4px;
      text-align: center;
    }
    .view-full-btn {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 8px 15px;
      margin-top: 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .view-full-btn:hover {
      background-color: #0b7dda;
    }

    /* 媒体查询，适应不同屏幕尺寸 */
    @media (max-width: 768px) {
      .input-group label {
        width: 80px;
      }
      .button-group {
        flex-direction: column;
      }
      button {
        width: 100%;
        margin: 5px 0;
      }
    }

    @media (max-height: 600px) {
      .preview-area, .parse-result {
        min-height: 120px;
      }
      .file-list {
        max-height: 15%;
      }
    }
  </style>
</head>
<body>
  <div class="main-container">
    <h2>文档解析助手</h2>

    <div class="content-area">
      <div class="upload-area" id="uploadArea">
        <p>点击或拖拽文件到此处</p>
        <p>支持Word(.docx, .doc)和Excel(.xlsx, .xls)</p>
        <input type="file" id="fileInput" multiple accept=".docx,.doc,.xlsx,.xls" style="display: none;">
      </div>

      <div class="file-list" id="fileList">
        <!-- 文件列表将在这里显示 -->
      </div>

      <div class="preview-area" id="previewArea">
        <!-- 预览内容将在这里显示 -->
      </div>

      <div class="button-group">
        <button id="exportBtn">导出全部为TXT</button>
        <button id="clearBtn">清空所有</button>
      </div>

      <div class="parse-section">
        <h3>解析到多维表格</h3>
        <div class="input-group">
          <label for="oaIdInput">OA ID:</label>
          <input type="text" id="oaIdInput" placeholder="请输入OA ID" value="123456">
        </div>
        <div class="input-group">
          <label for="envSelect">环境:</label>
          <select id="envSelect" style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
            <option value="beta" selected>Beta</option>
            <option value="prod">Prod</option>
          </select>
        </div>
        <div class="input-group">
          <label for="flowSignInput">Flow Sign:</label>
          <input type="text" id="flowSignInput" placeholder="请输入Flow Sign" value="sdads">
        </div>
        <button id="parseToTableBtn" class="primary-btn">开始解析到多维表格</button>
      </div>

      <div class="loading" id="loading">
        处理中，请稍候...
      </div>

      <div class="parse-result" id="parseResult">
        <h3>解析结果</h3>
        <div class="result-content" id="resultContent">
          <div class="initial-message">解析结果将显示在这里</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 自定义弹窗 -->
  <div class="custom-alert" id="customAlert" style="display: none;">
    <div class="alert-content">
      <h3>提示</h3>
      <p id="alertMessage"></p>
      <button id="alertCloseBtn">确定</button>
    </div>
  </div>

  <!-- 预览弹窗 -->
  <div class="preview-modal" id="previewModal" style="display: none;">
    <div class="preview-modal-content" id="previewModalContent">
      <div class="preview-modal-header">
        <h3 id="previewModalTitle">文件预览</h3>
        <button id="previewModalCloseBtn" class="preview-modal-close-btn">×</button>
      </div>
      <div class="preview-modal-body" id="previewModalBody">
        <!-- 预览内容将在这里显示 -->
      </div>
    </div>
  </div>

  <script src="lib/xlsx.full.min.js"></script>
  <script src="lib/mammoth.browser.min.js"></script>
  <script src="documentProcessor.js"></script>
  <script src="popup.js"></script>
</body>
</html>