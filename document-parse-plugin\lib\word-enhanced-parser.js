/**
 * Microsoft Word增强解析器
 * 专门处理Microsoft Word创建的.doc文件乱码问题
 */
class WordEnhancedParser {
  constructor() {
    // Word特有的文档结构标识
    this.wordStructures = [
      'Microsoft Word', 'Microsoft Office', 'MSWordDoc', 'Word.Document',
      'Normal.dot', 'Normal.dotm', 'Winword', 'MSWORD'
    ];
    
    // Word版本特征
    this.wordVersions = [
      { version: '16.0', name: 'Word 2016/2019/365' },
      { version: '15.0', name: 'Word 2013' },
      { version: '14.0', name: 'Word 2010' },
      { version: '12.0', name: 'Word 2007' },
      { version: '11.0', name: 'Word 2003' }
    ];
    
    // Word特有的编码模式
    this.wordEncodings = [
      'utf-16le',    // Word主要使用的编码
      'utf-16be',    // 大端序UTF-16
      'utf-8',       // 现代Word版本
      'windows-1252', // 西欧字符集
      'iso-8859-1'   // Latin-1
    ];
    
    // Word文档的字节偏移模式
    this.wordOffsets = [
      0x200,   // 标准Word文档偏移
      0x400,   // 备用偏移
      0x800,   // 大文档偏移
      0x1000,  // 复杂文档偏移
      0x2000   // 特大文档偏移
    ];
  }

  // 增强的Word文档解析
  async parseWordDocument(arrayBuffer, fileName) {
    console.log('开始增强Word文档解析:', fileName);
    
    const results = [];
    
    // 方法1: Word特有的OLE流解析
    try {
      const oleResult = await this.parseWordOLEStreams(arrayBuffer);
      if (oleResult.text.length > 10) {
        results.push(oleResult);
      }
    } catch (e) {
      console.warn('Word OLE流解析失败:', e.message);
    }
    
    // 方法2: Word版本特定解析
    try {
      const versionResult = await this.parseByWordVersion(arrayBuffer);
      if (versionResult.text.length > 10) {
        results.push(versionResult);
      }
    } catch (e) {
      console.warn('Word版本特定解析失败:', e.message);
    }
    
    // 方法3: Word字符表解析
    try {
      const charTableResult = await this.parseWordCharTable(arrayBuffer);
      if (charTableResult.text.length > 10) {
        results.push(charTableResult);
      }
    } catch (e) {
      console.warn('Word字符表解析失败:', e.message);
    }
    
    // 方法4: Word段落结构解析
    try {
      const paragraphResult = await this.parseWordParagraphs(arrayBuffer);
      if (paragraphResult.text.length > 10) {
        results.push(paragraphResult);
      }
    } catch (e) {
      console.warn('Word段落解析失败:', e.message);
    }
    
    // 方法5: Word二进制重构
    try {
      const binaryResult = await this.reconstructWordBinary(arrayBuffer);
      if (binaryResult.text.length > 10) {
        results.push(binaryResult);
      }
    } catch (e) {
      console.warn('Word二进制重构失败:', e.message);
    }
    
    // 选择最佳结果
    if (results.length > 0) {
      const bestResult = results.reduce((best, current) => 
        current.score > best.score ? current : best
      );
      
      return {
        success: true,
        text: bestResult.text,
        html: this.textToHtml(bestResult.text),
        method: `Word增强解析 (${bestResult.method})`,
        allResults: results
      };
    }
    
    return {
      success: false,
      text: '无法解析Microsoft Word文档',
      html: '<p>无法解析Microsoft Word文档</p>',
      method: 'Word增强解析失败'
    };
  }

  // Word OLE流解析
  async parseWordOLEStreams(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 查找WordDocument流
    const wordDocStream = this.findWordDocumentStream(uint8Array);
    if (wordDocStream) {
      const text = this.extractTextFromWordStream(wordDocStream);
      return {
        text: text,
        score: this.scoreText(text),
        method: 'Word OLE流解析'
      };
    }
    
    return { text: '', score: 0, method: 'Word OLE流解析失败' };
  }

  // 查找WordDocument流
  findWordDocumentStream(uint8Array) {
    // 查找"WordDocument"字符串的位置
    const wordDocPattern = [0x57, 0x6F, 0x72, 0x64, 0x44, 0x6F, 0x63, 0x75, 0x6D, 0x65, 0x6E, 0x74]; // "WordDocument"
    
    for (let i = 0; i <= uint8Array.length - wordDocPattern.length; i++) {
      let match = true;
      for (let j = 0; j < wordDocPattern.length; j++) {
        if (uint8Array[i + j] !== wordDocPattern[j]) {
          match = false;
          break;
        }
      }
      
      if (match) {
        // 找到WordDocument流，提取后续数据
        const streamStart = i + wordDocPattern.length + 100; // 跳过头部信息
        const streamEnd = Math.min(streamStart + 10000, uint8Array.length); // 限制读取长度
        return uint8Array.slice(streamStart, streamEnd);
      }
    }
    
    return null;
  }

  // 从Word流提取文本
  extractTextFromWordStream(stream) {
    let text = '';
    
    // Word文档的文本通常以UTF-16LE编码存储
    for (let i = 0; i < stream.length - 1; i += 2) {
      const char = stream[i] | (stream[i + 1] << 8);
      
      // 检查是否为有效字符
      if ((char >= 0x20 && char <= 0x7E) || // ASCII可打印字符
          (char >= 0x4e00 && char <= 0x9fff) || // 中文字符
          char === 0x0D || char === 0x0A || char === 0x09 || char === 0x20) {
        text += String.fromCharCode(char);
      }
      
      // 限制文本长度
      if (text.length > 5000) break;
    }
    
    return this.cleanWordText(text);
  }

  // 按Word版本解析
  async parseByWordVersion(arrayBuffer) {
    const rawText = new TextDecoder('utf-8', { fatal: false }).decode(arrayBuffer);
    
    // 检测Word版本
    let detectedVersion = null;
    for (const version of this.wordVersions) {
      if (rawText.includes(version.version)) {
        detectedVersion = version;
        break;
      }
    }
    
    if (detectedVersion) {
      console.log(`检测到Word版本: ${detectedVersion.name}`);
      
      // 根据版本使用不同的解析策略
      const text = this.parseBySpecificVersion(arrayBuffer, detectedVersion);
      return {
        text: text,
        score: this.scoreText(text),
        method: `Word ${detectedVersion.name} 解析`
      };
    }
    
    return { text: '', score: 0, method: 'Word版本检测失败' };
  }

  // 特定版本解析
  parseBySpecificVersion(arrayBuffer, version) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';
    
    if (version.version >= '12.0') {
      // Word 2007及以后版本，主要使用UTF-16LE
      text = this.extractUTF16LEText(uint8Array, 0x400);
    } else {
      // Word 2003及以前版本，可能使用不同编码
      text = this.extractLegacyWordText(uint8Array);
    }
    
    return this.cleanWordText(text);
  }

  // Word字符表解析
  async parseWordCharTable(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // Word文档的字符表通常在特定位置
    const charTableOffsets = [0x300, 0x500, 0x700, 0x900];
    let bestText = '';
    let maxScore = 0;
    
    for (const offset of charTableOffsets) {
      if (offset < uint8Array.length) {
        const text = this.extractCharTableText(uint8Array, offset);
        const score = this.scoreText(text);
        
        if (score > maxScore) {
          maxScore = score;
          bestText = text;
        }
      }
    }
    
    return {
      text: bestText,
      score: maxScore,
      method: 'Word字符表解析'
    };
  }

  // 提取字符表文本
  extractCharTableText(uint8Array, offset) {
    let text = '';
    let consecutiveValid = 0;
    
    for (let i = offset; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if ((char >= 0x20 && char <= 0x7E) || (char >= 0x4e00 && char <= 0x9fff)) {
        text += String.fromCharCode(char);
        consecutiveValid++;
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
        consecutiveValid = 0;
      } else if (char === 0x09) {
        text += '\t';
        consecutiveValid = 0;
      } else if (char === 0x20) {
        text += ' ';
        consecutiveValid = 0;
      } else {
        consecutiveValid = 0;
        // 如果连续遇到无效字符，可能需要停止
        if (text.length > 100 && consecutiveValid === 0) {
          break;
        }
      }
      
      // 限制扫描长度
      if (text.length > 2000) break;
    }
    
    return this.cleanWordText(text);
  }

  // Word段落结构解析
  async parseWordParagraphs(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 查找段落标记
    const paragraphs = this.findWordParagraphs(uint8Array);
    const text = paragraphs.join('\n\n');
    
    return {
      text: text,
      score: this.scoreText(text),
      method: 'Word段落结构解析'
    };
  }

  // 查找Word段落
  findWordParagraphs(uint8Array) {
    const paragraphs = [];
    let currentParagraph = '';
    
    // 扫描整个文档寻找段落模式
    for (let i = 0; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if ((char >= 0x20 && char <= 0x7E) || (char >= 0x4e00 && char <= 0x9fff)) {
        currentParagraph += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        if (currentParagraph.trim().length > 5) {
          paragraphs.push(currentParagraph.trim());
        }
        currentParagraph = '';
      } else if (char === 0x20 || char === 0x09) {
        currentParagraph += ' ';
      }
    }
    
    // 处理最后一个段落
    if (currentParagraph.trim().length > 5) {
      paragraphs.push(currentParagraph.trim());
    }
    
    return paragraphs.filter(p => p.length > 5);
  }

  // Word二进制重构
  async reconstructWordBinary(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 尝试多种二进制重构方法
    const methods = [
      () => this.reconstructByByteSwap(uint8Array),
      () => this.reconstructByEndianness(uint8Array),
      () => this.reconstructByAlignment(uint8Array)
    ];
    
    let bestText = '';
    let maxScore = 0;
    
    for (const method of methods) {
      try {
        const text = method();
        const score = this.scoreText(text);
        
        if (score > maxScore) {
          maxScore = score;
          bestText = text;
        }
      } catch (e) {
        // 忽略重构错误
      }
    }
    
    return {
      text: bestText,
      score: maxScore,
      method: 'Word二进制重构'
    };
  }

  // 字节交换重构
  reconstructByByteSwap(uint8Array) {
    let text = '';
    
    for (let i = 0; i < uint8Array.length - 1; i += 2) {
      // 尝试交换字节顺序
      const char1 = uint8Array[i] | (uint8Array[i + 1] << 8); // 小端序
      const char2 = (uint8Array[i] << 8) | uint8Array[i + 1]; // 大端序
      
      // 选择更合理的字符
      const char = this.isValidChar(char1) ? char1 : (this.isValidChar(char2) ? char2 : 0);
      
      if (char > 0) {
        text += String.fromCharCode(char);
      }
    }
    
    return this.cleanWordText(text);
  }

  // 字节序重构
  reconstructByEndianness(uint8Array) {
    // 尝试不同的字节序解释
    const decoders = [
      new TextDecoder('utf-16le', { fatal: false }),
      new TextDecoder('utf-16be', { fatal: false })
    ];
    
    let bestText = '';
    let maxScore = 0;
    
    for (const decoder of decoders) {
      try {
        const text = decoder.decode(uint8Array);
        const cleaned = this.cleanWordText(text);
        const score = this.scoreText(cleaned);
        
        if (score > maxScore) {
          maxScore = score;
          bestText = cleaned;
        }
      } catch (e) {
        // 忽略解码错误
      }
    }
    
    return bestText;
  }

  // 对齐重构
  reconstructByAlignment(uint8Array) {
    // 尝试不同的字节对齐
    const alignments = [1, 2, 4];
    let bestText = '';
    let maxScore = 0;
    
    for (const alignment of alignments) {
      let text = '';
      
      for (let i = 0; i < uint8Array.length - alignment; i += alignment) {
        if (alignment === 1) {
          const char = uint8Array[i];
          if (char >= 0x20 && char <= 0x7E) {
            text += String.fromCharCode(char);
          }
        } else if (alignment === 2) {
          const char = uint8Array[i] | (uint8Array[i + 1] << 8);
          if (this.isValidChar(char)) {
            text += String.fromCharCode(char);
          }
        }
      }
      
      const cleaned = this.cleanWordText(text);
      const score = this.scoreText(cleaned);
      
      if (score > maxScore) {
        maxScore = score;
        bestText = cleaned;
      }
    }
    
    return bestText;
  }

  // 提取UTF-16LE文本
  extractUTF16LEText(uint8Array, startOffset) {
    let text = '';
    
    for (let i = startOffset; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if (this.isValidChar(char)) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
      
      if (text.length > 3000) break;
    }
    
    return text;
  }

  // 提取旧版Word文本
  extractLegacyWordText(uint8Array) {
    // 旧版Word可能使用不同的编码
    const encodings = ['windows-1252', 'iso-8859-1', 'utf-8'];
    let bestText = '';
    let maxScore = 0;
    
    for (const encoding of encodings) {
      try {
        const decoder = new TextDecoder(encoding, { fatal: false });
        const text = decoder.decode(uint8Array);
        const cleaned = this.cleanWordText(text);
        const score = this.scoreText(cleaned);
        
        if (score > maxScore) {
          maxScore = score;
          bestText = cleaned;
        }
      } catch (e) {
        // 忽略解码错误
      }
    }
    
    return bestText;
  }

  // 检查是否为有效字符
  isValidChar(char) {
    return (char >= 0x20 && char <= 0x7E) || // ASCII可打印字符
           (char >= 0x4e00 && char <= 0x9fff) || // 中文字符
           (char >= 0x00A0 && char <= 0x00FF); // 扩展ASCII
  }

  // 清理Word文本
  cleanWordText(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fff\u0020-\u007E\s]/g, '') // 只保留中文和ASCII
      .replace(/\s{3,}/g, '  ') // 合并空格
      .replace(/(.)\1{5,}/g, '$1') // 移除重复字符
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .trim();
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;
    
    let score = 0;
    
    // 基础长度分
    score += Math.min(text.length / 10, 50);
    
    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    if (chineseChars) {
      score += chineseChars.length * 3;
    }
    
    // 有意义的英文单词加分
    const englishWords = text.match(/\b[a-zA-Z]{3,}\b/g);
    if (englishWords) {
      score += englishWords.length * 2;
    }
    
    // Word特征加分
    if (text.includes('Microsoft') || text.includes('Word') || text.includes('Office')) {
      score += 20;
    }
    
    return score;
  }

  // 文本转HTML
  textToHtml(text) {
    if (!text) return '<p>无内容</p>';
    
    const paragraphs = text.split('\n\n');
    let html = '';
    
    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        html += `<p>${this.escapeHtml(paragraph.trim())}</p>`;
      }
    });
    
    return html || '<p>无法提取内容</p>';
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WordEnhancedParser;
} else if (typeof window !== 'undefined') {
  window.WordEnhancedParser = WordEnhancedParser;
}
