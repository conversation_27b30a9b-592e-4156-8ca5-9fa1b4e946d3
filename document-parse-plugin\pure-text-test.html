<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>纯正文提取测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
      font-size: 2.5em;
    }
    .header-icon {
      text-align: center;
      font-size: 4em;
      margin-bottom: 20px;
    }
    .test-section {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 10px;
      margin-bottom: 25px;
      border: 2px solid #28a745;
    }
    .section-title {
      font-weight: bold;
      color: #28a745;
      margin-bottom: 15px;
      font-size: 18px;
    }
    .input-group {
      margin-bottom: 20px;
    }
    .input-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #495057;
    }
    textarea {
      width: 100%;
      padding: 15px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      resize: vertical;
    }
    .original-text {
      height: 60px;
    }
    .garbled-text {
      height: 150px;
    }
    .button-group {
      text-align: center;
      margin: 25px 0;
    }
    button {
      background: #28a745;
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      margin: 5px;
      transition: all 0.3s ease;
    }
    button:hover {
      background: #1e7e34;
      transform: translateY(-2px);
    }
    .extract-btn {
      background: #dc3545;
      font-size: 18px;
      padding: 18px 35px;
    }
    .extract-btn:hover {
      background: #c82333;
    }
    .sample-btn {
      background: #007bff;
    }
    .sample-btn:hover {
      background: #0056b3;
    }
    .clear-btn {
      background: #6c757d;
    }
    .clear-btn:hover {
      background: #545b62;
    }
    .results-section {
      margin-top: 30px;
      display: none;
    }
    .result-card {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 15px 0;
    }
    .result-card.success {
      border-color: #28a745;
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
    .result-card.failed {
      border-color: #dc3545;
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
    .result-title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .score-badge {
      background: #28a745;
      color: white;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 14px;
    }
    .score-badge.failed {
      background: #dc3545;
    }
    .result-content {
      background: white;
      padding: 20px;
      border-radius: 8px;
      font-family: monospace;
      white-space: pre-wrap;
      border: 1px solid #e9ecef;
      font-size: 14px;
      line-height: 1.6;
      min-height: 50px;
    }
    .comparison-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }
    .before-after {
      background: white;
      padding: 20px;
      border-radius: 10px;
      border: 2px solid #e9ecef;
    }
    .before-after.before {
      border-color: #dc3545;
    }
    .before-after.after {
      border-color: #28a745;
    }
    .before-after h4 {
      margin-top: 0;
      color: #495057;
    }
    .status {
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .highlight {
      background: #fff3cd;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
      border-left: 4px solid #ffc107;
    }
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .stat-item {
      background: white;
      padding: 15px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid #e9ecef;
    }
    .stat-number {
      font-size: 1.8em;
      font-weight: bold;
      color: #28a745;
    }
    .stat-label {
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header-icon">📝✨🎯</div>
    <h1>纯正文提取器</h1>

    <div class="highlight">
      <strong>🎯 目标：</strong>从WPS和Word的乱码中提取纯正文内容，去除所有格式信息、软件信息和乱码干扰。
    </div>

    <div class="test-section">
      <div class="section-title">📝 输入测试数据</div>

      <div class="input-group">
        <label for="originalText">原始正文（如果知道）：</label>
        <textarea id="originalText" class="original-text" placeholder="输入您知道的原始正文内容..."></textarea>
      </div>

      <div class="input-group">
        <label for="garbledText">乱码文本：</label>
        <textarea id="garbledText" class="garbled-text" placeholder="粘贴从.doc文件中提取的乱码文本..."></textarea>
      </div>
    </div>

    <div class="button-group">
      <button class="extract-btn" onclick="extractPureText()">🎯 提取纯正文</button>
      <button class="sample-btn" onclick="loadWPSSample()">📄 WPS示例</button>
      <button class="sample-btn" onclick="loadWordSample()">📄 Word示例</button>
      <button class="clear-btn" onclick="clearAll()">🗑️ 清空</button>
    </div>

    <div class="results-section" id="resultsSection">
      <h2>🎯 提取结果</h2>
      <div id="extractionResults"></div>

      <div class="comparison-section">
        <div class="before-after before">
          <h4>❌ 提取前（乱码）</h4>
          <div class="result-content" id="beforeContent"></div>
        </div>
        <div class="before-after after">
          <h4>✅ 提取后（纯正文）</h4>
          <div class="result-content" id="afterContent"></div>
        </div>
      </div>

      <div class="stats" id="statsSection"></div>
    </div>
  </div>

  <!-- 加载纯正文提取器 -->
  <script src="lib/pure-text-extractor.js"></script>

  <script>
    let pureTextExtractor = null;

    // 初始化
    try {
      pureTextExtractor = new PureTextExtractor();
      console.log('纯正文提取器初始化成功');
      showStatus('success', '纯正文提取器加载成功，可以开始测试');
    } catch (error) {
      console.error('纯正文提取器初始化失败:', error);
      showStatus('error', '纯正文提取器加载失败: ' + error.message);
    }

    function extractPureText() {
      const originalText = document.getElementById('originalText').value.trim();
      const garbledText = document.getElementById('garbledText').value.trim();

      if (!garbledText) {
        showStatus('error', '请输入乱码文本');
        return;
      }

      if (!pureTextExtractor) {
        showStatus('error', '纯正文提取器未初始化');
        return;
      }

      showStatus('info', '开始提取纯正文...');

      try {
        // 执行提取
        const extractedText = pureTextExtractor.extractPureText(garbledText, originalText);
        const score = pureTextExtractor.evaluateExtraction(extractedText, originalText);

        // 显示结果
        displayResults(extractedText, garbledText, originalText, score);

        showStatus('success', `纯正文提取完成！得分: ${score.toFixed(1)}`);

        // 显示结果区域
        document.getElementById('resultsSection').style.display = 'block';

      } catch (error) {
        showStatus('error', '提取失败: ' + error.message);
        console.error('提取错误:', error);
      }
    }

    function loadWPSSample() {
      document.getElementById('originalText').value = 'LAsSASASsSS';
      document.getElementById('garbledText').value = `Root EntryData 0TableWordDocument !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUWXYZ[d]^_\`abcF怀F正文$恁$默认段落字体8怠8页脚$怟页眉c搒搤d伄搦d億爐逖蜄z 吀椀洀攀猀一攀眀刀漀洀愀渀逄蘁輀謀卛O逄蘁蜄劀潟途匀礀洀戀漀氀逤*x@爀椀愀氀逄蘁厞O逴*x@漀甀爀椀攀爀一攀眀逄圀椀渀最搀椀渀最猀逄.寡\` 吠愀栀漀洀愀蠈桑帀G娀崀紀峿巿廿嬀笀寿鰀?匀耀脀鰁茀猁>0(0C 袉倔卋卋$* 洈猄1連逤倲週藠俹醫0耀SummaryInformation(VDocumentSummaryInformation8\\ WPS Office 专业版王如根@鞓D鞓耀 (\\耀dlKSOProductBuildVer2052-9.1.0.39140澐C LAsSASASsSS`;

      showStatus('info', 'WPS示例已加载，点击"提取纯正文"开始测试');
    }

    function loadWordSample() {
      document.getElementById('originalText').value = '你好傻逼傻逼';
      document.getElementById('garbledText').value = `>/1.勰橢橢謯謯柩柩鈖爁$6耝或0鈖$,鈖戌瀓你好傻逼傻逼譨梺漀0週倲舮連逤匃逌踓槸葛码頀鸀瘂栁瀂嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄堁嘂縂适耂 佊偊兊彈浈湈獈瑈J怀J正文$愤 彈慊浈湈獈瑈$$默认段落字体BiB普通表格4l愀 欠 无列表偋!孃湯整瑮呟灹獥崮浸沬釋仃菥鲲苇軇粢餤喧呂氖鸻狣飃鲧嵇伷襖脚憥頏肇襉劚箥烦杌邎爟邤瘦能耎痍凜醉堽飹邴丶莳鋀覡姍哂敞谒褌忏朠鸻觬汙躲粎帶仌怔孿缂偋!牟汥猯爮汥葳迏櫃蟯薽菑兽瘯邥綣缨栢俛萈议铧骪棡瘽羿苉薤嬈灸蚣篛徵傼锒蠏俙劼擑戴缤醧影麘刖悮辨侞翁渄鐷楌拤厽邨斪倀!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸氌綡遷掻抲C鰚鿛菣鯕夬鰇訍旍蠮簬泡砘贓珈絑薐庹樤謽均偋!励桴浥支桴浥支桴浥攱砮汭姬毝缐缸撱鄶菵銑鋇閴宺毯巙寑鹈偈緩義譃髾鴑鏝目喥鯷駟賍滶糜儘獁屘诳螾逛侮黻逨拊峳堧逡躰鐿奘紑塆灋貆嘏茝裞閕鄈戔臚郴糿彇鿀悢阨成箔衇糖惇巼僺骍忐贕襲匿惀淲鞠闊嚦弃岨鋩仃匮櫪瞳峚娫硃铪磑毂詐竓肯骥荟傮暻鶷犩嵝鴪减逍癙旋倀詀褤餡忿癈昨嘂皅愍鋟訃萲阖俴操絚爃韣輧缽秲叛镚跢铀篻埃翤矋齯鵾麚漓淍鱶篢鯴徾紸鬟鼺澴燔崒懡揯謮荠嫨膫換迷吹堒蟆愻搁眙疣鷆庸沣渙軮鱭轍巜躄岶倧襢敋訃襢揩箩葬掱眏鰉鐶焑穋嘶芸屌雞癯篯畆嶻滢嵌襣霢誨蓷屍璈耩嫳葋齦练趠僜憜鏈牆鴮蠻擙袢藄郭龱訑璘矁駽蒨衫訃蚗囁髂铲億矆寋妘駐劮滄燢軌劰笇詣軑挀甆墖沪坣拝癆暱仮愐沥氉鿝峉駡籂駦蠽図迃轌腉綔賤庪噄匃鶯渓螗贖猼霠茯藝禔澧袺娆蓲蠭壡獔拕控桾烀操觏耚鿲缷熀獇熟唗琜雕鋭昙繮榨C夛堬圣驇族泜籤駩擲痞鄞齃棩駏傐釚訓葷晵蛐辧焽瘆蒘凰怰躴資駼賈蜎誾划袩砉瀓暤鶗躞嶣輺譅疬皙腖撾帨柫灌匥璥騟旟邋谰綨下遳笯倭奪哠毒纹燇鬰吏鳒像菹嚙耽胋諫皩蹈嬤璌棏葏驸檝洊刏冽霖贵鬚芕燍锡旺辆儂埔砃霼締匯夒搮褓田椺蠵裄櫹姻栘殬裦屖芅宒螲莑諆瓲稉嫼惲仮競瓌犵市潓鹙獿榍癚矍儺蟒畄錐妊双紝闹髮檸據笈檁厭驮赵铃箶郲农榞唕嗝欰猃鍜堷峍槰黩娷嵤黹痏樌骋押虘嗍麮掶鶨唘医柍服玥畾溾曖額狇吊楸撓敃溋偋!龐'桴浥支桴浥支牟汥猯桴浥敍湡条牥砮汭爮汥葳轍蓷瞂漈濓鄦裝蓤儤蜮憾榙鞻鷉挓栱闩驱淁蹀刖襎撰艠澎朕酋刨犘鍊鳐咊胹辣囤痷綠鯱簁抽笐阙驐褚术巾允珙蔅鮪寊倀!嬀潃瑮湥彴祔数嵳砮汭偋-!牟汥猯爮汥偳!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸偬!勪琀敨敭琯敨敭琯敨敭浸偬!郑龶'琀敨敭琯敨敭弯敲獬琯敨敭慍慮敧爮浸氮敲獬偋崁浸氠敶獲潩渽湥潣楤杮呕猠慴摮污湯攽礢獥愺汣牍灡砠汭獮愺瑨灴猯档浥獡漮数确汭潦浲瑡献牯术牤睡湩浧氯洯楡渢戠朱瑬琠砱此戠朲瑬琠砲此愠捣湥琱捡散瑮愠捣湥琲捡散瑮愠捣湥琳捡散瑮愠捣湥琴捡散瑮愠捣湥琵捡散瑮愠捣湥琶捡散瑮栠楬歮汨湩欢映汯汈湩欽昢汯汈湩欢"8@肀耀鋰匀?3億(朗謀老Unknown送硛Times New Roman送耀Symbol送硛Arial送蘃 糺等线DengXian蘀 糺等线 Light送Cambria Math 耘栁逃?!%),.:;>?]}嫾峾廾峿巿廿$([{姾対巾寿鰀肂2茑偀値翿2xx王如根王如根椉跰娧馵悼路娧馵藠鿲俹栐醫0氁退頀吁封搁潎浲污搮瑯m2楍牣獯景琠晏楦散圠牯d@@蛾@蛾鰮鞓D鰮鞓簁頀 \` ?G卋偏潲畤瑣畂汩噤牥2 !"#$%'()*+,-0Root EntryF耝2耀Data1TableWordDocumentSummaryInformation(DocumentSummaryInformation8&CompObjnF楍牣獯景琠潗摲卍潗摲潄c潗摲捯浵湥琮8熲`;

      showStatus('info', 'Word示例已加载，点击"提取纯正文"开始测试');
    }

    function displayResults(extractedText, garbledText, originalText, score) {
      const resultsDiv = document.getElementById('extractionResults');
      const beforeDiv = document.getElementById('beforeContent');
      const afterDiv = document.getElementById('afterContent');
      const statsDiv = document.getElementById('statsSection');

      const isSuccess = score > 80;

      // 显示主要结果
      resultsDiv.innerHTML = `
        <div class="result-card ${isSuccess ? 'success' : 'failed'}">
          <div class="result-title">
            ${isSuccess ? '✅ 提取成功' : '⚠️ 提取需要改进'}
            <span class="score-badge ${isSuccess ? '' : 'failed'}">${score.toFixed(1)}分</span>
          </div>
          <div class="result-content">${extractedText || '无法提取有效内容'}</div>
        </div>
      `;

      // 显示对比
      beforeDiv.textContent = garbledText.substring(0, 500) + (garbledText.length > 500 ? '...' : '');
      afterDiv.textContent = extractedText || '无内容';

      // 显示统计信息
      const originalLength = originalText ? originalText.length : 0;
      const garbledLength = garbledText.length;
      const extractedLength = extractedText ? extractedText.length : 0;
      const cleanRatio = pureTextExtractor.calculateCleanRatio(extractedText);
      const compressionRatio = garbledLength > 0 ? (extractedLength / garbledLength * 100) : 0;

      statsDiv.innerHTML = `
        <div class="stat-item">
          <div class="stat-number">${originalLength}</div>
          <div class="stat-label">原文长度</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${garbledLength}</div>
          <div class="stat-label">乱码长度</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${extractedLength}</div>
          <div class="stat-label">提取长度</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${(cleanRatio * 100).toFixed(1)}%</div>
          <div class="stat-label">清洁度</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${compressionRatio.toFixed(1)}%</div>
          <div class="stat-label">压缩比</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${score.toFixed(1)}</div>
          <div class="stat-label">总评分</div>
        </div>
      `;
    }

    function clearAll() {
      document.getElementById('originalText').value = '';
      document.getElementById('garbledText').value = '';
      document.getElementById('resultsSection').style.display = 'none';
      showStatus('info', '已清空所有内容');
    }

    function showStatus(type, message) {
      const container = document.querySelector('.container');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;

      container.insertBefore(statusDiv, container.firstChild);

      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof PureTextExtractor !== 'undefined') {
        showStatus('success', '纯正文提取器加载完成，可以开始测试');
        // 自动加载Word示例
        loadWordSample();
      } else {
        showStatus('error', '纯正文提取器未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
