from typing import Optional, Dict, Any
import copy


def merge_al_response(param: Dict[str, Any]) -> Dict[str, Any]:
    """合并AI响应结果到原始参数中

    Args:
        param: 原始参数，包含needExecAl和alResponse字段

    Returns:
        合并后的结果字典
    """
    # 深拷贝原始参数
    result = copy.deepcopy(param)

    # 检查是否需要执行AI处理
    if result.get("needExecAl") == "是":
        al_response = result.get("alResponse")

        if not al_response or not isinstance(al_response, dict):
            result["error"] = "ai调用返回结果格式错误"
            return result

        # 检查alResponse中的字段是否为空
        if (
            al_response
            and al_response.get("firstReason")
            and al_response.get("secondReason")
            and al_response.get("thirdReason")
        ):
            # 更新result中的对应字段
            result["firstReason"] = al_response.get("firstReason", "")
            result["secondReason"] = al_response.get("secondReason", "")
            result["thirdReason"] = al_response.get("thirdReason", "")
        else:
            # 如果alResponse中的字段都为空，设置错误信息
            result["error"] = "ai调用返回结果为空"

    return result


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """主处理流程"""
    # 首先合并AI响应结果
    merged_result = merge_al_response(param)
    return merged_result
