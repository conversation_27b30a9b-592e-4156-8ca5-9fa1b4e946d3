import subprocess
import re

# 需要检查的依赖列表（已过滤注释和安装命令）
required_packages = [
    "eventlet",
    "airtest",
    "poco",
    "facebook-wda",
    "tars",
    "python-dateutil",
    "retrying",
    "wget",
    "fastapi",
    "uvicorn",
    "websockets",
    "six",
    "websocket-client",
    "requests",
    "nest-asyncio",
    "setuptools",
    "pywinauto",
    "Jinja2",
    "retry",
    "Deprecated",
    "urllib3",
    "wheel",
    "pydantic",
    "requests_toolbelt",
    "sqlalchemy",
    "aiofiles",
    "pymysql",
    "apscheduler",
    "loguru",
    "psutil",
    "exchangelib",
    "Texttable",
    "python-multipart",
    "xlwt",
    "xlrd",
    "xlutils",
    "python-office",
    "bs4",
    "aiohttp",
    "jsonpath_rw",
    "jsonsearch",
]


# 获取已安装的包列表（忽略版本）
def get_installed_packages():
    result = subprocess.run(["pip", "freeze"], capture_output=True, text=True)
    installed = set()
    for line in result.stdout.splitlines():
        # 处理常规包和可编辑包（-e）
        if line.startswith("-e"):
            pkg_name = re.split(r"[=@#]", line.split()[-1])
        else:
            pkg_name = re.split(r"[=<>@#]", line)[0].lower()
        installed.add(pkg_name)
    return installed


# 检查缺失的包
def check_missing_packages():
    installed = get_installed_packages()
    missing = []

    for pkg in required_packages:
        # 统一转换为小写比较（PyPI包名不区分大小写）
        if pkg.lower() not in installed:
            missing.append(pkg)

    return missing


if __name__ == "__main__":
    missing_packages = check_missing_packages()

    if missing_packages:
        print("以下包未安装:")
        for pkg in missing_packages:
            print(f" - {pkg}")
    else:
        print("所有依赖包均已安装")
