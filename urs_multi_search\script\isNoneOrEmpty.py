from typing import Any

def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空

print(is_deep_empty([None, [], {}]))  # 输出 True
print(is_deep_empty({"a": None}))     # 输出 True
print(is_deep_empty([1]))             # 输出 False
