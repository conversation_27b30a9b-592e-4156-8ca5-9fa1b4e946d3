// 飞书网页交互脚本

// 监听来自插件的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'checkFeishuPage') {
    // 检查当前页面是否是飞书表格页面
    const isFeishuSheet = window.location.href.includes('feishu.cn/sheets/') ||
                          window.location.href.includes('feishu.cn/base/');
    sendResponse({ isFeishuSheet });
  }

  else if (request.action === 'getTableInfo') {
    // 获取当前表格的信息
    try {
      // 尝试从页面中提取表格信息
      const title = document.title.replace(' - 飞书云文档', '');
      const spreadsheetId = window.location.href.match(/\/([^\/]+)$/)[1];

      sendResponse({
        success: true,
        data: {
          title,
          spreadsheetId
        }
      });
    } catch (error) {
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  else if (request.action === 'highlightTable') {
    // 高亮显示表格，提示用户正在操作
    try {
      const tableElement = document.querySelector('.sheet-table');
      if (tableElement) {
        // 添加高亮样式
        tableElement.style.boxShadow = '0 0 0 2px #3370ff';

        // 3秒后移除高亮
        setTimeout(() => {
          tableElement.style.boxShadow = '';
        }, 3000);

        sendResponse({ success: true });
      } else {
        sendResponse({
          success: false,
          error: '未找到表格元素'
        });
      }
    } catch (error) {
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  // 返回true以保持消息通道开放，允许异步响应
  return true;
});

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  // 检查是否在飞书表格页面
  const isFeishuSheet = window.location.href.includes('feishu.cn/sheets/') ||
                        window.location.href.includes('feishu.cn/base/');

  if (isFeishuSheet) {
    // 通知插件当前在飞书表格页面
    chrome.runtime.sendMessage({
      action: 'onFeishuSheet',
      data: {
        url: window.location.href,
        title: document.title
      }
    });
  }
});
