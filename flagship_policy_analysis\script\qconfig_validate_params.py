# 导入需要的包(本案例不需要导入以下两个python包，如果有不支持的包，可以找平台管理员安装)
# import json
#
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple, Union
import random
import string
import json
import requests
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> Dict[str, Any]:
    """统一处理请求异常，返回统一的错误格式"""
    if isinstance(e, requests.exceptions.HTTPError):
        return {"status": 1, "message": f"http错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.ConnectionError):
        return {"status": 1, "message": f"连接错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.Timeout):
        return {"status": 1, "message": f"超时错误: {str(e)}"}
    elif isinstance(e, requests.exceptions.RequestException):
        return {"status": 1, "message": f"请求错误: {str(e)}"}
    else:
        # 其他异常转换为统一格式
        return {"status": 1, "message": f"{e.__class__.__name__}: {str(e)}"}

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 校验入参
    required_params = ['targetgroupid', 'dataid', 'env', 'checkParams', 'processId', 'qunar_account']
    for param_name in required_params:
        if param_name not in param or not param[param_name]:
            return {
                "status": 1,
                "message": f"参数 {param_name} 不能为空"
            }
    
    # 校验processId和oaId格式并转换为int
    try:
        process_id = int(param['processId'])
    except ValueError:
        return {
            "status": 1,
            "message": "参数 processId格式不正确，必须为整数"
        }
    
    # 获取并验证环境变量
    env = param.get('env', '')
    # 定义允许的环境变量值，这里是qconfig的配置环境，不是真实的机器部署环境
    ALLOWED_ENVS = {"resources", "prod", "beta"}
    # 如果环境变量为空或不在允许的范围内，返回默认值beta
    if not env or env not in ALLOWED_ENVS:
        env = "beta"# 获取环境变量，默认为beta

    # 处理数据
    checkParams, parse_status = parse_urlencoded_structured_data(param['checkParams'])
    if parse_status["status"] != "success":
        return {
            "status": 1,
            "message": "入参配置解析失败"
        }
    
    # 校验并提取版本信息和状态信息
    new_content = []
    for item in checkParams:
        if isinstance(item, dict):
            new_item = {}
            if 'qconfig_remote_edit_version' in item:
                new_item['qconfig_remote_edit_version'] = item['qconfig_remote_edit_version']
            if 'qconfig_remote_status' in item:
                new_item['qconfig_remote_status'] = item['qconfig_remote_status']
            if new_item:  # 只有当至少有一个属性时才添加到新列表
                new_content.append(new_item)
    
    # 检查是否找到必要的参数
    if not new_content:
        return {
            "status": 1,
            "message": "配置中缺少必要的版本信息(qconfig_remote_edit_version)或状态信息(qconfig_remote_status)"
        }
    # 步骤五：POST请求更新配置
    return check_config_by_env(env, process_id, param['dataid'], param['qunar_account'], new_content)


def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result


def generate_config(dataid: str, config_data: list) -> dict:
    """
    将配置数据按 dataid 封装为指定格式

    :param dataid: 配置文件名（如 "product_code.t"）
    :param config_data: 配置数据列表，格式为 List[Dict]
    :return: 结构化配置字典
    """
    return {dataid: config_data}

def check_config_by_env(env: str, process_id: int, dataid: str, qunar_account: str, new_content: Any) -> Dict[str, Any]:
    """根据环境标识提交配置到不同的URL
    
    参数:
    env: str - 环境标识，如'beta', 'prod', 'resources'
    process_id: int - 流程ID
    dataid: str - 数据ID
    new_content: Any - 待校验配置信息
    
    返回:
    Dict[str, Any] - 提交结果
    """
    # 根据环境设置不同的URL
    if env == "prod" or env == "resources":
        post_url = "http://quan.corp.qunar.com/activityConfig/visualizationPublish/checkQConfigByAi"
        user_id = qunar_account
    else:
        # 默认使用beta环境
        post_url = "http://f-tts-activity-config.fd-329539-tros.inner3.beta.qunar.com/activityConfig/visualizationPublish/checkQConfigByAi"
        user_id = "pengyy.tan"
    try:
        headers = {
            "Content-Type": "application/json",
            "Cookie": "_xconfig_userId=" + user_id
        }

        post_data = {
            "processId": process_id,
            "configCheckParams": generate_config(dataid, new_content)
        }
        post_response = requests.post(
            post_url,
            headers=headers,
            json=post_data,
            timeout=30
        )
        post_response.raise_for_status()
        post_result = post_response.json()

        # 处理post返回结果
        if post_result.get('status') != 0:
            return {
                "status": post_result.get('status', 1),
                "message": post_result.get('msg', '校验配置失败')
            }
        
        # 成功时返回统一格式的字典
        return {
            "status": 0,  # 成功状态
            "message": "校验通过"
        }

    except Exception as e:
        return handle_request_exception(e)