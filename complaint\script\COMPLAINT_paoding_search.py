from typing import Any, Dict
import requests
import json

# 白名单标签列表
WHITELIST_TAGS = [
    'HNC1', 'JXL1', 'CZA1', 'TYL1', 'GST1', 'JXX1', 'DSB1', 'PDD1', 'ZYJ1', 'ZXY1', 'ZYX1',
    'AGE1', 'JXB1', 'TTD1', 'CZD1', 'ZHT1', 'MUX1', 'SEE1', 'OLD1', 'GSX1', 'PEC1', 'HCP1',
    'TBX1', 'GPB1', 'CZB1', 'OKR1', 'QHY1', 'HUJ1', 'TYJ1', 'NOY1', 'HUL1', 'TTX1', 'OKK1',
    'TYB1', 'SLD1', 'SOA1', 'TTB1', 'TTC1', 'SLA1', 'MUJ1', 'HUB1', 'JXH1', 'HDY1', 'ZYT1',
    'JXY1', 'STU1', 'TTA1', 'SLG1', 'LHE1', 'JXF1', 'KNH1', 'TBL1', 'PEE1', 'UZH1', 'UZZ1',
    'HUG1', 'SEA1', 'JXJ1', 'TYY1', 'NQZ1', 'TPF1', 'GTT1', 'MUV1', 'FGB1', 'TYF1', 'SLB1',
    'UAA1', 'TPQ1', 'MUG1', 'TPE1', 'STD1', 'BPZ1', 'TST1', 'BPL1', 'QSW1', 'HUM1', 'SLY1',
    'TPD1', 'QXH1', 'SLP1', 'SLN1', 'QHY1', 'TPO1', 'TPA1', 'TPB1', 'WFY1', 'WFX1', 'WFZ1',
    'KFF1', 'ZYF1', 'TAF1', 'TAL1', 'KFB1', 'KFD1', 'KFZ1', 'KFM1', 'KFE1', 'CDJ1', 'JCY1',
    'JCB1', 'TTL1', 'RQL2', 'ZHZ1', 'KFK1', 'KFW1'
]



def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        appCode (str): Application code for authentication
        appToken (str): Token for authentication
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }


def check_whitelist(tag: str) -> str:
    """
    检查标签是否在白名单中
    
    Args:       
        tag (str): 要检查的标签
        
    Returns:
        str: '是' 如果在白名单中，否则 '否'
    """
    return '是' if tag in WHITELIST_TAGS else '否'

def check_automatic_refund(product_labels: str) -> str:
    """
    检查是否自动退款
    
    Args:
        product_labels (str): 产品标签
        
    Returns:
        str: '是' 如果自动退款，否则 '否'
    """
    return '是' if product_labels == '1' else '否'

def search_order_info(order_no: str, invokeAppCode: str, invokeToken: str) -> Dict[str, str]:
    """
    通过订单号查询订单信息
    
    Args:
        order_no (str): 订单号
        invokeAppCode (str): 调用方应用代码
        invokeToken (str): 调用方token
        
    Returns:
        Dict[str, str]: 包含查询结果的字典
    """
    # 构建请求参数
    proxyData = {
        "method": "get",
        "url": f"https://paoding.corp.qunar.com/visual/mainSearch?orderNo={order_no}&qTraceId=",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://paoding.corp.qunar.com/login",
            "authCookies": ["user_id", "JSESSIONID"],
        },
    }
    
    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    
    # 调用代理接口
    result = invoke_http_by_proxy(invokeAppCode, invokeToken, proxyData, proxy)
    
    if not result.get("success"):
        error_msg = result.get("error", "Unknown error")
        return {"error": error_msg,"results": {}}  
    
    try:
        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))
        
        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
            return {"error": error_msg, "results": {}}
            
        # 获取内层data
        data = response_data.get("data", {})
        if not data:
            return {"error": "Inner data is empty", "results": {}}
        
        # 获取标签
        tag = data.get("baseInfo", {}).get("basicAttributes", {}).get("asyncPriceInfo", {}).get("tag", "")
        
        # 获取产品标签
        product_labels = data.get("computingProcess", {}).get("async", {}).get("packageInfo", {}).get("postPackageInfo", {}).get("productLabels", "")

        return {"error": "", 
                "results": {    
                    "iswhitelist": check_whitelist(tag),
                    "tag": tag,
                    "automaticRefund": check_automatic_refund(product_labels),
                    "product_labels": product_labels
                }}
        
        
    except Exception as e:
        return {"error": str(e), "results": {}}

def main(param: Dict[str, str]) -> Dict[str, Any]:
    # 测试示例
    order_no = param.get("orderNo")
    invokeAppCode = param.get("invokeAppCode")
    invokeToken = param.get("invokeToken")
    result = search_order_info(order_no, invokeAppCode, invokeToken)
    return result



def test():
    # 测试示例
    param = {
        "orderNo": "xep250410101928103",
        "invokeAppCode": "f_pangu",
        "invokeToken": "Hnu88YsOdF2FekK3qbEBhpPzK8ix8OhdGuwok9RaQsFd54/2hkM8VXaUTyAp/qJR9KtgLQH8J+OoP6KsnxKBEom/ju5QamxJIgzeIyxsSC0mzQ3m7T6ZCW2d5cdSR+rAbsg5cqXlCwqM5KxElKz6wKcm5CM35atOjQDM9Whing4="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False))


if __name__ == "__main__":
    test()     