from datetime import datetime, date, timedelta
from typing import Union, Optional


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d:%H:%M",  # 新增格式支持 YYYY-MM-DD:HH:mm
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError, Exception) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None


def main(param):
    # Get time parameter from param
    time_str = param.get("time") if param else None
    if not time_str:
        return {"error": "time parameter is required", "startDate": "", "endDate": ""}

    # Parse the time string
    oriTime = safe_parse_datetime(time_str)
    if not oriTime:
        return {"error": "invalid time format", "startDate": "", "endDate": ""}

    # Convert to datetime.date if it's datetime
    if isinstance(oriTime, datetime):
        oriTime = oriTime.date()

    # Calculate date range
    startDate = oriTime - timedelta(days=6)
    endDate = oriTime + timedelta(days=1)

    # Format dates and return JSON
    return {
        "error": "",
        "startDate": startDate.strftime("%Y-%m-%d"),
        "endDate": endDate.strftime("%Y-%m-%d"),
    }


if __name__ == "__main__":
    param = {
        "time": "2025-04-07",
    }
    result = main(param)
    print(result)
