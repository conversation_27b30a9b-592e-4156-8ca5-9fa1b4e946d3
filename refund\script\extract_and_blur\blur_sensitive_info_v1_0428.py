#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import cv2
import numpy as np
from pathlib import Path
import argparse
import requests
from io import BytesIO
from PIL import Image

# 定义敏感信息的正则表达式模式
PATTERNS = {
    '电话': [r'电话[：:]\s*(\d+)', r'联系电话[：:]\s*(\d+)', r'手机[：:]\s*(\d+)', r'联系方式[：:]\s*(\d+)', r'\d{11}'],
    '身份证号码': [r'([0-9]{17}[0-9X])', r'([0-9]{15})', r'身份证[：:]\s*([0-9X]{15,18})']
}

def download_image(url):
    """
    从URL下载图片并返回图片流
    
    参数:
        url: 图片URL地址
    
    返回:
        image: OpenCV格式的图片
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        image = Image.open(BytesIO(response.content))
        return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"下载图片失败: {e}")
        return None

def apply_mosaic(image, box, mosaic_size=8):
    """
    对图像的指定区域应用马赛克效果
    
    参数:
        image: 原始图像
        box: 要应用马赛克的区域 [x1, y1, x2, y2]
        mosaic_size: 马赛克块的大小（值越小，马赛克效果越明显）
    
    返回:
        处理后的图像
    """
    x1, y1, x2, y2 = box
    
    # 添加一些padding，使马赛克区域更大
    padding = 10
    height, width = image.shape[:2]
    
    # 确保坐标在图像范围内
    x1 = max(0, x1 - padding)
    y1 = max(0, y1 - padding)
    x2 = min(width, x2 + padding)
    y2 = min(height, y2 + padding)
    
    print(f"Original box: {box}")
    print(f"Adjusted box with padding: [{x1}, {y1}, {x2}, {y2}]")
    print(f"Image dimensions: {width}x{height}")
    
    # 提取区域
    region = image[y1:y2, x1:x2].copy()
    
    # 应用马赛克效果
    h, w = region.shape[:2]
    
    print(f"Region dimensions: {w}x{h}")
    
    # 如果区域太小，直接返回
    if h <= 0 or w <= 0:
        print("Region too small, skipping mosaic")
        return image
    
    # 缩小然后放大以创建马赛克效果
    # 使用更小的mosaic_size值来创建更明显的马赛克效果
    mosaic_h = max(1, h // mosaic_size)
    mosaic_w = max(1, w // (mosaic_size * 2))  # 宽度缩小1/4
    
    print(f"Mosaic dimensions: {mosaic_w}x{mosaic_h}")
    
    # 缩小
    small = cv2.resize(region, (mosaic_w, mosaic_h), interpolation=cv2.INTER_LINEAR)
    # 放大
    mosaic = cv2.resize(small, (w, h), interpolation=cv2.INTER_NEAREST)
    
    # 将马赛克区域放回原图
    result = image.copy()
    result[y1:y2, x1:x2] = mosaic
    
    # 验证马赛克是否被应用
    diff = np.sum(np.abs(result[y1:y2, x1:x2] - image[y1:y2, x1:x2]))
    print(f"Difference between original and mosaic region: {diff}")
    
    return result

def process_image(image_url, ocr_data, output_path):
    """
    处理单个图片URL，下载图片，进行OCR识别，并处理敏感信息
    
    参数:
        image_url: 图片URL地址
        ocr_data: OCR识别结果
        output_path: 输出文件路径
    """
    # 下载图片
    image = download_image(image_url)
    if image is None:
        print(f"无法下载图片: {image_url}")
        return False
    
    print(f"Processing image with shape: {image.shape}")
    print(f"Image type: {type(image)}")
    print(f"Image dtype: {image.dtype}")
    
    # 提取OCR结果
    ocr_results = []
    try:
        if "data" in ocr_data and "res" in ocr_data["data"]:
            for item in ocr_data["data"]["res"]:
                if "ocr_result" in item:
                    ocr_results.extend(item["ocr_result"])
            print(f"Found {len(ocr_results)} OCR results")
        else:
            print(f"OCR数据结构不符合预期")
            return False
    except Exception as e:
        print(f"处理OCR数据错误: {e}")
        return False
    
    # 找出敏感信息
    sensitive_boxes = []
    
    for item in ocr_results:
        text = item.get("text", "")
        box = item.get("box", [])
        
        # 检查是否包含敏感信息
        is_sensitive = False
        
        # 检查电话
        for pattern in PATTERNS['电话']:
            if re.search(pattern, text):
                is_sensitive = True
                print(f"找到电话: {text}")
                break
        
        # 检查身份证号码
        for pattern in PATTERNS['身份证号码']:
            match = re.search(pattern, text)
            if match:
                # 如果文本中包含"身份证"，只打码身份证号码部分
                if "身份证" in text and match.group(1):
                    # 获取匹配的身份证号码的位置
                    start_idx = match.start(1)
                    end_idx = match.end(1)
                    
                    # 计算身份证号码在原始文本中的比例
                    if len(text) > 0:
                        id_ratio = (end_idx - start_idx) / len(text)
                        
                        # 根据比例计算身份证号码在box中的位置
                        if len(box) == 4:
                            x1, y1, x2, y2 = box
                            width = x2 - x1
                            
                            # 调整box只包含身份证号码部分
                            adjusted_x1 = int(x1 + start_idx / len(text) * width)
                            adjusted_x2 = int(x1 + end_idx / len(text) * width)
                            
                            # 添加调整后的box
                            sensitive_boxes.append([adjusted_x1, y1, adjusted_x2, y2])
                            print(f"找到身份证号码: {match.group(1)}，只打码号码部分")
                            continue  # 跳过下面的代码，不添加整个box
                
                # 如果不是上面的特殊情况，标记整个文本为敏感
                is_sensitive = True
                print(f"找到身份证号码: {text}")
                break
        
        # 如果是敏感信息，记录其位置
        if is_sensitive and len(box) == 4:
            # box格式为 [x1, y1, x2, y2]
            sensitive_boxes.append(box)
    
    print(f"Found {len(sensitive_boxes)} sensitive areas to blur")
    
    # 对敏感区域进行马赛克处理
    for box in sensitive_boxes:
        print(f"Applying mosaic to box: {box}")
        image = apply_mosaic(image, box)
    
    # 保存处理后的图片
    try:
        success = cv2.imwrite(output_path, image)
        if success:
            print(f"已处理并保存图片: {output_path}")
        else:
            print(f"保存图片失败: {output_path}")
        return success
    except Exception as e:
        print(f"保存图片时发生错误: {e}")
        return False

def main(image_url, ocr_data):
    """
    主函数
    
    参数:
        image_url: 图片URL地址
        ocr_data: OCR识别结果
    """
    # 设置输出目录
    output_dir = Path('/Users/<USER>/Desktop/qnideawork/urs_script/refund/script/output')
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件名
    output_filename = f"processed_2.jpg"
    output_path = output_dir / output_filename
    
    # 处理图片
    success = process_image(image_url, ocr_data, output_path)
    
    if not success:
        print("处理图片失败")
        exit(1)
    
    return {"status": "success", "output_path": str(output_path)}

if __name__ == "__main__":
    # 测试数据
    test_url = "https://fuwu.qunar.com/orderview/upload/queryFile/VYVYGHgN1yA828T4gQ8qiInT8VmRUglkBwQ39m7__ZDV_J_3c1f7cZdmaWIgbA5U5pqG_t4y6BPOo9pM-_iEqkf-Xff3uzBq8MiCnevDO_d5m4rCJQI1UCHDXGSJF0-Y7BsyAUN2ILgdWlr8Bm9mjZFDeoMe1lgqoYKQBthsqMb9DMMBkiUc9ibYiZvBr6IByh4pjasuRaYuq7A1mCkTTHp4vJOE0vNiu4MaLCFNvQE.png"
    test_ocr_data = {
    "Angle": -0.3017705,
    "RequestId": "b1f2c174-87ca-46f1-b152-55258d98b746",
    "StructuralList": [
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "项目名称"
                            },
                            "Value": {
                                "AutoContent": "检查费",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 58,
                                        "Y": 167
                                    },
                                    "LeftTop": {
                                        "X": 58,
                                        "Y": 154
                                    },
                                    "RightBottom": {
                                        "X": 97,
                                        "Y": 166
                                    },
                                    "RightTop": {
                                        "X": 97,
                                        "Y": 153
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "项目名称"
                            },
                            "Value": {
                                "AutoContent": "化验费",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 459,
                                        "Y": 166
                                    },
                                    "LeftTop": {
                                        "X": 459,
                                        "Y": 153
                                    },
                                    "RightBottom": {
                                        "X": 498,
                                        "Y": 166
                                    },
                                    "RightTop": {
                                        "X": 498,
                                        "Y": 153
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "金额(元)"
                            },
                            "Value": {
                                "AutoContent": "34.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 366,
                                        "Y": 165
                                    },
                                    "LeftTop": {
                                        "X": 366,
                                        "Y": 155
                                    },
                                    "RightBottom": {
                                        "X": 399,
                                        "Y": 165
                                    },
                                    "RightTop": {
                                        "X": 399,
                                        "Y": 155
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "金额(元)"
                            },
                            "Value": {
                                "AutoContent": "223.50",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 758,
                                        "Y": 166
                                    },
                                    "LeftTop": {
                                        "X": 758,
                                        "Y": 155
                                    },
                                    "RightBottom": {
                                        "X": 797,
                                        "Y": 166
                                    },
                                    "RightTop": {
                                        "X": 797,
                                        "Y": 155
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "项目名称"
                            },
                            "Value": {
                                "AutoContent": "治疗费",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 58,
                                        "Y": 184
                                    },
                                    "LeftTop": {
                                        "X": 58,
                                        "Y": 172
                                    },
                                    "RightBottom": {
                                        "X": 97,
                                        "Y": 183
                                    },
                                    "RightTop": {
                                        "X": 97,
                                        "Y": 171
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "项目名称"
                            },
                            "Value": {
                                "AutoContent": "卫生材料费",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 459,
                                        "Y": 184
                                    },
                                    "LeftTop": {
                                        "X": 459,
                                        "Y": 171
                                    },
                                    "RightBottom": {
                                        "X": 523,
                                        "Y": 184
                                    },
                                    "RightTop": {
                                        "X": 523,
                                        "Y": 171
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "金额(元)"
                            },
                            "Value": {
                                "AutoContent": "6.50",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 372,
                                        "Y": 182
                                    },
                                    "LeftTop": {
                                        "X": 372,
                                        "Y": 173
                                    },
                                    "RightBottom": {
                                        "X": 399,
                                        "Y": 182
                                    },
                                    "RightTop": {
                                        "X": 399,
                                        "Y": 173
                                    }
                                }
                            }
                        },
                        {
                            "Key": {
                                "AutoName": "金额(元)"
                            },
                            "Value": {
                                "AutoContent": "2.12",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 771,
                                        "Y": 183
                                    },
                                    "LeftTop": {
                                        "X": 771,
                                        "Y": 173
                                    },
                                    "RightBottom": {
                                        "X": 797,
                                        "Y": 183
                                    },
                                    "RightTop": {
                                        "X": 797,
                                        "Y": 173
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "标题"
                            },
                            "Value": {
                                "AutoContent": "四川省医疗门诊收费票据(电子)",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 264,
                                        "Y": 48
                                    },
                                    "LeftTop": {
                                        "X": 264,
                                        "Y": 24
                                    },
                                    "RightBottom": {
                                        "X": 631,
                                        "Y": 46
                                    },
                                    "RightTop": {
                                        "X": 631,
                                        "Y": 22
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "票据代码"
                            },
                            "Value": {
                                "AutoContent": "51060125",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 113,
                                        "Y": 92
                                    },
                                    "LeftTop": {
                                        "X": 113,
                                        "Y": 81
                                    },
                                    "RightBottom": {
                                        "X": 165,
                                        "Y": 92
                                    },
                                    "RightTop": {
                                        "X": 165,
                                        "Y": 81
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "票据号码"
                            },
                            "Value": {
                                "AutoContent": "0412877607",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 647,
                                        "Y": 93
                                    },
                                    "LeftTop": {
                                        "X": 647,
                                        "Y": 82
                                    },
                                    "RightBottom": {
                                        "X": 711,
                                        "Y": 92
                                    },
                                    "RightTop": {
                                        "X": 711,
                                        "Y": 81
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "交款人统一社会信用代码"
                            },
                            "Value": {
                                "AutoContent": "510524*********1067",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 204,
                                        "Y": 109
                                    },
                                    "LeftTop": {
                                        "X": 204,
                                        "Y": 97
                                    },
                                    "RightBottom": {
                                        "X": 320,
                                        "Y": 108
                                    },
                                    "RightTop": {
                                        "X": 320,
                                        "Y": 96
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "校验码"
                            },
                            "Value": {
                                "AutoContent": "9b5e62",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 647,
                                        "Y": 109
                                    },
                                    "LeftTop": {
                                        "X": 647,
                                        "Y": 98
                                    },
                                    "RightBottom": {
                                        "X": 686,
                                        "Y": 108
                                    },
                                    "RightTop": {
                                        "X": 686,
                                        "Y": 97
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "交款人"
                            },
                            "Value": {
                                "AutoContent": "陈思羽",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 101,
                                        "Y": 123
                                    },
                                    "LeftTop": {
                                        "X": 101,
                                        "Y": 111
                                    },
                                    "RightBottom": {
                                        "X": 141,
                                        "Y": 123
                                    },
                                    "RightTop": {
                                        "X": 141,
                                        "Y": 111
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "开票日期"
                            },
                            "Value": {
                                "AutoContent": "2025年03月29日",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 645,
                                        "Y": 125
                                    },
                                    "LeftTop": {
                                        "X": 645,
                                        "Y": 114
                                    },
                                    "RightBottom": {
                                        "X": 707,
                                        "Y": 124
                                    },
                                    "RightTop": {
                                        "X": 707,
                                        "Y": 113
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "金额合计(大写)"
                            },
                            "Value": {
                                "AutoContent": "贰佰陆拾陆元壹角贰分",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 160,
                                        "Y": 331
                                    },
                                    "LeftTop": {
                                        "X": 160,
                                        "Y": 317
                                    },
                                    "RightBottom": {
                                        "X": 289,
                                        "Y": 330
                                    },
                                    "RightTop": {
                                        "X": 289,
                                        "Y": 316
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "金额合计(小写)"
                            },
                            "Value": {
                                "AutoContent": "266.12",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 547,
                                        "Y": 331
                                    },
                                    "LeftTop": {
                                        "X": 547,
                                        "Y": 318
                                    },
                                    "RightBottom": {
                                        "X": 587,
                                        "Y": 331
                                    },
                                    "RightTop": {
                                        "X": 587,
                                        "Y": 318
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息业务流水号"
                            },
                            "Value": {
                                "AutoContent": "GCGCC498549130P20250118221230527",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 156,
                                        "Y": 364
                                    },
                                    "LeftTop": {
                                        "X": 156,
                                        "Y": 339
                                    },
                                    "RightBottom": {
                                        "X": 286,
                                        "Y": 363
                                    },
                                    "RightTop": {
                                        "X": 286,
                                        "Y": 338
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息门诊号"
                            },
                            "Value": {
                                "AutoContent": "20759134",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 335,
                                        "Y": 351
                                    },
                                    "LeftTop": {
                                        "X": 335,
                                        "Y": 340
                                    },
                                    "RightBottom": {
                                        "X": 387,
                                        "Y": 351
                                    },
                                    "RightTop": {
                                        "X": 387,
                                        "Y": 340
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息就诊日期"
                            },
                            "Value": {
                                "AutoContent": "2025年03月29日",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 734,
                                        "Y": 350
                                    },
                                    "LeftTop": {
                                        "X": 734,
                                        "Y": 340
                                    },
                                    "RightBottom": {
                                        "X": 782,
                                        "Y": 350
                                    },
                                    "RightTop": {
                                        "X": 782,
                                        "Y": 340
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息医疗机构类型"
                            },
                            "Value": {
                                "AutoContent": "综合医院",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 170,
                                        "Y": 379
                                    },
                                    "LeftTop": {
                                        "X": 170,
                                        "Y": 366
                                    },
                                    "RightBottom": {
                                        "X": 222,
                                        "Y": 379
                                    },
                                    "RightTop": {
                                        "X": 222,
                                        "Y": 366
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息性别"
                            },
                            "Value": {
                                "AutoContent": "女",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 708,
                                        "Y": 378
                                    },
                                    "LeftTop": {
                                        "X": 708,
                                        "Y": 366
                                    },
                                    "RightBottom": {
                                        "X": 722,
                                        "Y": 378
                                    },
                                    "RightTop": {
                                        "X": 722,
                                        "Y": 366
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息医保统筹基金支付"
                            },
                            "Value": {
                                "AutoContent": "0.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 196,
                                        "Y": 405
                                    },
                                    "LeftTop": {
                                        "X": 196,
                                        "Y": 394
                                    },
                                    "RightBottom": {
                                        "X": 223,
                                        "Y": 405
                                    },
                                    "RightTop": {
                                        "X": 223,
                                        "Y": 394
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息其他支付"
                            },
                            "Value": {
                                "AutoContent": "0.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 346,
                                        "Y": 405
                                    },
                                    "LeftTop": {
                                        "X": 346,
                                        "Y": 394
                                    },
                                    "RightBottom": {
                                        "X": 373,
                                        "Y": 405
                                    },
                                    "RightTop": {
                                        "X": 373,
                                        "Y": 394
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息个人账户支付"
                            },
                            "Value": {
                                "AutoContent": "0.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 556,
                                        "Y": 405
                                    },
                                    "LeftTop": {
                                        "X": 556,
                                        "Y": 394
                                    },
                                    "RightBottom": {
                                        "X": 583,
                                        "Y": 405
                                    },
                                    "RightTop": {
                                        "X": 583,
                                        "Y": 394
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息个人现金支付"
                            },
                            "Value": {
                                "AutoContent": "266.12",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 760,
                                        "Y": 405
                                    },
                                    "LeftTop": {
                                        "X": 760,
                                        "Y": 394
                                    },
                                    "RightBottom": {
                                        "X": 799,
                                        "Y": 405
                                    },
                                    "RightTop": {
                                        "X": 799,
                                        "Y": 394
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息个人自付"
                            },
                            "Value": {
                                "AutoContent": "0.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 145,
                                        "Y": 429
                                    },
                                    "LeftTop": {
                                        "X": 145,
                                        "Y": 418
                                    },
                                    "RightBottom": {
                                        "X": 172,
                                        "Y": 429
                                    },
                                    "RightTop": {
                                        "X": 172,
                                        "Y": 418
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息个人自费"
                            },
                            "Value": {
                                "AutoContent": "0.00",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 345,
                                        "Y": 429
                                    },
                                    "LeftTop": {
                                        "X": 345,
                                        "Y": 418
                                    },
                                    "RightBottom": {
                                        "X": 372,
                                        "Y": 429
                                    },
                                    "RightTop": {
                                        "X": 372,
                                        "Y": 418
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "其他信息登记号"
                            },
                            "Value": {
                                "AutoContent": "0007173790",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 268,
                                        "Y": 444
                                    },
                                    "LeftTop": {
                                        "X": 268,
                                        "Y": 433
                                    },
                                    "RightBottom": {
                                        "X": 334,
                                        "Y": 444
                                    },
                                    "RightTop": {
                                        "X": 334,
                                        "Y": 433
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "收款单位(章)"
                            },
                            "Value": {
                                "AutoContent": "成都市第二人民医院",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 159,
                                        "Y": 479
                                    },
                                    "LeftTop": {
                                        "X": 159,
                                        "Y": 465
                                    },
                                    "RightBottom": {
                                        "X": 273,
                                        "Y": 478
                                    },
                                    "RightTop": {
                                        "X": 273,
                                        "Y": 464
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "复核人"
                            },
                            "Value": {
                                "AutoContent": "微信002",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 531,
                                        "Y": 479
                                    },
                                    "LeftTop": {
                                        "X": 531,
                                        "Y": 466
                                    },
                                    "RightBottom": {
                                        "X": 577,
                                        "Y": 479
                                    },
                                    "RightTop": {
                                        "X": 577,
                                        "Y": 466
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        },
        {
            "Groups": [
                {
                    "Lines": [
                        {
                            "Key": {
                                "AutoName": "收款人"
                            },
                            "Value": {
                                "AutoContent": "微信002",
                                "Coord": {
                                    "LeftBottom": {
                                        "X": 723,
                                        "Y": 479
                                    },
                                    "LeftTop": {
                                        "X": 723,
                                        "Y": 466
                                    },
                                    "RightBottom": {
                                        "X": 769,
                                        "Y": 479
                                    },
                                    "RightTop": {
                                        "X": 769,
                                        "Y": 466
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        }
    ],
    "WordList": [

    ]
}
    
    result = main(test_url, test_ocr_data)
    print(json.dumps(result, ensure_ascii=False, indent=2))

