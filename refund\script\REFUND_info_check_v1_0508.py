import re
from urllib.parse import unquote_to_bytes
import json
import datetime
from typing import Dict, List, Any
import hashlib
from enum import Enum

def get_carrier_requirements(carrier: str) -> Dict[str, Any]:
    """
    获取航司的验证要求
    
    Args:
        carrier: 航空公司代码
        
    Returns:
        Dict[str, Any]: 航司的验证要求
    """
    # 定义所有航司的验证要求
    CARRIER_REQUIREMENTS = {
        # 东航和上航要求
        "3U": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "50",  
            }
        },
        "8L": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "50",     
            }
        },
        "PN": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "50",     
            }
        },
        "9D": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",    
            }
        },
        "DR": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
         "JD": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
         "Y8": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
         "A6": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "AQ": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "BK": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "200",     
            }
        },
        "HU": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "CN": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "FU": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "GS": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "GX": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "UQ": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "DZ": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "EU": {
            "diagnosis_certificate": {
                "signatureOrStamp": "stamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "G5": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "200",     
            }
        },
        "GT": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "200",     
            }
        },
         "GY": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "200",     
            }
        },
         "HO": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "二级",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "JR": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "KN": {
            "diagnosis_certificate": {
                "signatureOrStamp": "stamp",  # 需要医生签字或医院盖章
                "hospital_grade": "二级甲等",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "KY": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "SC": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "ZH": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "LT": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureOrStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": False,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "MF": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "NS": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "QW": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "0",     
            }
        },
        "RY": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },
        "TV": {
            "diagnosis_certificate": {
                "signatureOrStamp": "signatureAndStamp",  # 需要医生签字或医院盖章
                "hospital_grade": "",  # 需要医院等级
                "county_level_or_above_hospital": True,   # 需要县级或以上医院
            },
            "invoice": {
                "amount": "100",     
            }
        },

    }
    
    return CARRIER_REQUIREMENTS.get(carrier)

def is_id_card_valid(valid_period: str) -> bool:
    """
    判断身份证是否在有效期内
    
    Args:
        valid_period: 有效期限字符串，格式如 "2022.04.07-2032.04.07" 或 "长期" 或"2023年12月13日 - 2028年06月30日"
    
    Returns:
        bool: 是否在有效期内
    """
    if not valid_period:
        return False
        
    # 如果包含"长期"，则认为有效
    if "长期" in valid_period:
        return True
        
    # 尝试解析有效期范围
    try:
        # 分割开始和结束日期
        if "-" in valid_period:
            start_date_str, end_date_str = valid_period.split("-", 1)
            
            # 解析结束日期
            if "." in end_date_str:
                end_date = datetime.datetime.strptime(end_date_str.strip(), "%Y.%m.%d")
                # 判断当前日期是否在有效期内
                return datetime.datetime.now() <= end_date
            elif "年" in end_date_str:
                end_date = datetime.datetime.strptime(end_date_str.strip(), "%Y年%m月%d日")
                # 判断当前日期是否在有效期内
                return datetime.datetime.now() <= end_date
    except Exception:
        # 解析错误，认为无效
        pass
        
    return False

def is_name_in_passenger_list(name: str, passenger_names: List[str]) -> bool:
    """
    检查姓名是否在乘客列表中
    
    Args:
        name: 姓名
        passenger_names: 乘客列表
    
    Returns:
        bool: 姓名是否在乘客列表中
    """
    if not name or not passenger_names:
        return False
        
    # 如果清洗后的名字为空，返回False
    if not name:
        return False
    
    # 检查清洗后的姓名是否是任何乘客姓名的子串
    for passenger in passenger_names:
        if name in passenger:
            return True
            
    return False

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def get_earliest_flight_time(airline_date: List[str]) -> datetime.datetime:
    """
    获取最早的航班时间
    
    Args:
        airline_date: 航班时间列表，格式如 ["2025-04-15 16:35-18:30", "2025-04-16 12:20-16:55"]
    
    Returns:
        datetime.datetime: 最早的航班时间
    """
    if not airline_date:
        return None
        
    earliest_time = None
    for flight_time in airline_date:
        try:
            # 先找到日期和时间的部分（格式为：yyyy-MM-dd HH:mm）
            datetime_part = flight_time.split(" ")[0] + " " + flight_time.split(" ")[1].split("-")[0]
            # 解析时间
            flight_datetime = datetime.datetime.strptime(datetime_part, "%Y-%m-%d %H:%M")
            if earliest_time is None or flight_datetime < earliest_time:
                earliest_time = flight_datetime
        except Exception:
            continue
            
    return earliest_time

def is_date_within_valid_range(date_str: str, earliest_flight_time: datetime.datetime) -> bool:
    """
    检查日期是否在有效范围内（航班最早时间前3个月内，包含当天）
    
    Args:
        date_str: 日期字符串 格式：2025-04-14或2025/04/14 或2025.04.14或2025年04月14日 或2025年4月14日 或2025.4.14 或2025-4-14 或2025/4/14
        earliest_flight_time: 最早的航班时间 格式：2025-04-15 16:35:00
    
    Returns:
        bool: 是否在有效范围内
    """
    if not date_str or not earliest_flight_time:
        return False
        
    try:
        # 预处理日期字符串 - 规范化分隔符和格式
        processed_str = date_str.strip()
        
        # 尝试解析多种格式的日期字符串
        date_obj = None
        
        # 标准化日期格式
        if "年" in processed_str:
            # 处理中文日期格式
            processed_str = processed_str.replace("年", "-").replace("月", "-").replace("日", "")
        else:
            # 统一替换分隔符为"-"
            processed_str = processed_str.replace("/", "-").replace(".", "-")
        
        # 处理单数月日 (如 2025-4-5 转为 2025-04-05)
        parts = processed_str.split("-")
        if len(parts) == 3:
            year, month, day = parts
            if len(month) == 1:
                month = "0" + month
            if len(day) == 1:
                day = "0" + day
            processed_str = f"{year}-{month}-{day}"
        
        # 定义常用日期格式
        date_formats = [
            "%Y-%m-%d",           # 2025-04-14
            "%Y-%m-%d %H:%M:%S",  # 2025-04-14 00:00:00
            "%Y-%m-%d %H:%M",     # 2025-04-14 00:00
            "%Y%m%d",             # 20250414
        ]
        
        # 尝试不同的格式解析
        for fmt in date_formats:
            try:
                if (":" not in processed_str and ":" not in fmt) or \
                   (":" in processed_str and ":" in fmt and fmt.count(":") == processed_str.count(":")):
                    date_obj = datetime.datetime.strptime(processed_str, fmt)
                    # 如果没有时间部分，设置为当天的00:00
                    if ":" not in fmt:
                        date_obj = date_obj.replace(hour=0, minute=0)
                    break
            except ValueError:
                continue
            
        if not date_obj:
            return False
            
        # 计算3个月前的时间
        three_months_ago = earliest_flight_time - datetime.timedelta(days=90)
        
        # 检查日期是否在范围内 (包含当天)
        return three_months_ago < date_obj <= earliest_flight_time
    except Exception:
        return False

def check_date_validity(date_str: str, earliest_flight_time: datetime.datetime, field_name: str) -> tuple:
    """
    检查日期是否在有效范围内
    
    Args:
        date_str: 日期字符串
        earliest_flight_time: 最早的航班时间
        field_name: 字段名称，用于错误提示
        
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not date_str:
        return True, ""
        
    if not is_date_within_valid_range(date_str, earliest_flight_time):
        return False, f"{field_name}{date_str}不在航班起{earliest_flight_time}飞前3个月内"
    return True, ""

def check_required_fields(img_content: dict, required_fields: List[str]) -> tuple:
    """
    检查必需字段是否存在且非空
    
    Args:
        img_content: 图片内容字典
        required_fields: 必需字段列表
        
    Returns:
        tuple: (是否通过, 错误信息)
    """
    missing_fields = []
    for field in required_fields:
        if field not in img_content or not img_content[field]:
            missing_fields.append(field)
            
    if missing_fields:
        return False, f"缺少必需字段: {', '.join(missing_fields)}"
    return True, ""

def validate_identity_document(material: dict, img_content: dict, passenger_names: List[str],system_doc_type: str) -> None:
    """
    验证身份证明类文档
    
    Args:
        material: 材料信息
        img_content: 图片内容
        passenger_names: 乘客姓名列表
    """
    if system_doc_type == "诊断证明" or system_doc_type == "发票证明":
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = f"非{system_doc_type}文件"
        return
    
    cert_type = img_content.get("证件类型", "")
    
    if cert_type == "身份证":
        validate_id_card(material, img_content, passenger_names)
    elif cert_type == "护照":
        validate_passport(material, img_content, passenger_names)
    elif cert_type == "出生证明":
        validate_birth_certificate(material, img_content, passenger_names)
    elif cert_type == "户口本":
        validate_household_register(material, img_content, passenger_names)
    elif cert_type == "结婚证":
        validate_marriage_certificate(material, img_content, passenger_names)
    elif "公安、政府机构证明" in cert_type:
        validate_government_certificate(material, img_content, passenger_names)
    elif cert_type == "死亡证明":
        validate_death_certificate(material, img_content, passenger_names)
    else:
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "身份证明类型未知"

def validate_id_card(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证身份证"""
    has_name = "姓名" in img_content and img_content.get("姓名")
    has_id_number = "身份证号" in img_content and img_content.get("身份证号")
    has_validity_period = "有效期限" in img_content and img_content.get("有效期限")
    
    if has_name and has_id_number:
        material["checkResult"] = "通过"
        if has_validity_period:
            valid_period = img_content.get("有效期限", "")
            if not is_id_card_valid(valid_period):
                material["checkResult"] = "不通过"
                material["checkBlockReason"] = f"身份证已过期，有效期限：{valid_period}"
    elif not has_name and not has_id_number:
        if not has_validity_period:
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = "身份证反面缺少有效期限"
        else:
            valid_period = img_content.get("有效期限", "")
            if is_id_card_valid(valid_period):
                material["checkResult"] = "通过"
            else:
                material["checkResult"] = "不通过"
                material["checkBlockReason"] = f"身份证已过期，有效期限：{valid_period}"
    else:
        material["checkResult"] = "不通过"
        if has_name and not has_id_number:
            material["checkBlockReason"] = "身份证识别内容中有姓名但缺少身份证号"
        else:
            material["checkBlockReason"] = "身份证识别内容中有身份证号但缺少姓名"

def validate_passport(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证护照"""
    has_name = ("姓名" in img_content and img_content.get("姓名")) or ("姓名/Name" in img_content and img_content.get("姓名/Name"))
    has_passport_number = "护照号码" in img_content and img_content.get("护照号码")
    has_validity_period = ("有效期" in img_content and img_content.get("有效期")) or \
                         ("有效期至" in img_content and img_content.get("有效期至"))
    
    if not (has_name and has_passport_number and has_validity_period):
        material["checkResult"] = "不通过"
        missing_fields = []
        if not has_name:
            missing_fields.append("姓名")
        if not has_passport_number:
            missing_fields.append("护照号码")
        if not has_validity_period:
            missing_fields.append("有效期/有效期限")
        material["checkBlockReason"] = f"护照识别内容不全，缺少: {', '.join(missing_fields)}"
    else:
        valid_period = img_content.get("有效期", "") or img_content.get("有效期至", "")
        if is_id_card_valid(valid_period):
            material["checkResult"] = "通过"
        else:
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = f"护照已过期，有效期限：{valid_period}"

def validate_birth_certificate(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证出生证明"""
    has_required_names = False
    if ("新生儿姓名" in img_content and img_content.get("新生儿姓名") and
        "母亲姓名" in img_content and img_content.get("母亲姓名") and
        "父亲姓名" in img_content and img_content.get("父亲姓名")):
        has_required_names = True
    
    if not has_required_names:
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "出生证明中新生儿姓名/母亲姓名/父亲姓名不完整"
    
    has_stamp = img_content.get("是否有卫生部/医学证明公章", "") == "是" 
    if not has_stamp:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        material["checkBlockReason"] += "出生证明中没有卫生部/医学证明公章"
    
    if has_required_names and has_stamp:
        material["checkResult"] = "通过"

def validate_household_register(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证户口本"""
    has_required_fields = ("姓名" in img_content and img_content.get("姓名") and
            "身份证号" in img_content and img_content.get("身份证号")) 
    has_householder_name = ("户主姓名" in img_content and img_content.get("户主姓名"))
    
    if not has_required_fields and not has_householder_name:
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "户口本中姓名、身份证号不完整或户主姓名缺失"
    
    has_stamp = img_content.get("是否有公安局/派出所公章", "") == "是" 
    not_moved = img_content.get("- 是否已迁出（红章）", "") == "否"
    
    if not has_stamp:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        if not has_stamp:
            material["checkBlockReason"] += "户口本中没有公安局/派出所公章"
    
    if has_required_fields and has_stamp:
        material["checkResult"] = "通过"

def validate_marriage_certificate(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证结婚证"""
    has_names = ("姓名（男）" in img_content and img_content.get("姓名（男）") and
            "姓名（女）" in img_content and img_content.get("姓名（女）"))
    
    if not has_names:
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "结婚证中姓名（男）或姓名（女）不完整"
    
    has_ids = ("身份证号（男）" in img_content and img_content.get("身份证号（男）") and
            "身份证号（女）" in img_content and img_content.get("身份证号（女）"))
    
    if not has_ids:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        material["checkBlockReason"] += "结婚证中身份证号（男）或身份证号（女）不完整"
    
    if has_names and has_ids:
        material["checkResult"] = "通过"

def validate_government_certificate(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证公安、政府机构证明"""
    has_name = "姓名" in img_content and img_content.get("姓名")
    has_id = (("身份证号" in img_content and img_content.get("身份证号")) or 
             ("证件号" in img_content and img_content.get("证件号")) or 
             ("身份证" in img_content and img_content.get("身份证")))
    
    if not (has_name and has_id):
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "公安/政府机构证明中姓名或身份证号缺失"
    
    has_date = "开具日期" in img_content and img_content.get("开具日期")
    if not has_date:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        material["checkBlockReason"] += "公安/政府机构证明中开具日期不存在"
    
    has_stamp = "是否有机构盖章" in img_content and img_content.get("是否有机构盖章") == "是"
    if not has_stamp:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        material["checkBlockReason"] += "公安/政府机构证明中没有机构盖章"
    
    if has_name and has_id and has_date and has_stamp:
        material["checkResult"] = "通过"

def validate_death_certificate(material: dict, img_content: dict, passenger_names: List[str]) -> None:
    """验证死亡证明"""
    has_name = "姓名" in img_content and img_content.get("姓名")
    has_id = (("证件号" in img_content and img_content.get("证件号")) or 
             ("身份证号" in img_content and img_content.get("身份证号")) or 
             ("身份证" in img_content and img_content.get("身份证")))
    
    if not (has_name and has_id):
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "死亡证明中姓名或证件号缺失"
    
    has_stamp = "是否有派出所盖章" in img_content and img_content.get("是否有派出所盖章") == "是"
    if not has_stamp:
        material["checkResult"] = "不通过"
        if material["checkBlockReason"]:
            material["checkBlockReason"] += "；"
        material["checkBlockReason"] += "死亡证明中没有派出所盖章"
    
    if has_name and has_id and has_stamp:
        material["checkResult"] = "通过"

def validate_invoice(material: dict, img_content: dict, passenger_names: List[str], earliest_flight_time: datetime.datetime, grouped_materials: dict, system_doc_type: str, carrier: str) -> None:
    """验证发票证明"""
    if system_doc_type == "诊断证明" or system_doc_type == "身份证明":
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = f"非{system_doc_type}文件"
        return
    
    if material.get("invoice_validation_result", "") == "成功":
        payer = img_content.get("交款人_md5", "") or material.get("invoice_validation_content", {}).get("data", {}).get("jkr_md5", "")
        payer_name = img_content.get("交款人", "") or material.get("invoice_validation_content", {}).get("data", {}).get("jkr", "")
        if payer:
            if is_name_in_passenger_list(payer, passenger_names):
                material["checkResult"] = "通过"
            else:
                material["checkResult"] = "不通过"
                material["checkBlockReason"] = f"交款人{payer_name}不在乘客列表或有效身份证明文件中"
        else:
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = "发票中交款人为空"
        
        if material["checkResult"] == "通过" and material["aiResult"]:
            invoice_date = material["aiResult"].get("issuance_time", "") if material["aiResult"].get("issuance_time", "") else img_content.get("开票日期", "")
            visit_date = material["aiResult"].get("date_treatment", "")
            inpatient_time = material["aiResult"].get("admission_time", "")
            invoice_time_check = material["aiResult"].get("issuance_time_check", "")
            visit_date_check = material["aiResult"].get("date_treatment_check", "")
            inpatient_time_check = material["aiResult"].get("admission_time_check", "")

            if not invoice_date:
                material["checkResult"] = "不通过"
                if material["checkBlockReason"]:
                    material["checkBlockReason"] += "；"
                material["checkBlockReason"] += "发票中开票日期为空"

            # 根据航司要求检查发票金额
            carrier_requirements = get_carrier_requirements(carrier)
            
            if carrier_requirements and "invoice" in carrier_requirements and "amount" in carrier_requirements["invoice"]:
                required_amount = carrier_requirements["invoice"]["amount"]
                if required_amount != "0":  # 如果航司要求的金额不为0
                    invoice_amount_str = img_content.get("金额（小写）", "")
                    try:
                        # 移除金额中的逗号、空格等非数字字符
                        invoice_amount = 0
                        if invoice_amount_str:
                            # 只保留数字和小数点，去除¥、汉字、括号等所有其他字符
                            invoice_amount_str = re.sub(r'[^\d.]', '', invoice_amount_str)
                            invoice_amount = float(invoice_amount_str) if invoice_amount_str else 0
                        required_amount_float = float(required_amount)
                        
                        if invoice_amount < required_amount_float:
                            material["checkResult"] = "不通过"
                            if material["checkBlockReason"]:
                                material["checkBlockReason"] += "；"
                            material["checkBlockReason"] += f"发票金额{invoice_amount}小于航司要求的最低金额{required_amount}"
                    except ValueError:
                        # 金额格式无效时，记录错误
                        material["checkResult"] = "不通过"
                        if material["checkBlockReason"]:
                            material["checkBlockReason"] += "；"
                        material["checkBlockReason"] += "发票金额校验失败"
               
            
            # 检查就诊日期和住院时间
            #if material["checkResult"] == "通过":
            #    visit_date = img_content.get("就诊日期", "")
            #    inpatient_time = img_content.get("住院时间", "")
                
            #    is_valid, reason = check_date_validity(visit_date, earliest_flight_time, "就诊日期")
            #    if not is_valid:
            #        material["checkResult"] = "不通过"
            #        if material["checkBlockReason"]:
            #            material["checkBlockReason"] += "；"
            #        material["checkBlockReason"] += reason
                
            #    is_valid, reason = check_date_validity(inpatient_time, earliest_flight_time, "住院时间")
            #    if not is_valid:
            #        material["checkResult"] = "不通过"
            #        if material["checkBlockReason"]:
            #        material["checkBlockReason"] += "；"
            #    material["checkBlockReason"] += reason

            # 与诊断证明进行字段比对
            #if "诊断证明" in grouped_materials and material["checkResult"] == "通过":
            #    field_pairs = [
            #        ("就诊号", "就诊号"),
            #        ("门诊号", "门诊号"),
            #        ("住院号", "住院号")
            #    ]
                
            #    is_consistent, reason = check_field_consistency(material, grouped_materials["诊断证明"], field_pairs)
            #    if not is_consistent:
            #        material["checkResult"] = "不通过"
            #        if material["checkBlockReason"]:
            #            material["checkBlockReason"] += "；"
            #        material["checkBlockReason"] += reason
    else:
        if material.get("invoice_validation_msg", "") == "发票识别失败,可能为纸质发票，待确认":
            material["checkResult"] = "待确认"
        else:
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = "发票验证失败"

def validate_diagnosis_certificate(material: dict, img_content: dict, passenger_names: List[str], earliest_flight_time: datetime.datetime, grouped_materials: dict, system_doc_type: str, carrier: str) -> None:
    """验证诊断证明"""
    if system_doc_type == "发票证明" or system_doc_type == "身份证明":
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = f"非{system_doc_type}文件"
        return
    
    name = img_content.get("姓名_md5", "")
    name_real = img_content.get("姓名", "")
    if name:
        if is_name_in_passenger_list(name, passenger_names):
            material["checkResult"] = "通过"
        else:
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = f"姓名{name_real}不在乘客列表或有效身份证明文件中"
    else:
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = "诊断证明中姓名为空"
    
    if material["checkResult"] == "通过" and material["aiResult"]:
        visit_date = material["aiResult"].get("date_treatment", "")
        inpatient_time = material["aiResult"].get("admission_time", "")
        invoice_time = material["aiResult"].get("issuance_time", "")
        
        # 检查三个日期是否都为空
        if not visit_date and not inpatient_time:
            material["checkResult"] = "不通过"
            if material["checkBlockReason"]:
                material["checkBlockReason"] += "；"
            material["checkBlockReason"] += "就诊日期为空/入院时间为空"
        else:
            # 记录各日期是否通过校验
            date_validation_passed = False
            
            # 检查就诊日期
            if visit_date:
                is_valid, reason = check_date_validity(visit_date, earliest_flight_time, "就诊日期")
                if is_valid:
                    date_validation_passed = True
                else:
                    # 记录错误原因，但不立即设置为不通过
                    if material["checkBlockReason"]:
                        material["checkBlockReason"] += "；"
                    material["checkBlockReason"] += reason
            
            # 检查入院时间
            if inpatient_time:
                is_valid, reason = check_date_validity(inpatient_time, earliest_flight_time, "住院时间")
                if is_valid:
                    date_validation_passed = True
                else:
                    # 记录错误原因，但不立即设置为不通过
                    if material["checkBlockReason"]:
                        material["checkBlockReason"] += "；"
                    material["checkBlockReason"] += reason
            
            # 如果没有一个日期通过校验，则设置为不通过
            if not date_validation_passed:
                material["checkResult"] = "不通过"
            else:
                # 如果至少有一个日期通过校验，清除可能的错误原因
                material["checkBlockReason"] = ""
                
    # 根据航司要求进行额外校验
    carrier_requirements = get_carrier_requirements(carrier)

    # 添加航司规则校验
    if carrier_requirements and "diagnosis_certificate" in carrier_requirements:
        # 校验签字或盖章要求
        if "signatureOrStamp" in carrier_requirements["diagnosis_certificate"]:
            required_signature_or_stamp = carrier_requirements["diagnosis_certificate"]["signatureOrStamp"]
            has_signature = img_content.get("是否有医生签字", "") == "是"
            has_stamp = img_content.get("是否有盖章", "") == "是"
            
            signature_stamp_valid = False
            if required_signature_or_stamp == "signatureAndStamp" and has_signature and has_stamp:
                signature_stamp_valid = True
            elif required_signature_or_stamp == "signatureOrStamp" and (has_signature or has_stamp):
                signature_stamp_valid = True
            elif required_signature_or_stamp == "signature" and has_signature:
                signature_stamp_valid = True
            elif required_signature_or_stamp == "stamp" and has_stamp:
                signature_stamp_valid = True
                
            if not signature_stamp_valid:
                material["checkResult"] = "不通过"
                if material["checkBlockReason"]:
                    material["checkBlockReason"] += "；"
                
                if required_signature_or_stamp == "signatureAndStamp":
                    material["checkBlockReason"] += "航司要求医生签字和医院盖章"
                elif required_signature_or_stamp == "signatureOrStamp":
                    material["checkBlockReason"] += "航司要求医生签字或医院盖章"
                elif required_signature_or_stamp == "signature":
                    material["checkBlockReason"] += "航司要求医生签字"
                elif required_signature_or_stamp == "stamp":
                    material["checkBlockReason"] += "航司要求医院盖章"
        
        # 校验医院等级要求
        if "hospital_grade" in carrier_requirements["diagnosis_certificate"] and carrier_requirements["diagnosis_certificate"]["hospital_grade"]:
            required_hospital_grade = carrier_requirements["diagnosis_certificate"]["hospital_grade"]
            hospital_grade = material["hospital_grade"]
            
            # 检查医院等级是否符合要求
            if not hospital_grade:
                material["checkResult"] = "不通过"
                if material["checkBlockReason"]:
                    material["checkBlockReason"] += "；"
                material["checkBlockReason"] += f"航司要求{required_hospital_grade}级医院"
            else:
                # 解析医院等级
                grade_levels = {
                    "一级": 1,
                    "二级": 2,
                    "三级": 3
                }
                
                sub_levels = {
                    "甲等": 3,
                    "乙等": 2,
                    "丙等": 1,
                    "": 0  # 无子等级时默认为0
                }
                
                # 解析实际医院等级
                actual_level = 0
                actual_sublevel = 0
                
                for level, value in grade_levels.items():
                    if level in hospital_grade:
                        actual_level = value
                        break
                
                for sublevel, value in sub_levels.items():
                    if sublevel in hospital_grade:
                        actual_sublevel = value
                        break
                
                # 解析要求的医院等级
                required_level = 0
                required_sublevel = 0
                
                for level, value in grade_levels.items():
                    if level in required_hospital_grade:
                        required_level = value
                        break
                
                for sublevel, value in sub_levels.items():
                    if sublevel in required_hospital_grade:
                        required_sublevel = value
                        break
                
                # 比较等级
                # 1. 主等级必须大于等于要求的主等级
                # 2. 如果主等级等于要求的主等级，则子等级必须大于等于要求的子等级
                meets_requirement = False
                
                if actual_level > required_level:
                    meets_requirement = True
                elif actual_level == required_level and actual_sublevel >= required_sublevel:
                    meets_requirement = True
                
                if not meets_requirement:
                    material["checkResult"] = "不通过"
                    if material["checkBlockReason"]:
                        material["checkBlockReason"] += "；"
                    material["checkBlockReason"] += f"航司要求{required_hospital_grade}级医院"
        
        # 校验是否需要县级或以上医院
        if "county_level_or_above_hospital" in carrier_requirements["diagnosis_certificate"]:
            requires_county_level = carrier_requirements["diagnosis_certificate"]["county_level_or_above_hospital"]
            county_level_or_above_hospital= material["county_level_or_above_hospital"] == "是"
            if requires_county_level and not county_level_or_above_hospital:
                material["checkResult"] = "不通过"
                if material["checkBlockReason"]:
                    material["checkBlockReason"] += "；"
                    material["checkBlockReason"] += "航司要求县级或以上医院"

    # 与发票证明进行字段比对
    # if "发票证明" in grouped_materials and material["checkResult"] == "通过":
    #     field_pairs = [
    #         ("就诊号", "就诊号"),
    #         ("门诊号", "门诊号"),
    #         ("住院号", "住院号")
    #     ]
    #     
    #     is_consistent, reason = check_field_consistency(material, grouped_materials["发票证明"], field_pairs)
    #     if not is_consistent:
    #         material["checkResult"] = "不通过"
    #         if material["checkBlockReason"]:
    #             material["checkBlockReason"] += "；"
    #         material["checkBlockReason"] += reason

def validate_materials(filtered_material_list: List[Dict], passenger_names: List[str], airlineDate: List[str], carrier: str) -> tuple:
    """
    验证材料是否符合要求，按分组优先级校验：身份证明>发票证明>诊断证明
    
    Args:
        filtered_material_list: 材料列表
        passenger_names: 乘客姓名列表
        airlineDate: 航班日期列表
        carrier: 航空公司
    
    Returns:
        tuple: (filtered_material_list, relationUserNames)
    """
    # 首先检查图片内容是否为空以及材料类型是否为空
    for material in filtered_material_list:
        # 初始化校验结果
        material["checkResult"] = "不通过"
        material["checkBlockReason"] = ""
        
        # 检查图片内容是否为空
        img_content = material.get("imgContent", {})
        if img_content == {} or img_content == None or img_content == "{}" or img_content == "null":
            material["checkBlockReason"] = "源材料图片内容为空"
            continue
        
        # 检查材料类型是否为空
        doc_type = material.get("docType", "")
        if not doc_type:
            material["checkBlockReason"] = "材料类型为空"
            continue
    
    # 按docType分组
    grouped_materials = {}
    for material in filtered_material_list:
        doc_type = material.get("docType", "")
        if doc_type not in grouped_materials:
            grouped_materials[doc_type] = []
        grouped_materials[doc_type].append(material)
    
    # 获取最早的航班时间
    earliest_flight_time = get_earliest_flight_time(airlineDate)
    
    # 存储亲属关系人员姓名
    relationUserNames = []
    
    # 按优先级验证：身份证明 > 发票证明 > 诊断证明
    validation_order = ["身份证明", "发票证明", "诊断证明"]
    for doc_type in validation_order:
        if doc_type not in grouped_materials:
            continue
            
        for material in grouped_materials[doc_type]:
            # 初始化校验结果
            material["checkResult"] = "不通过"
            material["checkBlockReason"] = ""

            try:
                materialStringType = material.get("materialStringType", "")
                # 定义关键词映射表 
                doc_type_mapping = {
                    0: ["身份", "证件"],
                    1: ["发票"],
                    2: ["诊断", "医", "病", "检查", "处方", "小结"]
                }
                            
                material_type = materialStringType.lower()
                            
                # 默认为空
                system_doc_type = ""
                            
                # 按照优先级 0, 1, 2 进行匹配，一旦匹配到就不再继续匹配
                # 先尝试匹配身份证明(0)
                for keyword in doc_type_mapping[0]:
                    if keyword in material_type:
                        system_doc_type = "身份证明"
                        break
                            
                # 如果没有匹配到0，尝试匹配发票证明(1)
                if system_doc_type == "":
                    for keyword in doc_type_mapping[1]:
                        if keyword in material_type:
                            system_doc_type = "发票证明"
                            break
                            
                # 如果没有匹配到0和1，尝试匹配诊断证明(2)
                if system_doc_type == "":
                    for keyword in doc_type_mapping[2]:
                        if keyword in material_type:
                            system_doc_type = "诊断证明"
                            break
                        
                img_content = material.get("imgContent", {})
                
                if img_content == {} or img_content == None or img_content == "{}" or img_content == "null":
                    material["checkResult"] = "不通过"
                    material["checkBlockReason"] = "源材料图片内容为空"
                    continue
                
                if doc_type == "身份证明":
                    # 验证身份证明，并从结婚证、出生证明中收集姓名
                    if "证件类型" in img_content:
                        cert_type = img_content.get("证件类型", "")
                        # 如果是结婚证，收集男女双方姓名
                        if cert_type == "结婚证":
                            male_name = img_content.get("姓名（男）_md5", "")
                            male_name_real = img_content.get("姓名（男）", "")
                            female_name = img_content.get("姓名（女）_md5", "")
                            female_name_real = img_content.get("姓名（女）", "")
                            if male_name and male_name not in relationUserNames and male_name not in passenger_names:
                                relationUserNames.append(male_name) 
                                relationUserNames.append(male_name_real)
                            if female_name and female_name not in relationUserNames and female_name not in passenger_names:
                                relationUserNames.append(female_name)
                                relationUserNames.append(female_name_real)
                        # 如果是出生证明，收集父母姓名
                        elif cert_type == "出生证明":
                            father_name = img_content.get("父亲姓名_md5", "")   
                            father_name_real = img_content.get("父亲姓名", "")
                            mother_name = img_content.get("母亲姓名_md5", "")
                            mother_name_real = img_content.get("母亲姓名", "")
                            newborn_name = img_content.get("新生儿姓名_md5", "")
                            newborn_name_real = img_content.get("新生儿姓名", "")
                            if father_name and father_name not in relationUserNames and father_name not in passenger_names:
                                relationUserNames.append(father_name)
                                relationUserNames.append(father_name_real)
                            if mother_name and mother_name not in relationUserNames and mother_name not in passenger_names:
                                relationUserNames.append(mother_name)
                                relationUserNames.append(mother_name_real)
                            if newborn_name and newborn_name not in relationUserNames and newborn_name not in passenger_names:
                                relationUserNames.append(newborn_name)
                                relationUserNames.append(newborn_name_real)
                    try:
                        validate_identity_document(material, img_content, passenger_names, system_doc_type)
                    except Exception as e:
                        material["checkResult"] = "不通过"
                        material["checkBlockReason"] = f"验证身份证明时发生错误: {str(e)}"
                elif doc_type == "发票证明":
                    # 在验证发票时，将乘客名单扩展为包含亲属姓名
                    extended_names = passenger_names + relationUserNames
                    try:
                        validate_invoice(material, img_content, extended_names, earliest_flight_time, grouped_materials, system_doc_type, carrier)
                    except Exception as e:
                        material["checkResult"] = "不通过"
                        material["checkBlockReason"] = f"验证发票证明时发生错误: {str(e)}"
                elif doc_type == "诊断证明":
                    # 在验证诊断证明时，将乘客名单扩展为包含亲属姓名
                    extended_names = passenger_names + relationUserNames
                    try:
                        validate_diagnosis_certificate(material, img_content, extended_names, earliest_flight_time, grouped_materials, system_doc_type, carrier)
                    except Exception as e:
                        material["checkResult"] = "不通过"
                        material["checkBlockReason"] = f"验证诊断证明时发生错误: {str(e)}"
            except Exception as e:
                material["checkResult"] = "不通过"
                material["checkBlockReason"] = f"处理材料时发生错误: {str(e)}"
    
    # 返回修改后的材料列表和亲属姓名列表
    return filtered_material_list, relationUserNames

def process_passenger_names(passenger_names: str) -> List[str]:
    """
    处理乘客名单，支持多种输入格式
    
    Args:
        passenger_names: 乘客名单字符串，可以是JSON字符串或逗号分隔的字符串
        
    Returns:
        List[str]: 处理后的乘客名单列表
    """
    if isinstance(passenger_names, str):
        if ',' in passenger_names:
            return [name.strip() for name in passenger_names.split(',') if name.strip()]
        elif passenger_names.strip():
            try:
                return json.loads(passenger_names)
            except json.JSONDecodeError:
                return [passenger_names.strip()]
    return []

def process_material_list(material_list: List[Dict]) -> List[Dict]:
    """
    处理材料列表，确保必要的字段存在并格式正确
    
    Args:
        material_list: 原始材料列表
        
    Returns:
        List[Dict]: 处理后的材料列表
    """
    filtered_material_list = []
    for material in material_list:
        # 确保inconsistencies字段存在
        if "inconsistencies" not in material:
            material["inconsistencies"] = []
            
        # 处理imgContent
        if "imgContent" in material:
            if material["imgContent"] == "{}" or material["imgContent"] == "" or material["imgContent"]=="null":
                material["imgContent"] = {}
            elif isinstance(material["imgContent"], str) and material["imgContent"]:
                try:
                    material["imgContent"] = json.loads(material["imgContent"])
                except:
                    material["imgContent"] = {}
        else:
            material["imgContent"] = {}

        # 处理diagnosisSertificateAiResult，ai解析后的结果
        if "aiResult" in material:
            if material["aiResult"] == "{}" or material["aiResult"] == "" or material["aiResult"]=="null":
                material["aiResult"] = {}
            elif isinstance(material["aiResult"], str) and material["aiResult"]:
                try:
                    material["aiResult"] = json.loads(material["aiResult"])
                except:
                    material["aiResult"] = {}
        else:
            material["aiResult"] = {}    
            
        # 处理invoice_validation_content
        if "invoice_validation_content" in material:
            if material["invoice_validation_content"] == "{}" or material["invoice_validation_content"] == "" or material["invoice_validation_content"]=="null":
                material["invoice_validation_content"] = {}
            elif isinstance(material["invoice_validation_content"], str) and material["invoice_validation_content"]:
                try:
                    material["invoice_validation_content"] = json.loads(material["invoice_validation_content"])
                except:
                    material["invoice_validation_content"] = {}
        else:
            material["invoice_validation_content"] = {}
            
        # 确保医院等级和县级或以上医院字段存在
        if "hospital_grade" not in material:
            material["hospital_grade"] = ""
            
        if "county_level_or_above_hospital" not in material:
            material["county_level_or_above_hospital"] = ""
            
        filtered_material_list.append(material)
    
    return filtered_material_list

def expand_passenger_names(passenger_names: List[str]) -> List[str]:
    """
    扩展乘客名单，添加MD5加密版本
    
    Args:
        passenger_names: 原始乘客名单
        
    Returns:
        List[str]: 扩展后的乘客名单（包含原始名称和MD5值）
    """
    expanded_names = passenger_names.copy()
    for name in passenger_names:
        if name:
            md5_name = hashlib.md5(name.encode('utf-8')).hexdigest()
            expanded_names.append(md5_name)
    return expanded_names

def generate_check_result(filtered_material_list: List[Dict]) -> tuple:
    """
    生成最终的检查结果
    
    Args:
        filtered_material_list: 处理后的材料列表
        
    Returns:
        tuple: (order_check_result, order_check_block_reason)
    """
    order_check_result = "通过"
    order_check_block_reason = ""
    doc_type_reasons = {}
    
    for material in filtered_material_list:
        if material.get("checkResult", "") == "不通过":
            order_check_result = "不通过"
            doc_type = material.get("docType", "未知类型")
            reason = material.get("checkBlockReason", "")
            material_string_type = material.get("materialStringType", "")
            img_path_md5 = material.get("imgPathMd5", "")
            
            if doc_type not in doc_type_reasons:
                doc_type_reasons[doc_type] = []
            
            if reason:
                doc_type_reasons[doc_type].append({
                    "materialStringType": material_string_type,
                    "imgPathMd5": img_path_md5,
                    "reason": reason
                })
    
    # 构建优化后的orderCheckBlockReason
    doc_types_list = list(doc_type_reasons.keys())
    for i, doc_type in enumerate(doc_types_list):
        reasons = doc_type_reasons[doc_type]
        if reasons:
            chinese_numbers = ["一", "二", "三"]
            prefix = f"{chinese_numbers[i] if i < len(chinese_numbers) else str(i+1)}、{doc_type}："
            
            if order_check_block_reason:
                order_check_block_reason += "  "
            
            order_check_block_reason += prefix
            
            for j, item in enumerate(reasons):
                material_string_type = item.get("materialStringType", "")
                img_path_md5 = item.get("imgPathMd5", "")
                reason = item.get("reason", "")
                
                if j > 0:
                    order_check_block_reason += "；"
                
                order_check_block_reason += f"{j+1}、{material_string_type}（{img_path_md5}），{reason}"
    
    return order_check_result, order_check_block_reason

def main(param: dict) -> dict:
    """
    主函数
    Args:
        param: 包含orderNo、invokeAppCode和invokeToken的参数字典，
               以及airlineDate、passengerNames和allData参数
    Returns:
        Dict: 处理结果
    """
    orderNo = param.get("orderNo", "")
    carrier = param.get("carrier","")
    if not orderNo:
        return {"error": "订单号不能为空", "results": []}

    try:
        # 处理allData
        allData = param.get("allData", [])
        if isinstance(allData, str):
            parsed_data, parseStatus = parse_urlencoded_structured_data({"allData": allData}, "allData")
            if parsed_data:
                allData = parsed_data
        
        if not allData:
            return {"error": "获取材料列表失败", "results": []}
        
        # 处理材料列表
        material_list = allData
        filtered_material_list = process_material_list(material_list)
        
        # 处理airlineDate
        airlineDate = param.get("airlineDate", "")
        if isinstance(airlineDate, str) and airlineDate:
            airlineDate = [date.strip() for date in airlineDate.split(',') if date.strip()]
        else:
            airlineDate = []
        
        # 处理乘客名单
        passengerNames = process_passenger_names(param.get("passengerNames", ""))
        passengerNames = expand_passenger_names(passengerNames)
        
        # 执行材料验证，获取处理后的材料列表和关联人员姓名
        filtered_material_list, relationUserNames = validate_materials(filtered_material_list, passengerNames, airlineDate, carrier)
        
        # 生成最终检查结果
        order_check_result, order_check_block_reason = generate_check_result(filtered_material_list)
        
        return {
            "error": "",
            "results": filtered_material_list,
            "orderCheckResult": order_check_result,
            "orderCheckBlockReason": order_check_block_reason,
            "relationUserNames": relationUserNames  # 返回关联人员姓名列表
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "results": []}

def test():
    param = {"orderNo":"xep250507194122916","uniqKey":"d569e114-e9dc-41b7-bd1e-5b25e3403c74","allData":"orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A8ad707fd5768758ff05f431b44479c05%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%7D%23%2A%23traceId%3A36d98727-e1b6-49f3-9920-fbcf8de4e8ea%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Af59e38cc60b890a1f27322397db0a6f1%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%22348eb4f1f9d7540434b3655aea032221%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E8%80%B3%E9%BC%BB%E5%92%BD%E5%96%89%E4%BA%8C%E7%A7%91%E9%97%A8%E8%AF%8A%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E4%B8%BB%E8%AF%89%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E6%9F%A5%E4%BD%93%EF%BC%9A%E4%BD%93%E6%B8%A938.2%E2%84%83%EF%BC%8C%E8%84%89%E6%90%8F100%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E5%91%BC%E5%90%B822%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E8%A1%80%E5%8E%8B90%2F60mmHg%E3%80%82%5Cn%E6%97%A2%E5%BE%80%E5%8F%B2%E5%92%8C%E5%85%B6%E4%BB%96%E7%97%85%E5%8F%B2%EF%BC%9A%E6%97%A0%5Cn%E8%BF%87%E6%95%8F%E5%8F%B2%EF%BC%9A%E6%97%A0%5Cn%E8%BE%85%E5%8A%A9%E6%A3%80%E6%9F%A5%EF%BC%9A%5Cn%E9%97%A8%E8%AF%8A%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%7D%23%2A%23traceId%3A15a12078-3df4-4e5c-b15e-9c38e316d8d0%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%22%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Adf7862f13d9c168fe10f2d0de159cfd2%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22code%22%3A0%2C%22data%22%3A%7B%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22qtxx%22%3A%5B%7B%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038589598_20250511122235628%22%7D%2C%7B%22value%22%3A%221240942792%22%2C%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%7D%2C%7B%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%7D%2C%7B%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%2C%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%7D%2C%7B%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%7D%2C%7B%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%7D%2C%7B%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22name%22%3A%22xb%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%7D%2C%7B%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%225.50%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%7D%5D%2C%22cysj%22%3A%222025-05-22%2001%3A24%3A21%22%2C%22czbmyz%22%3A%22%22%2C%22chrq%22%3A%22%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22chsj%22%3A%22%22%2C%22dy%22%3Afalse%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22jym_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jehjcn%22%3A%22%E4%BC%8D%E5%9C%86%E4%BC%8D%E8%A7%92%22%2C%22rz%22%3Afalse%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22chyy%22%3A%22%22%2C%22czbmyzbh%22%3A%22%22%2C%22fphm_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22cycs%22%3A%2218%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22ch%22%3Afalse%2C%22xmmx%22%3A%5B%5D%2C%22xmqd%22%3A%5B%7B%22zfje_bl%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22bz%22%3A%22%22%2C%22xmmc%22%3A%22%E6%8C%82%E5%8F%B7%E8%B4%B9%22%2C%22ggbz%22%3A%22%22%2C%22je%22%3A%220.50%22%2C%22lx%22%3A%22%22%2C%22xmbh%22%3A%22901011%22%7D%2C%7B%22xmbh%22%3A%22901012%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22je%22%3A%225.00%22%2C%22sl%22%3A%221%22%2C%22ggbz%22%3A%22%22%2C%22xmmc%22%3A%22%E5%85%B6%E4%BB%96%E9%97%A8%E6%80%A5%E8%AF%8A%E6%94%B6%E8%B4%B9%22%2C%22lx%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22bz%22%3A%22%22%7D%5D%2C%22jehj%22%3A%225.5%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22jym%22%3A%22e9%2A%2A%2A%2A%22%7D%2C%22requestId%22%3A%2293eb39ada126001cce7c940de36cbe06%22%2C%22success%22%3Atrue%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%225.50%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%22e9%2A%2A%2A%2A%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%7D%23%2A%23traceId%3A15626543-b180-4968-9fd3-822d7720162e%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A3f3e81450b778b0f21a3db1dc6e8767f%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22requestId%22%3A%2278e9bbbcdd97aa86cc5d13450363b986%22%2C%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22czbmyz%22%3A%22%22%2C%22chyy%22%3A%22%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22ch%22%3Afalse%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22rz%22%3Afalse%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22xmmx%22%3A%5B%5D%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22qtxx%22%3A%5B%7B%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%7D%2C%7B%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%7D%2C%7B%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22chrq%22%3A%22%22%2C%22cysj%22%3A%222025-05-22%2001%3A24%3A23%22%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22jehj%22%3A%2210%22%2C%22chsj%22%3A%22%22%2C%22xmqd%22%3A%5B%7B%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22je%22%3A%2210.00%22%2C%22ggbz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901003%22%2C%22zfje%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22bz%22%3A%22%22%7D%5D%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22czbmyzbh%22%3A%22%22%2C%22dy%22%3Afalse%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22cycs%22%3A%2221%22%7D%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%7D%23%2A%23traceId%3A0568b583-bbe6-4e8b-8f73-18ddbd206f89%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A0bd96ad96f9166db528ba103ed1231d6%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222023.01.09-2033.01.09%22%7D%23%2A%23traceId%3A8f6a000a-f936-4e39-8f5f-f67a9cce342b%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ada29d105d774f20b125e0b84fece477a%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222002%E5%B9%B47%E6%9C%8814%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A528%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%2221456a9bed0d97a792358efeae3e74a6%22%7D%23%2A%23traceId%3Aadf02b13-90c7-4da1-b1eb-7361aae7c863%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Ad11e49744c8d287c25ad057540e6c14d%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%E7%89%B9%E6%AD%A4%E8%AF%81%E6%98%8E%E3%80%82%22%7D%23%2A%23traceId%3Ae4d28481-a284-4c86-8e31-4accdffe580d%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A3af0fd92993568b17654024fa63f19b8%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E4%B8%BB%E8%AF%89%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E7%8E%B0%E7%97%85%E5%8F%B2%EF%BC%9A%E5%8F%B3%E8%80%B3%E7%96%BC%E7%97%9B%E4%BC%B4%E5%90%AC%E5%8A%9B%E4%B8%8B%E9%99%8D%5Cn%E8%BE%85%E5%8A%A9%E6%A3%80%E6%9F%A5%EF%BC%9A%E4%BD%93%E6%B8%A938.2%E2%84%83%EF%BC%8C%E8%84%89%E6%90%8F100%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E5%91%BC%E5%90%B822%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E8%A1%80%E5%8E%8B90%2F60mmHg%E3%80%82%5Cn%E9%97%A8%E8%AF%8A%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E8%80%B3%E9%BC%BB%E5%92%BD%E5%96%89%E4%BA%8C%E7%A7%91%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A4be6da62-a0ba-4657-a917-3d9a927e524c%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8_%E8%A1%A5%E5%85%85%23%2A%23imgPathMd5%3Ad3f89f648e9dd9fa674d37f1a9cdddf7%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22data%22%3A%7B%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22qtxx%22%3A%5B%7B%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%7D%2C%7B%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22name%22%3A%22xb%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%7D%5D%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22czbmyzbh%22%3A%22%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22ch%22%3Afalse%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22chrq%22%3A%22%22%2C%22chsj%22%3A%22%22%2C%22xmqd%22%3A%5B%7B%22xmbh%22%3A%22901003%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22zfje_bl%22%3A%22%22%2C%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22ggbz%22%3A%22%22%2C%22zfje%22%3A%22%22%2C%22bz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22je%22%3A%2210.00%22%2C%22lx%22%3A%22%22%7D%5D%2C%22xmmx%22%3A%5B%5D%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22dy%22%3Afalse%2C%22cycs%22%3A%2222%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22jehj%22%3A%2210%22%2C%22chyy%22%3A%22%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22cysj%22%3A%222025-05-22%2001%3A25%3A07%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22czbmyz%22%3A%22%22%2C%22rz%22%3Afalse%7D%2C%22requestId%22%3A%228e1a436d5e096a36f7a8ae0bd0293c1c%22%2C%22success%22%3Atrue%2C%22code%22%3A0%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7_md5%22%3A%22348eb4f1f9d7540434b3655aea032221%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%22348eb4f1f9d7540434b3655aea032221%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%7D%23%2A%23traceId%3Accf9f521-5342-4a6d-8fad-db0919eaa935%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Abb6ccf80bf26eb63e10e0c228d017b87%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22code%22%3A0%2C%22data%22%3A%7B%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22qtxx%22%3A%5B%7B%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%7D%2C%7B%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22chrq%22%3A%22%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22xmmx%22%3A%5B%5D%2C%22cysj%22%3A%222025-05-22%2001%3A26%3A58%22%2C%22chyy%22%3A%22%22%2C%22cycs%22%3A%2225%22%2C%22czbmyzbh%22%3A%22%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22lx%22%3A%22%22%2C%22je%22%3A%2210.00%22%2C%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22xmbh%22%3A%22901003%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22bz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%7D%5D%2C%22ch%22%3Afalse%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jehj%22%3A%2210%22%2C%22dy%22%3Afalse%2C%22chsj%22%3A%22%22%2C%22czbmyz%22%3A%22%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22rz%22%3Afalse%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%7D%2C%22requestId%22%3A%22eebfd3b7311732c4d163315efce15172%22%2C%22success%22%3Atrue%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%7D%23%2A%23traceId%3A27d43808-e8e0-42a1-8a0b-457408e51f9c%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ae5d141d77118c467e9f9b54ccf451327%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22requestId%22%3A%22daf177120adceb2b92f2e0bb0da704d0%22%2C%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22dy%22%3Afalse%2C%22xmqd%22%3A%5B%7B%22ggbz%22%3A%22%22%2C%22je%22%3A%220.50%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22xmmc%22%3A%22%E6%8C%82%E5%8F%B7%E8%B4%B9%22%2C%22zfje_bl%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22bz%22%3A%22%22%2C%22xmbh%22%3A%22901011%22%2C%22lx%22%3A%22%22%7D%2C%7B%22dw%22%3A%22%E9%A1%B9%22%2C%22bz%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22zfje%22%3A%22%22%2C%22xmmc%22%3A%22%E5%85%B6%E4%BB%96%E9%97%A8%E6%80%A5%E8%AF%8A%E6%94%B6%E8%B4%B9%22%2C%22zfje_bl%22%3A%22%22%2C%22ggbz%22%3A%22%22%2C%22je%22%3A%225.00%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901012%22%7D%5D%2C%22ch%22%3Afalse%2C%22czbmyzbh%22%3A%22%22%2C%22chyy%22%3A%22%22%2C%22xmmx%22%3A%5B%5D%2C%22cycs%22%3A%2219%22%2C%22jehjcn%22%3A%22%E4%BC%8D%E5%9C%86%E4%BC%8D%E8%A7%92%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22cysj%22%3A%222025-05-22%2001%3A25%3A00%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22czbmyz%22%3A%22%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22chsj%22%3A%22%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22jehj%22%3A%225.5%22%2C%22jym_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22rz%22%3Afalse%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22qtxx%22%3A%5B%7B%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038589598_20250511122235628%22%7D%2C%7B%22value%22%3A%221240942792%22%2C%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%7D%2C%7B%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%7D%2C%7B%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%7D%2C%7B%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%225.50%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%7D%5D%2C%22fphm_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22jym%22%3A%22e9%2A%2A%2A%2A%22%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22chrq%22%3A%22%22%7D%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%225.50%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%22e9%2A%2A%2A%2A%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A091af3bb-845e-45ab-ad0e-6be307e06979%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%294%23%2A%23imgPathMd5%3Ac168f2c2b05a16bd2d4e355f6450a5dc%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222020.03.02-2030.03.02%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A72ad38ec-aef1-4e8f-8a1b-40f99b3770b4%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%293%23%2A%23imgPathMd5%3A874dad32d18696c6243f158f375b910d%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%225c94881059587b1c3569c386670d0045%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%221996%E5%B9%B412%E6%9C%882%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%96%B9%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%22d83d2533e1d745c8307b9adf8d405b1b%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E7%94%B7%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A332%22%7D%23%2A%23traceId%3Ada8df580-f92c-49ff-b899-1a698a7a79e9%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Af24978c45ba0506002e4ca18f9a1330d%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222023.01.09-2033.01.09%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%7D%23%2A%23traceId%3A7c1ef17f-9269-4d54-a60a-4af29dee6250%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ab37c128ce7b645f2428250a3924656b1%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A528%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%2221456a9bed0d97a792358efeae3e74a6%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222002%E5%B9%B47%E6%9C%8814%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%7D%23%2A%23traceId%3A71b93754-2bdf-4e67-8d87-872acb3c1771%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Ab66c032df85e830473f1097b6200b591%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%7D%23%2A%23traceId%3A760ce339-626d-4775-8909-03785e1f95f4%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%22%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A17256c762f79a72a7c93c2a51e911d64%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E4%B8%BB%E8%AF%89%3A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E7%8E%B0%E7%97%85%E5%8F%B2%3A%E5%8F%B3%E8%80%B3%E7%96%BC%E7%97%9B%E4%BC%B4%E5%90%AC%E5%8A%9B%E4%B8%8B%E9%99%8D%5Cn%E6%97%A2%E5%BE%80%E5%8F%B2%E5%92%8C%E5%85%B6%E4%BB%96%E7%97%85%E5%8F%B2%3A%E6%97%A0%5Cn%E8%BF%87%E6%95%8F%E5%8F%B2%3A%E6%97%A0%5Cn%E6%9F%A5%E4%BD%93%3A%E4%BD%93%E6%B8%A9%2038.2%E2%84%83%EF%BC%8C%E8%84%89%E6%90%8F%20100%20%E6%AC%A1%20%2F%20%E5%88%86%EF%BC%8C%E5%91%BC%E5%90%B8%2022%20%E6%AC%A1%20%2F%20%E5%88%86%EF%BC%8C%E8%A1%80%E5%8E%8B%2090%2F60mmHg%E3%80%82%5Cn%E8%BE%85%E5%8A%A9%E6%A3%80%E6%9F%A5%3A%20%5Cn%E9%97%A8%E8%AF%8A%E8%AF%8A%E6%96%AD%3A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%3A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB%207%20%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%9C%89%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%22a4bb3a03482e569240496d77fedecdc5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%97%A0%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E8%80%B3%E9%BC%BB%E5%92%BD%E5%96%89%E4%BA%8C%E7%A7%91%E9%97%A8%E8%AF%8A%EF%BC%88%E5%91%88%E8%B4%A1%EF%BC%89%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A9ce33eba-f294-4685-b046-66782ddc57ed%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%22%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A21dc7d328b11b5d0e02ca64aa046ab2e%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22requestId%22%3A%2219566855bcc5a011df6dccfb94e8e3dd%22%2C%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22cysj%22%3A%222025-05-22%2001%3A25%3A52%22%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22jehj%22%3A%2210%22%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22qtxx%22%3A%5B%7B%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%7D%2C%7B%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22dy%22%3Afalse%2C%22czbmyzbh%22%3A%22%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22chyy%22%3A%22%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22cycs%22%3A%2224%22%2C%22czbmyz%22%3A%22%22%2C%22ch%22%3Afalse%2C%22xmqd%22%3A%5B%7B%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22bz%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22zfje_bl%22%3A%22%22%2C%22xmbh%22%3A%22901003%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22je%22%3A%2210.00%22%7D%5D%2C%22xmmx%22%3A%5B%5D%2C%22rz%22%3Afalse%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22chsj%22%3A%22%22%2C%22chrq%22%3A%22%22%7D%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%7D%23%2A%23traceId%3A8f621745-3876-41e9-a920-2c0020b851c3%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ae5a8b8283bdd8ec90702e1420304b409%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jehjcn%22%3A%22%E4%BC%8D%E5%9C%86%E4%BC%8D%E8%A7%92%22%2C%22dy%22%3Afalse%2C%22chsj%22%3A%22%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22ch%22%3Afalse%2C%22jym%22%3A%22e9%2A%2A%2A%2A%22%2C%22cysj%22%3A%222025-05-22%2001%3A25%3A59%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22cycs%22%3A%2220%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22czbmyz%22%3A%22%22%2C%22czbmyzbh%22%3A%22%22%2C%22jym_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22qtxx%22%3A%5B%7B%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038589598_20250511122235628%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%7D%2C%7B%22value%22%3A%221240942792%22%2C%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%7D%2C%7B%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%7D%2C%7B%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%7D%2C%7B%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22name%22%3A%22xb%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%7D%2C%7B%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%2C%22value%22%3A%225.50%22%7D%2C%7B%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%7D%5D%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22rz%22%3Afalse%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22fphm_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22xmmx%22%3A%5B%5D%2C%22jehj%22%3A%225.5%22%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22ggbz%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22je%22%3A%220.50%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901011%22%2C%22xmmc%22%3A%22%E6%8C%82%E5%8F%B7%E8%B4%B9%22%2C%22bz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%7D%2C%7B%22lx%22%3A%22%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22xmmc%22%3A%22%E5%85%B6%E4%BB%96%E9%97%A8%E6%80%A5%E8%AF%8A%E6%94%B6%E8%B4%B9%22%2C%22xmbh%22%3A%22901012%22%2C%22sl%22%3A%221%22%2C%22zfje_bl%22%3A%22%22%2C%22ggbz%22%3A%22%22%2C%22je%22%3A%225.00%22%2C%22bz%22%3A%22%22%7D%5D%2C%22chrq%22%3A%22%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22chyy%22%3A%22%22%7D%2C%22requestId%22%3A%22207d7afbfce7b012c547a1402b042b1e%22%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%225.50%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%22e9%2A%2A%2A%2A%22%7D%23%2A%23traceId%3A7e51cec1-bf01-4cc5-8cd3-da1344c538cc%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%294%23%2A%23imgPathMd5%3A309d7b04b544bd1c1004759489e79d71%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A528%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%2221456a9bed0d97a792358efeae3e74a6%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222002%E5%B9%B47%E6%9C%8814%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%7D%23%2A%23traceId%3A13224581-976f-4f56-b807-f09018e89fc6%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%293%23%2A%23imgPathMd5%3Affcc4357145b700ea8612125dd181981%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222023.01.09-2033.01.09%22%7D%23%2A%23traceId%3A0f6cf545-3dcb-44fc-aa23-b2a91a539579%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A6e5050b824ca573f279be031b48f0e69%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222020.03.02-2030.03.02%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%7D%23%2A%23traceId%3Abadf29e4-ca66-4c51-87ea-7d9fb6d4a16c%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Aadd2ef4610856bc672f9a77bc20ee47b%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E6%80%A7%E5%88%AB%22%3A%22%E7%94%B7%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%E9%95%BF%E6%9C%9F%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A332%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%225c94881059587b1c3569c386670d0045%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%221996%E5%B9%B412%E6%9C%882%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%96%B9%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%22d83d2533e1d745c8307b9adf8d405b1b%22%7D%23%2A%23traceId%3Adfa0f4c9-e02f-4c41-9980-0e0e83ededc1%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E_%E8%A1%A5%E5%85%85%23%2A%23imgPathMd5%3A55a44252f94d2e5cd8efbaca80e38893%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%E7%89%B9%E6%AD%A4%E8%AF%81%E6%98%8E%E3%80%82%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%22348eb4f1f9d7540434b3655aea032221%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%7D%23%2A%23traceId%3A19d34d0f-3d16-4338-8e1e-db3b35c367e3%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%22%22%2C%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Aea10744ba10015e533b3a43e3666f866%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E7%A7%91%E5%AE%A4%22%3A%22%E8%80%B3%E9%BC%BB%E5%92%BD%E5%96%89%E4%BA%8C%E7%A7%91%E9%97%A8%E8%AF%8A%EF%BC%88%E5%91%88%E8%B4%A1%EF%BC%89%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E4%B8%BB%E8%AF%89%3A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E7%8E%B0%E7%97%85%E5%8F%B2%3A%E5%8F%B3%E8%80%B3%E7%96%BC%E7%97%9B%E4%BC%B4%E5%90%AC%E5%8A%9B%E4%B8%8B%E9%99%8D%5Cn%E6%97%A2%E5%BE%80%E5%8F%B2%E5%92%8C%E5%85%B6%E4%BB%96%E7%97%85%E5%8F%B2%3A%E6%97%A0%5Cn%E8%BF%87%E6%95%8F%E5%8F%B2%3A%E6%97%A0%5Cn%E6%9F%A5%E4%BD%93%3A%E4%BD%93%E6%B8%A9%2038.2%E2%84%83%EF%BC%8C%E8%84%89%E6%90%8F%20100%20%E6%AC%A1%20%2F%20%E5%88%86%EF%BC%8C%E5%91%BC%E5%90%B8%2022%20%E6%AC%A1%20%2F%20%E5%88%86%EF%BC%8C%E8%A1%80%E5%8E%8B%2090%2F60mmHg%E3%80%82%5Cn%E8%BE%85%E5%8A%A9%E6%A3%80%E6%9F%A5%3A%20%5Cn%E9%97%A8%E8%AF%8A%E8%AF%8A%E6%96%AD%3A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%3A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB%207%20%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%22a4bb3a03482e569240496d77fedecdc5%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%9C%89%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%97%A0%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%7D%23%2A%23traceId%3Aad67ab01-0971-4c20-aaa5-f87eca6ff850%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%22%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-18%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A0b6530f6a5c0b9157029ef3f45334e0e%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%2242bc88e74856a04ddd887231f45601c9%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%E7%89%B9%E6%AD%A4%E8%AF%81%E6%98%8E%E3%80%82%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%7D%23%2A%23traceId%3Acfe6171c-6f3f-42c1-acd9-7b7550f8dee5%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3Anull%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Af9127d757f7445c41aab555a374f2661%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22chrq%22%3A%22%22%2C%22rz%22%3Afalse%2C%22czbmyz%22%3A%22%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22cysj%22%3A%222025-05-22%2001%3A27%3A05%22%2C%22dy%22%3Afalse%2C%22czbmyzbh%22%3A%22%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22jehj%22%3A%225.5%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jym%22%3A%22e9%2A%2A%2A%2A%22%2C%22ch%22%3Afalse%2C%22xmqd%22%3A%5B%7B%22bz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22zfje%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901011%22%2C%22xmmc%22%3A%22%E6%8C%82%E5%8F%B7%E8%B4%B9%22%2C%22je%22%3A%220.50%22%7D%2C%7B%22bz%22%3A%22%22%2C%22xmmc%22%3A%22%E5%85%B6%E4%BB%96%E9%97%A8%E6%80%A5%E8%AF%8A%E6%94%B6%E8%B4%B9%22%2C%22zfje%22%3A%22%22%2C%22xmbh%22%3A%22901012%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22lx%22%3A%22%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22zfje_bl%22%3A%22%22%2C%22je%22%3A%225.00%22%7D%5D%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22chyy%22%3A%22%22%2C%22qtxx%22%3A%5B%7B%22value%22%3A%22D09038589598_20250511122235628%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%7D%2C%7B%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%2C%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%7D%2C%7B%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%7D%2C%7B%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%2C%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%7D%2C%7B%22value%22%3A%225.50%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%7D%5D%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22chsj%22%3A%22%22%2C%22cycs%22%3A%2221%22%2C%22fphm_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22jehjcn%22%3A%22%E4%BC%8D%E5%9C%86%E4%BC%8D%E8%A7%92%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22jym_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22xmmx%22%3A%5B%5D%7D%2C%22requestId%22%3A%221b89bebee59711c831b52799b5b69faf%22%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%22e9%2A%2A%2A%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%225.50%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A32ac2a3d-48b3-425e-a2f7-283529c47ef7%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Afe731e407ea7f053dabf9b8f497b732b%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22code%22%3A0%2C%22data%22%3A%7B%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22qtxx%22%3A%5B%7B%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%7D%2C%7B%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22chrq%22%3A%22%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22xmmx%22%3A%5B%5D%2C%22cysj%22%3A%222025-05-22%2001%3A26%3A58%22%2C%22chyy%22%3A%22%22%2C%22cycs%22%3A%2225%22%2C%22czbmyzbh%22%3A%22%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22lx%22%3A%22%22%2C%22je%22%3A%2210.00%22%2C%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22xmbh%22%3A%22901003%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22bz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%7D%5D%2C%22ch%22%3Afalse%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jehj%22%3A%2210%22%2C%22dy%22%3Afalse%2C%22chsj%22%3A%22%22%2C%22czbmyz%22%3A%22%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22rz%22%3Afalse%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%7D%2C%22requestId%22%3A%22eebfd3b7311732c4d163315efce15172%22%2C%22success%22%3Atrue%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%7D%23%2A%23traceId%3A2e65b875-22d5-4e6f-be13-9a194843b3a4%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%294%23%2A%23imgPathMd5%3A13da10907149bf033812f7e5dbdfd212%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E6%80%A7%E5%88%AB%22%3A%22%E7%94%B7%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%E9%95%BF%E6%9C%9F%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A332%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%225c94881059587b1c3569c386670d0045%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%221996%E5%B9%B412%E6%9C%882%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%96%B9%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%22d83d2533e1d745c8307b9adf8d405b1b%22%7D%23%2A%23traceId%3A22924f38-75ac-4cc3-b3e6-44db6307098e%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%293%23%2A%23imgPathMd5%3Afcb5061276baa912e83ff4b9d3e5506e%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222020.03.02-2030.03.02%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%7D%23%2A%23traceId%3A13329b16-dd0c-4489-bcad-85d3836c3c8a%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A67ae8b4df014fa051074ae660425cd17%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222023.01.09-2033.01.09%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%7D%23%2A%23traceId%3A7ab4b866-99f3-41fd-83e0-11b057c0771c%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Aa4bb7e3456855305427c16b8426ea70c%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A528%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%2221456a9bed0d97a792358efeae3e74a6%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222002%E5%B9%B47%E6%9C%8814%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%7D%23%2A%23traceId%3A9c85daca-47e5-4f27-874c-938bafaed924%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A73a8457bdf96a31c96176b008b7f8fb6%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%221%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E8%AF%8A%E6%96%AD%EF%BC%9A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E5%A4%84%E7%90%86%E6%84%8F%E8%A7%81%EF%BC%9A%E5%B1%85%E5%AE%B6%E9%9D%99%E5%85%BB7%E5%A4%A9%EF%BC%8C%E4%B8%8D%E5%AE%9C%E8%BF%9C%E8%A1%8C%E3%80%82%E4%BF%9D%E6%8C%81%E5%AE%89%E9%9D%99%E7%8E%AF%E5%A2%83%EF%BC%8C%E9%81%BF%E5%85%8D%E5%87%BA%E5%85%A5%E6%B0%94%E5%8E%8B%E6%B3%A2%E5%8A%A8%E5%8A%A0%E5%A4%A7%E5%9C%BA%E6%89%80%E3%80%82%E7%89%B9%E6%AD%A4%E8%AF%81%E6%98%8E%E3%80%82%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-05-11%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%22348eb4f1f9d7540434b3655aea032221%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%22%22%7D%23%2A%23traceId%3A8633fae8-4043-46f5-86f3-ec8b0dbc7492%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E6%98%AF%23%2A%23aiResult%3A%7B%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22not_suitable_for_flying%22%3A%22%E6%98%AF%22%2C%22not_suitable_timeRange%22%3A%222025-05-11%2C2025-05-17%22%2C%22admission_time%22%3A%22%22%2C%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%22%22%2C%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A154OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ae0d10ccfca705e2fc1809b5a852200ba%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E8%80%B3%E9%BC%BB%E5%92%BD%E5%96%89%E4%BA%8C%E7%A7%91%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D_md5%22%3A%222bbf1b037a1c9e5d9f5ed2977a475c17%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2222%E5%B2%81%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E4%BD%95%2A%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E4%B8%BB%E8%AF%89%3A%E4%B8%AD%E8%80%B3%E7%82%8E%5Cn%E7%8E%B0%E7%97%85%E5%8F%B2%3A%E5%8F%B3%E8%80%B3%E7%96%BC%E7%97%9B%E4%BC%B4%E5%90%AC%E5%8A%9B%E4%B8%8B%E9%99%8D%5Cn%E8%BE%85%E5%8A%A9%E6%A3%80%E6%9F%A5%3A%E4%BD%93%E6%B8%A938.2%E2%84%83%EF%BC%8C%E8%84%89%E6%90%8F100%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E5%91%BC%E5%90%B822%E6%AC%A1%2F%E5%88%86%EF%BC%8C%E8%A1%80%E5%8E%8B90%2F60mmHg%E3%80%82%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%7D%23%2A%23traceId%3A704eeba8-01b1-441f-9211-113f46ba8032%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%E5%90%A6%23%2A%23aiResult%3A%7B%22county_level_or_above_hospital%22%3A%22%E6%98%AF%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22hospital_grade%22%3A%22%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%22%2C%22issuance_time%22%3A%22%22%2C%22not_suitable_for_flying%22%3A%22%E5%90%A6%22%2C%22not_suitable_timeRange%22%3A%22%22%2C%22admission_time%22%3A%22%22%7D%23%2A%23hospital_grade%3A%E4%B8%89%E7%BA%A7%E7%94%B2%E7%AD%89%23%2A%23county_level_or_above_hospital%3A%E6%98%AF%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A2a7286f08e0f4972da4a4d2ad0e2b219%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22code%22%3A0%2C%22data%22%3A%7B%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22jym%22%3A%2229%2A%2A%2A%2A%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22qtxx%22%3A%5B%7B%22value%22%3A%22D09038590293_20250511132534985%22%2C%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%2C%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%2C%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%7D%2C%7B%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%7D%2C%7B%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%7D%2C%7B%22name%22%3A%22grxjzf%22%2C%22value%22%3A%2210.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22chrq%22%3A%22%22%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22xmmx%22%3A%5B%5D%2C%22cysj%22%3A%222025-05-22%2001%3A26%3A58%22%2C%22chyy%22%3A%22%22%2C%22cycs%22%3A%2225%22%2C%22czbmyzbh%22%3A%22%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22lx%22%3A%22%22%2C%22je%22%3A%2210.00%22%2C%22xmmc%22%3A%22%E5%8C%96%E9%AA%8C%E8%B4%B9%22%2C%22xmbh%22%3A%22901003%22%2C%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22bz%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%7D%5D%2C%22ch%22%3Afalse%2C%22jym_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jehj%22%3A%2210%22%2C%22dy%22%3Afalse%2C%22chsj%22%3A%22%22%2C%22czbmyz%22%3A%22%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E5%9C%86%E6%95%B4%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22rz%22%3Afalse%2C%22fphm_md5%22%3A%2225811c946cf92413fbd34887750de869%22%7D%2C%22requestId%22%3A%22eebfd3b7311732c4d163315efce15172%22%2C%22success%22%3Atrue%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2210.00%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%2298209a84741b2c0970b72ba8fffa009a%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A35%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%2229%2A%2A%2A%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%2225811c946cf92413fbd34887750de869%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3Aa915d72b-932a-46b7-accc-60f9fcdc81f2%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22admission_time_check%22%3A%22%22%2C%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A69OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Aa18b557268b192b60c003c100445885c%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22data%22%3A%7B%22kprq%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22fpdm_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22jkrnsrsbh%22%3A%22532925%2A%2A%2A%2A%2A%2A%2A%2A0528%22%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22dw%22%3A%22%E9%A1%B9%22%2C%22zfje_bl%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901011%22%2C%22lx%22%3A%22%22%2C%22je%22%3A%220.50%22%2C%22bz%22%3A%22%22%2C%22xmmc%22%3A%22%E6%8C%82%E5%8F%B7%E8%B4%B9%22%2C%22ggbz%22%3A%22%22%7D%2C%7B%22ggbz%22%3A%22%22%2C%22sl%22%3A%221%22%2C%22xmbh%22%3A%22901012%22%2C%22bz%22%3A%22%22%2C%22je%22%3A%225.00%22%2C%22zfje%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22xmmc%22%3A%22%E5%85%B6%E4%BB%96%E9%97%A8%E6%80%A5%E8%AF%8A%E6%94%B6%E8%B4%B9%22%2C%22dw%22%3A%22%E9%A1%B9%22%7D%5D%2C%22jkr%22%3A%22%E6%9D%8E%2A%22%2C%22pjmc%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E6%94%B6%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22chyy%22%3A%22%22%2C%22jkr_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22jehjcn%22%3A%22%E4%BC%8D%E5%9C%86%E4%BC%8D%E8%A7%92%22%2C%22cysj%22%3A%222025-05-22%2001%3A27%3A34%22%2C%22fphm_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22jehj%22%3A%225.5%22%2C%22rz%22%3Afalse%2C%22fpdm%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22jym_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22fphm%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22cycs%22%3A%2222%22%2C%22czbmyz%22%3A%22%22%2C%22chrq%22%3A%22%22%2C%22fhr%22%3A%22%E4%B8%9C%E8%BD%AF%E4%BA%92%E8%81%94%E7%BD%91%22%2C%22chsj%22%3A%22%22%2C%22dy%22%3Afalse%2C%22xmmx%22%3A%5B%5D%2C%22jym%22%3A%22e9%2A%2A%2A%2A%22%2C%22ch%22%3Afalse%2C%22qtxx%22%3A%5B%7B%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%2C%22value%22%3A%22D09038589598_20250511122235628%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%221240942792%22%7D%2C%7B%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250511%22%7D%2C%7B%22value%22%3A%22%E7%BB%BC%E5%90%88%E5%8C%BB%E9%99%A2%22%2C%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%7D%2C%7B%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%7D%2C%7B%22value%22%3A%22%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%7D%2C%7B%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%2C%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%7D%2C%7B%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%7D%2C%7B%22value%22%3A%225.50%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.00%22%7D%2C%7B%22value%22%3A%220.00%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%2C%22name%22%3A%22grzfe%22%7D%5D%2C%22czbmyzbh%22%3A%22%22%2C%22skr%22%3A%22%E6%88%B4%E6%B0%B8%E7%90%B3%22%2C%22skdw%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%7D%2C%22requestId%22%3A%22568d46b85bc25fcbe1f4c335f279a2cb%22%2C%22success%22%3Atrue%2C%22code%22%3A0%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22ad1d98e0aec3a2a00c4243c23be5da43%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%220%2A%2A%2A%2A%2A%2A%2A77%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%2284a1df1884ef089b4ef7d143add3f180%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%22e9%2A%2A%2A%2A%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222025-05-11%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%22ffae1c91dd08f4053c4ee59ca923170f%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%225%2A%2A%2A%2A%2A25%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%225.50%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E4%BA%91%E5%8D%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250511%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%98%86%E6%98%8E%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%7D%23%2A%23traceId%3Af9efa4b0-a68f-4d6c-82ae-9411b2602b1b%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%7B%22date_treatment%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22date_treatment_check%22%3A%22%E6%98%AF%22%2C%22issuance_time%22%3A%222025-05-11%2000%3A00%3A00%22%2C%22issuance_time_check%22%3A%22%E6%98%AF%22%2C%22admission_time%22%3A%22%22%2C%22admission_time_check%22%3A%22%22%7D%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%294%23%2A%23imgPathMd5%3A431252c595f58bf1ed76952a1d1f7db9%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222020.03.02-2030.03.02%22%7D%23%2A%23traceId%3A2c186dbb-81b7-479c-b6ee-76055162f84c%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%293%23%2A%23imgPathMd5%3A27510d79176e826cce598aa02d2b34ed%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A332%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%225c94881059587b1c3569c386670d0045%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%221996%E5%B9%B412%E6%9C%882%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%96%B9%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%22d83d2533e1d745c8307b9adf8d405b1b%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E7%94%B7%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%E9%95%BF%E6%9C%9F%22%7D%23%2A%23traceId%3A8a89eb01-8fad-415a-aa86-c09b0e5bdbc9%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A96ef0701844ecc750e835656507161eb%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222023.01.09-2033.01.09%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%7D%23%2A%23traceId%3A15f9a274-c3dc-487f-a2d6-1d38c3f412e1%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E, orderNo%3Axep250507194122916%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A115OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Addad888625101512b74e4782e2d47529%23%2A%23uniqKey%3Ad569e114-e9dc-41b7-bd1e-5b25e3403c74%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250511192810743722%23%2A%23imgContent%3A%7B%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22532%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A528%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%2221456a9bed0d97a792358efeae3e74a6%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222002%E5%B9%B47%E6%9C%8814%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E6%9D%8E%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%228df887415685ad39fefc036fc5eccb18%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%7D%23%2A%23traceId%3Ad79c232a-3b23-41f5-af74-9dfed43e2aaa%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%23%2A%23notSuitableForFlying%3A%23%2A%23aiResult%3A%23%2A%23hospital_grade%3A%23%2A%23county_level_or_above_hospital%3A%7E%7E%2A%7E%7E","airlineDate":"2025-05-11 22:55-01:25","passengerNames":"李蕾"}
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 
