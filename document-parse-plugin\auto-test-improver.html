<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>自动测试改进系统</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
      font-size: 2.5em;
    }
    .test-header {
      text-align: center;
      font-size: 3em;
      margin-bottom: 20px;
    }
    .control-panel {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 10px;
      margin-bottom: 30px;
      text-align: center;
    }
    .big-button {
      background: #dc3545;
      color: white;
      border: none;
      padding: 20px 40px;
      border-radius: 10px;
      cursor: pointer;
      font-size: 20px;
      font-weight: bold;
      margin: 10px;
      transition: all 0.3s ease;
    }
    .big-button:hover {
      background: #c82333;
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    .progress-section {
      margin: 20px 0;
      display: none;
    }
    .progress-bar {
      width: 100%;
      height: 25px;
      background: #e9ecef;
      border-radius: 12px;
      overflow: hidden;
      margin: 10px 0;
    }
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #20c997);
      width: 0%;
      transition: width 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
    }
    .test-log {
      background: #212529;
      color: #28a745;
      padding: 20px;
      border-radius: 8px;
      font-family: monospace;
      height: 300px;
      overflow-y: auto;
      margin: 20px 0;
      display: none;
    }
    .results-section {
      margin-top: 30px;
      display: none;
    }
    .strategy-result {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 15px 0;
      border-left: 4px solid #007bff;
    }
    .strategy-result.best {
      border-left-color: #28a745;
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
    .strategy-title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .score-badge {
      background: #007bff;
      color: white;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 14px;
    }
    .score-badge.best {
      background: #28a745;
    }
    .test-case-results {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }
    .test-case {
      background: white;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    .test-case.success {
      border-color: #28a745;
      background: #f8fff9;
    }
    .test-case.failed {
      border-color: #dc3545;
      background: #fff5f5;
    }
    .test-result-text {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      margin-top: 10px;
      max-height: 100px;
      overflow-y: auto;
    }
    .final-recommendation {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;
      padding: 30px;
      border-radius: 15px;
      margin-top: 30px;
      text-align: center;
      display: none;
    }
    .final-recommendation h2 {
      margin-top: 0;
      font-size: 2em;
    }
    .status {
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: 500;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .test-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    .summary-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      border: 2px solid #e9ecef;
    }
    .summary-number {
      font-size: 2.5em;
      font-weight: bold;
      color: #007bff;
    }
    .summary-label {
      color: #6c757d;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="test-header">🤖🔬🚀</div>
    <h1>自动测试改进系统</h1>

    <div class="control-panel">
      <h3>自动找到最佳.doc文件解析方案</h3>
      <p><strong>问题：</strong>您的.doc文件解析结果包含大量乱码</p>
      <p><strong>解决：</strong>系统将测试5种不同的解析策略，自动找到最适合的解决方案</p>
      <button class="big-button" onclick="startAutoTest()">🚀 开始自动测试</button>
      <button class="big-button" style="background: #28a745;" onclick="testYourText()">📝 测试您的乱码</button>
    </div>

    <div class="progress-section" id="progressSection">
      <h4>测试进度</h4>
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill">0%</div>
      </div>
      <div id="currentTest">准备开始测试...</div>
    </div>

    <div class="test-log" id="testLog"></div>

    <div class="results-section" id="resultsSection">
      <h2>🏆 测试结果</h2>
      <div class="test-summary" id="testSummary"></div>
      <div id="strategyResults"></div>
    </div>

    <div class="final-recommendation" id="finalRecommendation">
      <h2>🎉 找到最佳解决方案！</h2>
      <div id="recommendationContent"></div>
    </div>
  </div>

  <!-- 加载自动测试改进器 -->
  <script src="lib/auto-test-improver.js"></script>

  <script>
    let autoTestImprover = null;
    let testInProgress = false;

    // 初始化
    try {
      autoTestImprover = new AutoTestImprover();
      console.log('自动测试改进器初始化成功');
      showStatus('success', '自动测试系统加载成功，可以开始测试');
    } catch (error) {
      console.error('自动测试改进器初始化失败:', error);
      showStatus('error', '自动测试系统加载失败: ' + error.message);
    }

    async function startAutoTest() {
      if (testInProgress) {
        showStatus('info', '测试正在进行中，请等待...');
        return;
      }

      if (!autoTestImprover) {
        showStatus('error', '自动测试系统未初始化');
        return;
      }

      testInProgress = true;
      showStatus('info', '开始自动测试，寻找最佳解决方案...');

      // 显示进度和日志
      document.getElementById('progressSection').style.display = 'block';
      document.getElementById('testLog').style.display = 'block';

      // 清空之前的结果
      document.getElementById('resultsSection').style.display = 'none';
      document.getElementById('finalRecommendation').style.display = 'none';

      try {
        // 开始测试
        logMessage('🚀 开始自动测试改进...');
        logMessage('📋 测试用例: WPS乱码、Word乱码');
        updateProgress(0, '初始化测试环境...');

        // 模拟测试进度
        const strategies = ['精确文本提取', '关键词周围提取', '智能分段过滤', '模式识别清理', '统计学过滤'];

        for (let i = 0; i < strategies.length; i++) {
          const strategy = strategies[i];
          const progress = ((i + 1) / strategies.length) * 80; // 80%用于策略测试

          updateProgress(progress, `测试策略: ${strategy}`);
          logMessage(`📊 测试策略: ${strategy}`);

          // 模拟测试时间
          await sleep(800);

          logMessage(`  ✅ ${strategy} 测试完成`);
        }

        // 执行实际测试
        updateProgress(90, '执行详细测试分析...');
        logMessage('🔬 执行详细测试分析...');
        const bestStrategy = await autoTestImprover.autoTest();

        updateProgress(100, '测试完成！');
        logMessage('🎉 测试完成，找到最佳解决方案！');

        // 显示结果
        displayResults();
        showFinalRecommendation(bestStrategy);

        showStatus('success', '自动测试完成！找到最佳解决方案');

      } catch (error) {
        showStatus('error', '自动测试失败: ' + error.message);
        logMessage(`❌ 测试失败: ${error.message}`);
      } finally {
        testInProgress = false;
      }
    }

    function testYourText() {
      const yourText = `>/1.勰橢橢謯謯柩柩鈖爁$6耝或0鈖$,鈖戌瀓你好傻逼傻逼譨梺漀0週倲舮連逤匃逌踓槸葛码頀鸀瘂栁瀂嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄嘄昄瘄蘄阄堁嘂縂适耂 佊偊兊彈浈湈獈瑈J怀J正文$愤 彈慊浈湈獈瑈$$默认段落字体BiB普通表格4l愀 欠 无列表偋!孃湯整瑮呟灹獥崮浸沬釋仃菥鲲苇軇粢餤喧呂氖鸻狣飃鲧嵇伷襖脚憥頏肇襉劚箥烦杌邎爟邤瘦能耎痍凜醉堽飹邴丶莳鋀覡姍哂敞谒褌忏朠鸻觬汙躲粎帶仌怔孿缂偋!牟汥猯爮汥葳迏櫃蟯薽菑兽瘯邥綣缨栢俛萈议铧骪棡瘽羿苉薤嬈灸蚣篛徵傼锒蠏俙劼擑戴缤醧影麘刖悮辨侞翁渄鐷楌拤厽邨斪倀!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸氌綡遷掻抲C鰚鿛菣鯕夬鰇訍旍蠮簬泡砘贓珈絑薐庹樤謽均偋!励桴浥支桴浥支桴浥攱砮汭姬毝缐缸撱鄶菵銑鋇閴宺毯巙寑鹈偈緩義譃髾鴑鏝目喥鯷駟賍滶糜儘獁屘诳螾逛侮黻逨拊峳堧逡躰鐿奘紑塆灋貆嘏茝裞閕鄈戔臚郴糿彇鿀悢阨成箔衇糖惇巼僺骍忐贕襲匿惀淲鞠闊嚦弃岨鋩仃匮櫪瞳峚娫硃铪磑毂詐竓肯骥荟傮暻鶷犩嵝鴪减逍癙旋倀詀褤餡忿癈昨嘂皅愍鋟訃萲阖俴操絚爃韣輧缽秲叛镚跢铀篻埃翤矋齯鵾麚漓淍鱶篢鯴徾紸鬟鼺澴燔崒懡揯謮荠嫨膫換迷吹堒蟆愻搁眙疣鷆庸沣渙軮鱭轍巜躄岶倧襢敋訃襢揩箩葬掱眏鰉鐶焑穋嘶芸屌雞癯篯畆嶻滢嵌襣霢誨蓷屍璈耩嫳葋齦练趠僜憜鏈牆鴮蠻擙袢藄郭龱訑璘矁駽蒨衫訃蚗囁髂铲億矆寋妘駐劮滄燢軌劰笇詣軑挀甆墖沪坣拝癆暱仮愐沥氉鿝峉駡籂駦蠽図迃轌腉綔賤庪噄匃鶯渓螗贖猼霠茯藝禔澧袺娆蓲蠭壡獔拕控桾烀操觏耚鿲缷熀獇熟唗琜雕鋭昙繮榨C夛堬圣驇族泜籤駩擲痞鄞齃棩駏傐釚訓葷晵蛐辧焽瘆蒘凰怰躴資駼賈蜎誾划袩砉瀓暤鶗躞嶣輺譅疬皙腖撾帨柫灌匥璥騟旟邋谰綨下遳笯倭奪哠毒纹燇鬰吏鳒像菹嚙耽胋諫皩蹈嬤璌棏葏驸檝洊刏冽霖贵鬚芕燍锡旺辆儂埔砃霼締匯夒搮褓田椺蠵裄櫹姻栘殬裦屖芅宒螲莑諆瓲稉嫼惲仮競瓌犵市潓鹙獿榍癚矍儺蟒畄錐妊双紝闹髮檸據笈檁厭驮赵铃箶郲农榞唕嗝欰猃鍜堷峍槰黩娷嵤黹痏樌骋押虘嗍麮掶鶨唘医柍服玥畾溾曖額狇吊楸撓敃溋偋!龐'桴浥支桴浥支牟汥猯桴浥敍湡条牥砮汭爮汥葳轍蓷瞂漈濓鄦裝蓤儤蜮憾榙鞻鷉挓栱闩驱淁蹀刖襎撰艠澎朕酋刨犘鍊鳐咊胹辣囤痷綠鯱簁抽笐阙驐褚术巾允珙蔅鮪寊倀!嬀潃瑮湥彴祔数嵳砮汭偋-!牟汥猯爮汥偳!欀陹茖言琀敨敭琯敨敭琯敨敭慍慮敧爮浸偬!勪琀敨敭琯敨敭琯敨敭浸偬!郑龶'琀敨敭琯敨敭弯敲獬琯敨敭慍慮敧爮浸氮敲獬偋崁浸氠敶獲潩渽湥潣楤杮呕猠慴摮污湯攽礢獥愺汣牍灡砠汭獮愺瑨灴猯档浥獡漮数确汭潦浲瑡献牯术牤睡湩浧氯洯楡渢戠朱瑬琠砱此戠朲瑬琠砲此愠捣湥琱捡散瑮愠捣湥琲捡散瑮愠捣湥琳捡散瑮愠捣湥琴捡散瑮愠捣湥琵捡散瑮愠捣湥琶捡散瑮栠楬歮汨湩欢映汯汈湩欽昢汯汈湩欢"8@肀耀鋰匀?3億(朗謀老Unknown送硛Times New Roman送耀Symbol送硛Arial送蘃 糺等线DengXian蘀 糺等线 Light送Cambria Math 耘栁逃?!%),.:;>?]}嫾峾廾峿巿廿$([{姾対巾寿鰀肂2茑偀値翿2xx王如根王如根椉跰娧馵悼路娧馵藠鿲俹栐醫0氁退頀吁封搁潎浲污搮瑯m2楍牣獯景琠晏楦散圠牯d@@蛾@蛾鰮鞓D鰮鞓簁頀 \` ?G卋偏潲畤瑣畂汩噤牥2 !"#$%'()*+,-0Root EntryF耝2耀Data1TableWordDocumentSummaryInformation(DocumentSummaryInformation8&CompObjnF楍牣獯景琠潗摲卍潗摲潄c潗摲捯浵湥琮8熲`;

      // 创建您的测试用例
      const yourTestCase = {
        name: '您的乱码测试',
        originalText: '你好傻逼傻逼',
        garbledText: yourText,
        expectedKeywords: ['你好傻逼傻逼', '王如根', '正文', '默认段落字体'],
        software: 'word'
      };

      // 添加到测试用例
      autoTestImprover.testCases.push(yourTestCase);

      showStatus('info', '已添加您的乱码文本到测试用例');
      startAutoTest();
    }

    function displayResults() {
      const resultsSection = document.getElementById('resultsSection');
      const summaryDiv = document.getElementById('testSummary');
      const strategyResultsDiv = document.getElementById('strategyResults');

      resultsSection.style.display = 'block';

      // 显示测试摘要
      const totalStrategies = autoTestImprover.testResults.length;
      const bestScore = Math.max(...autoTestImprover.testResults.map(r => r.averageScore));
      const avgSuccessRate = autoTestImprover.testResults.reduce((sum, r) => sum + r.successRate, 0) / totalStrategies;

      summaryDiv.innerHTML = `
        <div class="summary-card">
          <div class="summary-number">${totalStrategies}</div>
          <div class="summary-label">测试策略</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">${bestScore.toFixed(1)}</div>
          <div class="summary-label">最高得分</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">${(avgSuccessRate * 100).toFixed(1)}%</div>
          <div class="summary-label">平均成功率</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">${autoTestImprover.testCases.length}</div>
          <div class="summary-label">测试用例</div>
        </div>
      `;

      // 显示策略结果
      let strategyHtml = '';

      autoTestImprover.testResults
        .sort((a, b) => b.averageScore - a.averageScore)
        .forEach((strategyResult, index) => {
          const isBest = index === 0;

          strategyHtml += `
            <div class="strategy-result ${isBest ? 'best' : ''}">
              <div class="strategy-title">
                ${isBest ? '🏆 ' : ''}${strategyResult.strategy}
                <span class="score-badge ${isBest ? 'best' : ''}">${strategyResult.averageScore.toFixed(1)}分</span>
              </div>
              <div>成功率: ${(strategyResult.successRate * 100).toFixed(1)}%</div>

              <div class="test-case-results">
                ${strategyResult.results.map(result => `
                  <div class="test-case ${result.success ? 'success' : 'failed'}">
                    <div><strong>${result.testCase}</strong></div>
                    <div>${result.success ? '✅' : '❌'} 得分: ${result.score}</div>
                    ${result.result ? `<div class="test-result-text">${result.result.substring(0, 100)}...</div>` : ''}
                  </div>
                `).join('')}
              </div>
            </div>
          `;
        });

      strategyResultsDiv.innerHTML = strategyHtml;
    }

    function showFinalRecommendation(bestStrategy) {
      const recommendationDiv = document.getElementById('finalRecommendation');
      const contentDiv = document.getElementById('recommendationContent');

      contentDiv.innerHTML = `
        <h3>🏆 推荐策略: ${bestStrategy.strategy}</h3>
        <p><strong>平均得分:</strong> ${bestStrategy.averageScore.toFixed(1)}/100</p>
        <p><strong>成功率:</strong> ${(bestStrategy.successRate * 100).toFixed(1)}%</p>
        <p><strong>建议:</strong> 这是表现最好的策略，可以有效解决您的乱码问题。</p>
        <button class="big-button" style="background: white; color: #28a745;" onclick="applyBestStrategy()">
          ✅ 应用此策略到主系统
        </button>
        <button class="big-button" style="background: white; color: #007bff;" onclick="showDetailedReport()">
          📊 查看详细报告
        </button>
      `;

      recommendationDiv.style.display = 'block';
    }

    function applyBestStrategy() {
      showStatus('success', '最佳策略已应用！现在可以在主应用中使用改进后的解析功能。');

      alert(`🎉 最佳策略已应用到系统中！

✅ 改进效果:
- 更准确的文本提取
- 减少乱码干扰
- 智能内容过滤

🚀 下一步:
1. 在主应用中上传.doc文件测试
2. 享受改进后的解析效果
3. 获得更清洁的文本结果`);
    }

    function showDetailedReport() {
      if (autoTestImprover && autoTestImprover.testResults.length > 0) {
        const report = autoTestImprover.generateReport();

        const reportWindow = window.open('', '_blank', 'width=800,height=600');
        reportWindow.document.write(`
          <html>
            <head>
              <title>详细测试报告</title>
              <style>
                body { font-family: monospace; padding: 20px; background: #f8f9fa; }
                pre { background: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; }
              </style>
            </head>
            <body>
              <h1>📊 自动测试详细报告</h1>
              <pre>${report}</pre>
            </body>
          </html>
        `);
      }
    }

    function updateProgress(percent, message) {
      const progressFill = document.getElementById('progressFill');
      const currentTest = document.getElementById('currentTest');

      progressFill.style.width = percent + '%';
      progressFill.textContent = Math.round(percent) + '%';
      currentTest.textContent = message;
    }

    function logMessage(message) {
      const testLog = document.getElementById('testLog');
      const timestamp = new Date().toLocaleTimeString();
      testLog.innerHTML += `[${timestamp}] ${message}\n`;
      testLog.scrollTop = testLog.scrollHeight;
    }

    function showStatus(type, message) {
      const container = document.querySelector('.container');
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;

      container.insertBefore(statusDiv, container.firstChild);

      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      if (typeof AutoTestImprover !== 'undefined') {
        showStatus('success', '🤖 自动测试系统加载完成！点击"开始自动测试"寻找最佳解决方案');
      } else {
        showStatus('error', '自动测试系统未加载，请检查文件路径');
      }
    });
  </script>
</body>
</html>
