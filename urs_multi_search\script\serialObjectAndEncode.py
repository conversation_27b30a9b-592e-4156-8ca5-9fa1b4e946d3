import json
from urllib.parse import quote
from typing import Tuple, Optional

def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串
    
    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(obj, 
                            ensure_ascii=False, 
                            separators=(',', ':'),  # 移除多余空格
                            check_circular=True)
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe='', encoding='utf-8', errors='strict')
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"

# 测试用例
if __name__ == "__main__":
    test_cases = [
        # 正常用例
        ({"name": "张三", "age": 25}, 
         "%7B%22name%22%3A%22%E5%BC%A0%E4%B8%89%22%2C%22age%22%3A25%7D"),
        
        # 包含特殊字符
        ({"msg": "Hello World!@#$"}, 
         "%7B%22msg%22%3A%22Hello%20World%21%40%23%24%22%7D"),
        
        # 嵌套结构
        ({"data": [1, 2, {"key": "值"}]}, 
         "%7B%22data%22%3A%5B1%2C2%2C%7B%22key%22%3A%22%E5%80%BC%22%7D%5D%7D"),
        
        # 不可序列化对象
        ({"dt": object()}, None)
    ]

    for obj, expected in test_cases:
        result, error = json_to_urlencoded(obj)
        print(f"输入: {obj}")
        if error:
            print(f"错误: {error}")
        else:
            print(f"编码结果: {result}")
            if expected and result == expected:
                print("✅ 测试通过")
            else:
                print("❌ 测试未通过")
        print("-" * 60)
