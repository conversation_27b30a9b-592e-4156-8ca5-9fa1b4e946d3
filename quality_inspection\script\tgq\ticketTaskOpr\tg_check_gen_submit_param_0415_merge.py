import json
from pathlib import Path
import re
import traceback
from typing import Any, Callable, Optional, Union


def getBusinessLineByFOrderNo(orderNo: str) -> str:
    """
    根据订单号后三位匹配业务线

    Args:
        orderNo: 订单号

    Returns:
        str: 业务线名称
    """
    # 默认返回国内OTA
    DEFAULT_BUSINESS_LINE = "国内OTA"

    if not orderNo or len(orderNo) < 3:
        return DEFAULT_BUSINESS_LINE

    # 获取订单号后三位
    suffix = orderNo[:3].lower()

    # 创建业务线映射map（只包含非默认值的映射）
    business_line_map = {
        # 国内自营
        "xuy": "国内自营",
        "xep": "国内自营",
        "xdr": "国内自营",
        "cxf": "国内自营",
        "pzf": "国内自营",
        "lah": "国内自营",
        "qnt": "国内自营",
        "yau": "国内自营",
        "xae": "国内自营",
        "xyg": "国内自营",
        "yro": "国内自营",
        "taq": "国内自营",
        "nyl": "国内自营",
        "nss": "国内自营",
        "qnk": "国内自营",
        "qnf": "国内自营",
        "abc": "国内自营",
        "dmo": "国内自营",
        "dhz": "国内自营",
        "qcx": "国内自营",
        "hul": "国内自营",
        "qnd": "国内自营",
        "qnc": "国内自营",
        "tbl": "国内自营",
        "xcx": "国内自营",
        "yxa": "国内自营",
        "xyj": "国内自营",
        "jya": "国内自营",
        "xac": "国内自营",
        "xoa": "国内自营",
        "xam": "国内自营",
        "xrp": "国内自营",
        "qnu": "国内自营",
        "fzg": "国内自营",
        "jbb": "国内自营",
        "qzj": "国内自营",
        "qnn": "国内自营",
        "okm": "国内自营",
        "mia": "国内自营",
        "gqd": "国内自营",
        "cha": "国内自营",
        "ope": "国内自营",
        "fli": "国内自营",
        "exp": "国内自营",
        "wux": "国内自营",
        "ckg": "国内自营",
        "dfu": "国内自营",
        "tyx": "国内自营",
        "hzz": "国内自营",
        "hzw": "国内自营",
        "ltl": "国内自营",
        "btb": "国内自营",
        "sba": "国内自营",
        "grf": "国内自营",
        "ree": "国内自营",
        "yps": "国内自营",
        "ypk": "国内自营",
        "ybt": "国内自营",
        "zoy": "国内自营",
        # 旗舰店
        "cea": "旗舰店",
        "xma": "旗舰店",
        "xia": "旗舰店",
        "qda": "旗舰店",
        "don": "旗舰店",
        "chi": "旗舰店",
        "rui": "旗舰店",
        "kna": "旗舰店",
        "tib": "旗舰店",
        "cgz": "旗舰店",
        "hga": "旗舰店",
        "joy": "旗舰店",
        "loo": "旗舰店",
        "cej": "旗舰店",
        "yin": "旗舰店",
        "uru": "旗舰店",
        "cho": "旗舰店",
        "fuz": "旗舰店",
        "xbh": "旗舰店",
        "szd": "旗舰店",
        "cap": "旗舰店",
        "szf": "旗舰店",
        "szg": "旗舰店",
        "gxa": "旗舰店",
        "qlz": "旗舰店",
        "hhj": "旗舰店",
        "dhp": "旗舰店",
        "qde": "旗舰店",
        "xfu": "旗舰店",
        "jun": "旗舰店",
        "ghq": "旗舰店",
        "sub": "旗舰店",
        "xer": "旗舰店",
        "sdc": "旗舰店",
        "tjf": "旗舰店",
        "scv": "旗舰店",
        "xxx": "旗舰店",
        "ghc": "旗舰店",
        "srs": "旗舰店",
        "hiy": "旗舰店",
        "cqh": "旗舰店",
        "fhr": "旗舰店",
        "kmb": "旗舰店",
        "hzq": "旗舰店",
        "bbw": "旗舰店",
        "sme": "旗舰店",
        "www": "旗舰店",
        "mcj": "旗舰店",
        "nhq": "旗舰店",
        "qdh": "旗舰店",
        "xpb": "旗舰店",
        "qdu": "旗舰店",
        "dhe": "旗舰店",
        "wla": "旗舰店",
        "zbr": "旗舰店",
        "dhy": "旗舰店",
        "gld": "旗舰店",
        "jbk": "旗舰店",
        "xgo": "旗舰店",
        "dhv": "旗舰店",
        "fod": "旗舰店",
        "xzk": "旗舰店",
        "hkh": "旗舰店",
        "dce": "旗舰店",
        "trs": "旗舰店",
        # 分销
        "jlx": "分销",
        "czh": "分销",
        "zbv": "分销",
        "jbx": "分销",
        "ffm": "分销",
        "xnp": "分销",
        "sec": "分销",
        "mwb": "分销",
        "xwc": "分销",
        "qor": "分销",
        "abz": "分销",
        "xna": "分销",
        "kas": "分销",
        "jbw": "分销",
        "rnb": "分销",
        "gek": "分销",
        "pme": "分销",
        "xkv": "分销",
        "tdw": "分销",
        "teb": "分销",
        "zbw": "分销",
        "hpv": "分销",
        "bba": "分销",
        "mxu": "分销",
        "xiv": "分销",
        "kul": "分销",
        "lex": "分销",
        "zgy": "分销",
        "kcz": "分销",
        "fzv": "分销",
        "abw": "分销",
        "quo": "分销",
        "zqw": "分销",
        "zrv": "分销",
        "wtd": "分销",
        "xvv": "分销",
        "xug": "分销",
        "ted": "分销",
        "xnv": "分销",
        "hwu": "分销",
        "nth": "分销",
        "cpm": "分销",
        "aby": "分销",
        "qtl": "分销",
        "wxn": "分销",
        "lkh": "分销",
        "llp": "分销",
        "xng": "分销",
        "zbx": "分销",
        "lqm": "分销",
        "kql": "分销",
        "tgz": "分销",
        "jkd": "分销",
        "pgk": "分销",
        "vmi": "分销",
        "amb": "分销",
        "sfj": "分销",
        "hwm": "分销",
        "xux": "分销",
        "jfb": "分销",
        "oev": "分销",
        "sre": "分销",
        "jhe": "分销",
        "dah": "分销",
        "uah": "分销",
        "acf": "分销",
        "jgb": "分销",
        "rpk": "分销",
        "drz": "分销",
        "tec": "分销",
        "knx": "分销",
        "cay": "分销",
        "jnv": "分销",
        "lfx": "分销",
        "hrk": "分销",
        "dxi": "分销",
        "jbs": "分销",
        "teg": "分销",
        "xiz": "分销",
        "bkb": "分销",
        "cjd": "分销",
        "tee": "分销",
        "teh": "分销",
        "xoi": "分销",
        "ach": "分销",
        "xom": "分销",
        "blq": "分销",
        "acl": "分销",
        "xpp": "分销",
        "sio": "分销",
        "tem": "分销",
        "xok": "分销",
        "aco": "分销",
        "tek": "分销",
        "ter": "分销",
        "jbz": "分销",
        "acr": "分销",
        "kpa": "分销",
        "ssx": "分销",
        "sei": "分销",
        "xol": "分销",
        "jby": "分销",
        "wdm": "分销",
        "jfc": "分销",
        "zcj": "分销",
        "tlf": "分销",
        "wwq": "分销",
        "sqj": "分销",
        "fzj": "分销",
        "acn": "分销",
        "zck": "分销",
        "zkn": "分销",
        "tpx": "分销",
        "kpb": "分销",
        "sej": "分销",
        "fns": "分销",
        "acq": "分销",
        "nbo": "分销",
        "adn": "分销",
        "zfb": "分销",
        "xor": "分销",
        "sen": "分销",
        "qzw": "分销",
        "ezs": "分销",
        "com": "分销",
        "xpu": "分销",
        "pak": "分销",
        "rsc": "分销",
        "dzy": "分销",
        "ppu": "分销",
        "aws": "分销",
        "str": "分销",
        "sqw": "分销",
        "bkc": "分销",
        "tiz": "分销",
        "lqa": "分销",
        "lbz": "分销",
        "acv": "分销",
        "bmw": "分销",
        "tep": "分销",
        "bgb": "分销",
        "lhc": "分销",
        "nfv": "分销",
        "iod": "分销",
        "adi": "分销",
        "caz": "分销",
        "juq": "分销",
        "sel": "分销",
        "glg": "分销",
        "kat": "分销",
        "cpz": "分销",
        "sto": "分销",
        "stv": "分销",
        "acy": "分销",
        "zsx": "分销",
        "ofk": "分销",
        "xwf": "分销",
        "pxh": "分销",
        "teq": "分销",
        "adj": "分销",
        "dao": "分销",
        "ifv": "分销",
        "pai": "分销",
        "zpo": "分销",
        "zcu": "分销",
        "wse": "分销",
        "xuf": "分销",
        "nyq": "分销",
        "uyb": "分销",
        "nxh": "分销",
        "cxu": "分销",
        "cbe": "分销",
        "tes": "分销",
        "fio": "分销",
        "kcy": "分销",
        "acz": "分销",
        "swm": "分销",
        "tev": "分销",
        "tjx": "分销",
        "dak": "分销",
        "xoo": "分销",
        "ffv": "分销",
        "zfs": "分销",
        "rpn": "分销",
        "tzx": "分销",
        "wxx": "分销",
        "bbf": "分销",
        "lcq": "分销",
        "zwq": "分销",
        "suf": "分销",
        "ndk": "分销",
        "qfh": "分销",
        "spm": "分销",
        "zkg": "分销",
        "aza": "分销",
        "fbf": "分销",
        "jlz": "分销",
        "sis": "分销",
        "fig": "分销",
        "bsf": "分销",
        "aky": "分销",
        "jif": "分销",
        "mho": "分销",
        "amw": "分销",
        "tgn": "分销",
        "aoc": "分销",
        "sil": "分销",
        "siu": "分销",
        "aph": "分销",
        "msd": "分销",
        "sgx": "分销",
        "lra": "分销",
        "jgg": "分销",
        "ana": "分销",
        "qbb": "分销",
        "aqu": "分销",
        "apa": "分销",
        "ctd": "分销",
        "aqq": "分销",
        "apn": "分销",
        "aqx": "分销",
        "bbt": "分销",
        "aol": "分销",
        "are": "分销",
        "aqm": "分销",
        "ari": "分销",
        "siv": "分销",
        "aok": "分销",
        "ajm": "分销",
        "gau": "分销",
        "skz": "分销",
        "tgt": "分销",
        "fcf": "分销",
        "bby": "分销",
        "bea": "分销",
        "fcg": "分销",
        "auo": "分销",
        "atw": "分销",
        "jgx": "分销",
        "jju": "分销",
        "qtf": "分销",
        "feh": "分销",
        "zqe": "分销",
    }

    # 查找匹配的业务线，如果找不到则返回默认值（国内OTA）
    return business_line_map.get(suffix, DEFAULT_BUSINESS_LINE)


def try_parse_json(text: str) -> Optional[Union[dict, list]]:
    """
    尝试解析JSON字符串，支持处理多种转义字符格式

    Args:
        text (str): 要解析的JSON字符串

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None
    """
    if not isinstance(text, str):
        return None

    # 首先尝试直接解析，如果成功则直接返回
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    # 处理转义字符的情况
    try:
        # 1. 如果字符串被引号包裹，去除引号
        if text.startswith('"') and text.endswith('"'):
            text = text[1:-1]

        # 2. 检查是否有双重转义的情况 (\\)
        if "\\\\" in text or '\\"' in text:
            # 处理常见的转义序列
            text = text.replace("\\\\", "\\")
            text = text.replace('\\"', '"')
            text = text.replace("\\/", "/")
            text = text.replace("\\n", "\n")
            text = text.replace("\\r", "\r")
            text = text.replace("\\t", "\t")
            text = text.replace("\\b", "\b")
            text = text.replace("\\f", "\f")

            # 尝试解析处理后的文本
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass

        # 3. 处理多重转义的情况 - 例如 {\\\"key\\\": \\\"value\\\"}
        if '\\\\"' in text:
            # 删除额外的反斜杠层
            text = text.replace('\\\\"', '"')
            text = text.replace("\\\\\\", "\\")
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass

        # 4. 使用正则表达式提取JSON模式
        import re

        json_pattern = r"(\{.*\}|\[.*\])"
        match = re.search(json_pattern, text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass

        # 5. 如果上述都失败，尝试一个更激进的方法 - 对整个字符串进行编码解码
        try:
            # 先编码为bytes，然后解码字符串字面值
            decoded = bytes(text, "utf-8").decode("unicode_escape")
            return json.loads(decoded)
        except (json.JSONDecodeError, UnicodeError):
            pass

        return None
    except Exception:
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。
    支持处理转义字符的JSON字符串。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        # 专门处理类似 "{\\\"key\\\": \\\"value\\\"}" 格式的字符串
        if text.startswith('{\\"') or text.startswith('[\\"'):
            try:
                # 将双重转义的反斜杠替换为单个反斜杠
                processed_text = text.replace("\\\\", "\\").replace('\\"', '"')
                result = json.loads(processed_text)
                if result is not None:
                    return result
            except json.JSONDecodeError:
                pass

        # 使用正常的解析逻辑
        result = try_parse_json(text)
        if result is not None:
            return result

        # 如果直接解析失败，尝试使用extract_json_from_text
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        return None


def isAgentDispatchScene(row: dict) -> bool:
    """
    判断是否为国内派单场景

    Args:
        row (dict): 包含业务场景信息的字典

    Returns:
        bool: 如果是国内派单场景返回True，否则返回False

    判断条件:
    - businessLine = '国内派单'
    - oneLevelName = '向派单追款'
    """
    if not isinstance(row, dict):
        return False

    business_line = row.get("businessLine", "")
    one_level_name = row.get("oneLevelName", "")

    return business_line == "国内派单" and one_level_name == "向派单追款"


def main(param: dict) -> dict:
    try:
        fOrderNo = param.get("fOrderNo", "")
        is_valid = param.get("is_valid", False)
        errorMsg = param.get("errorMsg", "")
        aiResponse = safe_json_parse(param.get("aiResponse", ""))

        if not is_valid:
            return {
                "is_valid": is_valid,
                "errorMsg": errorMsg,
                "aiResponse": "",
            }
        if not aiResponse:
            return {
                "is_valid": False,
                "errorMsg": "大模型返回结果为空，请检查",
                "aiResponse": "",
            }

        businessLine = getBusinessLineByFOrderNo(fOrderNo)

        # 处理aiResponse
        if isinstance(aiResponse, dict):
            # 根据genRet更新is_valid
            if "genRet" in aiResponse:
                is_valid = aiResponse["genRet"]

            # 根据errMsg更新errorMsg
            if "errMsg" in aiResponse and aiResponse["errMsg"]:
                errorMsg = aiResponse["errMsg"]

            # 如果responsibilityRows不为空且为数组，则更新每个元素的businessLine
            if (
                "responsibilityRows" in aiResponse
                and isinstance(aiResponse["responsibilityRows"], list)
                and aiResponse["responsibilityRows"]
            ):
                for row in aiResponse["responsibilityRows"]:
                    if isinstance(row, dict) and not isAgentDispatchScene(row):
                        row["businessLine"] = businessLine

        aiResponseStr = json.dumps(aiResponse, ensure_ascii=False)
        return {
            "is_valid": is_valid,
            "errorMsg": errorMsg,
            "aiResponse": aiResponseStr,
            "businessLine": businessLine,
        }
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg, "aiResponse": ""}


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


def test_json_parsing():
    """
    测试JSON解析函数，特别是针对多重转义的情况
    """
    # 测试案例1: 正常JSON
    normal_json = '{"key": "value"}'
    result1 = safe_json_parse(normal_json)
    print("测试1 - 正常JSON:", result1)

    # 测试案例2: 单层转义
    escaped_json = '{"key": "value"}'
    result2 = safe_json_parse(escaped_json)
    print("测试2 - 单层转义:", result2)

    # 测试案例3: 双层转义 (类似代码中的测试用例)
    double_escaped_json = '{\\"key\\": \\"value\\"}'
    result3 = safe_json_parse(double_escaped_json)
    print("测试3 - 双层转义:", result3)

    # 测试案例4: 空字符串
    empty_json = ""
    result4 = safe_json_parse(empty_json)
    print("测试4 - 空字符串:", result4)

    # 测试案例5: 实际测试用例中的复杂JSON
    complex_json = '{\\"genRet\\": true, \\"errMsg\\": \\"\\", \\"responsibilityRows\\": [{\\"businessLine\\": \\"\\", \\"dutyPart\\": \\"1\\", \\"dutyPartName\\": \\"代理商\\", \\"responsibleType\\": \\"1\\", \\"responsibleTypeName\\": \\"代赔付\\", \\"oneLevel\\": \\"999911\\", \\"oneLevelName\\": \\"向采购单追款\\", \\"twoLevel\\": \\"999921\\", \\"twoLevelName\\": \\"代理商\\", \\"threeLevel\\": \\"999931\\", \\"threeLevelName\\": \\"代理商\\", \\"price\\": 70, \\"refundMarkMoney\\": 70, \\"compensationMarkMoney\\": 0, \\"keyValueGenDesc\\": \\"代理商责任金额70元为平台与航司手续费差异（退的金额），赔付金额为0。\\"}, {\\"businessLine\\": \\"\\", \\"dutyPart\\": \\"2\\", \\"dutyPartName\\": \\"Qunar\\", \\"responsibleType\\": \\"2\\", \\"responsibleTypeName\\": \\"直接赔付\\", \\"oneLevel\\": \\"3\\", \\"oneLevelName\\": \\"呼叫中心\\", \\"twoLevel\\": \\"39\\", \\"twoLevelName\\": \\"用户体验\\", \\"threeLevel\\": \\"2703\\", \\"threeLevelName\\": \\"A转B体验相关\\", \\"price\\": 1022, \\"refundMarkMoney\\": 0, \\"compensationMarkMoney\\": 1022, \\"keyValueGenDesc\\": \\"Qunar责任金额1022元为超出差价的协商赔付（赔付金额），退的金额为0。\\"}]}"'
    result5 = safe_json_parse(complex_json)
    print(
        "测试5 - 实际复杂JSON:",
        result5 is not None,
        "包含genRet字段:" if result5 else "",
        "genRet" in result5 if result5 else False,
    )


if __name__ == "__main__":
    # 运行测试函数
    print("=== 测试JSON解析函数 ===")
    test_json_parsing()

    # 测试主函数
    print("\n=== 测试主函数 ===")
    param = {
        "fOrderNo": "xep220725025312898",
        "is_valid": True,
        "errorMsg": "",
        "aiResponse": '{\\"genRet\\": true, \\"errMsg\\": \\"\\", \\"responsibilityRows\\": [{\\"businessLine\\": \\"\\", \\"dutyPart\\": \\"1\\", \\"dutyPartName\\": \\"代理商\\", \\"responsibleType\\": \\"1\\", \\"responsibleTypeName\\": \\"代赔付\\", \\"oneLevel\\": \\"999911\\", \\"oneLevelName\\": \\"向采购单追款\\", \\"twoLevel\\": \\"999921\\", \\"twoLevelName\\": \\"代理商\\", \\"threeLevel\\": \\"999931\\", \\"threeLevelName\\": \\"代理商\\", \\"price\\": 70, \\"refundMarkMoney\\": 70, \\"compensationMarkMoney\\": 0, \\"keyValueGenDesc\\": \\"代理商责任金额70元为平台与航司手续费差异（退的金额），赔付金额为0。\\"}, {\\"businessLine\\": \\"\\", \\"dutyPart\\": \\"2\\", \\"dutyPartName\\": \\"Qunar\\", \\"responsibleType\\": \\"2\\", \\"responsibleTypeName\\": \\"直接赔付\\", \\"oneLevel\\": \\"3\\", \\"oneLevelName\\": \\"呼叫中心\\", \\"twoLevel\\": \\"39\\", \\"twoLevelName\\": \\"用户体验\\", \\"threeLevel\\": \\"2703\\", \\"threeLevelName\\": \\"A转B体验相关\\", \\"price\\": 1022, \\"refundMarkMoney\\": 0, \\"compensationMarkMoney\\": 1022, \\"keyValueGenDesc\\": \\"Qunar责任金额1022元为超出差价的协商赔付（赔付金额），退的金额为0。\\"}]}"',
    }
    result = main(param)
    print(result)
