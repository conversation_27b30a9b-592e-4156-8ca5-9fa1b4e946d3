/**
 * 表格格式化工具库
 * 专门用于优化Markdown表格的显示和结构化
 */
class TableFormatter {
  constructor() {
    this.options = {
      maxCellWidth: 50,
      minCellWidth: 8,
      alignment: 'left',
      emptyCell: '-',
      numberFormat: true,
      dateFormat: true,
      percentFormat: true
    };
  }

  /**
   * 格式化表格数据为结构化Markdown
   * @param {Array} data - 二维数组表格数据
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的Markdown表格
   */
  formatTable(data, options = {}) {
    if (!Array.isArray(data) || data.length === 0) {
      return '> ⚠️ 表格数据为空\n\n';
    }

    const opts = { ...this.options, ...options };
    
    // 数据预处理和分析
    const processedData = this.preprocessData(data, opts);
    const tableAnalysis = this.analyzeTable(processedData);
    
    let markdown = '';
    
    // 添加表格分析信息
    markdown += this.generateTableAnalysis(tableAnalysis);
    
    // 生成格式化的表格
    markdown += this.generateFormattedTable(processedData, tableAnalysis, opts);
    
    // 添加表格总结
    markdown += this.generateTableSummary(tableAnalysis);
    
    return markdown;
  }

  /**
   * 数据预处理
   */
  preprocessData(data, options) {
    return data.map((row, rowIndex) => {
      if (!Array.isArray(row)) return [];
      
      return row.map((cell, colIndex) => {
        let value = cell;
        
        // 处理null/undefined
        if (value === null || value === undefined) {
          return { 
            raw: value, 
            formatted: options.emptyCell, 
            type: 'empty',
            rowIndex,
            colIndex
          };
        }
        
        // 转换为字符串
        const strValue = value.toString().trim();
        
        // 检测数据类型和格式化
        const cellInfo = this.detectCellType(strValue);
        
        return {
          raw: value,
          formatted: this.formatCellValue(strValue, cellInfo.type, options),
          type: cellInfo.type,
          rowIndex,
          colIndex,
          ...cellInfo
        };
      });
    });
  }

  /**
   * 检测单元格数据类型
   */
  detectCellType(value) {
    if (!value || value === '') {
      return { type: 'empty' };
    }
    
    // 数字检测
    if (/^\d+(\.\d+)?$/.test(value)) {
      return { 
        type: 'number', 
        isInteger: !value.includes('.'),
        value: parseFloat(value)
      };
    }
    
    // 百分比检测
    if (/^\d+(\.\d+)?%$/.test(value)) {
      return { 
        type: 'percentage', 
        value: parseFloat(value.replace('%', ''))
      };
    }
    
    // 货币检测
    if (/^[¥$€£]\d+(\.\d+)?$/.test(value) || /^\d+(\.\d+)?[¥$€£]$/.test(value)) {
      return { type: 'currency' };
    }
    
    // 日期检测
    if (this.isDate(value)) {
      return { type: 'date' };
    }
    
    // URL检测
    if (/^https?:\/\//.test(value)) {
      return { type: 'url' };
    }
    
    // 邮箱检测
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return { type: 'email' };
    }
    
    return { type: 'text' };
  }

  /**
   * 格式化单元格值
   */
  formatCellValue(value, type, options) {
    switch (type) {
      case 'empty':
        return options.emptyCell;
      
      case 'number':
        if (options.numberFormat) {
          const num = parseFloat(value);
          return num.toLocaleString('zh-CN');
        }
        return value;
      
      case 'percentage':
        return value; // 保持原格式
      
      case 'currency':
        return value; // 保持原格式
      
      case 'date':
        if (options.dateFormat) {
          try {
            const date = new Date(value);
            return date.toLocaleDateString('zh-CN');
          } catch (e) {
            return value;
          }
        }
        return value;
      
      case 'url':
        return `[链接](${value})`;
      
      case 'email':
        return `[${value}](mailto:${value})`;
      
      default:
        // 处理长文本
        if (value.length > options.maxCellWidth) {
          return value.substring(0, options.maxCellWidth - 3) + '...';
        }
        return value;
    }
  }

  /**
   * 分析表格结构
   */
  analyzeTable(data) {
    const analysis = {
      totalRows: data.length,
      totalCols: Math.max(...data.map(row => row.length)),
      headerRow: data[0] || [],
      dataRows: data.slice(1),
      columnTypes: {},
      columnStats: {},
      hasHeader: true,
      isEmpty: data.length === 0
    };
    
    // 分析每列的数据类型
    for (let colIndex = 0; colIndex < analysis.totalCols; colIndex++) {
      const columnData = data.slice(1).map(row => row[colIndex]).filter(cell => cell && cell.type !== 'empty');
      
      if (columnData.length > 0) {
        const types = columnData.map(cell => cell.type);
        const typeCount = {};
        types.forEach(type => {
          typeCount[type] = (typeCount[type] || 0) + 1;
        });
        
        // 主要数据类型
        const mainType = Object.keys(typeCount).reduce((a, b) => typeCount[a] > typeCount[b] ? a : b);
        
        analysis.columnTypes[colIndex] = {
          mainType,
          typeDistribution: typeCount,
          totalValues: columnData.length
        };
        
        // 数值统计
        if (mainType === 'number') {
          const numbers = columnData.filter(cell => cell.type === 'number').map(cell => cell.value);
          analysis.columnStats[colIndex] = {
            min: Math.min(...numbers),
            max: Math.max(...numbers),
            avg: numbers.reduce((a, b) => a + b, 0) / numbers.length,
            sum: numbers.reduce((a, b) => a + b, 0)
          };
        }
      }
    }
    
    return analysis;
  }

  /**
   * 生成表格分析信息
   */
  generateTableAnalysis(analysis) {
    let markdown = '**📊 表格结构分析**:\n\n';
    markdown += `- 数据维度: ${analysis.totalRows} 行 × ${analysis.totalCols} 列\n`;
    markdown += `- 表头状态: ${analysis.hasHeader ? '✅ 包含表头' : '⚠️ 无表头'}\n`;
    markdown += `- 数据行数: ${analysis.dataRows.length} 行\n`;
    
    // 列类型分析
    if (Object.keys(analysis.columnTypes).length > 0) {
      markdown += '- 列类型分布:\n';
      Object.entries(analysis.columnTypes).forEach(([colIndex, typeInfo]) => {
        const headerName = analysis.headerRow[colIndex]?.formatted || `列${parseInt(colIndex) + 1}`;
        markdown += `  - ${headerName}: ${typeInfo.mainType} (${typeInfo.totalValues}个值)\n`;
      });
    }
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 生成格式化的表格
   */
  generateFormattedTable(data, analysis, options) {
    if (data.length === 0) return '';
    
    let markdown = '**📋 表格数据**:\n\n';
    
    // 计算列宽
    const colWidths = this.calculateColumnWidths(data, options);
    
    // 生成表头
    if (analysis.hasHeader && data.length > 0) {
      const headerRow = data[0];
      markdown += '| ' + headerRow.map((cell, index) => {
        const content = cell.formatted || `列${index + 1}`;
        return this.padCell(content, colWidths[index], 'center');
      }).join(' | ') + ' |\n';
      
      // 生成分隔线，根据列类型设置对齐方式
      markdown += '| ' + headerRow.map((cell, index) => {
        const colType = analysis.columnTypes[index]?.mainType || 'text';
        const alignment = this.getColumnAlignment(colType);
        return this.generateSeparator(colWidths[index], alignment);
      }).join(' | ') + ' |\n';
    }
    
    // 生成数据行
    const dataRows = analysis.hasHeader ? data.slice(1) : data;
    dataRows.forEach((row, rowIndex) => {
      if (!Array.isArray(row)) return;
      
      // 检查是否为空行
      const hasData = row.some(cell => cell && cell.type !== 'empty');
      if (!hasData) return;
      
      // 确保行长度与表头一致
      const paddedRow = [...row];
      while (paddedRow.length < analysis.totalCols) {
        paddedRow.push({ formatted: options.emptyCell, type: 'empty' });
      }
      
      markdown += '| ' + paddedRow.slice(0, analysis.totalCols).map((cell, colIndex) => {
        const content = cell?.formatted || options.emptyCell;
        const colType = analysis.columnTypes[colIndex]?.mainType || 'text';
        const alignment = this.getColumnAlignment(colType);
        return this.padCell(content, colWidths[colIndex], alignment);
      }).join(' | ') + ' |\n';
    });
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 生成表格总结
   */
  generateTableSummary(analysis) {
    let markdown = '**📈 数据总结**:\n\n';
    
    // 数值列统计
    const numericColumns = Object.entries(analysis.columnStats);
    if (numericColumns.length > 0) {
      markdown += '- 数值统计:\n';
      numericColumns.forEach(([colIndex, stats]) => {
        const headerName = analysis.headerRow[colIndex]?.formatted || `列${parseInt(colIndex) + 1}`;
        markdown += `  - ${headerName}: 总和=${stats.sum.toLocaleString('zh-CN')}, 平均=${stats.avg.toFixed(2)}, 范围=${stats.min}-${stats.max}\n`;
      });
    }
    
    // 数据质量
    const totalCells = analysis.totalRows * analysis.totalCols;
    const emptyCells = this.countEmptyCells(analysis);
    const dataCompleteness = ((totalCells - emptyCells) / totalCells * 100).toFixed(1);
    
    markdown += `- 数据完整度: ${dataCompleteness}% (${totalCells - emptyCells}/${totalCells} 个单元格有数据)\n`;
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 计算列宽
   */
  calculateColumnWidths(data, options) {
    const colWidths = [];
    const maxCols = Math.max(...data.map(row => row.length));
    
    for (let colIndex = 0; colIndex < maxCols; colIndex++) {
      let maxWidth = options.minCellWidth;
      
      data.forEach(row => {
        if (row[colIndex]) {
          const cellWidth = (row[colIndex].formatted || '').length;
          maxWidth = Math.max(maxWidth, Math.min(cellWidth, options.maxCellWidth));
        }
      });
      
      colWidths[colIndex] = maxWidth;
    }
    
    return colWidths;
  }

  /**
   * 填充单元格内容
   */
  padCell(content, width, alignment = 'left') {
    const str = content.toString();
    const padding = Math.max(0, width - str.length);
    
    switch (alignment) {
      case 'center':
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        return ' '.repeat(leftPad) + str + ' '.repeat(rightPad);
      case 'right':
        return ' '.repeat(padding) + str;
      default:
        return str + ' '.repeat(padding);
    }
  }

  /**
   * 生成分隔线
   */
  generateSeparator(width, alignment) {
    const dashes = '-'.repeat(width);
    switch (alignment) {
      case 'center':
        return ':' + dashes.slice(2) + ':';
      case 'right':
        return dashes.slice(1) + ':';
      default:
        return dashes;
    }
  }

  /**
   * 获取列对齐方式
   */
  getColumnAlignment(type) {
    switch (type) {
      case 'number':
      case 'percentage':
      case 'currency':
        return 'right';
      case 'date':
        return 'center';
      default:
        return 'left';
    }
  }

  /**
   * 检测是否为日期
   */
  isDate(value) {
    const date = new Date(value);
    return !isNaN(date.getTime()) && value.length > 4;
  }

  /**
   * 统计空单元格数量
   */
  countEmptyCells(analysis) {
    // 这里需要根据实际数据结构来计算
    return 0;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TableFormatter;
} else {
  window.TableFormatter = TableFormatter;
}
