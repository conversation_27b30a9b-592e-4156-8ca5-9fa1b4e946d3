from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串
    
    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(obj, 
                            ensure_ascii=False, 
                            separators=(',', ':'),  # 移除多余空格
                            check_circular=True)
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe='', encoding='utf-8', errors='strict')
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    
    参数：
    data_str : str - 输入的原生字符串数据
    
    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []
    
    # 第一阶段：分割并清洗原始数据
    raw_entries = [entry.strip(', ') for entry in data_str.split('~~*~~') if entry.strip()]
    
    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split('#*#') if f.strip()]
        
        for field in fields:
            # 第三阶段：键值对解析
            if ':' not in field:
                continue  # 跳过无效字段
                
            key, value = field.split(':', 1)
            key = key.strip()
            value = value.strip()
            
            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == 'null':
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识
                
            record[key] = value
        
        if record:  # 跳过空记录
            result.append(record)
    
    return result

def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}

def split_price_changes(groups: dict, group_type: str) -> tuple:
    """拆分价格变化的分组（增强版）"""
    price_change = {}
    no_change = {}
    
    for key, items in groups.items():
        # 处理航司/航班号分组
        if group_type in ('airline', 'flight'):
            # 获取所有唯一价格（字符串形式）
            prices = {str(item['price']) for item in items}
            
            if len(prices) > 1:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': list({item['flightNo'] for item in items}),  # 去重后的航班号列表
                    'prices': list(prices)  # 保证单值
                }
        
        # 处理OTA关联数据
        elif group_type == 'list2ota':
            ota_price = items['otaPrice']['price']
            list_price = items['listPrice']['price']
            
            if ota_price != list_price:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': [items['otaPrice']['flightNo']],
                    'prices': [ota_price]
                }
    
    return price_change, no_change

def generate_no_change_report(no_change_groups: dict) -> str:
    """生成无价格变动报告字符串"""
    if not no_change_groups:
        return ""
    
    report = []
    for key, group in no_change_groups.items():
        report.append(
            f"结论：无价格变动 | "
            f"分组类型: {group['group_type']} | "
            f"分组值: {group['group_value']}\n"
            f"  涉及航班: {', '.join(group['flight_nos'])}\n"
            f"  稳定价格: {group['prices'][0]}\n"
            f"{'-'*40}"
        )
    
    return "\n".join(report) + "\n"

def get_airline(flight_no: str) -> str:
    if '/' in flight_no:
        parts = flight_no.split('/')
        return '/'.join([p[:2] for p in parts])
    return flight_no[:2]

def group_by_airline(data: List[dict]) -> List[dict]:
    # 第一层分组：按航司
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}
    
    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")
        
        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        airline = get_airline(flight_no=flight_no)
        # 初始化航班分组
        if airline not in flight_groups:
            flight_groups[airline] = {}
        
        # 初始化追踪ID分组
        if tracer_id not in flight_groups[airline]:
            flight_groups[airline][tracer_id] = []
        
        # 添加原始数据（保留完整字段）
        flight_groups[airline][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append({
                "groupKey": tracer_key,
                "groupType": "tradeId",
                "groupValue": items.copy()  # 包含完整的原始数据
            })
        
        result.append({
            "groupKey": flight_key,
            "groupType": "airline",
            "groupValue": tracer_list
        })
    
    return result

def group_by_flightno(data: List[dict]) -> List[dict]:
    # 第一层分组：按航班号
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}
    
    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")
        
        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        
        # 初始化航班分组
        if flight_no not in flight_groups:
            flight_groups[flight_no] = {}
        
        # 初始化追踪ID分组
        if tracer_id not in flight_groups[flight_no]:
            flight_groups[flight_no][tracer_id] = []
        
        # 添加原始数据（保留完整字段）
        flight_groups[flight_no][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append({
                "groupKey": tracer_key,
                "groupType": "tradeId",
                "groupValue": items.copy()  # 包含完整的原始数据
            })
        
        result.append({
            "groupKey": flight_key,
            "groupType": "flightNo",
            "groupValue": tracer_list
        })
    
    return result

# 方法二：判断是否统一航班号
def is_unified_flight(data: list) -> bool:
    flights = {item["flightNo"] for item in data}
    return len(flights) == 1



# 方法四：处理OTA数据关联
def process_ota_data(data: list) -> dict:
    # 建立list数据索引
    list_data = {item["tradeId"]: item for item in data if item["tSource"] == "list"}
    
    # 处理OTA数据
    result = {}
    for item in data:
        listTradeId = item.get("listTradeId")
        if item["tSource"] == "ota" and listTradeId is not None :
            list_item = list_data.get(item["listTradeId"])
            result[item["tradeId"]] = {
                "otaPrice": item,
                "listPrice": list_item
            }
    return flatten_grouped_data(result, "list2ota")

def flatten_grouped_data(
    grouped_data: Dict[str, any],
    group_type: str
) -> List[dict]:
    result = []
    
    for group_key, groupData in grouped_data.items():
        # 合并所有trace分组下的数据
        merged_data = [] 
        result.append({
            "groupKey": group_key,
            "groupType": group_type,
            "groupValue": groupData
        })
    
    return result

def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False

def main(param: dict) -> dict:
    
    data, parseStatus = parse_urlencoded_structured_data(param, 'param')
    
    if parseStatus['status'] != 'success':
        return {"status":404, "errMsg": parseStatus['message']}
    
    # print(json.dumps(data, indent=2, ensure_ascii=False))

    #final_report = ""
    # 方法二调用
    unified = is_unified_flight(data)
    # print(f"\n是否统一航班号：{unified}")
    airline_groups: Dict[str, Dict[str, List[dict]]] = {}
    if not unified:
         airline_groups = group_by_airline(data)
         # print("航司分组结果：")
         # print(json.dumps(airline_groups, indent=2, ensure_ascii=False))
        
         # 拆分价格变动组
         #airline_pc, airline_nc = split_price_changes(airline_groups, 'airline')
         #final_report += generate_no_change_report(airline_nc)
         

    # 方法三调用
    flight_groups = group_by_flightno(data)
    # print("\n航班号分组结果：")
    # print(json.dumps(flight_groups, indent=2, ensure_ascii=False))
    # 拆分价格变动组
    #flight_pc, flight_nc = split_price_changes(flight_groups, 'flight')
    #final_report += generate_no_change_report(flight_nc)

    # 方法四调用
    ota_relations = process_ota_data(data)
    # print("\nOTA数据关联结果：")
    # print(json.dumps(ota_relations, indent=2, ensure_ascii=False))

    
    # 拆分价格变动组
    #ota_pc, ota_nc = split_price_changes(ota_relations, 'ota')
    
    # 生成报告
    # 生成综合报告
    #final_report += generate_no_change_report(ota_nc)

    finalData = {
        "status": 200,
        "errMsg": "",
        "airLinePriceGroup": airline_groups,
        "airLinePriceGroupEmpty":is_deep_empty(airline_groups),
        "flightPriceGroup": flight_groups,
        "flightPriceGroupEmpty":is_deep_empty(flight_groups),
        "list2otaPriceGroup": ota_relations,
        "list2otaPriceGroupEmpty":is_deep_empty(ota_relations)
    }
    return finalData

# 使用示例
if __name__ == "__main__":
    rowData = 'tradeId%3Aops_slugger_250227.194111.10.90.5.105.2299869.2843914271_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-14%23%2A%23coupon%3A10%23%2A%23tag%3AGPC1%23%2A%23searchDateTime%3A2025-02-27%2019%3A41%3A11%23%2A%23cut%3Anull%23%2A%23arrivalCity%3ASZX%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU9807%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AHRB%23%2A%23price%3A%22568%22%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A221%23%2A%23cabin%3AT%23%2A%23packagePrice%3A578%23%2A%23basePrice%3A577.9%23%2A%23viewPrice%3A598%23%2A%23policyId%3A1419348301%23%2A%23autoPriceDecreaseAmount%3A16.7%23%2A%23secondPrice%3A%22587.9%22%23%2A%23CPT%3A1740655166%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%7E%7E%2A%7E%7E'
    # encodeData, error = json_to_urlencoded(jsonData)
    input = {"param": rowData}

    print(json.dumps(input, indent=2, ensure_ascii=False))
    
    finalData = main(input)
    print('执行结果：' + json.dumps(finalData, indent=2, ensure_ascii=False))
    
