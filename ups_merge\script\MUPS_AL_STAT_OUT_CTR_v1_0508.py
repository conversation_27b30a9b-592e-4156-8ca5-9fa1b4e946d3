from collections import defaultdict
import copy
import traceback
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from datetime import datetime
from string import Formatter
import re
from typing import Union


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "Missing required parameter: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


supported_scenes = {"多次搜索", "支付时变价", "页面间变价-D2B", "页面间变价-S2D"}
# 定义场景优先级
scenarioPriority = {
    "支付时变价": 3,
    "多次搜索": 2,
    "页面间变价-D2B": 1,
    "页面间变价-S2D": 0,
}


# 常量定义
PAYMENT_UNCONTROLLABLE_REASONS = {
    ("多报价生单变价", "乘机人数限制"),
    ("多报价生单变价", "年龄限制"),
    ("多报价生单变价", "机票余位不足"),
    ("多报价生单变价", "乘机人限制"),
    ("多报价生单变价", "其他"),
    ("挽留二次加购", "辅营商品变化"),
}

MULTIPLE_SEARCH_UNCONTROLLABLE_REASONS = [
    (("疑似平台问题", None), 1),
    (("航班基础数据变化", None), 2),
    (("用户行为导致", "搜索筛选项不一致"), 3),
    (("用户行为导致", "乘机人变化"), 4),
    (("用户行为导致", "代金券变化"), 5),
]

S2D_UNCONTROLLABLE_REASONS = [
    (("疑似平台问题", None), 1),
    (("航班基础数据变化", None), 2),
    (("用户行为导致", "搜索筛选项不一致"), 3),
    (("用户行为导致", "乘机人变化"), 4),
    (("用户行为导致", "代金券变化"), 5),
]

D2B_UNCONTROLLABLE_REASONS = [
    (("booking变价", None), 1),
    (("营销金额变化", None), 2),
    (("辅营商品变化", "商旅返现"), 3),
    (("辅营商品变化", None), 4),
    (("膨胀金金额变化", None), 5),
    (("展示价变化", None), 6),
]


def is_analyzed(analysis_flag: str) -> bool:
    """判断是否已分析"""
    return analysis_flag == "已分析"


def parse_json_array(json_str: str) -> list:
    """解析JSON数组字符串"""
    try:
        return json.loads(json_str) if json_str else []
    except:
        return []


def check_payment_uncontrollable(data: dict) -> tuple:
    """检查支付时变价是否不可控"""
    if not is_analyzed(data.get("支付时场景-分析标识", "")):
        return False, None

    second_reason = data.get("支付分析-二级原因", "")
    third_reason = data.get("支付分析-三级原因", "")

    if (second_reason, third_reason) in PAYMENT_UNCONTROLLABLE_REASONS:
        return True, {
            "不可控场景": "支付时变价",
            "不可控二级分类": second_reason,
            "不可控三级分类": third_reason,
        }
    return False, None


def check_multiple_search_uncontrollable(data: dict) -> tuple:
    """检查多次搜索是否不可控"""
    if not is_analyzed(data.get("多次搜索-分析标识", "")):
        return False, None

    first_reasons = parse_json_array(data.get("多次搜索-一级原因", "[]"))
    second_reasons = parse_json_array(data.get("多次搜索-二级原因", "[]"))

    # 如果二级原因为空，用None填充
    if not second_reasons:
        second_reasons = [None] * len(first_reasons)

    # 找出所有匹配的原因组合
    matched_reasons = []
    for first, second in zip(first_reasons, second_reasons):
        for (
            pattern_first,
            pattern_second,
        ), priority in MULTIPLE_SEARCH_UNCONTROLLABLE_REASONS:
            if first == pattern_first and (
                pattern_second is None or second == pattern_second
            ):
                matched_reasons.append(((first, second), priority))

    if not matched_reasons:
        return False, None

    # 按优先级排序，取优先级最高的
    matched_reasons.sort(key=lambda x: x[1])
    best_match = matched_reasons[0][0]

    return True, {
        "不可控场景": "多次搜索",
        "不可控二级分类": best_match[0],
        "不可控三级分类": best_match[1] if best_match[1] is not None else "",
    }


def check_s2d_uncontrollable(data: dict) -> tuple:
    """检查S2D页面间变价是否不可控"""
    if not is_analyzed(data.get("S2D-分析标识", "")):
        return False, None

    first_reasons = parse_json_array(data.get("S2D-一级原因", "[]"))
    second_reasons = parse_json_array(data.get("S2D-二级原因", "[]"))

    # 如果二级原因为空，用None填充
    if not second_reasons:
        second_reasons = [None] * len(first_reasons)

    # 找出所有匹配的原因组合
    matched_reasons = []
    for first, second in zip(first_reasons, second_reasons):
        for (pattern_first, pattern_second), priority in S2D_UNCONTROLLABLE_REASONS:
            if first == pattern_first and (
                pattern_second is None or second == pattern_second
            ):
                matched_reasons.append(((first, second), priority))

    if not matched_reasons:
        return False, None

    # 按优先级排序，取优先级最高的
    matched_reasons.sort(key=lambda x: x[1])
    best_match = matched_reasons[0][0]

    return True, {
        "不可控场景": "页面间变价-S2D",
        "不可控二级分类": best_match[0],
        "不可控三级分类": best_match[1] if best_match[1] is not None else "",
    }


def check_d2b_uncontrollable(data: dict) -> tuple:
    """检查D2B页面间变价是否不可控"""
    if not is_analyzed(data.get("页面间变价-分析标识", "")):
        return False, None

    # 特殊处理：当可控变价场景为D2B时直接返回
    """if (
        data.get("是否可控类型") == "是"
        and data.get("可控变价场景") == "页面间变价-D2B"
    ):
        return True, {
            "不可控场景": "页面间变价-D2B",
            "不可控二级分类": data.get("页面间变价-二级原因", ""),
            "不可控三级分类": data.get("页面间变价-三级原因", ""),
        }
    """

    second_reasons = parse_json_array(data.get("页面间变价-二级原因", "[]"))
    third_reasons = parse_json_array(data.get("页面间变价-三级原因", "[]"))

    # 如果三级原因为空，用None填充
    if not third_reasons:
        third_reasons = [None] * len(second_reasons)

    # 找出所有匹配的原因组合
    matched_reasons = []
    for second, third in zip(second_reasons, third_reasons):
        for (pattern_second, pattern_third), priority in D2B_UNCONTROLLABLE_REASONS:
            if second == pattern_second and (
                pattern_third is None or third == pattern_third
            ):
                matched_reasons.append(((second, third), priority))

    if not matched_reasons:
        return False, None

    # 按优先级排序，取优先级最高的
    matched_reasons.sort(key=lambda x: x[1])
    best_match = matched_reasons[0][0]

    return True, {
        "不可控场景": "页面间变价-D2B",
        "不可控二级分类": best_match[0],
        "不可控三级分类": best_match[1] if best_match[1] is not None else "",
    }


def tryFillOutCtrlField(oirAlRelateData: dict) -> tuple:
    """
    判断是否需要补充不可控字段说明

    Args:
        oirAlRelateData: 原始分析相关数据

    Returns:
        tuple: (是否需要补充不可控字段, 错误信息, 补充的数据)
    """
    # Step 1: 总体判断是否需要补充不可控字段
    if (
        oirAlRelateData.get("是否可控类型") == "是"
        and oirAlRelateData.get("可控变价场景") != "页面间变价-D2B"
    ):
        return False, "变价本身可控，无需补充不可控字段", {}

    # 检查是否有任何场景被分析
    analyzed_scenes = [
        is_analyzed(oirAlRelateData.get("支付时场景-分析标识", "")),
        is_analyzed(oirAlRelateData.get("多次搜索-分析标识", "")),
        is_analyzed(oirAlRelateData.get("S2D-分析标识", "")),
        is_analyzed(oirAlRelateData.get("页面间变价-分析标识", "")),
    ]

    if not any(analyzed_scenes):
        return False, "没有场景被分析，无需补充不可控字段", {}

    # Step 2: 按优先级检查各个场景
    # 支付时变价
    need_stat, data = check_payment_uncontrollable(oirAlRelateData)
    if need_stat:
        return True, "", data

    # 多次搜索
    need_stat, data = check_multiple_search_uncontrollable(oirAlRelateData)
    if need_stat:
        return True, "", data

    # S2D页面间变价
    need_stat, data = check_s2d_uncontrollable(oirAlRelateData)
    if need_stat:
        return True, "", data

    # D2B页面间变价
    need_stat, data = check_d2b_uncontrollable(oirAlRelateData)
    if need_stat:
        return True, "", data

    # 所有场景都不匹配
    return False, "无法确认不可控原因", {}


def main(param: dict) -> dict:
    try:
        oirAlRelateData = safe_json_parse(param.get("alRelateData"))
        username = param.get("username")

        if not oirAlRelateData or not isinstance(oirAlRelateData, dict):
            return {
                "username": username,
                "errorMsg": "请求参数中alRelateData字段解析失败",
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        # 获取alScene
        needStat, error_msg, data = tryFillOutCtrlField(oirAlRelateData)
        if not needStat:
            return {
                "username": username,
                "errorMsg": error_msg,
                "isSuccess": "false",
                "alSceneBriefData": {},
            }
        if not data:
            return {
                "username": username,
                "errorMsg": "不可控字段计算为空",
                "isSuccess": "false",
                "alSceneBriefData": {},
            }
        alSceneBriefData = {**oirAlRelateData, **data}
        return {
            "username": username,
            "isSuccess": "true",
            "alSceneBriefData": alSceneBriefData,
            "errorMsg": "",
        }
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"填充不可控字段异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {
            "username": username,
            "errorMsg": error_msg,
            "isSuccess": "false",
            "alSceneBriefData": {},
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# 使用示例
if __name__ == "__main__":

    input = {
        "username": "fbzahdf8502",
        "ursDate": "2025-05-07",
        "uniqKey": "f39707ef-cc94-4895-85e1-99b0e9ff7863",
        "alRelateData": {
            "用户名": "fbzahdf8502",
            "uniqKey": "f39707ef-cc94-4895-85e1-99b0e9ff7863",
            "urs-日期": "2025-05-07",
            "是否可控类型": "是",
            "可控变价场景": "页面间变价-D2B",
            "支付时场景-分析标识": "已分析",
            "支付分析-一级原因": "未匹配用户场景",
            "支付分析-二级原因": "用户下单未变价",
            "支付分析-三级原因": "用户下单未变价",
            "支付分析-是否可控场景": "",
            "多次搜索-分析标识": "已分析",
            "多次搜索-是否可控场景": "-",
            "多次搜索-AI定责说明": "平台无责",
            "多次搜索-AI变价原因归因": "-",
            "多次搜索-AI变价判断描述": "-",
            "多次搜索-无需归因原因": "阶段:变价数据分组和过滤, 不分析原因：执行过滤规则后无有效数据,请确认用户反馈是否有偏差",
            "页面间变价-分析标识": "已分析",
            "页面间变价-是否可控": "是",
            "页面间变价-AI定责": "平台有责",
            "页面间变价-一级原因": '["疑似平台问题", "疑似平台问题"]',
            "页面间变价-二级原因": '["展示价变化", "展示价变化"]',
            "页面间变价-三级原因": '["其他", "其他"]',
            "页面间变价-AI变价原因归因": '["展示价变化但核心影响因素（代金券、营销立减、膨胀金、抹零卡、商旅返现）未变化，且底价（basePrice、policyId、cabin、viewPrice）未变化", "展示价变化但未检测到coupon、activityCut、expCut、zeroCard或busRefund金额变化，可能与乘机人数（adultnum从0变1）相关但未触发代金券联动规则。"]',
            "页面间变价-AI变价判断说明": '["展示价从878变更为883，变价金额为+5", "价格从878变更为883"]',
            "页面间变价-无需归因原因": "",
            "多次搜索-一级原因": "-",
            "多次搜索-二级原因": "-",
            "S2D-分析标识": "",
            "S2D-是否可控": "",
            "S2D-AI定责": "",
            "S2D-一级原因": "",
            "S2D-二级原因": "",
            "S2D-三级原因": "",
            "S2D-AI变价原因归因": "",
            "S2D-AI变价描述": "",
            "S2D-无需归因说明": "",
            "不可控场景": "",
            "不可控二级分类": "",
            "不可控三级分类": "",
        },
    }
    print(json.dumps(input, indent=2, ensure_ascii=False))

    finalData = main(input)
    # print("执行结果：" + json.dumps(finalData, indent=2, ensure_ascii=False))
    write_json_to_file(
        finalData,
        file_path="ups_merge/data/mergeMultiSearch.json",
    )
