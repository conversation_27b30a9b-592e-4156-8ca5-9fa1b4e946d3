// 显示自定义弹窗
function showCustomAlert(message) {
  const customAlert = document.getElementById('customAlert');
  const alertMessage = document.getElementById('alertMessage');
  const alertCloseBtn = document.getElementById('alertCloseBtn');

  if (customAlert && alertMessage) {
    alertMessage.textContent = message;
    customAlert.style.display = 'flex';

    // 添加关闭按钮事件
    if (alertCloseBtn) {
      alertCloseBtn.onclick = function() {
        customAlert.style.display = 'none';
      };
    }

    // 点击背景也可以关闭
    customAlert.onclick = function(event) {
      if (event.target === customAlert) {
        customAlert.style.display = 'none';
      }
    };
  }
}

document.addEventListener('DOMContentLoaded', () => {
  // Check if required libraries are loaded
  if (typeof mammoth === 'undefined' || typeof XLSX === 'undefined') {
    showStatus('文档处理库未正确加载，请刷新页面重试', true);
    return;
  }

  const dp = new DocumentProcessor();
  const uploadArea = document.getElementById('uploadArea');
  const fileInput = document.getElementById('fileInput');
  const fileList = document.getElementById('fileList');
  const previewArea = document.getElementById('previewArea');
  const exportBtn = document.getElementById('exportBtn');
  const clearBtn = document.getElementById('clearBtn');
  const loading = document.getElementById('loading');
  const parseToTableBtn = document.getElementById('parseToTableBtn');
  const oaIdInput = document.getElementById('oaIdInput');
  const flowSignInput = document.getElementById('flowSignInput');
  const envSelect = document.getElementById('envSelect');
  const parseResult = document.getElementById('parseResult');
  const resultContent = document.getElementById('resultContent');

  // 注意：popup版本没有导出格式选择，默认使用文本格式

  // 检查必要的DOM元素是否存在
  if (!uploadArea || !fileInput || !fileList || !previewArea || !exportBtn || !clearBtn || !loading || !parseToTableBtn) {
    console.error('必要的DOM元素未找到');
    return;
  }

  // 显示/隐藏加载状态
  function showLoading(show) {
    if (loading) {
      loading.style.display = show ? 'block' : 'none';
    }
  }

  // 显示状态消息
  function showStatus(message, isError = false) {
    if (!previewArea) return;

    previewArea.innerHTML = `<div class="status-message ${isError ? 'error' : 'success'}">${message}</div>`;
    if (!isError) {
      setTimeout(() => {
        if (previewArea) {
          previewArea.innerHTML = '';
        }
      }, 3000);
    }
  }

  // 更新文件列表显示
  function updateFileList() {
    if (!fileList) return;

    const files = dp.getFileList();
    let html = '<h3>已上传文件</h3>';

    if (!files || !files.word || !files.excel) {
      html += '<p>文件列表获取失败</p>';
    } else if (files.word.length === 0 && files.excel.length === 0) {
      html += '<p>暂无上传文件</p>';
    } else {
      html += '<ul class="file-items">';

      if (Array.isArray(files.word)) {
        files.word.forEach(file => {
          if (file && file.name) {
            html += `
              <li>
                <div class="file-item" data-type="word" data-name="${file.name}">
                  <span class="file-icon">📄</span>
                  <span class="file-name">${file.name}</span>
                </div>
                <button class="delete-btn" data-type="word" data-name="${file.name}">×</button>
              </li>
            `;
          }
        });
      }

      if (Array.isArray(files.excel)) {
        files.excel.forEach(file => {
          if (file && file.name) {
            html += `
              <li>
                <div class="file-item" data-type="excel" data-name="${file.name}">
                  <span class="file-icon">📊</span>
                  <span class="file-name">${file.name}</span>
                </div>
                <button class="delete-btn" data-type="excel" data-name="${file.name}">×</button>
              </li>
            `;
          }
        });
      }

      html += '</ul>';
    }

    fileList.innerHTML = html;

    // 添加文件点击事件
    const fileItems = fileList.querySelectorAll('.file-item');
    if (fileItems) {
      fileItems.forEach(item => {
        if (item.dataset && item.dataset.type && item.dataset.name) {
          item.addEventListener('click', () => previewFile(item.dataset.type, item.dataset.name));
        }
      });
    }

    // 添加删除按钮点击事件
    const deleteButtons = fileList.querySelectorAll('.delete-btn');
    if (deleteButtons) {
      deleteButtons.forEach(button => {
        if (button.dataset && button.dataset.type && button.dataset.name) {
          button.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡，避免触发文件预览
            deleteFile(button.dataset.type, button.dataset.name);
          });
        }
      });
    }
  }

  // 删除单个文件
  function deleteFile(type, name) {
    if (!type || !name) return;

    const success = dp.removeFile(type, name);
    if (success) {
      updateFileList();
      showStatus(`已删除文件: ${name}`);

      // 如果预览区域正在显示该文件，则清空预览
      const currentPreview = previewArea.querySelector('.word-preview, .excel-preview');
      if (currentPreview) {
        const previewTitle = currentPreview.querySelector('h3');
        if (previewTitle && previewTitle.textContent === name) {
          previewArea.innerHTML = '';
        }
      }
    } else {
      showStatus(`删除文件失败: ${name}`, true);
    }
  }

  // 显示预览弹窗
  function showPreviewModal(title, content) {
    const previewModal = document.getElementById('previewModal');
    const previewModalTitle = document.getElementById('previewModalTitle');
    const previewModalBody = document.getElementById('previewModalBody');
    const previewModalCloseBtn = document.getElementById('previewModalCloseBtn');
    const previewModalContent = document.getElementById('previewModalContent');

    if (previewModal && previewModalTitle && previewModalBody) {
      previewModalTitle.textContent = title;
      previewModalBody.innerHTML = content;
      previewModal.style.display = 'flex';

      // 添加关闭按钮事件
      if (previewModalCloseBtn) {
        previewModalCloseBtn.onclick = function() {
          previewModal.style.display = 'none';
        };
      }

      // 点击背景关闭
      previewModal.onclick = function(event) {
        if (event.target === previewModal) {
          previewModal.style.display = 'none';
        }
      };

      // 添加拖动功能
      let isDragging = false;
      let offsetX, offsetY;

      if (previewModalContent) {
        const header = previewModalContent.querySelector('.preview-modal-header');
        if (header) {
          header.onmousedown = function(e) {
            isDragging = true;
            offsetX = e.clientX - previewModalContent.getBoundingClientRect().left;
            offsetY = e.clientY - previewModalContent.getBoundingClientRect().top;

            document.onmousemove = function(e) {
              if (isDragging) {
                const x = e.clientX - offsetX;
                const y = e.clientY - offsetY;

                previewModalContent.style.position = 'absolute';
                previewModalContent.style.margin = '0';
                previewModalContent.style.left = `${x}px`;
                previewModalContent.style.top = `${y}px`;
              }
            };

            document.onmouseup = function() {
              isDragging = false;
              document.onmousemove = null;
              document.onmouseup = null;
            };
          };
        }
      }
    }
  }

  // 预览文件内容
  function previewFile(type, name) {
    if (!type || !name) {
      showStatus('预览失败: 无效的文件信息', true);
      return;
    }

    showLoading(true);
    try {
      if (type === 'word') {
        const file = dp.files.word.find(f => f && f.name === name);
        if (!file || !file.data) {
          throw new Error('文件数据无效');
        }

        // 检查是否有HTML内容
        if (typeof file.data === 'object' && file.data.html) {
          // 使用HTML内容进行预览，但增强表格显示
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = file.data.html;

          // 增强表格样式
          const tables = tempDiv.querySelectorAll('table');
          if (tables && tables.length > 0) {
            tables.forEach(table => {
              // 添加CSS类
              table.classList.add('word-table');

              // 为表格的第一行添加表头样式
              const firstRow = table.querySelector('tr');
              if (firstRow) {
                firstRow.classList.add('header-row');

                // 将第一行的td转换为th
                const cells = firstRow.querySelectorAll('td');
                cells.forEach(cell => {
                  const th = document.createElement('th');
                  th.innerHTML = cell.innerHTML;
                  cell.parentNode.replaceChild(th, cell);
                });
              }

              // 为偶数行添加交替颜色
              const rows = table.querySelectorAll('tr');
              rows.forEach((row, index) => {
                if (index > 0 && index % 2 === 0) {
                  row.classList.add('even-row');
                }
              });
            });
          }

          // 在预览区域显示简短预览
          previewArea.innerHTML = `
            <div class="word-preview">
              <h3>${name}</h3>
              <div class="word-content-preview">
                <p>Word文档已加载，点击查看完整内容</p>
                <button class="view-full-btn">查看完整内容</button>
              </div>
            </div>
          `;

          // 添加查看完整内容按钮事件
          const viewFullBtn = previewArea.querySelector('.view-full-btn');
          if (viewFullBtn) {
            viewFullBtn.addEventListener('click', () => {
              const modalContent = `
                <div class="word-preview">
                  <div class="word-content">${tempDiv.innerHTML}</div>
                </div>
              `;
              showPreviewModal(`预览: ${name}`, modalContent);
            });
          }
        } else {
          // 回退到纯文本预览
          const textContent = typeof file.data === 'object' && file.data.text ?
            file.data.text :
            (typeof file.data === 'string' ? file.data : '无法显示内容');

          // 在预览区域显示简短预览
          previewArea.innerHTML = `
            <div class="word-preview">
              <h3>${name}</h3>
              <div class="word-content-preview">
                <p>Word文档已加载，点击查看完整内容</p>
                <button class="view-full-btn">查看完整内容</button>
              </div>
            </div>
          `;

          // 添加查看完整内容按钮事件
          const viewFullBtn = previewArea.querySelector('.view-full-btn');
          if (viewFullBtn) {
            viewFullBtn.addEventListener('click', () => {
              const modalContent = `
                <div class="word-preview">
                  <pre class="word-text">${textContent}</pre>
                </div>
              `;
              showPreviewModal(`预览: ${name}`, modalContent);
            });
          }
        }
      } else if (type === 'excel') {
        const file = dp.files.excel.find(f => f && f.name === name);
        if (!file || !file.data || !file.data.SheetNames) {
          throw new Error('文件数据无效');
        }

        // 在预览区域显示简短预览
        previewArea.innerHTML = `
          <div class="excel-preview">
            <h3>${name}</h3>
            <div class="excel-content-preview">
              <p>Excel文件已加载，包含 ${file.data.SheetNames.length} 个工作表</p>
              <button class="view-full-btn">查看完整内容</button>
            </div>
          </div>
        `;

        // 添加查看完整内容按钮事件
        const viewFullBtn = previewArea.querySelector('.view-full-btn');
        if (viewFullBtn) {
          viewFullBtn.addEventListener('click', () => {
            let modalContent = `<div class="excel-preview">`;

            // 添加工作表选择器
            if (file.data.SheetNames.length > 1) {
              modalContent += '<div class="sheet-selector"><label>选择工作表: </label><select id="modalSheetSelector">';
              file.data.SheetNames.forEach(sheetName => {
                modalContent += `<option value="${sheetName}">${sheetName}</option>`;
              });
              modalContent += '</select></div>';
            }

            // 为每个工作表创建表格，初始只显示第一个
            file.data.SheetNames.forEach((sheetName, index) => {
              const worksheet = file.data.Sheets[sheetName];
              if (!worksheet) return;

              const data = XLSX.utils.sheet_to_json(worksheet, {header: 1});
              if (!Array.isArray(data)) return;

              const display = index === 0 ? 'block' : 'none';
              modalContent += `<div class="sheet-content" id="modal-sheet-${sheetName}" style="display: ${display}">`;
              modalContent += `<h4>${sheetName}</h4>`;

              // 创建表格，显示所有行
              if (data.length > 0) {
                modalContent += '<table class="excel-table">';

                // 添加表头样式
                data.forEach((row, rowIndex) => {
                  if (!Array.isArray(row)) return;

                  const rowClass = rowIndex === 0 ? 'header-row' : '';
                  modalContent += `<tr class="${rowClass}">`;

                  row.forEach(cell => {
                    const cellContent = cell !== null && cell !== undefined ? cell : '';
                    modalContent += `<td>${cellContent}</td>`;
                  });

                  modalContent += '</tr>';
                });

                modalContent += '</table>';
              } else {
                modalContent += '<p>此工作表没有数据</p>';
              }

              modalContent += '</div>';
            });

            modalContent += '</div>';

            showPreviewModal(`预览: ${name}`, modalContent);

            // 添加工作表选择器的事件监听
            setTimeout(() => {
              const modalSheetSelector = document.getElementById('modalSheetSelector');
              if (modalSheetSelector) {
                modalSheetSelector.addEventListener('change', (e) => {
                  // 隐藏所有工作表内容
                  const sheetContents = document.querySelectorAll('.sheet-content');
                  sheetContents.forEach(el => el.style.display = 'none');

                  // 显示选中的工作表
                  const selectedSheet = document.getElementById(`modal-sheet-${e.target.value}`);
                  if (selectedSheet) {
                    selectedSheet.style.display = 'block';
                  }
                });
              }
            }, 100);
          });
        }
      }
    } catch (error) {
      showStatus('预览失败: ' + error.message, true);
    } finally {
      showLoading(false);
    }
  }

  // 处理文件上传
  async function handleFiles(files) {
    if (!files || !Array.isArray(files) || files.length === 0) {
      showStatus('请选择文件', true);
      return;
    }

    showLoading(true);
    try {
      let successCount = 0;
      let errorCount = 0;

      for (const file of files) {
        if (!file) continue;

        try {
          await dp.addFile(file);
          successCount++;
        } catch (error) {
          errorCount++;
          let errorMsg = error.message;

          // 特殊处理Word文档错误
          if (errorMsg.includes('Word文档') || errorMsg.includes('ZIP')) {
            errorMsg = `"${file.name}" 不是有效的Word文档`;
          }

          showStatus(errorMsg, true);
          await new Promise(resolve => setTimeout(resolve, 2000)); // 短暂显示错误
        }
      }

      updateFileList();
      if (successCount > 0) {
        showStatus(`成功添加 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个文件失败` : ''}`);
      } else if (errorCount > 0) {
        showStatus('所有文件处理失败', true);
      }
    } catch (error) {
      showStatus('文件处理失败: ' + error.message, true);
    } finally {
      showLoading(false);
    }
  }

  // 导出所有文件
  function exportAll() {
    showLoading(true);
    try {
      const text = dp.generateCombinedText();
      if (!text) {
        throw new Error('没有可导出的内容');
      }

      const blob = new Blob([text], {type: 'text/plain'});
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = '文档导出.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showStatus('导出成功');
    } catch (error) {
      showStatus('导出失败: ' + error.message, true);
    } finally {
      showLoading(false);
    }
  }

  // 事件监听
  if (uploadArea) {
    uploadArea.addEventListener('click', () => {
      if (fileInput) fileInput.click();
    });
  }

  if (fileInput) {
    fileInput.addEventListener('change', (e) => {
      if (e.target && e.target.files && e.target.files.length > 0) {
        handleFiles(Array.from(e.target.files));
        fileInput.value = '';
      }
    });
  }

  if (uploadArea) {
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#4CAF50';
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.style.borderColor = '#ccc';
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#ccc';
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFiles(Array.from(e.dataTransfer.files));
      }
    });
  }

  if (exportBtn) {
    exportBtn.addEventListener('click', exportAll);
  }

  if (clearBtn) {
    clearBtn.addEventListener('click', () => {
      dp.clear();
      if (fileList) fileList.innerHTML = '';
      if (previewArea) previewArea.innerHTML = '';
    });
  }

  // 解析到多维表格功能
  if (parseToTableBtn) {
    parseToTableBtn.addEventListener('click', parseToTable);
  }

  // 解析到多维表格
  async function parseToTable() {
    // 检查是否有上传的文件
    const files = dp.getFileList();
    if ((!files.word || files.word.length === 0) && (!files.excel || files.excel.length === 0)) {
      showStatus('请先上传文档', true);
      return;
    }

    // 获取输入值
    const oaId = oaIdInput ? oaIdInput.value.trim() : '123456';
    const flowSign = flowSignInput ? flowSignInput.value.trim() : 'sdads';
    const env = envSelect ? envSelect.value : 'beta'; // 获取环境选择值，默认为beta

    if (!oaId || !flowSign) {
      showStatus('请输入OA ID和Flow Sign', true);
      return;
    }

    // 获取文档内容
    const policyContent = dp.generateCombinedText();
    if (!policyContent) {
      showStatus('无法获取文档内容', true);
      return;
    }

    // 准备请求数据
    const requestData = {
      inputs: {
        policyContent: policyContent,
        oa_id: oaId,
        flow_sign: flowSign,
        env: env // 添加环境参数
      },
      response_mode: "streaming",
      user: "abc-123"
    };

    // 显示加载状态
    showLoading(true);

    // 清空之前的结果
    if (resultContent) {
      resultContent.innerHTML = '';
    }

    try {
      // 创建 EventSource 连接
      const response = await fetch('https://api.dify.ai/v1/workflows/run', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer app-Byb3KybatBEitafDTuaH7mWp',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // 结果区域已默认显示，无需额外设置

      // 读取流数据
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // 解码二进制数据
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理 SSE 格式数据
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.substring(5).trim();
            if (data === '[DONE]') {
              // 流结束
              if (resultContent) {
                const doneElement = document.createElement('div');
                doneElement.className = 'event-done';
                doneElement.textContent = '处理成功！';
                resultContent.appendChild(doneElement);
                resultContent.scrollTop = resultContent.scrollHeight;
              }
              continue;
            }

            // 处理 ping 事件
            if (line.includes('event: ping')) {
              if (resultContent) {
                const pingElement = document.createElement('div');
                pingElement.className = 'event-ping';
                pingElement.textContent = 'event: ping';
                resultContent.appendChild(pingElement);
                resultContent.scrollTop = resultContent.scrollHeight;
              }
              continue;
            }

            try {
              const jsonData = JSON.parse(data);

              // 创建新的元素来显示事件
              const eventElement = document.createElement('div');

              // 根据事件类型设置不同的样式和内容
              if (jsonData.event === 'message' && jsonData.data && jsonData.data.text) {
                // 普通消息事件
                eventElement.className = 'event-message';
                eventElement.textContent = jsonData.data.text;
              } else if (jsonData.event === 'node_started') {
                // 节点开始事件
                eventElement.className = 'event-node';
                const nodeInfo = jsonData.data;
                const title = nodeInfo.title || '未知节点';
                const nodeType = nodeInfo.node_type || '未知类型';
                const eventText = `[节点开始] ${title} (${nodeType})`;
                eventElement.textContent = eventText;

                // 检查是否包含 "(end)"
                if (eventText.includes('(end)')) {
                  // 标记这个元素，以便后续检查
                  eventElement.dataset.isEndNode = 'true';
                }
              } else if (jsonData.event === 'node_finished') {
                // 节点结束事件
                eventElement.className = 'event-node';
                eventElement.textContent = `[node_finished] ${JSON.stringify(jsonData.data)}`;
                eventElement.dataset.isNodeFinished = 'true';
              } else {
                // 其他事件类型
                eventElement.textContent = `[${jsonData.event}] ${JSON.stringify(jsonData.data)}`;
              }

              // 添加到结果区域
              if (resultContent && eventElement.textContent) {
                resultContent.appendChild(eventElement);

                // 检查特定模式：上一个元素是包含 "(end)" 的节点开始事件，当前是节点结束事件
                const previousElement = eventElement.previousElementSibling;
                if (previousElement &&
                    previousElement.dataset.isEndNode === 'true' &&
                    eventElement.dataset.isNodeFinished === 'true') {
                  // 显示自定义弹窗提示
                  showCustomAlert('数据解析结束，请检查多维表格数据，有问题请联系rugen.wang');
                }

                // 自动滚动到底部
                resultContent.scrollTop = resultContent.scrollHeight;
              }
            } catch (e) {
              console.error('解析SSE数据失败:', e, data);

              // 显示原始数据
              if (resultContent) {
                const rawElement = document.createElement('div');
                rawElement.className = 'event-error';
                rawElement.textContent = `[原始数据] ${data}`;
                resultContent.appendChild(rawElement);
                resultContent.scrollTop = resultContent.scrollHeight;
              }
            }
          } else if (line.startsWith('event:')) {
            // 处理事件行
            if (resultContent) {
              const eventElement = document.createElement('div');
              eventElement.className = 'event-ping';
              eventElement.textContent = line.trim();
              resultContent.appendChild(eventElement);
              resultContent.scrollTop = resultContent.scrollHeight;
            }
          }
        }
      }

      showStatus('解析完成');
    } catch (error) {
      console.error('解析到多维表格失败:', error);
      showStatus('解析失败: ' + error.message, true);

      // 显示错误信息
      if (resultContent) {
        resultContent.innerHTML = '';
        const errorElement = document.createElement('div');
        errorElement.className = 'event-error';
        errorElement.textContent = '解析失败: ' + error.message;
        resultContent.appendChild(errorElement);
      }
    } finally {
      showLoading(false);
    }
  }
});