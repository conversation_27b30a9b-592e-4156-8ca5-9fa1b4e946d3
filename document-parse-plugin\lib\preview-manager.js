/**
 * 预览管理器 - 支持多种格式的实时预览
 */
class PreviewManager {
  constructor() {
    this.previewWindows = new Map();
    this.previewData = new Map();
  }

  /**
   * 显示格式预览
   */
  showPreview(format, content, title = '预览') {
    const previewId = `preview-${format}-${Date.now()}`;
    
    // 创建预览窗口
    const previewWindow = this.createPreviewWindow(previewId, title, format);
    
    // 渲染内容
    this.renderPreviewContent(previewWindow, format, content);
    
    // 存储预览数据
    this.previewData.set(previewId, { format, content, title });
    this.previewWindows.set(previewId, previewWindow);
    
    return previewId;
  }

  /**
   * 创建预览窗口
   */
  createPreviewWindow(previewId, title, format) {
    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.className = 'preview-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    // 创建预览窗口
    const previewWindow = document.createElement('div');
    previewWindow.className = 'preview-window';
    previewWindow.style.cssText = `
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      width: 90%;
      height: 90%;
      max-width: 1200px;
      max-height: 800px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    `;

    // 创建标题栏
    const titleBar = document.createElement('div');
    titleBar.className = 'preview-title-bar';
    titleBar.style.cssText = `
      background: #f5f5f5;
      padding: 15px 20px;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;

    const titleElement = document.createElement('h3');
    titleElement.textContent = `${title} - ${format.toUpperCase()}格式预览`;
    titleElement.style.cssText = `
      margin: 0;
      color: #333;
      font-size: 16px;
    `;

    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.style.cssText = `
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: #666;
      padding: 5px 10px;
      border-radius: 4px;
    `;
    closeButton.onmouseover = () => closeButton.style.background = '#e0e0e0';
    closeButton.onmouseout = () => closeButton.style.background = 'none';
    closeButton.onclick = () => this.closePreview(previewId);

    titleBar.appendChild(titleElement);
    titleBar.appendChild(closeButton);

    // 创建内容区域
    const contentArea = document.createElement('div');
    contentArea.className = 'preview-content';
    contentArea.style.cssText = `
      flex: 1;
      overflow: auto;
      padding: 20px;
      background: white;
    `;

    // 创建工具栏
    const toolbar = document.createElement('div');
    toolbar.className = 'preview-toolbar';
    toolbar.style.cssText = `
      background: #f9f9f9;
      padding: 10px 20px;
      border-top: 1px solid #ddd;
      display: flex;
      gap: 10px;
      align-items: center;
    `;

    // 添加工具栏按钮
    const copyButton = this.createToolbarButton('📋 复制', () => this.copyContent(previewId));
    const downloadButton = this.createToolbarButton('💾 下载', () => this.downloadContent(previewId));
    const refreshButton = this.createToolbarButton('🔄 刷新', () => this.refreshPreview(previewId));

    toolbar.appendChild(copyButton);
    toolbar.appendChild(downloadButton);
    toolbar.appendChild(refreshButton);

    // 组装窗口
    previewWindow.appendChild(titleBar);
    previewWindow.appendChild(contentArea);
    previewWindow.appendChild(toolbar);
    overlay.appendChild(previewWindow);

    // 添加到页面
    document.body.appendChild(overlay);

    // 点击遮罩层关闭（可选）
    overlay.onclick = (e) => {
      if (e.target === overlay) {
        this.closePreview(previewId);
      }
    };

    return {
      overlay,
      window: previewWindow,
      content: contentArea,
      title: titleElement
    };
  }

  /**
   * 创建工具栏按钮
   */
  createToolbarButton(text, onClick) {
    const button = document.createElement('button');
    button.textContent = text;
    button.style.cssText = `
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    `;
    button.onmouseover = () => button.style.background = '#0056b3';
    button.onmouseout = () => button.style.background = '#007bff';
    button.onclick = onClick;
    return button;
  }

  /**
   * 渲染预览内容
   */
  renderPreviewContent(previewWindow, format, content) {
    const contentArea = previewWindow.content;
    contentArea.innerHTML = '';

    switch (format.toLowerCase()) {
      case 'html':
        this.renderHTMLPreview(contentArea, content);
        break;
      case 'markdown':
      case 'md':
        this.renderMarkdownPreview(contentArea, content);
        break;
      case 'json':
        this.renderJSONPreview(contentArea, content);
        break;
      case 'txt':
      case 'text':
        this.renderTextPreview(contentArea, content);
        break;
      default:
        this.renderTextPreview(contentArea, content);
    }
  }

  /**
   * 渲染HTML预览
   */
  renderHTMLPreview(container, content) {
    const iframe = document.createElement('iframe');
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      background: white;
    `;
    
    container.appendChild(iframe);
    
    // 写入HTML内容
    iframe.contentDocument.open();
    iframe.contentDocument.write(content);
    iframe.contentDocument.close();
  }

  /**
   * 渲染Markdown预览
   */
  renderMarkdownPreview(container, content) {
    // 创建两栏布局
    const layout = document.createElement('div');
    layout.style.cssText = `
      display: flex;
      height: 100%;
      gap: 20px;
    `;

    // 源码区域
    const sourceArea = document.createElement('div');
    sourceArea.style.cssText = `
      flex: 1;
      display: flex;
      flex-direction: column;
    `;

    const sourceTitle = document.createElement('h4');
    sourceTitle.textContent = 'Markdown源码';
    sourceTitle.style.cssText = `
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
    `;

    const sourceCode = document.createElement('pre');
    sourceCode.style.cssText = `
      flex: 1;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      overflow: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
    `;
    sourceCode.textContent = content;

    sourceArea.appendChild(sourceTitle);
    sourceArea.appendChild(sourceCode);

    // 预览区域
    const previewArea = document.createElement('div');
    previewArea.style.cssText = `
      flex: 1;
      display: flex;
      flex-direction: column;
    `;

    const previewTitle = document.createElement('h4');
    previewTitle.textContent = '渲染预览';
    previewTitle.style.cssText = `
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
    `;

    const previewContent = document.createElement('div');
    previewContent.style.cssText = `
      flex: 1;
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      overflow: auto;
    `;

    // 简单的Markdown渲染
    previewContent.innerHTML = this.simpleMarkdownRender(content);

    previewArea.appendChild(previewTitle);
    previewArea.appendChild(previewContent);

    layout.appendChild(sourceArea);
    layout.appendChild(previewArea);
    container.appendChild(layout);
  }

  /**
   * 简单的Markdown渲染
   */
  simpleMarkdownRender(markdown) {
    let html = markdown;
    
    // 标题
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
    
    // 粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // 表格
    html = html.replace(/\|(.+)\|/g, (match, content) => {
      if (content.includes('---')) {
        return ''; // 跳过分隔行
      }
      const cells = content.split('|').map(cell => cell.trim());
      const cellTags = cells.map(cell => `<td>${cell}</td>`).join('');
      return `<tr>${cellTags}</tr>`;
    });
    
    // 包装表格
    html = html.replace(/(<tr>.*<\/tr>)/gs, '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">$1</table>');
    
    // 段落
    html = html.replace(/\n\n/g, '</p><p>');
    html = '<p>' + html + '</p>';
    
    // 清理空段落
    html = html.replace(/<p><\/p>/g, '');
    
    return html;
  }

  /**
   * 渲染JSON预览
   */
  renderJSONPreview(container, content) {
    const jsonContainer = document.createElement('div');
    jsonContainer.style.cssText = `
      height: 100%;
      display: flex;
      flex-direction: column;
    `;

    // JSON格式化显示
    const jsonPre = document.createElement('pre');
    jsonPre.style.cssText = `
      flex: 1;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      overflow: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
      margin: 0;
    `;

    try {
      const parsed = typeof content === 'string' ? JSON.parse(content) : content;
      jsonPre.textContent = JSON.stringify(parsed, null, 2);
    } catch (e) {
      jsonPre.textContent = content;
    }

    jsonContainer.appendChild(jsonPre);
    container.appendChild(jsonContainer);
  }

  /**
   * 渲染文本预览
   */
  renderTextPreview(container, content) {
    const textPre = document.createElement('pre');
    textPre.style.cssText = `
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      overflow: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      height: 100%;
    `;
    textPre.textContent = content;
    container.appendChild(textPre);
  }

  /**
   * 复制内容到剪贴板
   */
  async copyContent(previewId) {
    const data = this.previewData.get(previewId);
    if (!data) return;

    try {
      await navigator.clipboard.writeText(data.content);
      this.showToast('内容已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
      this.showToast('复制失败', 'error');
    }
  }

  /**
   * 下载内容
   */
  downloadContent(previewId) {
    const data = this.previewData.get(previewId);
    if (!data) return;

    const extensions = {
      html: 'html',
      markdown: 'md',
      md: 'md',
      json: 'json',
      txt: 'txt',
      text: 'txt'
    };

    const ext = extensions[data.format.toLowerCase()] || 'txt';
    const filename = `${data.title}_${new Date().toISOString().slice(0, 10)}.${ext}`;

    const blob = new Blob([data.content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.showToast(`文件已下载: ${filename}`);
  }

  /**
   * 刷新预览
   */
  refreshPreview(previewId) {
    const data = this.previewData.get(previewId);
    const window = this.previewWindows.get(previewId);
    
    if (data && window) {
      this.renderPreviewContent(window, data.format, data.content);
      this.showToast('预览已刷新');
    }
  }

  /**
   * 关闭预览
   */
  closePreview(previewId) {
    const window = this.previewWindows.get(previewId);
    if (window && window.overlay) {
      document.body.removeChild(window.overlay);
      this.previewWindows.delete(previewId);
      this.previewData.delete(previewId);
    }
  }

  /**
   * 显示提示消息
   */
  showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'error' ? '#dc3545' : '#28a745'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10001;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 3000);
  }

  /**
   * 关闭所有预览窗口
   */
  closeAllPreviews() {
    this.previewWindows.forEach((window, previewId) => {
      this.closePreview(previewId);
    });
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PreviewManager;
} else {
  window.PreviewManager = PreviewManager;
}
