import json
import re
from typing import Any, Dict, Union, Tuple, List, Optional

def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default

def convert_values_to_strings(obj: Union[Dict[str, Any], List[Dict[str, Any]]]) -> Union[Dict[str, str], List[Dict[str, str]]]:
    """
    将JSON对象中的所有值转换为字符串类型
    如果值是数组，则转换为逗号分隔的字符串
    对于null值，转换为空字符串
    对于布尔值，保持小写形式
    
    参数:
        obj: Union[Dict[str, Any], List[Dict[str, Any]]] - 要转换的JSON对象或数组
        
    返回:
        Union[Dict[str, str], List[Dict[str, str]]] - 转换后的对象，所有值都是字符串类型
    """
    if isinstance(obj, list):
        # 处理JSON数组
        return [convert_values_to_strings(item) for item in obj]
    elif isinstance(obj, dict):
        # 处理JSON对象
        result = {}
        for key, value in obj.items():
            if value is None:
                # null值转换为空字符串
                result[key] = ""
            elif isinstance(value, (list, tuple)):
                # 数组类型转换为逗号分隔的字符串
                result[key] = ','.join(str(item) for item in value)
            elif isinstance(value, dict):
                # 嵌套字典递归处理
                result[key] = convert_values_to_strings(value)
            elif isinstance(value, bool):
                # 布尔值转换为小写字符串
                result[key] = str(value).lower()
            else:
                # 其他类型直接转换为字符串
                result[key] = str(value)
        return result
    else:
        # 非字典和数组类型直接转换为字符串
        return str(obj)

def parse_json_string(input_str: str) -> Tuple[Union[List[Dict[str, Any]], Dict[str, Any], None], Dict[str, str]]:
    """
    解析JSON字符串并返回解析结果和状态信息
    支持解析JSON对象或JSON数组
    使用safe_json_parse方法进行健壮的JSON解析
    
    参数:
        input_str: str - 输入的JSON字符串
        
    返回:
        Tuple[Union[List[Dict[str, Any]], Dict[str, Any], None], Dict[str, str]] - (解析后的JSON对象或数组, 状态信息)
        状态信息包含:
            - status: "success" 或 "error"
            - message: 成功或错误信息
    """
    # 检查输入是否为空
    if not input_str or not isinstance(input_str, str):
        return None, {
            "status": "error",
            "message": "输入为空或不是字符串类型"
        }
    
    # 去除首尾空白字符
    input_str = input_str.strip()
    
    # 使用safe_json_parse方法解析JSON
    json_obj = safe_json_parse(input_str)
    
    # 检查解析结果
    if json_obj is None:
        return None, {
            "status": "error",
            "message": "无法解析JSON字符串"
        }
    
    # 检查解析后的对象类型
    if isinstance(json_obj, list):
        # 验证数组中的每个元素是否为字典类型
        for item in json_obj:
            if not isinstance(item, dict):
                return None, {
                    "status": "error",
                    "message": "JSON数组中的元素必须是对象类型"
                }
        return json_obj, {
            "status": "success",
            "message": "解析成功，返回JSON数组"
        }
    elif isinstance(json_obj, dict):
        return json_obj, {
            "status": "success",
            "message": "解析成功，返回JSON对象"
        }
    else:
        return None, {
            "status": "error",
            "message": "JSON格式正确，但既不是对象类型也不是数组类型"
        }

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数入口，处理JSON解析请求
    
    参数:
        param: Dict[str, Any] - 输入参数，需要包含 analysisResult 字段
        
    返回:
        Dict[str, Any] - 包含状态和结果的字典
        {
            "status": "success" 或 "error",
            "message": "状态描述",
            "data": 解析后的数据（仅在成功时存在，始终为列表格式）
        }
    """
    try:
        # 检查输入参数
        if not isinstance(param, dict) or 'analysisResult' not in param:
            return {
                "status": "error",
                "message": "缺少必要的输入参数 analysisResult"
            }

        analysis_result = param['analysisResult']
        if not analysis_result:
            return {
                "status": "error",
                "message": "analysisResult 为空"
            }

        # 解析JSON
        result, status = parse_json_string(analysis_result)
        
        # 如果解析失败，直接返回错误状态
        if status["status"] != "success":
            return status
            
        # 将所有值转换为字符串类型
        string_result = convert_values_to_strings(result)
        
        # 确保输出始终是列表格式
        if isinstance(string_result, dict):
            # 如果是单一对象，转换为包含单个元素的列表
            string_result = [string_result]
            
        # 解析成功，返回完整结果
        return {
            "status": "success",
            "message": "解析成功",
            "data": string_result
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"解析数据流程异常: {str(e)}"
        }

