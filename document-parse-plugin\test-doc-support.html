<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>DOC文件支持测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .upload-area {
      border: 2px dashed #ccc;
      padding: 40px;
      text-align: center;
      border-radius: 10px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .upload-area:hover {
      border-color: #007bff;
      background-color: #f8f9ff;
    }
    .upload-area.dragover {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
    .file-input {
      display: none;
    }
    .result-area {
      margin-top: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #f9f9f9;
      min-height: 200px;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .content-preview {
      margin-top: 15px;
      padding: 15px;
      background: white;
      border-radius: 5px;
      border: 1px solid #ddd;
      max-height: 400px;
      overflow-y: auto;
    }
    .debug-info {
      margin-top: 15px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      color: #666;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #0056b3;
    }
    .library-status {
      margin-bottom: 20px;
      padding: 15px;
      background: #e9ecef;
      border-radius: 5px;
    }
    .library-item {
      margin: 5px 0;
      padding: 5px;
      border-radius: 3px;
    }
    .library-item.loaded {
      background: #d4edda;
      color: #155724;
    }
    .library-item.missing {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>📄 DOC文件支持测试</h1>

    <!-- 库状态检查 -->
    <div class="library-status">
      <h3>📚 库加载状态</h3>
      <div id="libraryStatus"></div>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-area" id="uploadArea">
      <p>📁 点击或拖拽.doc文件到这里</p>
      <p style="font-size: 14px; color: #666;">支持.doc和.docx文件</p>
      <input type="file" id="fileInput" class="file-input" accept=".doc,.docx">
    </div>

    <!-- 测试按钮 -->
    <div style="text-align: center;">
      <button onclick="testDocParser()">🧪 测试DOC解析器</button>
      <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <!-- 结果显示区域 -->
    <div class="result-area" id="resultArea">
      <p style="color: #666; text-align: center;">等待文件上传...</p>
    </div>
  </div>

  <!-- 加载必要的库 -->
  <script src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
  <script src="lib/mammoth.browser.min.js"></script>
  <script src="lib/cfb-lite.js"></script>
  <script src="lib/advanced-doc-extractor.js"></script>
  <script src="lib/doc-parser.js"></script>
  <script src="lib/doc-conversion-helper.js"></script>
  <script src="lib/doc-jszip-parser.js"></script>

  <script>
    // 检查库加载状态
    function checkLibraryStatus() {
      const libraries = [
        { name: 'JSZip', check: () => typeof JSZip !== 'undefined' },
        { name: 'Mammoth', check: () => typeof mammoth !== 'undefined' },
        { name: 'CFBLite', check: () => typeof CFBLite !== 'undefined' },
        { name: 'AdvancedDocExtractor', check: () => typeof AdvancedDocExtractor !== 'undefined' },
        { name: 'DocParser', check: () => typeof DocParser !== 'undefined' },
        { name: 'DocConversionHelper', check: () => typeof DocConversionHelper !== 'undefined' },
        { name: 'DocJSZipParser', check: () => typeof DocJSZipParser !== 'undefined' }
      ];

      const statusDiv = document.getElementById('libraryStatus');
      statusDiv.innerHTML = '';

      libraries.forEach(lib => {
        const div = document.createElement('div');
        div.className = `library-item ${lib.check() ? 'loaded' : 'missing'}`;
        div.textContent = `${lib.name}: ${lib.check() ? '✅ 已加载' : '❌ 未加载'}`;
        statusDiv.appendChild(div);
      });
    }

    // 初始化DOC解析器
    let docParser = null;
    let docJSZipParser = null;

    try {
      docParser = new DocParser();
      console.log('DOC解析器初始化成功');
    } catch (error) {
      console.error('DOC解析器初始化失败:', error);
    }

    try {
      docJSZipParser = new DocJSZipParser();
      console.log('JSZip DOC解析器初始化成功');
    } catch (error) {
      console.error('JSZip DOC解析器初始化失败:', error);
    }

    // 文件上传处理
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const resultArea = document.getElementById('resultArea');

    uploadArea.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽支持
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFile(files[0]);
      }
    });

    function handleFileSelect(e) {
      const file = e.target.files[0];
      if (file) {
        handleFile(file);
      }
    }

    async function handleFile(file) {
      showStatus('info', `开始处理文件: ${file.name}`);

      try {
        const arrayBuffer = await file.arrayBuffer();
        const ext = file.name.split('.').pop().toLowerCase();

        let result;
        if (ext === 'doc') {
          result = await parseDocFile(arrayBuffer, file.name);
        } else if (ext === 'docx') {
          result = await parseDocxFile(arrayBuffer, file.name);
        } else {
          throw new Error('不支持的文件类型');
        }

        displayResult(result, file.name);

      } catch (error) {
        showStatus('error', `处理失败: ${error.message}`);
        console.error('文件处理错误:', error);
      }
    }

    async function parseDocFile(arrayBuffer, fileName) {
      // 优先使用JSZip解析器
      if (docJSZipParser) {
        try {
          showStatus('info', '使用JSZip解析器处理文件...');
          const result = await docJSZipParser.parseDocFile(arrayBuffer, fileName);

          if (result.success) {
            showStatus('success', `JSZip解析成功! 方法: ${result.method}`);
            return result;
          } else {
            showStatus('warning', 'JSZip解析失败，尝试传统解析器...');
          }
        } catch (error) {
          showStatus('warning', `JSZip解析器错误: ${error.message}`);
        }
      }

      // 回退到传统DOC解析器
      if (docParser) {
        try {
          showStatus('info', '使用传统DOC解析器处理文件...');
          const result = await docParser.parseDocFile(arrayBuffer);
          showStatus('success', '传统DOC解析器成功!');
          return result;
        } catch (error) {
          showStatus('error', `传统解析器也失败: ${error.message}`);
          throw error;
        }
      }

      throw new Error('没有可用的DOC解析器');
    }

    async function parseDocxFile(arrayBuffer, fileName) {
      showStatus('info', '使用Mammoth处理DOCX文件...');

      if (typeof mammoth === 'undefined') {
        throw new Error('Mammoth库未加载');
      }

      const htmlResult = await mammoth.convertToHtml({arrayBuffer});
      const textResult = await mammoth.extractRawText({arrayBuffer});

      showStatus('success', 'DOCX文件解析成功!');
      return {
        html: htmlResult.value,
        text: textResult.value,
        tables: []
      };
    }

    function displayResult(result, fileName) {
      const html = `
        <h3>📄 ${fileName} - 解析结果</h3>

        <h4>📝 文本内容 (${result.text ? result.text.length : 0} 字符)</h4>
        <div class="content-preview">
          <pre style="white-space: pre-wrap; font-family: inherit;">${result.text || '无文本内容'}</pre>
        </div>

        <h4>🌐 HTML内容</h4>
        <div class="content-preview">
          ${result.html || '<p>无HTML内容</p>'}
        </div>

        <h4>📊 表格数据</h4>
        <div class="content-preview">
          ${result.tables && result.tables.length > 0 ?
            `<p>找到 ${result.tables.length} 个表格</p>` :
            '<p>未找到表格</p>'}
        </div>

        <div class="debug-info">
          <strong>调试信息:</strong><br>
          文本长度: ${result.text ? result.text.length : 0}<br>
          HTML长度: ${result.html ? result.html.length : 0}<br>
          表格数量: ${result.tables ? result.tables.length : 0}<br>
          解析时间: ${new Date().toLocaleTimeString()}
        </div>
      `;

      resultArea.innerHTML = html;
    }

    function showStatus(type, message) {
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;

      // 插入到结果区域顶部
      resultArea.insertBefore(statusDiv, resultArea.firstChild);

      // 3秒后自动移除状态消息
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 3000);
    }

    function testDocParser() {
      showStatus('info', '开始测试DOC解析器...');

      if (!docParser) {
        showStatus('error', 'DOC解析器未初始化');
        return;
      }

      // 创建一个包含测试文本的DOC文件结构
      const testData = new Uint8Array(2048);

      // DOC文件头
      const docHeader = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
      docHeader.forEach((byte, index) => {
        testData[index] = byte;
      });

      // 在文件中添加一些测试文本（UTF-16LE编码）
      const testText = "Hello World! 这是一个测试文档。";
      const textOffset = 1024;

      for (let i = 0; i < testText.length; i++) {
        const charCode = testText.charCodeAt(i);
        testData[textOffset + i * 2] = charCode & 0xFF;
        testData[textOffset + i * 2 + 1] = (charCode >> 8) & 0xFF;
      }

      console.log('测试数据创建完成，开始解析...');

      docParser.parseDocFile(testData.buffer)
        .then(result => {
          showStatus('success', '测试完成 - DOC解析器工作正常');
          console.log('解析结果:', result);
          displayResult(result, 'test.doc');
        })
        .catch(error => {
          showStatus('error', `测试失败: ${error.message}`);
          console.error('测试错误:', error);
        });
    }

    function clearResults() {
      resultArea.innerHTML = '<p style="color: #666; text-align: center;">等待文件上传...</p>';
    }

    // 页面加载完成后检查库状态
    window.addEventListener('load', () => {
      checkLibraryStatus();
      showStatus('info', '页面加载完成，可以开始测试DOC文件支持');
    });
  </script>
</body>
</html>
