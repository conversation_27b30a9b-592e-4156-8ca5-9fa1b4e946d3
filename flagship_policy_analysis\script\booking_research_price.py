# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
import requests # type: ignore
from dataclasses import dataclass

@dataclass
class ConfigError:
    """配置错误信息数据类"""
    status: int
    message: str

def handle_request_exception(e: Exception) -> ConfigError:
    """统一处理请求异常"""
    if isinstance(e, requests.exceptions.HTTPError):
        return ConfigError(1, f"http错误: {str(e)}")
    elif isinstance(e, requests.exceptions.ConnectionError):
        return ConfigError(1, "连接错误")
    elif isinstance(e, requests.exceptions.Timeout):
        return ConfigError(1, "超时错误")
    elif isinstance(e, requests.exceptions.RequestException):
        return ConfigError(1, "请求错误")
    else:
        return ConfigError(1, "未知错误")

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }

    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }

    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }

    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }

    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }
    productTag = mapping_data.get("productTag", "")
    if not productTag:
        return {
            "status": "error",
            "message": "productTag 不能为空",
            "data": None
        }
    # 赋值为子tag
    productTag = productTag + "1"
    
    carrier = mapping_data.get("carrier", "")
    if not carrier:
        return {
            "status": "error",
            "message": "carrier 不能为空",
            "data": None
        }

    # 获取qconfig的配置，并生成新的id和fare_type
    newData = process_qconfig_content(param)
    if newData["status"] != "success":
        return {
            "status": "error",
            "message": newData["message"],
            "data": None
        }

    # 会员限制为空，则跳过配置
    membership_limit = analysis_data.get("membership_limit", "")
    if not membership_limit or membership_limit not in ["新会员", "老会员"]:
        return {
            "status": "ignore",
            "message": "未满足会员软拦截配置场景",
            "data": None
        }

    # 根据会员限制设置ruleType
    if membership_limit == "新会员":
        rule_type = "6"
    elif membership_limit == "老会员":
        rule_type = "7"
    else:
        rule_type = "8"
    
    # 设置viewTextDetail根据不同条件
    if membership_limit == "新会员":
        viewTextDetail = "因您所选乘机人\"${#peoples}\"不满足【航司新会员规则】,不能购买该产品，已为您更换符合航司要求的报价。"
    else:
        viewTextDetail = "因您所选乘机人\"${#peoples}\"不满足【航司会员规则】,不能购买该产品，已为您更换符合航司要求的报价。"
    
    # 构建结果对象
    result_obj = {
            "id": newData["data"]["id"],
            "ruleType": rule_type,
            "ageRange": "0-110",
            "passengerNum": "",
            "currentTagCode": productTag,
            "currentProductMark": product_mark,
            "currentVirtualTagCode": "ABC1,ABC6,ABC8,ABC9,ABC10,ABC11,ABC12,ABC13,ABC14,ABC15",
            "tagCode": "TYL1,CZA1,JXL1",
            "productMark": "ALL",
            "domain": "ALL",
            "priceDiff": "-100",
            "carrierCode": carrier,
            "viewTextDetail": viewTextDetail,
            "productBlackList":"101206,105078",
            "activityCodeBlackList": "7",
            "idCardType": "NI",
            "needCheck": "true",
            "silentNotification": "true",
            "returnFeeDiff": 30,
            "changeFeeDiff": 30,
            "baggageDiff": 5,
            "viewTextDetailAuto": viewTextDetail,
            "region": "ALL",
            "userLabel": "ALL",
            "realNameAuth": "ALL",
            "site": mapping_data.get("carrierSite", ""),
            "passengerCount": 2,
            "realTimeLabel": "",
            "passengerRealNameAuth": "ALL",
            "silentButtonText": "",
            "silentButtonAction": "0",
            "regionLength": 3
    }

    return {
        "data": result_obj,
        "status": "success",
        "message": "操作成功"
    }


def process_qconfig_content(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理qconfig配置内容，选择最优tag

    参数:
    param: Dict[str, Any] - 请求参数
    analysis_data: Dict[str, Any] - 分析数据
    mapping_data: Dict[str, Any] - 映射数据

    返回:
    Dict[str, Any] - 包含选择的最优tag的结果
    """
    # 步骤一和二：GET请求获取当前配置
    try:
        get_url = "http://qconfig.corp.qunar.com/restapi/configs"
        get_params = {
            'token': '7B1ED8DC06716464C77A3547F7567616',
            'groupid': 'f_pangu',
            'dataid': 'booking_research_price.t',
            'env': param['env'],
            "subenv": param.get('subenv', ''),
            'targetgroupid': 'f_twell_domestic'
        }

        get_response = requests.get(get_url, params=get_params)
        get_response.raise_for_status()
        get_result = get_response.json()

    except Exception as e:
        error = handle_request_exception(e)
        return {
            "status": "error",
            "message": error.message,
            "data": None
        }

    # 步骤三：处理GET返回结果
    if get_result.get('status') != 0:
        return {
            "status": "error",
            "message": get_result.get('message', '获取配置失败'),
            "data": None
        }

    config_data = get_result['data'].get('data', '')
    config_list = json.loads(config_data) if config_data else []

    # 航司
    maxId = 0
    for item in config_list:
        try:
            columns = item.get('columns');
            # 判断columns是否为空
            if not columns:
                continue
                
            # 判断columns是否为字典类型
            if not isinstance(columns, dict):
                continue
            current_id = int(columns.get('id', 0))
            maxId = max(maxId, current_id)
        except (ValueError, TypeError):
            continue

    # 生成新的id（最大值+1）
    new_id = str(maxId + 1)
    return {
        "status": "success",
        "message": "操作成功",
        "data": {
            "id": new_id
        }
    }


def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗

    参数:
    part_str: str - 待解析的字符串部分

    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result

    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]

    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段

        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()

        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识

        result[key] = value

    return result