import json
import re
import requests
from typing import Any, Dict, Optional, Union

def build_request_url(callId: str) -> tuple[str, Dict[str, str]]:
    """
    构造请求的URL和参数
    """
    base_url = f"https://hcallcenter.corp.qunar.com/callcenter/logs/queryTextByCallUuid?callUuid={callId}"
    return base_url

def query_data_from_api(url: str, params: Dict[str, str], headers: Dict[str, str]) -> Dict[str, Any]:
    """
    发起HTTP GET请求并获取数据
    """
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": f"请求失败: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析失败: {str(e)}"}
    except Exception as e:
        return {"error": f"发生错误: {str(e)}"}

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        HEADER_SERVER_TOKEN = "Q-Server-Token"
        HEADER_APP_CODE = "Q-App-Code"
        # Make POST request with proxyData as JSON
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        response = requests.post(
            proxy, json=proxyData, headers=headers
        )

        response.raise_for_status() 
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }
    

def mask_sensitive_info(data: Any) -> Any:
    """
    递归遍历数据，对敏感信息进行脱敏处理。
    敏感信息包括：身份证号、手机号、银行卡号。
    保留前三位数字，其余用*替换。

    Args:
        data (Any): 需要处理的数据，可以是字典、列表或基本类型

    Returns:
        Any: 处理后的数据
    """
    # 定义敏感信息的正则表达式模式
    patterns = {
        'id_card': r'^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$',
        'phone': r'^1[3-9]\d{9}$',
        'bank_card': r'^\d{16,19}$'
    }

    if isinstance(data, dict):
        # 处理字典
        result = {}
        for key, value in data.items():
            # 检查值是否为字符串且匹配敏感信息模式
            if isinstance(value, str):
                for pattern_name, pattern in patterns.items():
                    if re.match(pattern, value):
                        # 保留前三位，其余用*替换
                        value = value[:3] + '*' * (len(value) - 3)
                        break
            # 递归处理嵌套数据
            result[key] = mask_sensitive_info(value)
        return result
    elif isinstance(data, list):
        # 处理列表
        return [mask_sensitive_info(item) for item in data]
    else:
        # 基本类型直接返回
        return data



def main(param: Dict[str, str]) -> Dict[str, Any]:
    """
    主入口函数
    """
    # 从参数中提取必要的字段
    callId = param.get("call_id")
    appCode = param.get("invokeAppCode")
    invokeToken = param.get("invokeToken")
    
    # 检查参数是否完整
    if not callId:
        return {"error": "缺少必要的参数: callId", "results": []}

    # 构造请求的URL和参数
    url = build_request_url(callId) 
    data = param.get("data")
    print("--------------------------------url:",url)

    # 构造代理请求数据
    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    proxyData = {
        "method": "get",
        "url": url,
        "data": data,
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    # 发起请求并获取数据
    response_data = invoke_http_by_proxy(appCode, invokeToken, proxyData, proxy)

# 检查ret字段和data字段
    if not response_data or response_data.get("ret") is False:
        error_msg = (
                response_data.get("errmsg")
                if response_data
                else "Response data is empty or ret is false"
            )
        return {"success": False, "error": error_msg, "results": []}

        # 获取内层data
    inner_data = response_data.get("data")
    if not inner_data:
            return {"success": False, "error": "Inner data is empty", "results": []}
    # 确保inner_data是JSON格式
    if isinstance(inner_data, str):
            try:
                inner_data = json.loads(inner_data)
            except json.JSONDecodeError:
                return {"success": False, "error": "Failed to parse inner data as JSON", "data": {}}
        
    # 对敏感信息进行脱敏处理
    masked_data = mask_sensitive_info(inner_data)
    return {"success": True, "error": "", "results": masked_data}

def test():
    """
    测试函数，用于测试API调用
    """
    test_params = {
        "call_id": "01PK8UHSF0BAJA0MA425K2LAES059SN1",
        "invokeAppCode": "f_pangu",
        "invokeToken": "oABPAGnqeGY7y8OtsKjLiHQ2K1SzBuOLT8zYWyHCnviQX+KqugKDLwDcGpD8qRXSGTz2BFiDyWrwmI1otiXHGjlAIndwbOQh3LdaHBI7NR6s6G04LJB3fUFQHpVpjC4ISGT7gjw8IgiGR+b+ln/t0BsdLbkp1ZeHRbCZcgLw2Z0="
    }

    result = main(test_params)  
    print("Test Result:", json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test()
