import numbers
import re
from string import Formatter
import requests


import json
import base64
from datetime import datetime, date, timedelta
from csv import DictReader, Error as CSVError
from io import StringIO
import uuid
from typing import List, Dict, Any, Set, Tuple, Union, Optional
import time
from urllib.parse import quote


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj.strip() == ""
    return False  # 非容器类型且非 None 的视为非空


def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None


def search_urs_ai_session_datas(startTime: str, endTime: str) -> dict:
    """
    Search URS AI session data within a specified time range.

    Args:
        startTime (str): Start time in format 'YYYY-MM-DD HH:MM:SS'
        endTime (str): End time in format 'YYYY-MM-DD HH:MM:SS'

    Returns:
        dict: Response data with structure:
            {
                "error": Optional error message if something went wrong,
                "data": {
                    "items": List of conversation items,
                    "total": Total count,
                    "page": Current page
                },
                "status": Response status (0 for success)
            }
    """
    url = "http://presale.ai.corp.qunar.com/dataAnalysis/queryLastIntentAnalyseRecord"
    params = {"startTime": startTime, "endTime": endTime}
    headers = {"Content-Type": "application/json"}

    try:
        # Set timeout to avoid hanging
        response = requests.get(url, params=params, headers=headers)

        # Check HTTP status code
        response.raise_for_status()

        # Try to parse JSON response
        response_data = response.json()

        # Validate response structure
        if not isinstance(response_data, dict):
            return {
                "error": "响应数据格式错误，预期为JSON对象",
                "data": None,
                "status": -1,
            }

        # Check status and message
        status = response_data.get("status")
        if status != 0:
            return {
                "error": response_data.get("msg", "请求失败"),
                "data": None,
                "status": status,
            }

        # Validate data structure
        data = response_data.get("data", {})
        if not isinstance(data, dict) or "items" not in data:
            return {
                "error": "响应数据结构不完整，缺少必要字段",
                "data": None,
                "status": -1,
            }

        return {"error": None, "data": data, "status": status}

    except requests.exceptions.Timeout:
        return {
            "error": "请求超时，请检查网络连接或稍后重试",
            "data": None,
            "status": 408,
        }
    except requests.exceptions.ConnectionError:
        return {"error": "网络连接错误，请检查网络连接", "data": None, "status": 503}
    except requests.exceptions.HTTPError as e:
        return {
            "error": f"HTTP请求错误: {str(e)}",
            "data": None,
            "status": response.status_code if "response" in locals() else 500,
        }
    except requests.exceptions.RequestException as e:
        return {"error": f"请求异常: {str(e)}", "data": None, "status": 500}
    except json.JSONDecodeError:
        return {
            "error": "响应数据解析失败，返回的不是有效的JSON格式",
            "data": None,
            "status": 500,
        }
    except Exception as e:
        return {"error": f"未知错误: {str(e)}", "data": None, "status": 500}


def buildSearchAndFilterUrsAiDataParam(param: dict) -> dict:
    """
    Build search parameters for URS AI data based on input parameters.

    Args:
        param (dict): Input parameters containing requestTime, startDay, endDay, and intentList

    Returns:
        dict: Formatted search parameters with startTime, endTime, and intentList
    """
    requestTime = param.get("requestTime")
    startDay = param.get("startDay")
    endDay = param.get("endDay")
    intentList = param.get("intentList")

    # Parse requestTime to get base date
    requestDate = safe_parse_datetime(requestTime)
    if not requestDate:
        return {
            "error": f"Invalid requestTime format: {requestTime}",
            "startTime": None,
            "endTime": None,
            "intentList": None,
        }

    # Convert requestDate to midnight of the same day
    if isinstance(requestDate, datetime):
        requestDate = datetime.combine(requestDate.date(), datetime.min.time())
    else:  # If it's already a date object
        requestDate = datetime.combine(requestDate, datetime.min.time())

    # Calculate startTime and endTime
    try:
        startDayInt = int(startDay) if startDay is not None else 0
        endDayInt = int(endDay) if endDay is not None else 0
        startTime = (requestDate + timedelta(days=startDayInt)).strftime(
            "%Y-%m-%d 00:00:00"
        )
        endTime = (requestDate + timedelta(days=endDayInt)).strftime(
            "%Y-%m-%d 00:00:00"
        )
    except (ValueError, TypeError, Exception) as e:
        return {
            "error": f"Invalid day values - startDay: {startDay}, endDay: {endDay}. Error: {str(e)}",
            "startTime": None,
            "endTime": None,
            "intentList": None,
        }

    # Handle intentList parameter
    processedIntentList = []
    if intentList:
        if isinstance(intentList, list):
            processedIntentList = intentList
        elif isinstance(intentList, str):
            processedIntentList = [
                intent.strip() for intent in intentList.split(";") if intent.strip()
            ]

    return {
        "error": None,
        "startTime": startTime,
        "endTime": endTime,
        "intentList": processedIntentList,
    }


def filterUrsAiData(
    ursAiDataItems: List[Dict], intentList: Optional[List[str]] = None
) -> List[Dict]:
    """
    Filter URS AI data items based on intent list.

    Args:
        ursAiDataItems (List[Dict]): List of URS AI data items to filter
        intentList (Optional[List[str]]): List of intents to filter by. If None or empty, returns all items.

    Returns:
        List[Dict]: Filtered list of URS AI data items
    """
    if not ursAiDataItems:
        return []

    # If no intent list provided or empty, return all items
    if not intentList:
        return ursAiDataItems

    filtered_items = []
    for item in ursAiDataItems:
        item_intent = item.get("intent", "")
        # Skip items without intent
        if not item_intent:
            continue

        # Check if item's intent matches any in the intent list
        if any(
            intent.strip().lower() == item_intent.strip().lower()
            for intent in intentList
        ):
            filtered_items.append(item)

    return filtered_items


def main(param):

    funeParam = buildSearchAndFilterUrsAiDataParam(param)
    if funeParam.get("error"):
        return {"error": funeParam.get("error"), "results": [], "recordCount": 0}

    ursAiDataQueryResult = search_urs_ai_session_datas(
        funeParam.get("startTime"), funeParam.get("endTime")
    )

    if ursAiDataQueryResult.get("error"):
        return {
            "error": ursAiDataQueryResult.get("error"),
            "results": [],
            "recordCount": 0,
        }

    ursAiData = ursAiDataQueryResult.get("data")

    if not ursAiData or len(ursAiData) == 0:
        return {"error": "URS AI数据为空", "results": [], "recordCount": 0}

    ursAiDataItems = ursAiData.get("items")
    if not ursAiDataItems or len(ursAiDataItems) == 0:
        return {"error": "URS AI数据为空", "results": [], "recordCount": 0}

    filteredUrsAiData = filterUrsAiData(ursAiDataItems, funeParam.get("intentList"))

    if not filteredUrsAiData or len(filteredUrsAiData) == 0:
        return {"error": "URS AI数据为空", "results": [], "recordCount": 0}

    return {
        "error": "",
        "results": filteredUrsAiData,
        "recordCount": len(filteredUrsAiData),
    }


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象安全地序列化为 JSON 并写入文件

    :param obj: 要序列化的对象，支持基础类型/列表/字典/自定义对象
    :param file_path: 目标文件路径（支持自动创建目录）
    :param encoding: 文件编码格式，默认utf-8
    :param ensure_ascii: 是否转义非ASCII字符，默认False保留Unicode
    :param indent: JSON缩进空格数，默认2（设为None可压缩输出）
    :param default: 自定义对象序列化函数，默认使用__dict__转换
    :param json_kwargs: 其他json.dump参数（如sort_keys等）
    :return: 是否成功写入文件

    :raises IOError: 当遇到文件系统级错误时会抛出（非返回False的情况）

    异常处理策略：
    - 类型错误：打印建议信息并返回False
    - 权限错误：打印错误路径并返回False
    - 其他错误：打印错误信息并返回False
    """
    try:
        target_path = Path(file_path)

        # 自动创建父目录（exist_ok防止竞态条件）
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的序列化
        serialize_default = default or (
            lambda o: o.__dict__ if hasattr(o, "__dict__") else repr(o)
        )

        # 使用上下文管理器确保文件正确关闭
        with target_path.open("w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(
            f"[序列化失败] 对象类型错误: {str(e)}，建议：1.检查数据类型 2.提供自定义序列化函数"
        )
    except PermissionError:
        print(f"[权限拒绝] 无法写入文件: {file_path}，请检查文件权限")
    except json.JSONEncodeError as e:
        print(f"[编码错误] 非法数据: {str(e)}，请检查特殊字符")
    except Exception as e:
        print(f"[系统错误] 操作异常: {str(e)}")

    return False


if __name__ == "__main__":

    param = {
        "requestTime": "2025-03-17 18:14:48",
        "startDay": -2,
        "endDay": -1,
        "intentList": "多次搜索时",
    }
    print(
        "请求参数Json:"
        + json.dumps(
            param,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    )
    reuslt = main(param)
    if reuslt.get("error"):
        print("获取记录失败，错误信息:" + reuslt.get("error"))
    else:
        print("获取记录条数:" + str(len(reuslt.get("results"))))
        # print("获取记录:" + json.dumps(
        #     reuslt.get("results"),
        #     ensure_ascii=False,
        #     separators=(",", ":"),  # 移除多余空格
        #     check_circular=True,
        # ))
