import json
import requests
from typing import Union, List, Set, Optional, Dict, Any, Tuple
from requests.exceptions import RequestException
from datetime import datetime, date
from urllib.parse import unquote_to_bytes
import numbers
import uuid
from io import StringIO
from csv import DictReader, Error as CSVError  
import base64
import time 
import re





TAMIAS_RESULT_DOWNLOAD_URL = (
    "http://tamias.corp.qunar.com/adhoc/externalserver/download/data?taskId="
)

UPS_PRICE_CHANGE_SQL = """
SELECT 
    dt,
    p_airline,
    p_carrier,
    p_user_name,
    p_flighttype,
    p_otaprice,
    p_otapricedetail,
    cutprice,
    activitycut,
    coupon,
    xproductcut,
    zerocard,
    expcut,
    infantprice,
    childprice,
    showprice,
    bareprice,
    baseprice,
    tag,
    oritag,
    adultcount,
    childcount,
    infantcount,
    averageprice,
    addprice,
    otafold,
    f_traceid,
    f_isautofill,
    f_isfristchoose,
    f_reducesumprice,
    f_adultnum,
    f_babynum,
    f_childnum,
    f_pricedetailitems,
    f_logdate,
    f_logtime,
    f_json_action
FROM 
    flight.dwd_flow_d_to_b_price_connect_detail_di
WHERE 
    dt = '{ursDate}'
    and f_traceid in ({traceIds})
    and p_user_name = '{username}'
    and p_flighttype = '单程'
"""

#todo 需要修改
def process_ext_json(item: Dict[str, Any]) -> None:
    """处理ext_json字段并添加URS前缀的新字段"""
    ext_json_str = item.get("ext_json")
    if not ext_json_str:
        return

    try:
        # 解析嵌套JSON字符串
        ext_data = json.loads(ext_json_str)

        # 定义需要提取的字段映射
        field_mapping = {
            "flightNos": "urs_flightNos",
            "depAndArrTime": "urs_depAndArrTime",
            "page": "urs_page",
            "routeType": "urs_routeType",
        }

        # 批量提取并重命名字段
        for origin_field, new_field in field_mapping.items():
            if origin_field in ext_data:
                item[new_field] = ext_data[origin_field]

    except json.JSONDecodeError as e:
        return
    except KeyError as e:
        return


def downloadResultFromUrl(url):
    try:
        # 1. 发送 HTTP 请求下载文件
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误状态码

    except requests.exceptions.RequestException as e:
        # print(f"[下载失败] 请求错误: {str(e)}")
        return "tamias结果文件下载失败！", None

    # 2. 处理内容编码（兼容 UTF-8 和 GBK）
    try:
        # 优先尝试 UTF-8 带 BOM 解码（常见于 Windows 生成的 CSV）
        content = response.content.decode("utf-8-sig")
    except UnicodeDecodeError:
        try:
            # 尝试 GBK 解码（常见中文编码）
            content = response.content.decode("gbk")
        except UnicodeDecodeError as e:
            # print(f"[解码失败] 不支持的编码格式: {str(e)}")
            return "tamias结果文件解析失败！", None

    # 3. 解析 CSV 内容
    try:
        csv_file = StringIO(content)
        reader = DictReader(csv_file)
        result = list(reader)
    except CSVError as e:
        # print(f"[解析失败] CSV 格式错误: {str(e)}")
        return "tamias结果文件解析失败！", None

    return None, result


#模拟查询请求、获取taskId
def submit_query(cookie, hql):
    url = "http://tamias.corp.qunar.com/query/server/submit"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "DNT": "1",
        "Origin": "http://tamias.corp.qunar.com",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    # Base64编码HQL
    encoded_hql = base64.b64encode(hql.encode()).decode()

    data = {
        "wareHouse": "flight",
        "hql": encoded_hql,
        "engineType": 2,
        "owner": "wanzhou.zheng",
        "comment": "",
        "description": "",
    }

    response = requests.post(
        url, headers=headers, json=data, verify=False  # 对应--insecure
    )
    # 解析JSON响应
    if response.status_code == 200:
        json_data = response.json()
        if json_data.get("ret") and json_data.get("errcode") == 0:
            task_id = json_data["data"]["id"]
            return task_id
    return None


#模拟获取taskId结果
def get_task_result(task_id, cookie):
    url = f"http://tamias.corp.qunar.com/adhoc/externalserver/taskResult"
    params = {
        "taskId": task_id,
        "start": 0,
        "len": 500,
        "_": "1739878700399",
        "jsoncallback": "__jp3",
    }

    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "DNT": "1",
        "Referer": "http://tamias.corp.qunar.com/adhoc?id=4557000",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-forwarded-for": "127.0.0.1",
        "x-real-ip": "127.0.0.1",
        "Cookie": cookie,
    }

    max_retries = 60
    retry_count = 0

    while retry_count < max_retries:
        response = requests.get(url, params=params, headers=headers, verify=False)

        if "任务正在运行" in response.text:
            time.sleep(2)
            retry_count += 1
            continue
        else:
            # 解析JSONP响应
            text = response.text
            # 移除JSONP包装 "__jp3(" 和 最后的 ")"
            json_str = text[6:-1]
            try:
                # print("json_str", json_str)
                json_data = json.loads(json_str)
                if json_data.get("ret") and json_data.get("errcode") == 0:
                    # 返回results数组
                    return json_data["data"]["results"]
            except json.JSONDecodeError:
                return None
            return None

    return None


def queryDataFromTamias(cookie, hql):
    task_id = submit_query(cookie, hql)
    if task_id:
        results = get_task_result(task_id, cookie)
        dataUrl = f"http://tamias.corp.qunar.com/adhoc?id={task_id}"
        # Check if results has exactly 1 item
        if isinstance(results, list) and len(results) == 1:
            return {"results": "当前条件未检索到数据", "sql": dataUrl}
        if results:
            downloadUrl = f"{TAMIAS_RESULT_DOWNLOAD_URL}{task_id}"
            errMsg, downLoadResult = downloadResultFromUrl(downloadUrl)
            if errMsg:
                return {"error": errMsg, "results": [], "sql": dataUrl}
            for item in downLoadResult:
                #item.update({"compareId": generateId()})
                #这一步是为了提取json中的字段到item中，数据拍平
                process_ext_json(item)
            return {
                "results": downLoadResult,
                "sql": dataUrl,
            }
        return {"error": "Failed to get results", "results": [], "sql": dataUrl}
    return {"error": "Failed to submit query"}


def buildSqlByQuery(ursData, username, bookingTraceId):

    #####
    sql = UPS_PRICE_CHANGE_SQL

    try:
        params = {}
        params["ursDate"] = ursData
        params["username"] = username
        params["traceId"] = bookingTraceId
        return sql.format_map(params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def parse_action_fields(json_action_str: str) -> dict:
    """
    解析f_json_action字段中的JSON内容，提取并转换operTime
    
    Args:
        json_action_str: JSON字符串，包含action信息
        
    Returns:
        dict: 包含提取出的字段值
            f_log_data_time: 格式化后的操作时间（年月日时分秒）
    """
    result = {
        "f_log_data_time": ""
    }
    
    if not json_action_str:
        return result
        
    try:
        # 解析JSON字符串
        action_data = json.loads(json_action_str)
        
        # 提取operTime字段（毫秒时间戳）
        oper_time = action_data.get("operTime")
        if oper_time:
            # 将毫秒时间戳转换为秒
            seconds = int(oper_time) / 1000.0
            # 转换为datetime对象
            dt = datetime.fromtimestamp(seconds)
            # 格式化为年月日时分秒
            result["f_log_data_time"] = dt.strftime("%Y-%m-%d %H:%M:%S")
            
    except (json.JSONDecodeError, ValueError, TypeError, Exception) as e:
        # 处理各种可能的异常，返回默认空值
        pass
        
    return result


def parse_price_detail_items(price_detail_items_str: str, f_reducesumprice: str = "") -> dict:
    """
    解析f_pricedetailitems字段中的JSON内容，提取特定字段
    
    Args:
        price_detail_items_str: JSON字符串，包含价格详情项
        f_reducesumprice: 减价总金额（可选）
        
    Returns:
        dict: 包含提取出的字段值
            f_zerocard: 零卡价值
            f_expcut: 膨胀金价值
            f_coupon: 优惠券价值
            f_activitycut: 活动折扣价值
            f_ticketprice: 机票价格
            f_showprice: 显示价格（机票价格-减价金额）
            forceBindPrice: 强制绑定产品价格
            f_busRefund: 商旅返现金额
            f_productDesc: 商品描述
    """
    result = {
        "f_zerocard": "0",
        "f_expcut": "0",
        "f_coupon": "0",
        "f_activitycut": "0",
        "f_ticketprice": "0",
        "f_showprice": "0",
        "forceBindOrPackage": "",
        "f_busRefund": "0",  # 添加商旅返现金额字段，默认为0
        "f_productDesc": ""  # 添加商品描述字段，默认为空字符串
    }
    
    if not price_detail_items_str:
        return result
        
    try:
        items = json.loads(price_detail_items_str)
        if not isinstance(items, list):
            return result
        
        # 收集所有类型为PACKAGE、INS、X的item
        force_bind_items = []
        # 用于拼接商品描述的列表
        product_desc_parts = []
            
        for item in items:
            # 处理零卡价值
            if item.get("activitySpecialType") == "1":
                result["f_zerocard"] = "0" if item.get("price", "0") is None else str(item.get("price", "0"))
                
            # 处理膨胀金
            if item.get("priceName") == "膨胀金":
                result["f_expcut"] = "0" if item.get("price", "0") is None else str(item.get("price", "0"))
                
            # 处理优惠券
            if item.get("isCashCoupon") == True and item.get("priceName") != "膨胀金":
                result["f_coupon"] = "0" if item.get("price", "0") is None else str(item.get("price", "0"))
                
            # 处理活动折扣
            if item.get("activityType") == "69":
                result["f_activitycut"] = "0" if item.get("price", "0") is None else str(item.get("price", "0"))
                
            # 处理机票价格
            if item.get("isTicketPrice") == True:
                ticket_price = item.get("price", "0")
                result["f_ticketprice"] = "0" if ticket_price is None else str(ticket_price)
                
            # 处理强制绑定产品
            item_type = item.get("type")
            if item_type in ["PACKAGE", "INS", "X"]:
                # 提取关键字段
                force_bind_item = {
                    "expCode": item.get("expCode", ""),
                    "expCode": item.get("expCode", ""),
                    "numLabel": item.get("numLabel", ""),
                    "priceLabel": item.get("priceLabel", ""),
                    "priceName": item.get("priceName", ""),
                    "type": item_type,
                    "price": item.get("price", "")
                }
                force_bind_items.append(force_bind_item)
                
                # 处理商旅返现金额
                if item_type == "PACKAGE" and item.get("priceName") and "商旅返现" in item.get("priceName"):
                    price_name = item.get("priceName", "")
                    # 使用正则表达式提取返现金额
                    import re
                    match = re.search(r'返¥(\d+)', price_name)
                    if match:
                        refund_amount = match.group(1)
                        result["f_busRefund"] = refund_amount
                
                # 拼接商品描述
                type_desc = ""
                if item_type == "PACKAGE":
                    type_desc = "服务包"
                elif item_type == "INS":
                    type_desc = "保险"
                elif item_type == "X":
                    type_desc = "单品"
                
                price_name = item.get("priceName", "")
                price = item.get("price", "")
                if price_name and price:
                    desc_part = f"{type_desc}:{price_name},价格:{price}; "
                    product_desc_parts.append(desc_part)
            
        # 将集合转换为JSON字符串
        if force_bind_items:
            result["forceBindOrPackage"] = json.dumps(force_bind_items, ensure_ascii=False)
        
        # 合并商品描述
        if product_desc_parts:
            result["f_productDesc"] = "".join(product_desc_parts)
                
        # 计算显示价格（机票价格-减价金额）
        if result["f_ticketprice"]:
            try:
                ticket_price = float(result["f_ticketprice"])
                reduce_sum = 0 if f_reducesumprice is None else float(f_reducesumprice)
                if ticket_price >= reduce_sum:
                    result["f_showprice"] = str(int(ticket_price - reduce_sum))
            except (ValueError, TypeError):
                # 如果转换失败，保持f_showprice为空
                pass
                
    except json.JSONDecodeError:
        # JSON解析错误，返回默认空值
        pass
    except Exception as e:
        # 其他异常，返回默认空值
        pass
        
    return result

def queryHivePriceCompareDatas(mached, param):
    """
    收集所有surTraceId，使用IN条件进行批量查询，然后根据traceId匹配回对应的item
    
    Args:
        mached: List[Dict] - 匹配的数据列表
        param: Dict - 请求参数
        
    Returns:
        Dict - 包含合并后的结果
    """
    try:
        searchDate = param.get("searchDate")
        username = param.get("username")
        cookie = param.get("cookie")
        
        if not mached or len(mached) == 0:
            return {
                "error": "无匹配数据，无法查询hive",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "无匹配数据，无法查询hive",
            }
            
        # 收集所有有效的surTraceId
        traceIds = []
        itemByTraceId = {}
        for item in mached:
            bookingTraceId = item.get("surTraceId")
            if bookingTraceId:
                traceIds.append(f"'{bookingTraceId}'")
                itemByTraceId[bookingTraceId] = item
        
        if not traceIds:
            return {
                "error": "无有效的traceId，无法查询hive",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "无有效的traceId，无法查询hive",
            }
        
        # 构建包含IN条件的SQL，不需要移除引号，直接拼接
        traceIdsStr = ",".join(traceIds)
        sql = UPS_PRICE_CHANGE_SQL.format(
            ursDate=searchDate, 
            username=username, 
            traceIds=traceIdsStr
        )
        
        # 执行单次查询
        oriDataResult = queryDataFromTamias(cookie, sql)
        
        if oriDataResult.get("error"):
            return {
                "error": oriDataResult.get("error"),
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": oriDataResult.get("error"),
            }
                
        hiveResults = oriDataResult.get("results")
        if not hiveResults or len(hiveResults) == 0:
            return {
                "error": "hive未查到任何用户单程变价数据",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "hive未查到任何用户单程变价数据",
            }
                
        # 存储所有处理结果
        mergedResults = []
        processedTraceIds = set()
            
        # 处理所有查询结果，并根据traceId匹配回原始item
        for hiveItem in hiveResults:
            traceId = hiveItem.get("f_traceid")
            if not traceId or traceId not in itemByTraceId:
                continue
                
            # 获取对应的原始item
            item = itemByTraceId[traceId]
            # 创建item的深拷贝，避免修改原始对象
            newItem = item.copy()
                
            # 解析价格详情项
            price_detail_fields = parse_price_detail_items(hiveItem.get("f_pricedetailitems", ""), hiveItem.get("f_reducesumprice", ""))
                
            # 获取日期时间信息（通过拼接f_logdate和f_logtime）
            f_log_data_time = ""
            try:
                f_logdate = hiveItem.get("f_logdate", "")
                f_logtime = hiveItem.get("f_logtime", "")
                if f_logdate and f_logtime:
                    f_log_data_time = f"{f_logdate} {f_logtime}"
            except Exception:
                pass
                
            # 合并字段到新item（使用单行代码设置值，而不是循环）
            newItem.update({
                "compareId": generate_compare_id(),
                "prePrice": "0" if hiveItem.get("showprice") is None or hiveItem.get("showprice") == "null" else hiveItem.get("showprice"),
                "surPrice": "0" if price_detail_fields.get("f_showprice") is None or price_detail_fields.get("f_showprice") == "null" else price_detail_fields.get("f_showprice"),
                "surSearchDataTime": f_log_data_time,
                "preZeroCard": "0" if hiveItem.get("zerocard") is None or hiveItem.get("zerocard") == "null" else hiveItem.get("zerocard"),
                "surZeroCard": "0" if price_detail_fields.get("f_zerocard") is None or price_detail_fields.get("f_zerocard") == "null" else price_detail_fields.get("f_zerocard"),
                "preExpCut": "0" if hiveItem.get("expcut") is None or hiveItem.get("expcut") == "null" else hiveItem.get("expcut"),
                "surExpCut": "0" if price_detail_fields.get("f_expcut") is None or price_detail_fields.get("f_expcut") == "null" else price_detail_fields.get("f_expcut"),
                "preCoupon": "0" if hiveItem.get("coupon") is None or hiveItem.get("coupon") == "null" else hiveItem.get("coupon"),
                "surCoupon": "0" if price_detail_fields.get("f_coupon") is None or price_detail_fields.get("f_coupon") == "null" else price_detail_fields.get("f_coupon"),
                "preActivityCut": "0" if hiveItem.get("activitycut") is None or hiveItem.get("activitycut") == "null" else hiveItem.get("activitycut"),
                "surActivityCut": "0" if price_detail_fields.get("f_activitycut") is None or price_detail_fields.get("f_activitycut") == "null" else price_detail_fields.get("f_activitycut"),
                "forceBindOrPackage": price_detail_fields.get("forceBindOrPackage", ""),
                "preAdultnum": "0" if hiveItem.get("adultcount") is None or hiveItem.get("adultcount") == "null" else hiveItem.get("adultcount"),
                "surAdultnum": "0" if hiveItem.get("f_adultnum") is None or hiveItem.get("f_adultnum") == "null" else hiveItem.get("f_adultnum"),
                "preBabynum": "0" if hiveItem.get("infantcount") is None or hiveItem.get("infantcount") == "null" else hiveItem.get("infantcount"),
                "surBabynum": "0" if hiveItem.get("f_babynum") is None or hiveItem.get("f_babynum") == "null" else hiveItem.get("f_babynum"),
                "preChildnum": "0" if hiveItem.get("childcount") is None or hiveItem.get("childcount") == "null" else hiveItem.get("childcount"),
                "surChildnum": "0" if hiveItem.get("f_childnum") is None or hiveItem.get("f_childnum") == "null" else hiveItem.get("f_childnum"),
                "busRefund": price_detail_fields.get("f_busRefund", "0"), # 添加商旅返现金额字段
                "productDesc": price_detail_fields.get("f_productDesc", ""), # 添加商品描述字段
            })
                
            # 将处理后的newItem添加到结果中
            mergedResults.append(newItem)
            processedTraceIds.add(traceId)
            
        # 检查是否有成功查询的结果
        if len(mergedResults) == 0:
            return {
                "error": "hive未查到任何用户单程变价数据",
                "results": [],
                "processStage": "URS-hive查询用户单程变价数据",
                "needExecAI": "否",
                "notExecAIReason": "hive未查到任何用户单程变价数据",
            }
            
        # 记录未能匹配到的traceId数量信息
        unmatched_count = len(traceIds) - len(processedTraceIds)
        if unmatched_count > 0:
            match_info = f"注意：有{unmatched_count}个traceId未匹配到hive数据"
        else:
            match_info = "所有traceId均匹配到hive数据"
            
        return {
            "error": None,
            "results": mergedResults,
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAI": "是",
            "notExecAIReason": "",
            "matchInfo": match_info
        }
        
    except Exception as e:
        return {
            "error": f"hive查询搜索有变价的搜索记录异常: {str(e)}",
            "results": [],
            "processStage": "URS-hive查询用户单程变价数据",
            "needExecAI": "否",
            "notExecAIReason": f"hive查询搜索有变价的搜索记录异常: {str(e)}",
        }


def safe_parse_datetime(
    datetime_str,
) -> Optional[Union[date, datetime]]:
    """
    安全的日期时间解析方法，支持多种常见格式，转换失败返回None
    当解析纯日期格式时返回datetime.date类型，其他格式返回datetime类型
    """

    # 如果是字符串类型，先处理小数点
    if isinstance(datetime_str, str):
        # 如果包含小数点，直接去掉小数点及后面的部分
        if "." in datetime_str:
            datetime_str = datetime_str.split(".")[0]

    # 常见的时间格式列表
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        "%Y%m%d %H:%M:%S",
        "%Y%m%d %H:%M",
        "%Y-%m-%d",
        "%Y%m%d",
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(datetime_str, fmt)
            # 如果是纯日期格式，返回date类型
            if fmt in ["%Y-%m-%d", "%Y%m%d"]:
                return dt.date()
            return dt
        except (ValueError, TypeError, AttributeError) as e:
            # print(
            #     f"[Error in safe_parse_datetime] Value: {datetime_str}, Error: {str(e)}, Type: {type(datetime_str)}"
            # )
            continue

    return None

def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False  # 非容器类型且非 None 的视为非空


def parse_multi_values(
    params: dict, fields: List, date_format: str = None, case_sensitive: bool = False
) -> Set[Union[str, datetime.date]]:
    """
    通用多值解析方法（支持日期/普通字段）
    :param params: 请求参数字典
    :param field: 字段名（自动识别带List后缀的字段）
    :param date_format: 日期格式（传参表示需要日期解析）
    :param case_sensitive: 是否区分大小写（默认False，自动转大写）
    :return: 合并去重后的值集合
    """
    values = []
    for field in fields:
        filedValue = params.get(field)
        if is_deep_empty(filedValue):
            continue
        if "," in filedValue:
            values.extend(filedValue.split(","))
        else:
            values.append(filedValue)

    # 格式标准化处理
    parsed = set()
    for v in values:
        v = str(v).strip()
        if not v:
            continue

        # 日期类型处理
        if date_format:
            dt = safe_parse_date(v, date_format)
            if dt:
                parsed.add(dt.date() if isinstance(dt, datetime) else dt)
        else:
            # 文本类型处理
            processed = v if case_sensitive else v.upper()
            parsed.add(processed)

    return parsed


def install_and_filter_flight_data(
    target_data: Dict[str, dict], qTraceId: str,param:dict
):
    """
    增强版多条件过滤（支持全量多值参数）
    入参：
    - target_data: Dict[str, dict]，键为surTraceId，值为item对象
    - qTraceId: str
    """
    matched = []
    notMatched = []

    # 需要过滤的特定tag列表
    filtered_tags = {
        "TOU1",
        "FXD1",
        "PTJ1",
        "WYY1",
        "SLN1",
        "SLP1",
        "CZB1",
        "SLD1",
        "SLG1",
        "BYJ1",
        "GPP1",
        "STU1",
        "YHU1",
        "CLP1",
        "LHO1",
        "LHS1",
        "LHN1",
        "LHO1",
        "LHT1",
        "LHX1",
        "LHM1",
        "LHE1",
        "HUJ1",
        "JTP1",
        "SOA1",
        "AGE1",
        "OLD1",
        "OKR1",
        "OKK1",
        "HUB1",
        "ZZM1",
        "HUL1",
        "XFQ1",
        "XFL1",
        "XFZ1",
        "XFA1",
        "XLE1",
        "QHY1",
        "GSX1",
        "HET1",
        "LNF1",
        "JCY1",
        "JCB1",
        "ZHZ1",
        "KFW1",
        "HYC1",
        "NQX1",
        "XFF1",
        "TTD1",
        "SLB1",
    }

    # 遍历字典而不是列表
    for surTraceId, item in target_data.items():
        # 转换数据结构
        pair_item = transform_item_to_pair(param, item, qTraceId,surTraceId)
        if pair_item is None or pair_item.__len__() == 0:
            continue
        # 检查是否为OTA数据且tag在过滤列表中
        if item.get("preTag") in filtered_tags:
            pair_item.update(
                {
                    "needExec": "不匹配",
                    "desc": f"OTA数据tag {item.get('tag')} 在过滤列表中",
                }
            )
            notMatched.append(pair_item)
            continue

        # 检查traceId是否相等
        if surTraceId == qTraceId:
            pair_item.update(
                {
                    "needExec": "不匹配",
                    "desc": f"ota页traceId与booking页traceId相等",
                }
            )   
            notMatched.append(pair_item)
            continue
        # 标注匹配状态及错误详情
        pair_item.update(
            {
                "matchQuestion": "匹配",
                "desc": "匹配",
            }
        )

        matched.append(pair_item)

    return matched, notMatched


def transform_item_to_pair(param: dict, item: dict, qTraceId: str,surTraceId:str) -> dict:
    """
    将item转换为pair_item结构，提取指定的字段
    """ 
    pair_item = {}
    if item is None:
        return pair_item
    
    # 从baseInfo.basicAttributes中获取searchPriceInfo和syncPriceInfo
    if item.get("baseInfo") is None:
        return pair_item
    basic_attrs = item.get("baseInfo", {}).get("basicAttributes", {})
    # 1. 处理searchPriceInfo，添加pre前缀
    search_price_info = basic_attrs.get("searchPriceInfo", {})
    pair_item.update({
        "preTag": search_price_info.get("tag"),
        "preCabin": search_price_info.get("cabin"),
        "prePolicyId": search_price_info.get("policyId"),
        "preViewPrice": search_price_info.get("printPrice"),
        "preBasePrice": search_price_info.get("basePrice"),
        "prePackagePrice": search_price_info.get("barePrice"),
        "preProductMark": search_price_info.get("productMark"),
        "preChildCabin": search_price_info.get("childCabin"),
        "preChildPrice": search_price_info.get("childPrice"),
        "preInfantCabin": search_price_info.get("infantCabin"),
        "preInfantPrice": search_price_info.get("infantPrice")
    })
    
    # 2. 处理syncPriceInfo，添加sur前缀
    sync_price_info = basic_attrs.get("syncPriceInfo", {})
    pair_item.update({
        "surTag": sync_price_info.get("tag"),
        "surCabin": sync_price_info.get("cabin"),
        "surPolicyId": sync_price_info.get("policyId"),
        "surViewPrice": sync_price_info.get("printPrice"),
        "surBasePrice": sync_price_info.get("basePrice"),
        "surPackagePrice": sync_price_info.get("barePrice"),
        "surProductMark": sync_price_info.get("productMark"),
        "surChildCabin": sync_price_info.get("childCabin"),
        "surChildPrice": sync_price_info.get("childPrice"),
        "surInfantCabin": sync_price_info.get("infantCabin"),
        "surInfantPrice": sync_price_info.get("infantPrice")
    })

    # 3. 处理computingProcess
    sync_data = item.get("computingProcess", {}).get("sync", {})
    async_data = item.get("computingProcess", {}).get("async", {})
    
    # 处理flightType
    flight_type = sync_data.get("flightType")
    if flight_type is None:
        flight_type = async_data.get("flightType")
    pair_item["flightType"] = flight_type
    
    # 处理flightNo
    flight_no = sync_data.get("flightNo")
    if flight_no is None:
        flight_no = async_data.get("flightNo")
    pair_item["flightNo"] = flight_no
    
    # 添加preTraceId
    pair_item["preTraceId"] = qTraceId
    
    # 添加bookingQTrace作为surTraceId
    #pair_item["surTraceId"] = item.get("qTraceInfo", {}).get("bookingQTrace")
    pair_item["surTraceId"] = surTraceId

    # 添加compareType
    pair_item["compareType"] = "ota-booking"

    # 添加请求参数
    pair_item["departureDate"] = param.get("departureDate")
    pair_item["departureCity"] = param.get("departureCity")
    pair_item["arrivalCity"] = param.get("arrivalCity")
    pair_item["preSearchDateTime"] = param.get("searchDateTime")
    
    return pair_item


def generate_compare_id() -> str:
    """
    生成唯一的compareId
    Returns:
        str: 唯一的compareId
    """
    return str(uuid.uuid4())

def safe_parse_date(date_str: str, format: str) -> Optional[datetime.date]:
    """
    安全的日期解析方法，转换失败返回None
    """
    try:
        # 处理含时间戳的日期（如 "2025-02-20 15:56:51"）
        clean_str = date_str.split()[0] if date_str else ""
        return datetime.strptime(clean_str, format).date()
    except (ValueError, TypeError, AttributeError):
        return None


def search_flight_case(
    qTraceId: str,
) -> Dict[str, Any]:
    url = "http://paoding.corp.qunar.com/open/visual/mainSearch"

    # 参数校验（关键必填项）
    if not qTraceId:
        raise ValueError("qTraceId 为必填参数")

    params = {
        "qTraceId": qTraceId,
    }

    headers = {"Paoding-Open-Source": "tradeCore"}

    try:
        response = requests.get(
            url, params=params, headers=headers, timeout=50  # 增加超时控制
        )
        response.raise_for_status()
        return response.json()
    except (RequestException, ValueError) as e:
        return {"error": f"请求失败: {str(e)}"}


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # 方法1 调用庖丁接口，获取价格信息，此处查出来直接为成对的价格消息
        searchResult = search_flight_case(
            qTraceId=param.get("qTraceId", ""),
        )
        
        # 检查searchResult是否为None
        if searchResult is None:
            return {"errorMsg": "庖丁请求异常！", "priceList": [], "notMatchData": []}
            
        priceList = searchResult.get("data")
        if priceList is None:
            if searchResult.get("errmsg") is not None:
                return {"errorMsg": f"庖丁请求异常！错误信息: {searchResult.get('errmsg')}", "priceList": [], "notMatchData": []}
            return {"errorMsg": "未获取到庖丁价格数据", "priceList": [], "notMatchData": []}
        qTraceId=param.get("qTraceId", "")
                
        mahed, notMahed = install_and_filter_flight_data(
            priceList, qTraceId, param
        )
        if len(mahed) == 0:
            return {"errorMsg": "过滤traceId与otaTag后没有数据", "priceList": mahed, "notMatchData": notMahed}  
        
        # 方法2 查询hive数据并合并数据（已经在queryHivePriceCompareDatas内部完成了合并）
        hiveResult = queryHivePriceCompareDatas(mahed, param)
        if hiveResult.get("error"):
            return {"errorMsg": hiveResult.get("error"), "priceList": [], "notMatchData": notMahed}
            
        # 使用合并后的结果
        mergedResults = hiveResult.get("results")

        print(json.dumps(mergedResults, indent=2, ensure_ascii=False))
        return {"errorMsg": "", "priceList": mergedResults, "notMatchData": notMahed}
    except KeyError as e:
        return {"errorMsg": f"缺少必要参数: {str(e)}", "priceList": []}
    except ValueError as e:
        return {"errorMsg": str(e), "priceList": []}
    except Exception as e:
        return {"errorMsg": f"系统异常: {str(e)}", "priceList": []}

def test_main():
    """
    测试main方法的执行，预留入参待填写
    """
    test_param = {
        "qTraceId": "ops_slugger_250406.191156.10.90.5.96.714022.6430150400_1",  # 必填，搜索跟踪ID
        "searchDate": "2025-04-06", # 必填，搜索日期，格式YYYY-MM-DD
        "username": "adatcka7201",   # 必填，用户名
        "cookie": "QN1=00014b0014346c74ca982920; cookie=wanzhou.zheng&744122&8A2EE47906AB79B2BEA42F3838EEA290; JSESSIONID=82B0396E4DA55F340BEC8276A78DFB93",     # 必填，用于调用tamias接口的cookie
        "departureDate": "", # 出发日期，格式YYYY-MM-DD
        "departureCity": "", # 出发城市
        "arrivalCity": "",   # 到达城市
        "searchDateTime": "2025-04-06 12:00:00" # 搜索时间
    }
    
    # 执行main方法
    result = main(test_param)
    
    # 打印结果
    print("测试结果:")
    print(f"错误信息: {result.get('errorMsg', '')}")
    
    print(f"匹配价格数据数量: {len(result.get('priceList', []))}")
    print(f"不匹配数据数量: {len(result.get('notMatchData', []))}")
    
    # 如果需要，可以打印详细的价格数据
    # import json
    # print(json.dumps(result.get('priceList', [])[:1], indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_main()

  