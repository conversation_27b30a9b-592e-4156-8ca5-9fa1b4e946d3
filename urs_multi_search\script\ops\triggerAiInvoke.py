import pandas as pd
import requests
import json
import time
from typing import List, Dict, Any


def read_excel_file(
    file_path: str, required_columns: List[str]
) -> List[Dict[str, Any]]:
    """
    读取Excel文件并返回数据数组

    Args:
        file_path: Excel文件路径

    Returns:
        包含Excel数据的数组，每行数据为一个字典
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 确保必要的列存在
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"Excel文件必须包含以下列: {required_columns}")

        # 将DataFrame转换为字典列表
        data_list = df.to_dict("records")
        return data_list
    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        return []


def call_http_api(
    url: str,
    user_name: str,
    uniq_key: str,
    userSqlId: str,
    ssCallBackUrl: str,
    s2DCallBackUrl: str,
) -> bool:
    """
    调用HTTP接口

    Args:
        url: 请求URL
        user_name: 用户名
        uniq_key: 唯一键
        urs_date: URS日期

    Returns:
        是否调用成功
    """
    try:
        # 构建请求数据
        payload = {
            "uniqKey": uniq_key,
            "userSqlId": userSqlId,
            "username": user_name,
            "s2sCallBackUrl": ssCallBackUrl,
            "s2DCallBackUrl": s2DCallBackUrl,
        }

        # 发送POST请求
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, json=payload, headers=headers)

        # 检查响应状态
        if response.status_code == 200:
            print(f"成功调用接口: {user_name}")
            return True
        else:
            print(f"调用接口失败: {user_name}, 状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"调用接口时发生错误: {str(e)}")
        return False


import json
import sys
from typing import List, Dict, Any


def filter_platform_responsibility(
    data: List[Dict[str, Any]], ursDate: str, userSqlId: str
) -> List[Dict[str, Any]]:
    """
    Filter data to only keep records where AI定责说明 equals 平台责任, ursDate matches, and userSqlId matches

    Args:
        data: List of dictionaries containing the data to filter
        ursDate: The date to filter by
        userSqlId: The questionnaire ID to filter by

    Returns:
        List of dictionaries where AI定责说明 equals 平台责任, ursDate matches, and userSqlId matches
    """
    # First filter by ursDate
    date_filtered = [record for record in data if record.get("ursDate") == ursDate]
    # Then filter by userSqlId
    id_filtered = [
        record for record in date_filtered if str(record.get("问卷id")) == userSqlId
    ]
    # Finally filter by platform responsibility
    return [record for record in id_filtered if record.get("AI定责说明") == "平台责任"]


def deduplicate_by_username_and_questionnaire(
    data: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """
    Deduplicate data based on the combination of username and questionnaire ID

    Args:
        data: List of dictionaries containing the data to deduplicate

    Returns:
        List of dictionaries with unique username + questionnaire ID combinations
    """
    seen = set()
    deduplicated_data = []

    for record in data:
        key = (record["用户名"], record["问卷id"])
        if key not in seen:
            seen.add(key)
            deduplicated_data.append(record)

    return deduplicated_data


def main():
    # Excel文件路径
    excel_path = "D:/Users/<USER>/Downloads/2449_250420_多次搜索_ai回调.xlsx"

    required_columns = ["用户名", "uniqKey", "问卷id", "AI定责说明", "ursDate"]

    ssCallBackUrl = "https://hf7l9aiqzx.feishu.cn/base/workflow/webhook/event/HOlQavnQQwfDgOhHj8kcWiclnec"
    s2DCallBackUrl = "https://hf7l9aiqzx.feishu.cn/base/workflow/webhook/event/CgsTaJcYFwqj5EhJCMUciS9JnIc"

    # API URL
    api_url = "https://hf7l9aiqzx.feishu.cn/base/workflow/webhook/event/F7nLaGomwwXMJMhpS0BcfGy0n1Q"  # 请根据实际情况修改API URL

    data_list = read_excel_file(excel_path, required_columns)
    ursDate = "2025-04-20"
    userSqlId = "2449"
    # 再进行平台责任过滤
    filtered_data = filter_platform_responsibility(data_list, ursDate, userSqlId)
    # 进行去重
    deduplicated_data = deduplicate_by_username_and_questionnaire(filtered_data)

    for row in deduplicated_data:
        user_name = row["用户名"]
        uniq_key = row["uniqKey"]
        userSqlId = row["问卷id"]
        success = call_http_api(
            api_url, user_name, uniq_key, userSqlId, ssCallBackUrl, s2DCallBackUrl
        )
        if not success:
            print(f"处理数据失败: {user_name}")


if __name__ == "__main__":
    main()
