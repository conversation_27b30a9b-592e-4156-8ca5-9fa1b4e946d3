# 导入需要的包
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Tuple
import json
from dataclasses import dataclass
import re

# 必须有一个main函数，作为入口
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    # 检查必要参数
    if not param.get('analysisResult'):
        return {
            "status": "error",
            "message": "缺少必要参数: analysisResult",
            "data": None
        }
    
    if not param.get('mappingData'):
        return {
            "status": "error",
            "message": "缺少必要参数: mappingData",
            "data": None
        }
    
    # 解析分析结果
    analysisResult, parse_status = parse_urlencoded_structured_data(param['analysisResult'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "analysisResult 解析失败",
            "data": None
        }
    
    # 解析映射数据
    mappingData, parse_status = parse_urlencoded_structured_data(param['mappingData'])
    if parse_status["status"] != "success":
        return {
            "status": "error",
            "message": "mappingData 解析失败",
            "data": None
        }
    
    # 获取解析后的数据
    analysis_data = analysisResult[0] if analysisResult else {}
    mapping_data = mappingData[0] if mappingData else {}

    # 获取product_mark
    product_mark = mapping_data.get("productMark", "")
    if not product_mark:
        return {
            "status": "error",
            "message": "productMark 不能为空",
            "data": None
        }

    # 获取航司
    carrier = mapping_data.get("carrier", "")
    if not carrier:
        return {
            "status": "error",
            "message": "carrier 不能为空",
            "data": None
        }
    
    # 域名
    domain = mapping_data.get("domain", "")
    if not domain:
        return {
            "status": "error",
            "message": "domain 不能为空",
            "data": None
        }

    # 构建结果对象
    result_list = parse_booking_new_validate_data(analysis_data, mapping_data)

    # 如果没有满足任何场景，则不返回结果
    if not result_list:
        return {
            "status": "ignore",
            "message": "未满足任何交易拦截场景",
            "data": None
        }
    
    return {
        "status": "success",
        "message": "操作成功",
        "data": result_list
    }

def parse_booking_new_validate_data(analysis_data: Dict[str, Any], mapping_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析为booking_new_validate_update.t数据
    
    参数:
    analysis_data: Dict[str, Any] - 分析数据
    mapping_data: Dict[str, Any] - 映射数据
    
    返回:
    List[Dict[str, Any]] - 解析后的配置结果列表
    """
    result_list = []
    
    # 获取基础配置
    domain = mapping_data.get("domain", "")
    carrier = mapping_data.get("carrier", "")
    product_mark = mapping_data.get("productMark", "")
    
    # 获取flight_type并转换为flightType
    flight_types = []
    flight_type_str = analysis_data.get("flight_type", "")
    if flight_type_str:
        flight_type_list = [ft.strip() for ft in flight_type_str.split(",")]
        for ft in flight_type_list:
            if ft in ["单程", "通程", "联程"]:
                flight_types.append("SINGLE")
            elif ft == "中转":
                flight_types.append("CONNECT")
            elif ft == "往返":
                flight_types.append("PACKAGE")
    
    # 如果没有有效的flight_type，默认使用SINGLE
    if not flight_types:
        flight_types = ["SINGLE"]
    
    # 调用各个子方法处理不同的ruleType
    # 乘机人数限制-2
    result_list.extend(parse_passenger_count_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 证件限制-5
    result_list.extend(parse_id_document_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 年龄限制-6
    result_list.extend(parse_age_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 身份证省份限制-11
    result_list.extend(parse_id_province_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 性别限制-12
    result_list.extend(parse_gender_limit(domain, carrier, product_mark, flight_types, analysis_data))
    #至少一人满足年龄限制-17
    result_list.extend(parse_at_least_one_age_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 认证学生限制-18
    result_list.extend(parse_student_verification_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 学生认证方式限制-20
    result_list.extend(parse_student_verification_method_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 用户画像限制-21
    result_list.extend(parse_user_portrait_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 成人数量限制-42
    result_list.extend(parse_adult_count_limit(domain, carrier, product_mark, flight_types, analysis_data))
    # 证件类型性别年龄限制-55，暂时忽略，使用场景较少，先不配置
    # 携程金融实名认证限制-66
    result_list.extend(parse_ctrip_finance_verification_limit(domain, carrier, product_mark, flight_types, analysis_data))
    
    # 处理互斥逻辑
    # 检查结果中是否存在成人数量限制配置(ruleType=42)
    has_adult_count_limit = any(item.get("ruleType") == "42" for item in result_list)
    
    # 如果存在成人数量限制配置，则移除乘机人数限制、年龄限制和至少一人满足年龄限制
    if has_adult_count_limit:
        # 移除乘机人数限制-2
        result_list = [item for item in result_list if item.get("ruleType") != "2"]
        # 移除年龄限制-6
        result_list = [item for item in result_list if item.get("ruleType") != "6"]
    
    # 处理年龄限制和至少一人满足年龄限制的互斥
    # 如果存在年龄限制-6，则移除年龄限制-6
    age_limit_6 = [item for item in result_list if item.get("ruleType") == "6"]
    age_limit_17 = [item for item in result_list if item.get("ruleType") == "17"]
    if age_limit_6 and age_limit_17:
        result_list = [item for item in result_list if item.get("ruleType") != "6"]
    
    return result_list

def create_base_config(domain: str, carrier: str, product_mark: str, flight_type: str) -> Dict[str, Any]:
    """
    创建基础配置对象
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_type: str - 航班类型
    
    返回:
    Dict[str, Any] - 基础配置对象
    """
    return {
        "ruleType": "",
        "domain": domain,
        "carrier": carrier,
        "productMark": product_mark,
        "tag": "ALL",
        "carrier2": "",
        "flightType": flight_type,
        "limitConfig": "",
        "limitText": "",
        "softInterceptText": "",
        "smallType": "",
        "userPortraitType": "1",
    }

def parse_passenger_count_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析乘机人数限制(ruleType=2)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []


    # 获取乘机人数限制数据
    capacity_limit = analysis_data.get("capacity_limit", "")
    if not capacity_limit:
        # 次卡或权益卡时，如果没限制年龄，则配置1单1人
        if analysis_data.get("card_limit") == "true":
            capacity_limit = "1-1"
    
    # 检查是否有乘机人数限制相关数据
    if not capacity_limit:
        return result_list
    
    # 校验格式 - 只处理横杆拼接的字符串格式，如"1-1"
    if not capacity_limit or capacity_limit == "0" or "-" not in capacity_limit:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "2"
        
        # 处理capacity_limit
        if capacity_limit == "1" or capacity_limit == "1-1":
            # 单人限制
            config["limitConfig"] = "1-1"
            config["limitText"] = "很抱歉，该产品要求每个订单仅限1人预订，多人请分开下单，感谢您的理解和支持。"
        elif "-" in capacity_limit:
            # 范围限制，如"2-9"
            try:
                min_count, max_count = map(int, capacity_limit.split("-"))
                if min_count > max_count or min_count < 1 or max_count > 9:
                    continue  # 跳过无效配置
                
                config["limitConfig"] = capacity_limit
                config["limitText"] = f"很抱歉，该产品要求订单乘机人需为{min_count}-{max_count}人，请调整乘机人数量或重新选择其他价格。"
                config["softInterceptText"] = f"订单乘机人需为{min_count}-{max_count}人"
            except (ValueError, TypeError):
                continue  # 跳过无效格式
        else:
            # 单个数字限制，如"5"
            try:
                count = int(capacity_limit)
                if count < 1 or count > 9:
                    continue  # 跳过无效配置
                
                config["limitConfig"] = str(count)
                config["limitText"] = f"很抱歉，每单最多只能添加{count}位乘机人"
            except (ValueError, TypeError):
                continue  # 跳过无效格式
        
        result_list.append(config)
    
    return result_list

def parse_id_document_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析证件限制(ruleType=5)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有证件限制相关数据
    if not analysis_data.get("document_restrictions"):
        return result_list
    
    # 获取证件限制数据
    document_restrictions = analysis_data.get("document_restrictions", "")
    
    # 将document_restrictions转换为数字列表
    # 数字含义: 1-身份证, 2-回乡证, 3-港澳台居民身份证
    try:
        doc_types = [int(x.strip()) for x in document_restrictions.split(",")]
    except (ValueError, TypeError):
        return result_list
    
    # 检查是否同时包含多个证件类型（异常情况）
    if 1 in doc_types and 2 in doc_types:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "5"
        
        # 根据证件类型设置限制配置
        if 1 in doc_types:  # 1-身份证
            config["limitConfig"] = "NI"
            config["limitText"] = "乘机人需使用身份证预订"
        elif 2 in doc_types:  # 2-回乡证
            config["limitConfig"] = "HX"
            config["limitText"] = "此价格为航司特邀香港/澳门旅客专享，需凭有效回乡证预定。"
        
        # 只有当有证件限制时才添加到结果列表
        if "limitConfig" in config:
            result_list.append(config)
    
    return result_list

def parse_age_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析年龄限制(ruleType=6)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有年龄限制相关数据
    if not analysis_data.get("age_limit"):
        return result_list
    
    # 获取年龄限制数据
    age_limit = analysis_data.get("age_limit", "")
    
    # 检查age_limit_supplementary
    age_limit_supplementary = analysis_data.get("age_limit_supplementary", "")
    
    # 只有当age_limit_supplementary为"全部满足"或为空时才处理
    if age_limit_supplementary and age_limit_supplementary != "全部满足":
        return result_list
    
    # 解析年龄区间
    age_ranges = []
    for age_range in age_limit.split(","):
        age_range = age_range.strip()
        if not age_range:
            continue
            
        # 检查格式是否正确
        if "-" not in age_range:
            continue
            
        try:
            min_age, max_age = map(int, age_range.split("-"))
            # 校验年龄范围是否合法
            if min_age < 0 or max_age < 0 or min_age > max_age:
                continue
                
            age_ranges.append((min_age, max_age))
        except (ValueError, TypeError):
            continue
    
    # 如果没有有效的年龄区间，则不处理
    if not age_ranges:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "6"
        
        # 设置限制配置
        config["limitConfig"] = age_limit
        
        # 根据年龄区间设置limitText和softInterceptText
        if len(age_ranges) == 1:
            # 单个区间的情况
            min_age, max_age = age_ranges[0]
            
            if max_age > 90:
                if min_age == 2:
                    # 特殊逻辑：下限为2且上限大于90
                    config["limitText"] = "该产品不适用婴儿旅客购买, 感谢您的理解和支持。"
                    config["softInterceptText"] = "限2岁以上(含)的旅客预订"
                else:
                    config["limitText"] = f"该产品仅限{min_age}周岁（含）以上的旅客购买, 感谢您的理解和支持。"
                    config["softInterceptText"] = f"限{min_age}岁以上(含)的旅客购买"
            elif min_age == 0 and max_age <= 90:
                config["limitText"] = f"很抱歉，该产品仅限{max_age+1}周岁以下（不含）旅客预订"
                config["softInterceptText"] = f"限{max_age+1}岁以下的旅客预订"
            else:
                config["limitText"] = f"很抱歉，该产品仅限{min_age}-{max_age}周岁（含）旅客预订。"
                config["softInterceptText"] = f"限{min_age}-{max_age}岁(含)的旅客购买"
        else:
            # 多个区间的情况
            limit_text_parts = []
            soft_intercept_text_parts = []
            
            for min_age, max_age in age_ranges:
                if max_age > 90:
                    limit_text_parts.append(f"{min_age}周岁以上（含）")
                    soft_intercept_text_parts.append(f"{min_age}岁以上(含)")
                else:
                    limit_text_parts.append(f"{min_age}-{max_age}周岁（含）")
                    soft_intercept_text_parts.append(f"{min_age}-{max_age}岁(含)")
            
            config["limitText"] = f"该产品仅限{' 或 '.join(limit_text_parts)}的旅客预订， 感谢您的理解和支持。"
            config["softInterceptText"] = f"限{' 或 '.join(soft_intercept_text_parts)}的旅客购买"
        
        result_list.append(config)
    
    return result_list

def parse_id_province_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析身份证省份限制(ruleType=11)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有身份证省份限制相关数据
    if not analysis_data.get("regional_limit"):
        return result_list
    
    # 获取身份证省份限制数据
    regional_limit = analysis_data.get("regional_limit", "")
    
    # 使用正则表达式提取阿拉伯数字
    
    # 提取所有2位及以上的阿拉伯数字
    id_prefixes = re.findall(r'\d{2,}', regional_limit)
    
    # 如果解析不成功或解析后的身份证开头数字列表为空，则忽略
    if not id_prefixes:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "11"
        
        # 设置限制配置
        config["limitConfig"] = ",".join(id_prefixes)
        
        # 根据身份证开头数量设置limitText和softInterceptText
        if len(id_prefixes) > 3:
            config["limitText"] = "仅限部分符合身份证开头的乘机人购买"
        else:
            prefix_str = ",".join(id_prefixes)
            config["limitText"] = f"仅限身份证开头为{prefix_str}的旅客购买"
            config["softInterceptText"] = f"仅限身份证开头为{prefix_str}的旅客购买"
        
        result_list.append(config)
    
    return result_list

def parse_gender_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析性别限制(ruleType=12)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有性别限制相关数据
    if not analysis_data.get("gender_restriction"):
        return result_list
    
    # 获取性别限制数据
    gender_restriction = analysis_data.get("gender_restriction", "")
    
    # 检查是否为有效值（只接受"男"或"女"）
    if gender_restriction != "男" and gender_restriction != "女":
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "12"
        
        # 根据性别设置限制配置
        if gender_restriction == "男":
            config["limitConfig"] = "M"
            config["limitText"] = "很抱歉，该产品仅限男性购买"
        elif gender_restriction == "女":
            config["limitConfig"] = "F"
            config["limitText"] = "很抱歉，该产品仅限女性购买"
        
        # 只有当有性别限制时才添加到结果列表
        if "limitConfig" in config:
            result_list.append(config)
    
    return result_list

def parse_at_least_one_age_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析至少一人满足年龄限制(ruleType=17)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有年龄限制相关数据
    if not analysis_data.get("age_limit"):
        return result_list
    
    # 获取年龄限制数据
    age_limit = analysis_data.get("age_limit", "")
    
    # 检查age_limit_supplementary
    age_limit_supplementary = analysis_data.get("age_limit_supplementary", "")
    
    # 只有当age_limit_supplementary为"至少一人满足"时才处理，为空也不处理
    if not age_limit_supplementary or age_limit_supplementary != "至少一人满足":
        return result_list
    
    # 解析年龄区间
    age_ranges = []
    for age_range in age_limit.split(","):
        age_range = age_range.strip()
        if not age_range:
            continue
            
        # 检查格式是否正确
        if "-" not in age_range:
            continue
            
        try:
            min_age, max_age = map(int, age_range.split("-"))
            # 校验年龄范围是否合法
            if min_age < 0 or max_age < 0 or min_age > max_age:
                continue
                
            age_ranges.append((min_age, max_age))
        except (ValueError, TypeError):
            continue
    
    # 如果没有有效的年龄区间，则不处理
    if not age_ranges:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "17"
        
        # 设置限制配置
        config["limitConfig"] = age_limit
        
        # 根据年龄区间设置limitText
        if len(age_ranges) == 1:
            # 单个区间的情况
            min_age, max_age = age_ranges[0]
            
            if max_age > 90:
                # 上限大于90的情况
                config["limitText"] = f"该产品需至少包含一位{min_age}（包含）周岁以上乘客购买"
            elif min_age == 0:
                # 下限为0的情况
                config["limitText"] = f"该产品需至少包含一位{max_age+1}周岁以下（不含）乘客购买"
            else:
                # 普通区间情况
                config["limitText"] = f"该产品需至少包含一位{min_age}至{max_age}岁的旅客购买, 感谢您的理解和支持。"
        else:
            # 多个区间的情况
            limit_text_parts = []
            
            for min_age, max_age in age_ranges:
                if max_age > 90:
                    # 上限大于90的情况
                    limit_text_parts.append(f"{min_age}周岁以上（含）")
                elif min_age == 0:
                    # 下限为0的情况
                    limit_text_parts.append(f"{max_age+1}周岁以下（不含）")
                else:
                    # 普通区间情况
                    limit_text_parts.append(f"{min_age}-{max_age}周岁（含）")
            
            config["limitText"] = f"该产品需至少包含一位{' 或 '.join(limit_text_parts)}的旅客购买, 感谢您的理解和支持。"
        
        result_list.append(config)
    
    return result_list

def parse_student_verification_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析认证学生限制(ruleType=18)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有学生认证限制相关数据
    if not analysis_data.get("student_verification"):
        return result_list
    
    # 获取学生认证限制数据
    student_verification = analysis_data.get("student_verification", "")
    
    # 检查是否为学生认证限制
    if student_verification != "true":
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "18"
        
        # 设置限制配置
        config["limitConfig"] = "true"
        config["limitText"] = "该产品仅限学生认证用户本人购买，感谢您的理解和支持。"
        
        result_list.append(config)
    
    return result_list

def parse_student_verification_method_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析学生认证方式限制(ruleType=20)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有学生认证相关数据
    student_verification = analysis_data.get("student_verification", "")
    student_verification_rules = analysis_data.get("student_verification_rules", "")
    
    # 只有当student_verification和student_verification_rules都为"true"时才处理
    if student_verification != "true" or student_verification_rules != "true":
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "20"
        
        # 设置限制配置
        config["limitConfig"] = "1,2,10,13,16,12,6"
        config["limitText"] = "该产品仅限上传学生证的乘机人购买，请去【我的-学生专区】上传。"
        
        result_list.append(config)
    
    return result_list

def parse_user_portrait_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析用户画像限制(ruleType=21)
    仅输出空白配置，不进入真实配置文件解析配置中
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有用户画像限制相关数据
    customer_limit = analysis_data.get("customer_limit", "")
    if not customer_limit:
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "21"
        
        # 设置限制配置
        config["limitConfig"] = "用户画像标签需要手动配置"
        config["limitText"] = customer_limit
        
        result_list.append(config)
    
    return result_list

def parse_adult_count_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析成人数量限制(ruleType=42)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有成人数量限制相关数据
    capacity_limit = analysis_data.get("capacity_limit", "")
    capacity_limit_supplementary = analysis_data.get("capacity_limit_supplementary", "")
    
    # 只有当capacity_limit_supplementary为"true"时才处理
    if capacity_limit_supplementary != "true":
        return result_list
    
    # 校验capacity_limit格式 - 只处理横杆拼接的字符串格式，如"1-1"
    if not capacity_limit or capacity_limit == "0" or "-" not in capacity_limit:
        return result_list
    
    # 解析capacity_limit
    try:
        if "-" in capacity_limit:
            # 范围格式，如"2-9"
            min_count, max_count = map(int, capacity_limit.split("-"))
            # 校验范围是否合法
            if min_count < 1 or max_count > 9 or min_count > max_count:
                return result_list
        else:
            # 单个数字格式，如"3"
            count = int(capacity_limit)
            # 校验数字是否合法
            if count < 1 or count > 9:
                return result_list
    except (ValueError, TypeError):
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "42"
        
        # 设置限制配置
        config["limitConfig"] = capacity_limit
        
        # 根据capacity_limit设置limitText
        if capacity_limit == "1":
            config["limitText"] = "很抱歉，该产品要求每个订单仅限1名成人预订，感谢您的理解和支持。"
        elif "-" in capacity_limit:
            min_count, max_count = map(int, capacity_limit.split("-"))
            if max_count == 9:
                config["limitText"] = f"很抱歉，该产品要求订单乘机人中成人数量需达到{min_count}位及以上，请继续添加乘机人或重新选择其他价格。"
            else:
                config["limitText"] = f"很抱歉，该产品要求订单乘机人中成人数量需达到{min_count}-{max_count}人，请调整乘机人数量或重新选择其他价格。"
        else:
            count = int(capacity_limit)
            config["limitText"] = f"很抱歉，每单最多只能添加{count}位成人乘机人"
        
        result_list.append(config)
    
    return result_list

def parse_ctrip_finance_verification_limit(domain: str, carrier: str, product_mark: str, flight_types: List[str], analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析携程金融实名认证限制(ruleType=66)
    
    参数:
    domain: str - 域名
    carrier: str - 航司
    product_mark: str - 产品标记
    flight_types: List[str] - 航班类型列表
    analysis_data: Dict[str, Any] - 分析数据
    
    返回:
    List[Dict[str, Any]] - 配置结果列表
    """
    result_list = []
    
    # 检查是否有实名认证限制相关数据
    if not analysis_data.get("realname_limit"):
        return result_list
    
    # 权益卡或次卡不配置实名认证
    if analysis_data.get("card_limit") == "true":
        return result_list
    
    # 获取实名认证限制数据
    realname_limit = analysis_data.get("realname_limit", "")
    
    # 检查是否为实名认证限制
    if realname_limit != "true":
        return result_list
    
    # 为每个航班类型创建配置
    for flight_type in flight_types:
        config = create_base_config(domain, carrier, product_mark, flight_type)
        config["ruleType"] = "66"
        
        # 设置限制配置
        config["limitConfig"] = "public_username_mobile_name_card_num"
        config["limitText"] = "很抱歉，该产品仅限乘机人为去哪儿实名认证用户本人购买"
        
        result_list.append(config)
    
    return result_list

def parse_urlencoded_structured_data(content: str) -> Tuple[Optional[List[Dict[str, Any]]], Dict[str, str]]:
    """
    解析包含URL编码structured字符串的字典参数

    :param content: 原始数据
    :return: tuple(解析后的对象, 错误信息)
    """
    raw_param = content

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}
    
def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表
    将字符串按照~~*~~分割为多行，每行解析为对象

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    # 解析每一行数据
    for entry in raw_entries:
        parsed_data = _parse_fields(entry)
        if parsed_data:  # 只添加非空数据
            result.append(parsed_data)

    return result    

def _parse_fields(part_str: str) -> Dict[str, Any]:
    """
    解析字段数据，提取键值对并进行清洗
    
    参数:
    part_str: str - 待解析的字符串部分
    
    返回:
    Dict[str, Any] - 解析后的字段字典
    """
    result = {}
    if not part_str:
        return result
        
    # 字段级解析
    fields = [f.strip() for f in part_str.split("#*#") if f.strip()]
    
    for field in fields:
        # 键值对解析
        if ":" not in field:
            continue  # 跳过无效字段
        
        key, value = field.split(":", 1)
        key = key.strip()
        value = value.strip()
        
        # 值清洗
        if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # 去除包裹的引号
        elif value.lower() == "null":
            value = None  # 处理空值
        elif value == '""':
            value = ""  # 处理空字符串标识
        
        result[key] = value
    
    return result    

