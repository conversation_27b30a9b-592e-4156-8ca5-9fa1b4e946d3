<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>文档解析助手</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      overflow-y: auto;
    }

    body {
      padding: 20px;
      min-height: 100vh;
    }

    .app-container {
      max-width: 1400px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.98);
      border-radius: 24px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(20px);
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px 40px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .header h2 {
      margin: 0;
      font-size: 32px;
      font-weight: 700;
      position: relative;
      z-index: 1;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .header .subtitle {
      margin-top: 8px;
      font-size: 16px;
      opacity: 0.9;
      position: relative;
      z-index: 1;
    }
    .main-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      min-height: calc(100vh - 40px);
    }

    .content-area {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      padding: 30px 40px 0 40px;
      flex: 0 0 auto;
    }

    .result-section {
      padding: 0 40px 30px 40px;
      flex: 0 0 auto;
    }

    .result-section .card {
      margin-bottom: 0;
    }

    .result-section .parse-result {
      height: 200px;
      max-height: 200px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .result-section .result-content {
      flex: 1;
      overflow-y: auto;
      min-height: 0;
    }

    .left-panel, .right-panel {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .card-title::before {
      content: '';
      width: 4px;
      height: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
    }
    .upload-area {
      border: 2px dashed #e0e6ed;
      padding: 40px 20px;
      text-align: center;
      cursor: pointer;
      border-radius: 16px;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .upload-area::before {
      content: '📁';
      font-size: 48px;
      display: block;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .upload-area:hover {
      border-color: #667eea;
      background: linear-gradient(135deg, #f0f2ff 0%, #e8ebff 100%);
      transform: translateY(-2px);
    }

    .upload-area p {
      margin: 8px 0;
      color: #666;
      font-size: 14px;
    }

    .upload-area .main-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }
    .file-list {
      margin-bottom: 10px;
      max-height: 150px;
      overflow-y: auto;
    }
    .file-list ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .file-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0;
      border: 1px solid #eee;
      margin-bottom: 5px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    .file-list .file-item {
      flex-grow: 1;
      padding: 8px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .file-list li:hover {
      background-color: #f5f5f5;
    }
    .file-list .file-icon {
      margin-right: 8px;
    }
    .file-list .file-name {
      flex-grow: 1;
    }
    .file-list .delete-btn {
      background-color: transparent;
      color: #f44336;
      border: none;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 0 4px 4px 0;
      transition: background-color 0.2s;
      margin-left: auto; /* 将按钮推到右侧 */
      width: 30px; /* 固定宽度，使按钮更小 */
      height: 30px; /* 固定高度，使按钮更小 */
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .file-list .delete-btn:hover {
      background-color: #ffebee;
    }
    .preview-area {
      border: 1px solid #ddd;
      padding: 10px;
      height: 150px;
      overflow-y: auto;
      margin-bottom: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 10px;
    }
    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      text-align: center;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border-radius: 12px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    button:hover::before {
      left: 100%;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    button:active {
      transform: translateY(0);
    }

    .btn-success {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }

    .btn-success:hover {
      box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    }

    .btn-danger {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    }

    .btn-danger:hover {
      box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    }

    .btn-info {
      background: linear-gradient(135deg, #2196F3 0%, #0b7dda 100%);
      box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
    }

    .btn-info:hover {
      box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
    }
    .loading {
      display: none;
      text-align: center;
      margin: 5px 0;
      padding: 8px;
      background-color: #f8f8f8;
      border-radius: 4px;
      font-size: 14px;
    }
    .status-message {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .status-message.error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
    .status-message.success {
      background-color: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    /* Word 文档预览样式 */
    .word-preview {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .word-preview h3 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 8px;
    }
    .word-content {
      line-height: 1.6;
    }
    .word-content table, .word-table {
      border-collapse: collapse;
      width: 100%;
      margin: 15px 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .word-content table, .word-content th, .word-content td,
    .word-table, .word-table th, .word-table td {
      border: 1px solid #ddd;
    }
    .word-content th, .word-content td,
    .word-table th, .word-table td {
      padding: 8px;
      text-align: left;
    }
    .word-content th, .word-table th,
    .word-content .header-row td, .word-table .header-row td {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .word-content tr:nth-child(even), .word-table tr:nth-child(even),
    .word-content .even-row, .word-table .even-row {
      background-color: #f9f9f9;
    }
    .word-content tr:hover, .word-table tr:hover {
      background-color: #f5f5f5;
    }
    .word-text {
      white-space: pre-wrap;
      font-family: Consolas, monospace;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }

    /* Excel 表格预览样式 */
    .excel-preview {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .excel-preview h3 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 8px;
    }
    .sheet-selector {
      margin: 10px 0;
    }
    .sheet-selector select {
      padding: 5px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    .sheet-content {
      margin-top: 15px;
    }
    .sheet-content h4 {
      color: #555;
      margin-bottom: 10px;
    }
    .excel-table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 15px;
    }
    .excel-table, .excel-table th, .excel-table td {
      border: 1px solid #ddd;
    }
    .excel-table th, .excel-table td {
      padding: 8px;
      text-align: left;
    }
    .excel-table .header-row td {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .excel-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .excel-table tr:hover {
      background-color: #f5f5f5;
    }
    .note {
      font-size: 12px;
      color: #777;
      font-style: italic;
    }

    /* 解析到多维表格相关样式 */
    .parse-section {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .parse-section h3 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #333;
      font-size: 16px;
    }
    .input-group {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
    .input-group label {
      width: 80px;
      font-weight: bold;
      flex-shrink: 0;
    }
    .input-group input {
      flex: 1;
      min-width: 150px;
      padding: 6px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .primary-btn {
      background-color: #2196F3;
      margin-top: 10px;
    }
    .primary-btn:hover {
      background-color: #0b7dda;
    }
    .parse-result {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #fff;
      height: 150px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .parse-result h3 {
      margin-top: 0;
      margin-bottom: 10px;
      flex-shrink: 0;
    }
    .result-content {
      white-space: pre-wrap;
      font-family: Consolas, monospace;
      background-color: #1e1e1e;
      color: #f8f8f8;
      padding: 8px;
      border-radius: 4px;
      height: 120px;
      overflow-y: auto;
      line-height: 1.4;
      font-size: 13px;
    }
    .event-node {
      color: #88c0d0;
      margin-bottom: 5px;
    }
    .event-ping {
      color: #a3be8c;
      font-style: italic;
    }
    .event-message {
      color: #ebcb8b;
    }
    .event-error {
      color: #bf616a;
    }
    .event-done {
      color: #a3be8c;
      font-weight: bold;
    }
    .initial-message {
      color: #888;
      text-align: center;
      padding: 20px;
      font-style: italic;
    }

    /* 自定义弹窗样式 */
    .custom-alert {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .alert-content {
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      max-width: 400px;
      width: 90%;
      margin: 0 auto;
    }
    .alert-content h3 {
      margin-top: 0;
      color: #2196F3;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .alert-content p {
      margin: 15px 0;
      line-height: 1.5;
    }
    .alert-content button {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      float: right;
    }
    .alert-content button:hover {
      background-color: #0b7dda;
    }

    /* 预览弹窗样式 */
    .preview-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .preview-modal-content {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      width: 80%;
      height: 80%;
      max-width: 1000px;
      max-height: 800px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      resize: both;
      min-width: 400px;
      min-height: 300px;
    }
    .preview-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-bottom: 1px solid #ddd;
      background-color: #f8f8f8;
      cursor: move;
    }
    .preview-modal-header h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
    }
    .preview-modal-close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: #666;
      cursor: pointer;
      padding: 0 5px;
    }
    .preview-modal-close-btn:hover {
      color: #f44336;
    }
    .preview-modal-body {
      flex: 1;
      padding: 15px;
      overflow: auto;
    }
    /* 预览内容样式 */
    .preview-modal .word-preview,
    .preview-modal .excel-preview {
      height: 100%;
      overflow: auto;
    }
    .preview-modal .word-content,
    .preview-modal .excel-table {
      margin-top: 10px;
    }

    /* 预览按钮样式 */
    .word-content-preview,
    .excel-content-preview {
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 4px;
      text-align: center;
    }
    .view-full-btn {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 8px 15px;
      margin-top: 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .view-full-btn:hover {
      background-color: #0b7dda;
    }

    /* 导出部分样式 */
    .export-section {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 8px;
    }
    .export-section label {
      font-weight: bold;
      color: #333;
    }
    .export-section select {
      min-width: 120px;
    }

    /* 动画效果 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .card {
      animation: fadeIn 0.6s ease-out;
    }

    .card:nth-child(1) { animation-delay: 0.1s; }
    .card:nth-child(2) { animation-delay: 0.2s; }
    .card:nth-child(3) { animation-delay: 0.3s; }
    .card:nth-child(4) { animation-delay: 0.4s; }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .content-area {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .left-panel, .right-panel {
        width: 100%;
      }

      .result-section .parse-result {
        height: 150px;
        max-height: 150px;
      }
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .app-container {
        border-radius: 16px;
      }

      .header {
        padding: 20px 24px;
      }

      .header h2 {
        font-size: 24px;
      }

      .content-area {
        padding: 20px 24px;
      }

      .card {
        padding: 20px;
      }

      .input-group {
        flex-direction: column;
        gap: 8px;
      }

      .input-group label {
        width: auto;
        font-weight: 600;
      }

      .export-section {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }

      .export-section select {
        width: 100%;
        margin: 0;
      }

      button {
        width: 100%;
        justify-content: center;
      }
    }

    @media (max-height: 600px) {
      .preview-area, .parse-result {
        min-height: 120px;
      }
      .file-list {
        max-height: 15%;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <div class="main-container">
      <div class="header">
        <h2>📄 文档解析助手</h2>
        <div class="subtitle">智能解析Word和Excel文档，支持多种格式导出</div>
      </div>

      <div class="content-area">
        <div class="left-panel">
          <div class="card">
            <div class="card-title">📁 文件上传</div>
            <div class="upload-area" id="uploadArea">
              <div class="main-text">点击或拖拽文件到此处</div>
              <p>支持Word(.docx, .doc)和Excel(.xlsx, .xls)</p>
              <input type="file" id="fileInput" multiple accept=".docx,.doc,.xlsx,.xls,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" style="display: none;">
            </div>
          </div>

          <div class="card">
            <div class="card-title">📋 文件列表</div>
            <div class="file-list" id="fileList">
              <!-- 文件列表将在这里显示 -->
            </div>
          </div>

          <div class="card">
            <div class="card-title">⚙️ 导出设置</div>
            <div class="export-section">
              <label for="exportFormat">导出格式:</label>
              <select id="exportFormat" style="margin: 0 10px; padding: 8px; border: 1px solid #ddd; border-radius: 8px; min-width: 120px;">
                <option value="txt" selected>文本 (TXT)</option>
                <option value="html">网页 (HTML)</option>
                <option value="markdown">Markdown (MD)</option>
                <option value="json">数据 (JSON)</option>
              </select>
            </div>
            <div style="display: flex; gap: 12px; margin-top: 16px;">
              <button id="previewBtn" class="btn-info">👁️ 预览</button>
              <button id="exportBtn" class="btn-success">💾 导出</button>
              <button id="clearBtn" class="btn-danger">🗑️ 清空</button>
            </div>
          </div>
        </div>

        <div class="right-panel">
          <div class="card">
            <div class="card-title">👁️ 文档预览</div>
            <div class="preview-area" id="previewArea">
              <div style="text-align: center; color: #999; padding: 40px;">
                选择文件后预览内容将显示在这里
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-title">🔄 多维表格解析</div>
            <div class="input-group">
              <label for="oaIdInput">OA ID:</label>
              <input type="text" id="oaIdInput" placeholder="请输入OA ID" value="123456" style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 8px;">
            </div>
            <div class="input-group">
              <label for="envSelect">环境:</label>
              <select id="envSelect" style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 8px;">
                <option value="beta" selected>Beta</option>
                <option value="prod">Prod</option>
              </select>
            </div>
            <div class="input-group">
              <label for="flowSignInput">Flow Sign:</label>
              <input type="text" id="flowSignInput" placeholder="请输入Flow Sign" value="sdads" style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 8px;">
            </div>
            <button id="parseToTableBtn" class="btn-info" style="width: 100%; margin-top: 16px;">🚀 开始解析到多维表格</button>
          </div>
        </div>
      </div>

      <!-- 解析结果区域 - 平铺在底部 -->
      <div class="result-section">
        <div class="card">
          <div class="card-title">📊 解析结果</div>
          <div class="loading" id="loading" style="display: none; text-align: center; padding: 20px; color: #666;">
            <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 10px;"></div>
            处理中，请稍候...
          </div>
          <div class="parse-result" id="parseResult">
            <div class="result-content" id="resultContent">
              <div class="initial-message" style="text-align: center; color: #999; padding: 40px;">
                解析结果将显示在这里
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 自定义弹窗 -->
  <div class="custom-alert" id="customAlert" style="display: none;">
    <div class="alert-content">
      <h3>提示</h3>
      <p id="alertMessage"></p>
      <button id="alertCloseBtn">确定</button>
    </div>
  </div>

  <!-- 预览弹窗 -->
  <div class="preview-modal" id="previewModal" style="display: none;">
    <div class="preview-modal-content" id="previewModalContent">
      <div class="preview-modal-header">
        <h3 id="previewModalTitle">文件预览</h3>
        <button id="previewModalCloseBtn" class="preview-modal-close-btn">×</button>
      </div>
      <div class="preview-modal-body" id="previewModalBody">
        <!-- 预览内容将在这里显示 -->
      </div>
    </div>
  </div>

  <script src="lib/xlsx.full.min.js"></script>
  <script src="lib/mammoth.browser.min.js"></script>
  <script src="lib/turndown.min.js"></script>
  <script src="lib/pako.min.js"></script>
  <script src="lib/lodash.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
  <script src="lib/cfb-lite.js"></script>
  <script src="lib/advanced-doc-extractor.js"></script>
  <script src="lib/doc-parser.js"></script>
  <script src="lib/doc-conversion-helper.js"></script>
  <script src="lib/doc-encoding-fixer.js"></script>
  <script src="lib/doc-brute-force-parser.js"></script>
  <script src="lib/doc-software-detector.js"></script>
  <script src="lib/word-enhanced-parser.js"></script>
  <script src="lib/pure-text-extractor.js"></script>
  <script src="lib/doc-smart-parser.js"></script>
  <script src="lib/doc-jszip-parser.js"></script>
  <script src="lib/table-formatter.js"></script>
  <script src="lib/enhanced-markdown-formatter.js"></script>
  <script src="lib/table-structure-analyzer.js"></script>
  <script src="lib/data-compressor.js"></script>
  <script src="lib/preview-manager.js"></script>
  <script src="documentProcessor.js"></script>
  <script src="main.js"></script>
</body>
</html>
