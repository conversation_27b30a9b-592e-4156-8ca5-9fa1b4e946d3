import os
import traceback
import requests
from json import loads
from typing import Optional

URL_QCONFIG_APP_INFO = "http://pbservice.corp.qunar.com/api/app/info.json"


class AppTokenManager:
    def __init__(self, app_token: str, env: Optional[str] = None):
        self.app_token = app_token
        self.env = env
        self.server_token = None
        self.hostname = None
        self.server_type = None
        self.app_code = None

    def _init_get_server_token(self):
        body = {"token": self.app_token, "server.pid": os.getpid()}
        if self.env:
            body["userdefine.env"] = self.env

        response = requests.post(
            URL_QCONFIG_APP_INFO,
            json=body,
            headers={"Content-Type": "application/json"},
        )
        data = loads(response.text)["data"]

        self.server_token = data["server.token"]
        self.hostname = data["server.hostname"]
        self.server_type = data["server.type"]
        self.app_code = data["name"]

    def get_server_token(self):
        if not self.server_token:
            self._init_get_server_token()
        return self.server_token

    def get_appCode(self):
        if not self.app_code:
            self._init_get_server_token()
        return self.app_code


def main(param: dict) -> dict:
    try:
        invokeToken = param.get("appToken")

        env = "prod"
        app_token_manager = AppTokenManager(invokeToken, env)
        serverToken = app_token_manager.get_server_token()
        appCode = app_token_manager.get_appCode()
        if serverToken:
            return {
                "isSuccess": "true",
                "errorMsg": "",
                "serverToken": serverToken,
                "appCode": appCode,
            }
        else:
            return {
                "isSuccess": "false",
                "errorMsg": "获取serverToken失败",
                "serverToken": "",
                "appCode": "",
            }
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"获取serverToken失败: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {
            "isSuccess": "false",
            "errorMsg": error_msg,
            "serverToken": "",
            "appCode": "",
        }
        return result


if __name__ == "__main__":
    param = {
        "appCode": "f_pangu",
        "appToken": "oABPAGnqeGY7y8OtsKjLiHQ2K1SzBuOLT8zYWyHCnviQX+KqugKDLwDcGpD8qRXSGTz2BFiDyWrwmI1otiXHGjlAIndwbOQh3LdaHBI7NR6s6G04LJB3fUFQHpVpjC4ISGT7gjw8IgiGR+b+ln/t0BsdLbkp1ZeHRbCZcgLw2Z0=",
    }
    result = main(param)
    print(result)
