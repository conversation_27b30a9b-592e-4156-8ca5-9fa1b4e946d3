from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from datetime import datetime
from string import Formatter
import re
from typing import Union


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "Missing required parameter: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "Missing required parameter: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def split_price_changes(groups: dict, group_type: str) -> tuple:
    """拆分价格变化的分组（增强版）"""
    price_change = {}
    no_change = {}

    for key, items in groups.items():
        # 处理航司/航班号分组
        if group_type in ("airline", "flight"):
            # 获取所有唯一价格（字符串形式）
            prices = {str(item["price"]) for item in items}

            if len(prices) > 1:
                price_change[key] = items
            else:
                no_change[key] = {
                    "group_type": group_type,
                    "group_value": key,
                    "flight_nos": list(
                        {item["flightNo"] for item in items}
                    ),  # 去重后的航班号列表
                    "prices": list(prices),  # 保证单值
                }

        # 处理OTA关联数据
        elif group_type == "list2ota":
            ota_price = items["otaPrice"]["price"]
            list_price = items["listPrice"]["price"]

            if ota_price != list_price:
                price_change[key] = items
            else:
                no_change[key] = {
                    "group_type": group_type,
                    "group_value": key,
                    "flight_nos": [items["otaPrice"]["flightNo"]],
                    "prices": [ota_price],
                }

    return price_change, no_change


def generate_no_change_report(no_change_groups: dict) -> str:
    """生成无价格变动报告字符串"""
    if not no_change_groups:
        return ""

    report = []
    for key, group in no_change_groups.items():
        report.append(
            f"结论：无价格变动 | "
            f"分组类型: {group['group_type']} | "
            f"分组值: {group['group_value']}\n"
            f"  涉及航班: {', '.join(group['flight_nos'])}\n"
            f"  稳定价格: {group['prices'][0]}\n"
            f"{'-'*40}"
        )

    return "\n".join(report) + "\n"


def get_airline(flight_no: str) -> str:
    if "/" in flight_no:
        parts = flight_no.split("/")
        return "/".join([p[:2] for p in parts])
    return flight_no[:2]


def group_by_airline(data: List[dict]) -> List[dict]:
    # 第一层分组：按航司
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}

    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")

        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]
        airline = get_airline(flight_no=flight_no)
        # 初始化航班分组
        if airline not in flight_groups:
            flight_groups[airline] = {}

        # 初始化追踪ID分组
        if tracer_id not in flight_groups[airline]:
            flight_groups[airline][tracer_id] = []

        # 添加原始数据（保留完整字段）
        flight_groups[airline][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append(
                {
                    "groupKey": tracer_key,
                    "groupType": "tradeId",
                    "groupValue": items.copy(),  # 包含完整的原始数据
                }
            )

        result.append(
            {"groupKey": flight_key, "groupType": "airline", "groupValue": tracer_list}
        )

    return result


def group_by_flightno(data: List[dict]) -> List[dict]:
    # 第一层分组：按航班号
    flight_groups: Dict[str, Dict[str, List[dict]]] = {}

    for item in data:
        # 验证必要字段存在
        if "flightNo" not in item or "tradeId" not in item:
            raise ValueError("数据必须包含flightNo和tradeId字段")

        flight_no = item["flightNo"]
        tracer_id = item["tradeId"]

        # 初始化航班分组
        if flight_no not in flight_groups:
            flight_groups[flight_no] = {}

        # 初始化追踪ID分组
        if tracer_id not in flight_groups[flight_no]:
            flight_groups[flight_no][tracer_id] = []

        # 添加原始数据（保留完整字段）
        flight_groups[flight_no][tracer_id].append(dict(item))

    # 构建最终结果结构
    result = []
    for flight_key, tracer_group in flight_groups.items():
        tracer_list = []
        for tracer_key, items in tracer_group.items():
            tracer_list.append(
                {
                    "groupKey": tracer_key,
                    "groupType": "tradeId",
                    "groupValue": items.copy(),  # 包含完整的原始数据
                }
            )

        result.append(
            {"groupKey": flight_key, "groupType": "flightNo", "groupValue": tracer_list}
        )

    return result


# 方法二：判断是否统一航班号
def is_unified_flight(data: list) -> bool:
    flights = {item["flightNo"] for item in data}
    return len(flights) == 1


# 方法四：处理OTA数据关联
def process_ota_data(data: list) -> dict:
    # 建立list数据索引
    list_data = {item["tradeId"]: item for item in data if item["tSource"] == "list"}

    # 处理OTA数据
    result = {}
    for item in data:
        listTradeId = item.get("listTradeId")
        if item["tSource"] == "ota" and listTradeId is not None:
            list_item = list_data.get(item["listTradeId"])
            result[item["tradeId"]] = {"otaPrice": item, "listPrice": list_item}
    return flatten_grouped_data(result, "list2ota")


def flatten_grouped_data(grouped_data: Dict[str, any], group_type: str) -> List[dict]:
    result = []

    for group_key, groupData in grouped_data.items():
        # 合并所有trace分组下的数据
        merged_data = []
        result.append(
            {"groupKey": group_key, "groupType": group_type, "groupValue": groupData}
        )

    return result


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    return False


def format_json_placeholder(template: str, obj1, obj2) -> str:
    """
    将两个对象JSON序列化后填充到字符串模板的占位符

    :param template: 含两个{}占位符的字符串模板
    :param obj1: 第一个可序列化对象
    :param obj2: 第二个可序列化对象
    :return: 格式化后的字符串

    示例：
    >>> data = {"name": "张三", "age": 25}
    >>> lst = [1, 2, 3]
    >>> print(format_json_placeholder("用户数据：{}，订单列表：{}", data, lst))
    用户数据：{"name": "张三", "age": 25}，订单列表：[1, 2, 3]
    """
    # 序列化时保持中文可读性（ensure_ascii=False）
    json_str2 = json.dumps(obj2, ensure_ascii=False, indent=None)  # 紧凑格式

    try:
        return template.format(obj1, json_str2)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


def getFlightGroupListPrices(flightPriceGroup: List[Dict]) -> Dict[str, List[Dict]]:
    """
    方法1: 获取每个航班号下所有list类型价格数据，并按时间排序

    参数:
    flightPriceGroup: 原始航班价格分组数据

    返回:
    {
        "MU9807": [sorted list prices...],
        "MU4890": [sorted list prices...]
    }
    """
    result = {}

    # 遍历航班分组
    for flight_group in flightPriceGroup:
        flight_no = flight_group["groupKey"]
        list_prices = []

        # 遍历tradeId分组
        for trade_group in flight_group["groupValue"]:
            # 筛选tSource=list的数据
            for price in trade_group["groupValue"]:
                if price.get("tSource") == "list":
                    list_prices.append(price)

        # 按searchDateTime排序
        if list_prices:
            list_prices.sort(
                key=lambda x: datetime.strptime(
                    x["searchDateTime"], "%Y-%m-%d %H:%M:%S"
                )
            )
            result[flight_no] = list_prices

    return result


def try_get_float(value):
    """尝试将值转换为浮点数，排除布尔类型"""
    if isinstance(value, bool):
        return None
    if isinstance(value, (int, float)):
        return float(value)
    elif isinstance(value, str):
        try:
            return float(value)
        except ValueError:
            return None
    else:
        return None


def fieldEquals(ota_value: Any, list_value: Any) -> bool:
    """严格字段值比对方法（不进行类型转换）"""
    # 处理空值逻辑
    if ota_value is None and list_value is None:
        return True
    if ota_value is None or list_value is None:
        return False

    # 处理字符串类型（去除首尾空格）
    if isinstance(ota_value, str) and isinstance(list_value, str):
        return ota_value.strip() == list_value.strip()

    # 尝试将两个参数都转为浮点数
    a_float = try_get_float(ota_value)
    b_float = try_get_float(list_value)

    # 两个都能转浮点数时比较数值
    if a_float is not None and b_float is not None:
        return a_float == b_float

    # 严格类型和值匹配
    return ota_value == list_value


def filterOtaPricesByFiledValue(
    ota_prices: List[Dict], list_price: Dict, field: str
) -> List[Dict]:
    """按字段值过滤（严格模式）"""
    target_value = list_price.get(field)

    # 精确匹配分组
    exact_matches = []
    non_exact = []

    for ota in ota_prices:
        ota_value = ota.get(field)
        if fieldEquals(ota_value, target_value):
            exact_matches.append(ota)
        else:
            non_exact.append(ota)

    # 优先返回精确匹配，否则保持原顺序
    return exact_matches if exact_matches else non_exact


def findBestMatchOtaPrice(flightPriceGroup: List[Dict], listPrice: Dict) -> Dict:
    """
    方法2: 查找与list价格最匹配的ota价格

    参数:
    flightPriceGroup: 原始数据
    listPrice: 要匹配的list价格条目

    返回:
    bestOtaPrice
    """
    # 步骤1: 获取目标航班分组
    target_flight = next(
        (fg for fg in flightPriceGroup if fg["groupKey"] == listPrice["flightNo"]), None
    )
    if not target_flight:
        return None

    # 步骤2: 筛选符合条件的ota数据
    candidate_otas = []
    for trade_group in target_flight["groupValue"]:
        for price in trade_group["groupValue"]:
            # 筛选条件
            if (
                price.get("tSource") == "ota"
                and price.get("listTradeId")
                and price["listTradeId"] == listPrice.get("tradeId")
            ):
                candidate_otas.append(price)

    # 匹配优先级逻辑
    def match_priority(ota_item: Dict) -> int:
        """定义匹配优先级"""
        # 第一优先级: tag精确匹配
        if ota_item["tag"] == listPrice["tag"]:
            return 1
        # 第二优先级: 交叉匹配
        elif (
            ota_item["tag"] == listPrice.get("oriTag")
            or ota_item.get("oriTag") == listPrice["tag"]
            or ota_item.get("oriTag") == listPrice.get("oriTag")
        ):
            return 2
        # 第三优先级
        else:
            return 3

    # 按优先级分组
    priority_groups = {1: [], 2: [], 3: []}
    for ota in candidate_otas:
        priority_groups[match_priority(ota)].append(ota)

    # 按优先级处理
    best_ota = None
    # 按优先级处理候选数据
    compare_fields = ["cabin", "wrapperId", "productMark"]
    best_match = None
    for priority in [1, 2, 3]:
        current_group = priority_groups[priority]
        if not current_group:
            continue

        # 步骤1：价格分组
        exact_price = [
            ota
            for ota in current_group
            if fieldEquals(ota.get("price"), listPrice.get("price"))
        ]
        other_price = [
            ota
            for ota in current_group
            if not fieldEquals(ota.get("price"), listPrice.get("price"))
        ]

        current_candidates = None
        # 步骤2：字段级精确匹配
        for group in [exact_price, other_price]:
            if not group:
                continue

            current_candidates = group
            for field in compare_fields:
                current_candidates = filterOtaPricesByFiledValue(
                    current_candidates, listPrice, field
                )
                if len(current_candidates) == 1:
                    best_match = current_candidates[0]
                    return best_match
            if current_candidates is not None and len(current_candidates) > 0:
                best_match = current_candidates[0]
                return best_match
    return None


def getFlightAllListOtaPairs(
    listPrices: Dict[str, List[Dict]], flightPriceGroup: List[Dict]
) -> Dict[str, List[Tuple[Dict, Dict]]]:
    """
    方法3: 获取所有航班list-ota配对

    参数:
    listPrices: 方法1的返回结果
    flightPriceGroup: 原始数据

    返回:
    {
        "MU9807": [
            (listPrice1, otaPrice1),
            (listPrice2, otaPrice2),
            ...
        ]
    }
    """
    result = {}

    for flight_no, prices in listPrices.items():
        pairs = []
        for list_price in prices:
            # 在原始数据中寻找最佳匹配的OTA价格
            matchOtaPrice = findBestMatchOtaPrice(flightPriceGroup, list_price)
            if matchOtaPrice is not None:
                # 当找到匹配时，组成配对元组
                pairs.append((list_price, matchOtaPrice))
        # 即使没有匹配项也保留空列表
        if len(pairs) > 0:
            result[flight_no] = pairs
    return result


from datetime import datetime
from typing import List, Dict, Tuple


def convert_price_to_numeric(priceValue):
    """
    将字典中的 price 字段转为数值类型
    如果转换失败则保留原值
    """
    try:
        if priceValue is None:
            return priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                return int(priceValue)
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                price = float(priceValue)
                # 如果是整数型浮点数（例如 "100.0" → 100）
                if price.is_integer():
                    return int(price)

        return priceValue
    except (ValueError, TypeError, AttributeError):
        # 捕获所有可能的转换异常，返回原数据
        return priceValue


def genPriceCompareRow(
    preSearchPrice: Dict,
    surSearchPrice: Dict,
    compareType: str,
    oriQe: str,
    templatePrompt: str,
) -> Dict:
    """
    方法4: 生成单行价格对比记录

    参数示例：
    preSearchPrice = {
        'flightNo': 'MU9807', 'departureDate': '2023-03-20',
        'price': 800, 'cabin': '经济舱', 'searchTime': '2023-03-20T10:00:00',
        'tag': '促销', 'oriTag': '早鸟票'
    }
    compareType = 'list-ota'
    """
    # 基础字段提取
    base_fields = {
        "preFlightNo": preSearchPrice.get("flightNo"),
        "surFlightNo": surSearchPrice.get("flightNo"),
        "preDepartureDate": preSearchPrice.get("departureDate"),
        "surDepartureDate": surSearchPrice.get("departureDate"),
        "preDepTime": preSearchPrice.get("depTime"),
        "surDepTime": surSearchPrice.get("depTime"),
        "departureCity": preSearchPrice.get("departureCity"),  # 假设城市信息相同
        "arrivalCity": preSearchPrice.get("arrivalCity"),
        "compareType": compareType,
        "preTradeId": preSearchPrice.get("tradeId"),
        "surTradeId": surSearchPrice.get("tradeId"),
        "preCabin": preSearchPrice.get("cabin"),
        "surCabin": surSearchPrice.get("cabin"),
    }

    time_diff = (
        parse_search_time(surSearchPrice["searchDateTime"])
        - parse_search_time(preSearchPrice["searchDateTime"])
    ).total_seconds()

    # 价格计算逻辑
    price_diff = convert_price_to_numeric(
        surSearchPrice.get("price", 0)
    ) - convert_price_to_numeric(preSearchPrice.get("price", 0))

    # Tag交叉匹配逻辑
    tag_fields = {
        "preTag": preSearchPrice.get("tag"),
        "preOriTag": preSearchPrice.get("oriTag"),
        "surTag": surSearchPrice.get("tag"),
        "surOriTag": surSearchPrice.get("oriTag"),
    }
    tag_is_same = any(
        [
            tag_fields["preTag"] == tag_fields["surTag"],
            tag_fields["preTag"] == tag_fields["surOriTag"],
            tag_fields["preOriTag"] == tag_fields["surTag"],
            tag_fields["preOriTag"] == tag_fields["surOriTag"],
        ]
    )

    return {
        **base_fields,
        "preSearchTime": preSearchPrice["searchDateTime"],
        "surSearchTime": surSearchPrice["searchDateTime"],
        "searchTimeDiff": time_diff,
        "prePrice": preSearchPrice.get("price"),
        "surPrice": surSearchPrice.get("price"),
        "priceDiff": price_diff,
        **tag_fields,
        "tagIsSame": str(tag_is_same),
        "comparePrompt": genPriceComparePrompt(
            preSearchPrice,
            surSearchPrice,
            oriQe=oriQe,
            compare_type=compareType,
            templatePrompt=templatePrompt,
        ),
    }


def genListPriceCompareRows(
    listprices: List[Dict],
    oriQe: str,
    templatePrompt: str,
    searchTimeDurationLimit: int = None,
) -> List[Dict]:
    """
    方法5: 生成列表价格对比记录

    示例输入：
    [
        {'searchDateTime': '2023-03-20 09:00:00', 'price': 800},
        {'searchDateTime': '2023-03-20 09:05:00', 'price': 790},
        {'searchDateTime': '2023-03-20 10:00:00', 'price': 810}
    ]
    """
    if len(listprices) <= 1:
        return []

    results = []
    for i in range(len(listprices) - 1):
        pre = listprices[i]
        sur = listprices[i + 1]

        # 计算时间间隔（秒）
        delta = (
            parse_search_time(sur["searchDateTime"])
            - parse_search_time(pre["searchDateTime"])
        ).total_seconds()

        # 时间剪枝逻辑
        if searchTimeDurationLimit and delta > searchTimeDurationLimit:
            continue

        results.append(genPriceCompareRow(pre, sur, "list-list", oriQe, templatePrompt))

    return results


def genListOtaPriceCompareRows(
    listOtaPairs: List[Tuple[Dict, Dict]], oriQe: str, templatePrompt: str
) -> List[Dict]:
    """
    方法6: 生成List-OTA价格对比记录

    示例输入：
    [
        (list_price1, ota_price1),
        (list_price2, ota_price2)
    ]
    """
    return [
        genPriceCompareRow(list_p, ota_p, "list-ota", oriQe, templatePrompt)
        for list_p, ota_p in listOtaPairs
    ]


def genAllPriceCompareRows(
    list_prices: Dict[str, List[Dict]],
    listOtaPricesPairs: Dict[str, List[Tuple[Dict, Dict]]],
    oriQe: str,
    templatePrompt: str,
    searchTimeDurationLimit: int = None,
) -> List[Dict]:
    all_rows = []

    list_prices = list_prices or {}
    listOtaPricesPairs = listOtaPricesPairs or {}

    # 使用union方法替代|操作符
    valid_flight_nos = set(list_prices.keys()).union(listOtaPricesPairs.keys())

    # 按航班号分组处理
    for flight_no in valid_flight_nos:
        flight_rows = []

        # 处理list-list对比（同航班价格波动）
        if flight_no in list_prices:
            sorted_prices = list_prices[flight_no]
            list_compare = genListPriceCompareRows(
                sorted_prices, oriQe, templatePrompt, searchTimeDurationLimit
            )
            flight_rows.extend(list_compare)

        # 处理list-ota对比（同航班渠道差异）
        if flight_no in listOtaPricesPairs:
            pairs = listOtaPricesPairs[flight_no]
            ota_compare = genListOtaPriceCompareRows(
                pairs, oriQe=oriQe, templatePrompt=templatePrompt
            )
            flight_rows.extend(ota_compare)

        all_rows.extend(flight_rows)

    return all_rows


# 需实现的提示生成函数（示例）
def genPriceComparePrompt(
    pre: Dict, sur: Dict, oriQe: str, compare_type: str, templatePrompt: str
) -> str:
    # 序列化时保持中文可读性（ensure_ascii=False）
    preData = json.dumps(pre, ensure_ascii=False, indent=None)  # 紧凑格式
    surData = json.dumps(sur, ensure_ascii=False, indent=None)  # 紧凑格式

    base_params = {
        "preData": preData,
        "surData": surData,
        "compareType": compare_type,
        "question": oriQe,
    }

    try:
        formatter = Formatter()
        required_fields = [fn for _, fn, _, _ in formatter.parse(templatePrompt) if fn]

        # 构建安全参数（自动填充缺失字段）
        safe_params = {field: "" for field in required_fields}
        safe_params.update(
            {k: v for k, v in base_params.items() if k in required_fields}
        )
        return templatePrompt.format_map(safe_params)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e


def genPrompt(obj1, obj2) -> str:
    template = """你是一名资深的机票行业运营，现在用户反馈机票搜索有变价，请结合用户反馈问题描述，搜索数据明细，搜索业务规则，变价场景和变价分析规则进行搜索变价分析
# 变价分析目标
搜索变价分析目标：根据用户的反馈question，搜索数据明细，搜索业务规则，变价场景和变价分析规则分析是否发生用户反馈的变价，并且进行变价归因和变价定责

#入参
##：入参说明
question参数： 用户反馈的信息
param 参数：结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
##：入参明细
question: {}
param: {}

#机票搜索业务说明
## 搜索流程
1、搜索分为list搜索（简称L页）和ota搜索（简称D页），用户搜索先进list，list展示用户选择的航线，起飞日期展示不同航班号的价格（一个航班号在list也只有一个航班号，通常是用户能看到的最低价）
2、用户在list页点击具体的航班会进入ota搜索，ota展示同航班，不同tag的报价，ota的tag分为(tag, oritag)，ota正常情况下有一个tag跟list tag(ota.tag = list.tag || ota.oriTag = list.tag)和价格(price)都一致的报价；从list进ota的血缘关系可以 ota.listTradeId = list.tradeId 关联
3、用户可能进行多次list搜索，也可以从同一个list点同一航班或不同航班多次进入ota搜索
4、每一次搜索事件都对应有一个唯一的tradeId

#搜索变价业务说明
## 用户视角能感知搜索变价业务场景
以下是用户视角能感知到的分维度变价场景，几个维度可以组合
- L页间变价 | L→D页变价
- 时间维度：秒级/分钟级/天级
- 产品维度：同航班/跨航司/跨航线

### 用户视角变价场景定责说明
#### 平台无责场景（通常我们分析变价可以直接忽略此类场景）
1、不同航线变价
2、不同航班变价
3、不同航司变价
4、风控用户：如黄牛用户（用户标签包含 HNMX,SBHN,ZFHN,DJH,HSRY），抓取用户已经被平台识别投毒（poison=true）
5、用户搜索筛选项变化：如filters内容不一致
6、用户搜索时身份变化：乘机人不一致
7、同航班搜索时间跨度大（目前天级维度的就可判断平台无责，小时级维度的需要进一步分析原因定责

#### 同航班时间跨度范围不大的定责
1、多次搜索舱位变化，一般是航班基础数据(cabin)变化，用户平台均无责
2、票面价变化，一般是航班基础数据变化（viewPrice），用户平台均无责
3、供应（wrapperId）变化且票面没变，用户平台均无责


## 数据字段说明
### param结构说明
结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
### param明细字段
searchDateTime:搜索时间
departureDate:航班起飞日期
depTime:航班起飞时间
tradeId：每次搜索事件唯一id
tSource:list/ota搜索枚举
cabinType:舱等信息，目前只有ota能拿到舱等，list可以默认经济舱价格
price:展示价
coupon:代金券，影响展示价格
cut:营销金额，影响展示价格
xCut:辅营营销金额，影响展示价格
tag:tag
oriTag:实tag，ota搜索会有根据一个实oriTag包装出多个tag的场景，这类case理论上他们之间的viewPrice和basePrice是一样的
expVendor:膨胀金金额，跟expansionType组合判断本价格是否用了膨胀金
expansionType:膨胀金计算类型，跟expVendor组合使用，当expVendor > 0时，根据下面枚举决定是否使用膨胀金，DURING说明当前搜索使用了膨胀金
   LIMIT(0, "不允许参与"),
   ALLOW(1, "可参与"),
   DURING(2, "参与中")
poison:投毒标识,=true说明平台识别的非法用户，进行了投毒
basicLabels:用户标签，识别用户身份使用（用户身份影响价格包装）
filters:搜索筛选项，如舱等选择，乘机人类型选择都可能影响价格
passengers:乘机人信息，影响价格（如特殊产品低价只有特定乘机人能买,会员产品,不同乘机人绑定不同代金券和营销）
wrapperId:供应id，供应不一致会导致价格变化
cabin:舱位，舱位变化大概率价格变化
packagePrice:包装价
basePrice:政策价
viewPrice:票面价
policyId:政策id，政策id不一致价格计算方式可能不一样
autoPriceDecreaseAmount:追价金额，平台低价策略，追价产品可根据次低价调整追价金额，达到低价优势,追价金额影响政策价计算
secondPrice:次低价，影响追价金额取值
CPT:报价生成时间，生成时间不一致可能是底层航班基础数据变化，结合viewPrice,basePrice,wrapperId,cabin看
allGoodItemPrice:加价商品金额，价格不一致可能是加价商品金额不一致

### param里价格计算依赖顺序
(先算)viewPrice -> basePrice -> packagePrice -> price(最后算)
#### 导致价格不一致的因素
viewPrice不一致: cabin变化, 航班调价
viewPrice一致，basePrice不一致影响因素：(secondPrice，autoPriceDecreaseAmount，policyId，wrapperId）
basePrice一致，packagePrice不一致影响因素(allGoodItemPrice,[expVendor,expansionType])
packagePrice一致，price不一致影响因素(coupon,cut,xCut,tag)

### 变价原因归因规则
1、展示价不同，判断投毒，如果结果不同，则归因为投毒
2、展示价不同，投毒相同，判断乘机人是否相同，如果乘机人不同，则归因为乘机人填写不同
3、展示价不同，投毒相同，乘机人相同，判断筛选项是否相同，如果结果为不同，则归因为筛选项
4、展示价不同，投毒相同，乘机人相同，筛选项相同，判断tag是否相同，如果tag不同则归因为无外露同tag
5、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，判断营销，如果包装价不变或变低，则归因为营销变化
6、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，判断用户是否参与膨胀金，如果不同，则归因为膨胀金类型不同，如果相同但膨胀金金额不同，则归因为膨胀金金额不同
7、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位变化，则归因为航班基础数据变化-舱位变化
8、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位不变，则归因为航班基础数据变化-运价变化
9、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，不同代理，则归因为供应变化-代理商调整价格
10、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，同政策，追价金额变化，tag内次低价变化，则归因为供应变化-tag内次低价变化
11、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，同政策，追价金额变化，则归因为平台策略-追价变化
12、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，同政策，追价金额不变，则归因为其他
13、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，非同政策，报价生成时间变化，则归因为供应变化-报价生成时间变化
14、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价未变，同代理，非同政策，低价被过滤 ，则归因为平台策略-低价被过滤
15、展示价不同，投毒相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价不变，加价变化（allGoodItemPrice）、限价变化、其余商品变化，则归因为平台策略-加价等商品变化

## 基于用户问题进行搜索变价分析说明

### 分析大类类型
1、 L页 -> L页变价分析：同航班不同list搜索变价分析，即分析的时候只取同行班且tSource=list的数据(L页 -> L页变价分析：变价)
2、 L页->D页变价分析：同航班具有血缘关系的list->ota搜索分析(L页：tSource=list -> D页：tSource=ota 变价) - 如果用户明确指定了是L页变价，可不执行L页->D页的变价分析

### param分析数据剪枝说明
1、 结合用户的问题，如果报价航班，航司，搜索日期，航班起飞日期，时间，价格跟 param里的报价项不匹配，可提前剪枝，忽略此类数据
2、 如果某类分析分组仅有一个搜索事件（如某个航班只有一次list搜索事件），可提前剪枝，忽略此类数据（记录剪枝原因）

### 分析前置数据处理
1、list搜索事件请按照搜索时间升序排序，如果没有搜索时间可从tradeId提取，如ops_slugger_250226.181810.10.90.5.84.1867071.8637025621_1 时间是(250226.181810)

### 价格比较说明
1、都是比展示价格，即price
2、同航班不同list价格比较，比价无需区分tag，直接比同航班的price，如果有变价则进行归因
3、list -> ota 的比价说明，优先选同航班，同tag进行对比(ota.tag = list.tag || ota.oriTag = list.tag，如果同航班同一个list 搜索有多条ota数据 && 有tag也匹配的ota数据，则可忽略其他同list但tag不一致的ota数据（优先取ota.tag = list.tag的数据），如果有变价则进行归因



### 分析步骤（以上两类分析都是执行下面步骤）
#### 执行L页 -> L页变价分析（分析步骤如下）： 执行执行L页 -> L页变价分析条件说明：用户未明确说明是L页还是D页变价 或者用户明确指定是L页变价
##### step1: 组合分类数据(同航班list搜索的数据，按照搜索时间升序排序)
##### step2：遍历分类数据 -> 执行剪枝规则-> 数据前置处理-> 执行比价分析(同航班不同list搜索数据前后两对比,首条数据作为基准值，不需要填写归因结论) -> 记录是否变价结果(这组比价的流水号flightNo，tradeId, price, tag, oriTag, searchDateTime，searchTimeDiff(搜索时间差，时间差用人类阅读友好的方式展示，如xx秒，xx分钟xx秒) tSource, isPriceChage（是否变价），priceChageDesc(变价判断依据))
##### step3: 如step2存在变价数据，对这组数据进行变价归因，输出归因信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）


#### L页->D页变价分析（分析步骤如下）：执行L页->D页变价分析条件说明： 用户未明确说明是L页还是D页变价 或者用户明确指定是L-D页变价才执行这个分析
##### step1: 组合分类数据(同航班ota 和有血缘关系的list数据进行分组,组合有血缘关系的L-D分组时，我们需要优先取D.listTradeId = L.tradeId && D.tag = L.tag的数据，只有不存在D.tag = L.tag 的数据才放宽只比D.listTradeId = L.tradeId，同航班，同list，同D.tradeId 只需要取一组最匹配的数据)
##### step2：遍历分类数据 -> 执行剪枝规则-> 数据前置处理-> 执行比价分析 -> 记录是否变价结果(这组比价的流水号flightNo，tradeId,listTradeId, price(list和ota), tag(list和ota), oriTag(list和ota), searchDateTime，searchTimeDiff(L和D搜索时间差，时间差用人类阅读友好的方式展示，如xx秒，xx分钟xx秒) tSource, isPriceChage（是否变价），priceChageDesc(变价判断依据))
##### step3: 如step2存在变价数据，对这组数据进行变价归因，输出归因信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）


# 输出
1、结合用户是问题给出是否有变价，字段名:hasPriceChange = true|false， 是否有变价的依据，字段名：hasPriceChangeDesc
2、如果执行L页 -> L页有变价，按照markdow格式输出变价信息，如果同航班有变价且有多个list搜索，这些list原始数据都输出，不要丢弃任何数据(首条数据作为基准值，priceChageDesc,attributeDesc,attributeReason,attributeResp无需赋值)
        flightNo，tradeId, price, tag, oriTag, searchDateTime，searchTimeDiff,tSource, isPriceChage（是否变价），priceChageDesc,(变价判断依据),信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）
3、如果执行L页 -> D页有变价，按照markdow格式输出变价信息，如果同航班有变价且有多个list搜索，尽量这些list分析都输出
        flightNo，tradeId,listTradeId, price(list和ota), tag(list和ota), oriTag(list和ota), searchDateTime，searchTimeDiff,tSource, isPriceChage（是否变价），priceChageDesc(变价判断依据),信息推理信息（attributeDesc），原因（attributeReason）和定责（attributeResp）
输出特别说明：
        a、tradeId不能截断和省略，searchDateTime需要有，如果是变价的case，attributeDesc，attributeReason，attributeResp不能省略
        b、list分析对比强制原始数据都输出，不要丢弃任何数据"""
    return format_json_placeholder(template, obj1, obj2)


def convert_to_seconds(param_value) -> Union[int, None]:
    """
    将输入参数转换为秒数，转换失败返回None

    支持格式：
    - 整数：直接视为秒数
    - 字符串格式：
      - 纯数字："120" → 120
      - 带单位："60s"、"5m"、"2h"、"3d"
    - 其他类型：返回None
    """
    # 处理空值
    if param_value is None:
        return None

    # 排除布尔值（isinstance(True, int) 会返回True）
    if isinstance(param_value, bool):
        return None

    # 尝试直接转换整数
    if isinstance(param_value, int):
        return param_value if param_value >= 0 else None

    # 处理字符串类型
    if isinstance(param_value, str):
        # 统一去除空格
        clean_str = param_value.strip().lower()

        # 空字符串处理
        if not clean_str:
            return None

        # 正则匹配数字和单位
        match = re.match(r"^(\d+)([smhd]?)$", clean_str)
        if not match:
            return None

        num_str, unit = match.groups()
        try:
            num = int(num_str)
        except ValueError:
            return None

        # 根据单位转换
        unit_multiplier = {
            "": 1,  # 无单位默认为秒
            "s": 1,  # 秒
            "m": 60,  # 分钟
            "h": 3600,  # 小时
            "d": 86400,  # 天
        }

        if unit not in unit_multiplier:
            return None

        return num * unit_multiplier[unit]

    # 其他类型不处理
    return None


def main(param: dict) -> dict:
    try:
        data, parseStatus = parse_urlencoded_structured_data(param, "param")
        oriQe = param.get("question")
        oneToOnetemplatePrompt = param.get("oneToOnetemplatePrompt")
        searchTimeDurationLimit = param.get("listSearchDurationLimit")
        listSearchDurationLimit = convert_to_seconds(searchTimeDurationLimit)

        if parseStatus["status"] != "success":
            return {
                "status": 404,
                "errMsg": parseStatus["message"],
                "prompt": "数据解析为空，生成提示词异常！",
                "allCompareRows": [],
            }

        # print(json.dumps(data, indent=2, ensure_ascii=False))

        # final_report = ""
        # 方法二调用
        # unified = is_unified_flight(data)
        # print(f"\n是否统一航班号：{unified}")
        # airline_groups: Dict[str, Dict[str, List[dict]]] = {}
        # if not unified:
        # airline_groups = group_by_airline(data)
        # print("航司分组结果：")
        # print(json.dumps(airline_groups, indent=2, ensure_ascii=False))

        # 拆分价格变动组
        # airline_pc, airline_nc = split_price_changes(airline_groups, 'airline')
        # final_report += generate_no_change_report(airline_nc)

        # 方法三调用
        flight_groups = group_by_flightno(data)
        # print("\n航班号分组结果：")
        # print(json.dumps(flight_groups, indent=2, ensure_ascii=False))
        # 拆分价格变动组
        # flight_pc, flight_nc = split_price_changes(flight_groups, 'flight')
        # final_report += generate_no_change_report(flight_nc)

        # 方法四调用
        # ota_relations = process_ota_data(data)
        # print("\nOTA数据关联结果：")
        # print(json.dumps(ota_relations, indent=2, ensure_ascii=False))

        # 拆分价格变动组
        # ota_pc, ota_nc = split_price_changes(ota_relations, 'ota')

        # 生成报告
        # 生成综合报告
        # final_report += generate_no_change_report(ota_nc)

        # 执行方法1
        list_prices = getFlightGroupListPrices(flight_groups)
        # print("List Prices:", len(list_prices))

        # 执行方法3
        listOtaPricesPairs = getFlightAllListOtaPairs(list_prices, flight_groups)
        # print("All Pairs:", json.dumps(listOtaPricesPairs, indent=2, ensure_ascii=False))

        allCompareRows = genAllPriceCompareRows(
            list_prices,
            listOtaPricesPairs,
            oriQe,
            oneToOnetemplatePrompt,
            listSearchDurationLimit,
        )
        # print("allCompareRows:", json.dumps(allCompareRows, indent=2, ensure_ascii=False))

        prompt = genPrompt(oriQe, flight_groups)
        finalData = {
            "status": 200,
            "errMsg": "",
            "prompt": prompt,
            "allCompareRows": allCompareRows,
        }
        return finalData
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        # print(f"参数缺失: {e}")
        result = {
            "status": 404,
            "errmsg": f"生成提示词异常！: {e}",
            "prompt": "生成提示词异常！",
            "allCompareRows": [],
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "status": 404,
            "errmsg": f"生成提示词异常！: {e}",
            "prompt": "生成提示词异常！",
            "allCompareRows": [],
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# 使用示例
if __name__ == "__main__":

    input = {
        "param": "tradeId%3Aops_slugger_250303.120653.10.95.133.43.848208.6589504898_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A06%3A53%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A315%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740974803%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120653.10.95.133.43.848208.6589504898_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A06%3A53%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2105%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A275%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd02919%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A285%23%2A%23basePrice%3A284.5%23%2A%23viewPrice%3A300%23%2A%23policyId%3A2947024463%23%2A%23autoPriceDecreaseAmount%3A13.5%23%2A%23secondPrice%3A293%23%2A%23CPT%3A1740974726%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A11%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120653.10.95.133.43.848208.6589504898_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A10%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A06%3A53%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2107%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A408%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00204%23%2A%23productMark%3A-1%23%2A%23cabin%3AT%23%2A%23packagePrice%3A423%23%2A%23basePrice%3A391%23%2A%23viewPrice%3A450%23%2A%23policyId%3A7240007911%23%2A%23autoPriceDecreaseAmount%3A52.2%23%2A%23secondPrice%3A%22435.7%22%23%2A%23CPT%3A1740974418%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A12%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120341.10.95.136.64.2832790.6428868200_1%23%2A%23oriTag%3ATTL1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-03%2012%3A03%3A41%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AKN6006%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A329%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03837%23%2A%23productMark%3A500%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A344%23%2A%23basePrice%3A344%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5942233873%23%2A%23autoPriceDecreaseAmount%3A56.0%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1740974418%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.120329.10.95.136.64.2832790.703150679_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120341.10.95.136.64.2832790.6428868200_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3ATTL1%23%2A%23searchDateTime%3A2025-03-03%2012%3A03%3A41%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AKN6006%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A329%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03837%23%2A%23productMark%3A500%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A344%23%2A%23basePrice%3A344%23%2A%23viewPrice%3A400%23%2A%23policyId%3A5942233873%23%2A%23autoPriceDecreaseAmount%3A56.0%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1740974418%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.120329.10.95.136.64.2832790.703150679_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120329.10.95.136.64.2832790.703150679_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A03%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A310%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740974562%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.120329.10.95.136.64.2832790.703150679_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2012%3A03%3A29%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2117%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A310%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740974562%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A18%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114859.10.95.133.56.313783.8675107691_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A59%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A310%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114859.10.95.133.56.313783.8675107691_1%23%2A%23oriTag%3AJXL1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A59%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ASC5338%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A330%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03652%23%2A%23productMark%3A1001%23%2A%23cabin%3AK%23%2A%23packagePrice%3A350%23%2A%23basePrice%3A350%23%2A%23viewPrice%3A350%23%2A%23policyId%3A3483372649%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A11%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114859.10.95.133.56.313783.8675107691_1%23%2A%23oriTag%3AJXL1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A59%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AZH1226%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A330%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03652%23%2A%23productMark%3A1001%23%2A%23cabin%3AK%23%2A%23packagePrice%3A350%23%2A%23basePrice%3A350%23%2A%23viewPrice%3A350%23%2A%23policyId%3A3483372682%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3A1530%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A18%3A40%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114851.10.90.5.71.635083.6122292359_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A51%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2101%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A252%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03738%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A267%23%2A%23basePrice%3A243.4%23%2A%23viewPrice%3A600%23%2A%23policyId%3A9862633283%23%2A%23autoPriceDecreaseAmount%3A353.6%23%2A%23secondPrice%3A%22247.79999%22%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A07%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114851.10.90.5.71.635083.6122292359_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A51%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3A3U4860%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A282%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00060%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A297%23%2A%23basePrice%3A296.9%23%2A%23viewPrice%3A310%23%2A%23policyId%3A4362110050%23%2A%23autoPriceDecreaseAmount%3A13.5%23%2A%23secondPrice%3A310%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114851.10.90.5.71.635083.6122292359_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A51%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A252%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03738%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A267%23%2A%23basePrice%3A243.4%23%2A%23viewPrice%3A600%23%2A%23policyId%3A9862633400%23%2A%23autoPriceDecreaseAmount%3A353.6%23%2A%23secondPrice%3A%22247.79999%22%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114851.10.90.5.71.635083.6122292359_1%23%2A%23oriTag%3AHYC1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A51%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3A3U4862%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A258%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00477%23%2A%23productMark%3A225%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A273%23%2A%23basePrice%3A272.9%23%2A%23viewPrice%3A280%23%2A%23policyId%3A14577504810%23%2A%23autoPriceDecreaseAmount%3A7.0%23%2A%23secondPrice%3A280%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A11%3A50%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMF3011%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A402%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd04020%23%2A%23productMark%3A894%23%2A%23cabin%3AT%23%2A%23packagePrice%3A422%23%2A%23basePrice%3A391.9%23%2A%23viewPrice%3A450%23%2A%23policyId%3A2006239464%23%2A%23autoPriceDecreaseAmount%3A50.0%23%2A%23secondPrice%3A%22399.8%22%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A22%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A252%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03738%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A267%23%2A%23basePrice%3A243.4%23%2A%23viewPrice%3A600%23%2A%23policyId%3A9862633400%23%2A%23autoPriceDecreaseAmount%3A353.6%23%2A%23secondPrice%3A%22247.79999%22%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3A3U4860%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A282%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00060%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A297%23%2A%23basePrice%3A296.9%23%2A%23viewPrice%3A310%23%2A%23policyId%3A4362110050%23%2A%23autoPriceDecreaseAmount%3A13.5%23%2A%23secondPrice%3A310%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3A3U4860%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A282%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00060%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A297%23%2A%23basePrice%3A296.9%23%2A%23viewPrice%3A310%23%2A%23policyId%3A4362110050%23%2A%23autoPriceDecreaseAmount%3A13.5%23%2A%23secondPrice%3A310%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AHYC1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMF4681%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A283%23%2A%23xCut%3Anull%23%2A%23expVendor%3A2%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd00477%23%2A%23productMark%3A225%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A303%23%2A%23basePrice%3A302.8%23%2A%23viewPrice%3A310%23%2A%23policyId%3A14577504738%23%2A%23autoPriceDecreaseAmount%3A7.0%23%2A%23secondPrice%3A310%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6604589795_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3A19%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ACZ8824%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A291%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd04112%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A310%23%2A%23basePrice%3A291%23%2A%23viewPrice%3A310%23%2A%23policyId%3A227858770%23%2A%23autoPriceDecreaseAmount%3A16.0%23%2A%23secondPrice%3A%22296.9%22%23%2A%23CPT%3A1740973664%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A14%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6295222056_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A265%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A280%23%2A%23basePrice%3A280%23%2A%23viewPrice%3A280%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6295222056_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A252%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgnd03738%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A267%23%2A%23basePrice%3A243.4%23%2A%23viewPrice%3A600%23%2A%23policyId%3A9862633400%23%2A%23autoPriceDecreaseAmount%3A353.6%23%2A%23secondPrice%3A%22249.9%22%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A23%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114815.10.90.5.71.635083.6295222056_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AZHZ1%23%2A%23searchDateTime%3A2025-03-03%2011%3A48%3A15%23%2A%23cut%3A2%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A263%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3Aname%3D%E9%9F%A9%E4%BF%8A%26ageType%3D0%26cardNo%3D612525198909074914%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A280%23%2A%23basePrice%3A280%23%2A%23viewPrice%3A280%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3A290%23%2A%23CPT%3A1740973696%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114249.10.95.133.35.3895356.5166177106_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A310%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114249.10.95.133.35.3895356.5166177106_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3A3U5001%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A320%23%2A%23xCut%3Anull%23%2A%23expVendor%3A5%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd00063%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A340%23%2A%23basePrice%3A324.7%23%2A%23viewPrice%3A350%23%2A%23policyId%3A8707495110%23%2A%23autoPriceDecreaseAmount%3A24.3%23%2A%23secondPrice%3A%22326.4%22%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A17%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114249.10.95.133.35.3895356.5166177106_1%23%2A%23oriTag%3AZYJ1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3ACA1202%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A324%23%2A%23xCut%3Anull%23%2A%23expVendor%3A8%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd04046%23%2A%23productMark%3A500%23%2A%23cabin%3AK%23%2A%23packagePrice%3A344%23%2A%23basePrice%3A344%23%2A%23viewPrice%3A350%23%2A%23policyId%3A44719795%23%2A%23autoPriceDecreaseAmount%3A5.3%23%2A%23secondPrice%3A0%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A18%3A10%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114249.10.95.133.35.3895356.5166177106_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A49%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMF3009%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A321%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3ADURING%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd00063%23%2A%23productMark%3A-1%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A341%23%2A%23basePrice%3A328.4%23%2A%23viewPrice%3A350%23%2A%23policyId%3A8707494711%23%2A%23autoPriceDecreaseAmount%3A20.6%23%2A%23secondPrice%3A349%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A17%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114244.10.95.133.35.3895356.9009549036_1%23%2A%23oriTag%3AYCP1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A44%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A255%23%2A%23xCut%3Anull%23%2A%23expVendor%3A3%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03738%23%2A%23productMark%3A894%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A270%23%2A%23basePrice%3A246.4%23%2A%23viewPrice%3A600%23%2A%23policyId%3A9862633400%23%2A%23autoPriceDecreaseAmount%3A353.6%23%2A%23secondPrice%3A%22247.70001%22%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A23%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114244.10.95.133.35.3895356.9009549036_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC6%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A44%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A265%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A280%23%2A%23basePrice%3A280%23%2A%23viewPrice%3A280%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114244.10.95.133.35.3895356.9009549036_1%23%2A%23oriTag%3A%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AZHZ1%23%2A%23searchDateTime%3A2025-03-03%2011%3A42%3A44%23%2A%23cut%3A2%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2109%23%2A%23cabinType%3A%E7%BB%8F%E6%B5%8E%E8%88%B1%23%2A%23tSource%3Aota%23%2A%23departureCity%3AXIY%23%2A%23price%3A263%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A280%23%2A%23basePrice%3A280%23%2A%23viewPrice%3A280%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3A290%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23depTime%3A13%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.114101.10.95.133.35.3895356.6486286097_1%23%2A%23oriTag%3ACZA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A41%3A01%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A310%23%2A%23xCut%3Anull%23%2A%23expVendor%3A0%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgndymu05%23%2A%23productMark%3A434%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A330%23%2A%23basePrice%3A330%23%2A%23viewPrice%3A330%23%2A%23policyId%3A0%23%2A%23autoPriceDecreaseAmount%3A%23%2A%23secondPrice%3Anull%23%2A%23CPT%3A1740973242%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.113342.10.95.133.43.1756939.5518333476_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A33%3A42%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2103%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A309%23%2A%23xCut%3Anull%23%2A%23expVendor%3A5%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd00063%23%2A%23productMark%3A-1%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A329%23%2A%23basePrice%3A329%23%2A%23viewPrice%3A350%23%2A%23policyId%3A8706655038%23%2A%23autoPriceDecreaseAmount%3A21.0%23%2A%23secondPrice%3A330%23%2A%23CPT%3A1740972734%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A09%3A00%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.113342.10.95.133.43.1756939.5518333476_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A33%3A42%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2105%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A275%23%2A%23xCut%3Anull%23%2A%23expVendor%3A5%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd03804%23%2A%23productMark%3A500%23%2A%23cabin%3AT%23%2A%23packagePrice%3A290%23%2A%23basePrice%3A289.9%23%2A%23viewPrice%3A300%23%2A%23policyId%3A1419348085%23%2A%23autoPriceDecreaseAmount%3A9.2%23%2A%23secondPrice%3A%22290.9%22%23%2A%23CPT%3A1740972282%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A11%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E, tradeId%3Aops_slugger_250303.113342.10.95.133.43.1756939.5518333476_1%23%2A%23oriTag%3ATSA1%23%2A%23departureDate%3A2025-03-06%23%2A%23coupon%3A15%23%2A%23tag%3AABC1%23%2A%23searchDateTime%3A2025-03-03%2011%3A33%3A42%23%2A%23cut%3Anull%23%2A%23arrivalCity%3APEK%23%2A%23matchQuestion%3A%E5%8C%B9%E9%85%8D%23%2A%23flightNo%3AMU2111%23%2A%23cabinType%3A%23%2A%23tSource%3Alist%23%2A%23departureCity%3AXIY%23%2A%23price%3A284%23%2A%23xCut%3Anull%23%2A%23expVendor%3A5%23%2A%23expansionType%3AALLOW%23%2A%23poison%3AFalse%23%2A%23basicLabels%3AZSJ6HEI%2CZSJ6%23%2A%23filters%3AECONOMY%2C%2CDEFAULT%23%2A%23passengers%3A%23%2A%23wrapperId%3Attsgnd00063%23%2A%23productMark%3A-1%23%2A%23cabin%3AZ%23%2A%23packagePrice%3A304%23%2A%23basePrice%3A304%23%2A%23viewPrice%3A330%23%2A%23policyId%3A8706655038%23%2A%23autoPriceDecreaseAmount%3A26.0%23%2A%23secondPrice%3A308%23%2A%23CPT%3A1740972734%23%2A%23allGoodItemPrice%3A0%23%2A%23listTradeId%3A%23%2A%23depTime%3A13%3A30%23%2A%23username%3Aaqlozin2906%7E%7E%2A%7E%7E",
        "question": "【多次搜索变价】用户反馈（本机）于L页搜索同一航班：东航承运的3月14约14：00出发18：45抵达的哈尔滨-深圳航班价格连续上涨，5-6天前开始关注此航班，价格分别为490左右-520左右-550左右（昨日价格）-597（今日价格）。两次询问其他平台波动，用户未明确告知，但告知同程旅行价格更低，用户未购买机票。",
        "listSearchDurationLimit": "30m",
        "oneToOnetemplatePrompt": "帮我对比新老数据价格是否变化，如果变化请进行变化归因\n对比类型：{compareType}\n用户问题：{question}\n原数据：\n{preData}\n新数据：\n{surData}\n",
    }
    # print(json.dumps(input, indent=2, ensure_ascii=False))

    finalData = main(input)
    print(
        "执行结果："
        + json.dumps(finalData.get("allCompareRows"), indent=2, ensure_ascii=False)
    )
    # write_json_to_file(obj = finalData.get('flightPriceGroup'), file_path = 'D:/work/otherdoc/flightPriceGroup.json')
