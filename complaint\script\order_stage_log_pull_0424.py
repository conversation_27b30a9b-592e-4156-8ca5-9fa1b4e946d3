import traceback
import uuid
import requests
import json
import copy
from datetime import datetime
from typing import Dict, Any, List, Optional
from requests.exceptions import RequestException
from collections import defaultdict

ERROR_CODE_MAPPING = {
    "00_000_000000_0000": "",
    "01_001_000000_0001": "生单-基础信息校验-无接口依赖-联系人手机号超过位数限制",
    "01_001_000000_0002": "生单-基础信息校验-无接口依赖-乘机人出生日期格式不对",
    "01_001_000000_0003": "生单-基础信息校验-无接口依赖-联系人手机号格式错误",
    "01_001_000000_0004": "生单-基础信息校验-无接口依赖-联系人手机号区号错误",
    "01_001_000000_0005": "生单-基础信息校验-无接口依赖-联系人手机号格式错误",
    "01_001_000000_0006": "生单-基础信息校验-无接口依赖-联系人|乘机人手机号格式错误",
    "01_001_000000_0007": "生单-基础信息校验-booking和生单用户名不一致",
    "01_001_000000_0008": "生单-基础信息校验-无接口依赖-乘机人手机号超过位数限制",
    "01_001_000000_0009": "生单-基础信息校验-无接口依赖-乘机人手机号国家代码填写错误",
    "01_002_F04005_0001": "生单-航班起飞时间校验-无接口依赖-航班时间不匹配",
    "01_003_000000_0001": "生单-起飞时间禁售校验-无接口依赖-代理商禁售时间校验不通过",
    "01_004_000000_0001": "生单-bookingTag校验-无接口依赖-同步bookingTagKey为空",
    "01_004_000000_0002": "生单-bookingTag校验-无接口依赖-bookingTagKey无效(token校验",
    "01_004_F01001_0003": "生单-bookingTag校验-bookingTag查询接口-bookingTag绝对拦截",
    "01_004_000000_0004": "生单-bookingTag校验-无接口依赖-婴儿乘机人缺报价",
    "01_004_000000_0005": "生单-bookingTag校验-无接口依赖-婴儿乘机人tag不匹配",
    "01_004_000000_0006": "生单-bookingTag校验-bookingTag校验-儿童乘机人缺报价",
    "01_004_000000_0007": "生单-bookingTag校验-bookingTag校验-儿童乘机人tag不匹配",
    "01_005_F01001_0001": "生单-舱位乘机人数限制-bookingTag查询接口-舱位余票不足",
    "01_005_F01001_0002": "生单-舱位乘机人数限制-bookingTag查询接口-儿童舱位余票不足",
    "01_006_000000_0001": "生单-乘机人信息校验-无接口依赖-乘机人信息校验失败",
    "01_006_000000_0021": "生单-乘机人证件号校验-无接口依赖-外国人永久居留身份证号码格式有误，请重新输入后提交",
    "01_006_000000_0022": "生单-乘机人证件号校验-无接口依赖-户口簿身份证格式有误，请重新输入后提交",
    "01_006_000000_0023": "生单-乘机人证件号校验-无接口依赖-只有出生小于16岁以内才能使用户口簿乘机",
    "01_006_000000_0024": "生单-乘机人证件号校验-无接口依赖-军人证件号长度有误，请重新输入后提交",
    "01_006_000000_0025": "生单-乘机人证件号校验-无接口依赖-回乡证件号长度有误，请重新输入后提交",
    "01_006_000000_0026": "生单-乘机人证件号校验-无接口依赖-台胞证件号长度有误，请重新输入后提交",
    "01_006_000000_0027": "生单-乘机人证件号校验-无接口依赖-港澳台居民居住证号长度有误，请重新输入后提交",
    "01_006_000000_0028": "生单-乘机人证件号校验-无接口依赖-只有出生14天到2岁以内才能使用出生证明乘机",
    "01_006_000000_0042": "生单-乘机人证件号校验-无接口依赖-乘机人护照长度，小于等于15位",
    "01_006_000000_0044": "生单-乘机人证件号校验-无接口依赖-军残证件号长度有误，请重新输入后提交",
    "01_006_000000_0045": "生单-乘机人证件号校验-无接口依赖-您输入的证件号有误，请重新输入",
    "01_006_F00005_0037": "生单-用户信息校验-用户信息校验qconfig配置-用户信息校验失败",
    "01_006_000000_0041": "生单-乘机人证件号校验-无接口依赖-乘机人身份证号长度，只允许18位",
    "01_006_000000_0046": "生单-乘机人证件号校验-无接口依赖-证件信息错误",
    "01_006_000000_0047": "生单-非白名单证件类型ext信息校验-无接口依赖-passengerExtMap为空或passengerExtInfo有空字段",
    "01_006_000000_0048": "生单-证件有效期校验-无接口依赖-乘客证件有效期早于旅行结束日",
    "01_006_000000_0054": "生单-乘机人证件号校验-无接口依赖-乘机人护照格式错误",
    "01_006_000000_0055": "生单-乘机人证件号校验-无接口依赖-证件号前缀错误",
    "01_006_000000_0060": "生单-乘机人证件号校验-无接口依赖-乘机人历史订单相同证件号不同姓名拦截",
    "01_006_000000_0061": "生单-乘机人证件号校验-无接口依赖-乘机人证件类型及证件号重复",
    "01_007_000000_0029": "生单-乘机人姓名校验-无接口依赖-抱歉，您输入的姓名过长，请按规则填写",
    "01_007_000000_0030": "生单-乘机人姓名校验-无接口依赖-使用身份证购票的乘机人姓名需包含汉字",
    "01_008_000000_0052": "生单-乘机人类型年龄匹配校验-无接口依赖-儿童乘机人年龄不符合规定",
    "01_008_000000_0053": "生单-乘机人类型年龄匹配校验-无接口依赖-婴儿乘机人年龄不符合规定",
    "01_009_000000_0008": "生单-乘机人报价匹配校验-无接口依赖-乘机人信息缺失，请核实后重新填写",
    "01_009_000000_0009": "生单-乘机人报价匹配校验-无接口依赖-成人不允许买儿童票",
    "01_009_000000_0010": "生单-乘机人报价匹配校验-无接口依赖-一个订单不允许既有儿童票又有儿童买成人票",
    "01_009_000000_0011": "生单-乘机人报价匹配校验-无接口依赖-航班信息缺失，请重新搜索",
    "01_009_000000_0012": "生单-乘机人报价匹配校验-无接口依赖-儿童、婴儿须由18岁以上成人陪同登机",
    "01_009_000000_0013": "生单-乘机人报价匹配校验-无接口依赖-一位成人最多可带两名儿童登机",
    "01_009_000000_0014": "生单-乘机人报价匹配校验-无接口依赖-婴儿乘机人年龄不满足购买年龄限制",
    "01_009_000000_0015": "生单-乘机人报价匹配校验-无接口依赖-一位成人乘客最多带一名婴儿登机",
    "01_009_000000_0016": "生单-乘机人报价匹配校验-无接口依赖-当前报价不支持婴儿售卖，请重新选择",
    "01_009_000000_0017": "生单-乘机人报价匹配校验-无接口依赖-婴儿不能购买成人票",
    "01_009_000000_0018": "生单-乘机人报价匹配校验-无接口依赖-成人只能购买成人票",
    "01_010_F01003_0032": "生单-政策对乘机人要求校验-政策查询接口-乘机人年龄不得超过政策最大年龄限制",
    "01_010_F01003_0033": "生单-政策对乘机人要求校验-政策查询接口-乘机人年龄不得小于政策最小年龄限制",
    "01_010_F01003_0034": "生单-政策对乘机人要求校验-政策查询接口-该报价只支持使用身份证购买",
    "01_011_F02003_0001": "生单-pc重复订单校验-交易重复订单接口-存在待支付的重复订单",
    "01_011_F02003_0002": "生单-pc重复订单校验-交易重复订单接口-重复下单",
    "01_012_F00006_0000": "生单-特殊产品校验-特殊产品qconfig配置-特殊产品校验失败",
    "01_012_F00006_0002": "生单-特殊产品校验-特殊产品qconfig配置-乘机人数限制",
    "01_012_F00006_0005": "生单-特殊产品校验-特殊产品qconfig配置-证件限制",
    "01_012_F00006_0006": "生单-特殊产品校验-特殊产品qconfig配置-年龄限制",
    "01_012_F00006_0011": "生单-特殊产品校验-特殊产品qconfig配置-身份证省份限制",
    "01_012_F00006_0012": "生单-特殊产品校验-特殊产品qconfig配置-性别限制",
    "01_012_F00006_0013": "生单-特殊产品校验-特殊产品qconfig配置-至少有一个人是身份证限制",
    "01_012_F00006_0014": "生单-特殊产品校验-特殊产品qconfig配置-乘机人必须都是成人限制",
    "01_012_F00006_0015": "生单-特殊产品校验-特殊产品qconfig配置-儿童最大数量限制",
    "01_012_F00006_0016": "生单-特殊产品校验-特殊产品qconfig配置-成人儿童比例限制",
    "01_012_F00006_0017": "生单-特殊产品校验-特殊产品qconfig配置-至少一人满足年龄限制",
    "01_012_F00006_0018": "生单-特殊产品校验-特殊产品qconfig配置-学生票本人购买限制",
    "01_012_F00006_0019": "生单-特殊产品校验-特殊产品qconfig配置-学生票年龄限制",
    "01_012_F00006_0020": "生单-特殊产品校验-特殊产品qconfig配置-学生票认证方式限制",
    "01_012_F00006_0021": "生单-特殊产品校验-特殊产品qconfig配置-用户画像限制",
    "01_012_F00006_0022": "生单-特殊产品校验-特殊产品qconfig配置-非实名认证限制",
    "01_012_F00006_0023": "生单-特殊产品校验-特殊产品qconfig配置-非航司会员限制",
    "01_012_F00006_0024": "生单-特殊产品校验-特殊产品qconfig配置-返程优惠券限制",
    "01_012_F00006_0038": "生单-特殊产品校验-特殊产品qconfig配置-至少一人满足性别年龄限制",
    "01_012_F00006_0039": "生单-特殊产品校验-特殊产品qconfig配置-乘机人手机号校验",
    "01_012_F00006_0041": "生单-特殊产品校验-特殊产品qconfig配置-深航新客代金券校验",
    "01_012_F00006_0042": "生单-特殊产品校验-特殊产品qconfig配置-成人数量限制",
    "01_012_F00006_0043": "生单-特殊产品校验-特殊产品qconfig配置-新生校验",
    "01_012_F00006_0055": "生单-特殊产品校验-特殊产品qconfig配置-证件类型性别年龄限制",
    "01_012_F00006_0056": "生单-特殊产品校验-特殊产品qconfig配置-乘机人必须包含实名认证用户限制",
    "01_012_F00006_0057": "生单-特殊产品校验-特殊产品qconfig配置-山东航优惠券同一用户只能使用一单",
    "01_012_F00006_0058": "生单-特殊产品校验-特殊产品qconfig配置-学生票购买限制",
    "01_012_F00006_0059": "生单-特殊产品校验-特殊产品qconfig配置-根据用户画像校验手机号",
    "01_012_F00006_0063": "生单-特殊产品校验-特殊产品qconfig配置-支持供应商投放复购类产品",
    "01_012_F00006_0064": "生单-特殊产品校验-特殊产品qconfig配置-截止下单时间校验",
    "01_012_F00006_0066": "生单-特殊产品校验-特殊产品qconfig配置-用户未进行实名认证",
    "01_012_F00006_0067": "生单-特殊产品校验-特殊产品qconfig配置-航司特殊库存产品库存校验",
    "01_012_D01001_0001": "生单-特殊产品校验（学生票）-用户画像批量查询接口-用户画像判断无学生标识",
    "01_012_D01002_0002": "生单-特殊产品校验（学生票）-用户画像查询接口-用户画像判断无学生标识",
    "01_012_D01002_0003": "生单-特殊产品校验（学生票）-用户画像查询接口-学生票乘机人数限制或无登录用户信息",
    "01_012_D01002_0004": "生单-特殊产品校验（学生票）-用户画像查询接口-学生票校验异常",
    "01_012_D01001_0005": "生单-特殊产品校验（学生票）-用户画像批量查询接口-用户画像返回标签信息为空",
    "01_012_F07001_0001": "生单-特殊产品校验-自营库存校验接口-自营库存校验接口信息为空",
    "01_012_F07001_0002": "生单-特殊产品校验-自营库存校验接口-自营库存校验接口超时",
    "01_012_F07001_0003": "生单-特殊产品校验-自营库存校验接口-自营库存校验接口异常",
    "01_012_F07001_0004": "生单-特殊产品校验-自营库存校验接口-自营库存拦截",
    "O_01_012_F02001_0001": "生单-特殊产品校验-呼和浩特首飞-Qunar检验存在非首飞乘机人",
    "O_01_012_F02001_0002": "生单-特殊产品校验-呼和浩特首飞-第三方接口超时或存在非首飞乘机人",
    "O_01_012_F02001_0003": "生单-特殊产品校验-呼和浩特首飞-检验发生异常",
    "01_013_R01001_0001": "生单-黑名单校验-风控接口(R01001-QJH 直接拦截",
    "01_013_R01001_0002": "生单-黑名单校验-风控接口-增加用户行为弹窗验证",
    "01_013_R01002_0003": "生单-黑名单校验-风控接口(R01002&F00007-黑名单用户＋黑名单标签拦截",
    "01_014_F02004_0001": "生单-订单落库-订单落库接口-订单落库失败",
    "01_014_F02004_0002": "生单-订单落库-订单落库接口-订单落库接口调用异常",
    "01_014_000000_0003": "生单-订单落库-无接口依赖-订单落库异常",
    "01_016_000000_0002": "生单-通用业务功能校验-无接口依赖-证件类型限制",
    "01_016_000000_0003": "生单-通用业务功能校验-无接口依赖-乘机人重复名字限制",
    "01_017_000000_0001": "生单-通用业务功能校验-无接口依赖-婴儿配额不足限制",
    "01_017_000000_0002": "生单-特殊航司政策校验-无接口依赖-生编政策校验失败",
    "01_017_000000_0003": "生单-特殊航司政策校验-无接口依赖-运价来源校验失败",
    "01_017_000000_0004": "生单-特殊航司政策校验-无接口依赖-afare参数为空校验失败",
    "02_003_000000_0001": "异步生单-直接兜底拦截-无接口依赖-婴儿乘机人直接兜底",
    "02_003_000000_0002": "异步生单-直接兜底拦截-无接口依赖-单独儿童买成人乘机人直接兜底",
    "02_003_R01001_0003": "异步生单-直接兜底拦截-风控标签-航司人员使用自营旗舰店报价兜底",
    "02_003_000000_0004": "异步生单-直接兜底拦截-无接口依赖-bookingTag绝对拦截兜底",
    "02_003_F00001_0005": "异步生单-直接兜底拦截-qconfig-生单特殊产品校验失败导致直接兜底",
    "02_003_F00001_0006": "异步生单-重复订单换供应-订单中心接口-用户已有该域名和航司下的重复订单直接兜底",
    "02_003_000000_0010": "异步生单-pnr/pata校验-异步生单价格校验结束清空缓存价格信息",
    "02_007_000000_0001": "异步生单/支付前校验-失信人拦截-乘机人存在失信人",
    "23_005_F04006_0001": "异步生单/支付前校验-sspat-validateOnpay-sspat失败",
    "23_005_F04006_0002": "异步生单/支付前校验-sspat-validateOnpay-TimeOutException",
    "23_005_F04006_0003": "异步生单/支付前校验-sspat-validateOnpay-Exception",
    "23_005_F00000_0004": "异步生单/支付前校验-sspat-validateOnpay-生编pid降级直接降级",
    "23_005_F04006_0005": "异步生单/支付前校验-sspat-validateOnpay-pid降级失败",
    "23_005_000000_0006": "异步生单/支付前校验-sspat-无接口依赖-SSPata特殊航司强制降级走验价队列",
    "23_005_F04007_0100": "异步生单/支付前校验-sspat-validateOnpay-av校验失败",
    "23_005_F04007_0101": "异步生单/支付前校验-sspat-validateOnpay-av校验无权限",
    "23_005_F04007_0102": "异步生单/支付前校验-sspat-validateOnpay-av返回为空",
    "23_005_F04007_0103": "异步生单/支付前校验-sspat-validateOnpay-av指令执行失败",
    "23_005_F04007_0104": "异步生单/支付前校验-sspat-validateOnpay-av座位不够",
    "23_005_F04008_0000": "异步生单/支付前校验-sspat-validateOnpay-ss校验失败",
    "23_005_F04008_0002": "异步生单/支付前校验-sspat-validateOnpay-SS非K",
    "23_005_F04008_0003": "异步生单/支付前校验-sspat-validateOnpay-SS Item列表null",
    "23_005_F04008_0017": "异步生单/支付前校验-sspat-validateOnPay-没有可售卖舱位",
    "23_005_F04008_0031": "异步生单/支付前校验-sspat-validateOnPay-DUPLICATE SEGMENT(ig上下文串了",
    "23_005_F04008_0053": "异步生单/支付前校验-sspat-validateOnPay-航信返回SEATS",
    "23_005_F04008_0100": "异步生单/支付前校验-sspat-validateOnPay-office不存在",
    "23_005_F04008_0101": "异步生单/支付前校验-sspat-validateOnPay-请联系pid组，检查office状态",
    "23_005_F04008_0103": "异步生单/支付前校验-sspat-validateOnPay-pid问题",
    "23_005_F04008_0105": "异步生单/支付前校验-sspat-validateOnPay-没权限",
    "23_005_F04008_0107": "异步生单/支付前校验-sspat-validateOnPay-office繁忙请求被拒绝",
    "23_005_F04008_0109": "异步生单/支付前校验-sspat-validateOnPay-ASE WAIT - TRANSACTION IN PROGRESS",
    "23_005_F04008_0415": "异步生单/支付前校验-sspat-validateOnPay-ss执行失败",
    "23_005_F04008_0500": "异步生单/支付前校验-sspat-validateOnPay-调用后端pidserver发生错误",
    "23_005_F04008_0604": "异步生单/支付前校验-sspat-validateOnPay-ss执行失败,航信返回unable无法执行",
    "23_005_F04008_0606": "异步生单/支付前校验-sspat-validateOnPay-请一次完成PNR并封口",
    "23_005_F04009_0000": "异步生单/支付前校验-sspat-validateOnPay-pat失败",
    "23_005_F04009_0001": "异步生单/支付前校验-sspat-validateOnPay-pat结果为空",
    "23_005_F04009_0015": "异步生单/支付前校验-sspat-validateOnPay-pat XML数据转对象异常",
    "23_005_F04009_0103": "异步生单/支付前校验-sspat-validateOnPay-pat pid问题",
    "23_005_F04009_0500": "异步生单/支付前校验-sspat-validateOnPay-调用后端pidserver发生错误",
    "23_005_F04009_0900": "异步生单/支付前校验-sspat-validateOnPay-解析PAT结果发生错误",
    "23_005_F04009_0902": "异步生单/支付前校验-sspat-validateOnPay-无符合条件的运价",
    "23_006_000000_0001": "异步生单/支付前校验-政策生编-政策紧俏属性-AV座位数小于紧俏座位数阈值,不生编,走兜底",
    "23_006_F04001_0001": "异步生单/支付前校验-政策生编-生编接口-生编失败(common",
    "23_006_F04001_0002": "异步生单/支付前校验-政策生编-生编接口-接口调用超时",
    "23_006_F04001_0003": "异步生单/支付前校验-政策生编-生编接口-接口调用异常",
    "23_006_F04001_0004": "异步生单/支付前校验-政策生编-生编接口-pid降级失败",
    "23_006_F04001_0100": "异步生单/支付前校验-政策生编-生编接口-office不存在",
    "23_006_F04001_0101": "异步生单/支付前校验-政策生编-生编接口-请联系pid组，检查office状态",
    "23_006_F04001_0102": "异步生单/支付前校验-政策生编-生编接口-SI未登录",
    "23_006_F04001_0103": "异步生单/支付前校验-政策生编-生编接口-pid问题",
    "23_006_F04001_0104": "异步生单/支付前校验-政策生编-生编接口-pid返回null",
    "23_006_F04001_0105": "异步生单/支付前校验-政策生编-生编接口-没权限",
    "23_006_F04001_0106": "异步生单/支付前校验-政策生编-生编接口-错误指令",
    "23_006_F04001_0107": "异步生单/支付前校验-政策生编-生编接口-office繁忙请求被拒绝",
    "23_006_F04001_0300": "异步生单/支付前校验-政策生编-生编接口-传递参数错误，参数不能为空或null",
    "23_006_F04001_0500": "异步生单/支付前校验-政策生编-生编接口-调用后端pidserver发生错误",
    "23_006_F04001_0505": "异步生单/支付前校验-政策生编-生编接口-团队PNR人数不合法",
    "23_006_F04001_0412": "异步生单/支付前校验-政策生编-生编接口-pnr创建失败,航信返回unable无法创建",
    "23_006_F04001_0413": "异步生单/支付前校验-政策生编-生编接口-pnr创建失败,从航信结果中无法提取pnr",
    "23_006_F04001_0419": "异步生单/支付前校验-政策生编-生编接口-旅客为失信被执行人",
    "23_007_F00002_0001": "异步生单/支付前校验-rtPat-qconfig-rtPat结果出票时限拦截",
    "23_007_F04002_0001": "异步生单/支付前校验-rtPat-rtPat接口-rtPat失败",
    "23_007_F04002_0002": "异步生单/支付前校验-rtPat-rtPat接口-rtPat接口调用超时",
    "23_007_F04002_0003": "异步生单/支付前校验-rtPat-rtPat接口-rtPat接口调用异常",
    "23_007_F04002_0004": "异步生单/支付前校验-rtPat-rtPat接口-rt提示乘机人存在失信人",
    "23_007_F04002_0005": "异步生单/支付前校验-rtPat-rtPat接口-rtPat返回结果为空",
    "23_007_F04002_0006": "异步生单/支付前校验-rtPat-rtPat接口-rt 校验pnr非K",
    "23_007_F04002_0100": "异步生单/支付前校验-rt-rtPat接口-office不存在",
    "23_007_F04002_0101": "异步生单/支付前校验-rt-rtPat接口-请联系pid组，检查office状态",
    "23_007_F04002_0107": "异步生单/支付前校验-rt-rtPat接口-office繁忙请求被拒绝",
    "23_007_F04002_0300": "异步生单/支付前校验-rt-rtPat接口-传递参数错误，参数不能为空或null",
    "23_007_F04002_0500": "异步生单/支付前校验-rt-rtPat接口-调用后端pidserver发生错误",
    "23_007_F04002_0503": "异步生单/支付前校验-rt-rtPat接口-服务程序执行期间发生错误",
    "23_007_F04002_0505": "异步生单/支付前校验-rt-rtPat接口-团队PNR人数不合法",
    "23_007_F04002_0407": "异步生单/支付前校验-rt-rtPat接口-该pnr不存在",
    "23_007_F04002_0408": "异步生单/支付前校验-rt-rtPat接口-该pnr已被取消",
    "23_007_F04002_0409": "异步生单/支付前校验-rt-rtPat接口-rt结果中不包含该pnr",
    "23_007_F04002_0421": "异步生单/支付前校验-rt-rtPat接口-需要授权",
    "23_008_F04002_0005": "异步生单/支付前校验-pat-rtPat接口-ibe pat找不到运价信息",
    "23_008_F04002_0100": "异步生单/支付前校验-pat-rtPat接口-office不存在",
    "23_008_F04002_0101": "异步生单/支付前校验-pat-rtPat接口-请联系pid组，检查office状态",
    "23_008_F04002_0103": "异步生单/支付前校验-pat-rtPat接口-pid问题",
    "23_008_F04002_0107": "异步生单/支付前校验-pat-rtPat接口-office繁忙请求被拒绝",
    "23_008_F04002_0300": "异步生单/支付前校验-pat-rtPat接口-传递参数错误，参数不能为空或null",
    "23_008_F04002_0500": "异步生单/支付前校验-pat-rtPat接口-调用后端pidserver发生错误",
    "23_008_F04002_0505": "异步生单/支付前校验-pat-rtPat接口-团队PNR人数不合法",
    "23_008_F04002_0902": "异步生单/支付前校验-pat-rtPat接口-没有符合条件的运价",
    "23_008_F04002_0407": "异步生单/支付前校验-pat-rtPat接口-该pnr不存在",
    "23_008_F04002_0408": "异步生单/支付前校验-pat-rtPat接口-该pnr已被取消",
    "23_008_F04002_0409": "异步生单/支付前校验-pat-rtPat接口-rt结果中不包含该pnr",
    "23_008_F04002_0421": "异步生单/支付前校验-pat-rtPat接口-需要授权",
    "02_009_F05002_0001": "异步生单-运价直连生编-单程运价直连生编接口-运价直连生编失败",
    "02_009_F05002_0002": "异步生单-运价直连生编-单程运价直连生编接口-运价直连接口生编超时",
    "02_009_F05002_0003": "异步生单-运价直连生编-单程运价直连生编接口-运价直连接口生编异常",
    "02_009_F05002_0004": "异步生单-运价直连生编-单程运价直连生编接口-运价直连接口返回pnr为空",
    "02_009_F05002_1001": "异步生单-运价直连生编-单程运价直连生编接口-createPnr代理商接口为空",
    "02_009_F05002_1002": "异步生单-运价直连生编-单程运价直连生编接口-恶意订单直接拦截",
    "02_009_F05002_1003": "异步生单-运价直连生编-单程运价直连生编接口-pnr获取代理商接口错误",
    "02_009_F05002_1004": "异步生单-运价直连生编-单程运价直连生编接口-航班信息为空",
    "02_009_F05002_1005": "异步生单-运价直连生编-单程运价直连生编接口-createPnr代理商接口返回错误",
    "02_009_F05002_1006": "异步生单-运价直连生编-单程运价直连生编接口-createPnr代理商接口返回超时",
    "02_009_F05002_1007": "异步生单-运价直连生编-单程运价直连生编接口-生单结果为空",
    "02_009_F05002_1008": "异步生单-运价直连生编-单程运价直连生编接口-成人pnr为空",
    "02_009_F05002_1009": "异步生单-运价直连生编-单程运价直连生编接口-儿童pnr为空",
    "02_009_F05002_1010": "异步生单-运价直连生编-单程运价直连生编接口-key非法",
    "02_009_F05002_1011": "异步生单-运价直连生编-单程运价直连生编接口-create_pnr解析接口错误",
    "02_009_F05002_1012": "异步生单-运价直连生编-单程运价直连生编接口-create_pnr接口加密错误",
    "02_009_F05005_0001": "异步生单-运价直连生编-往返运价直连生编接口-运价直连生编失败",
    "02_009_F05005_0002": "异步生单-运价直连生编-往返运价直连生编接口-运价直连接口生编超时",
    "02_009_F05005_0003": "异步生单-运价直连生编-往返运价直连生编接口-运价直连接口生编异常",
    "02_009_F05005_0004": "异步生单-运价直连生编-往返运价直连生编接口-运价直连接口返回pnr为空",
    "02_009_F05005_0005": "异步生单-运价直连生编-往返运价直连生编接口-运价直连接口返回pnr验签失败",
    "02_010_F05001_0001": "运价直连恶意订单booking失败",
    "02_011_F06001_0001": "运价直连反采生单失败",
    "02_011_F06001_0002": "运价直连反采生单异常",
    "02_012_F06001_0001": "异步生单-运价直连生单-反采生单接口-生单失败-检查参数",
    "02_012_F06001_0002": "异步生单-运价直连生单-反采生单接口-生单失败-保存订单错误",
    "02_012_F06001_0003": "异步生单-运价直连生单-反采生单接口-生单失败-NULL,错误码保留暂不启用",
    "02_012_F06001_0004": "异步生单-运价直连生单-反采生单接口-生单失败-参数错误",
    "02_012_F06001_0005": "异步生单-运价直连生单-反采生单接口-生单失败-未知错误",
    "02_012_F06001_0006": "异步生单-运价直连生单-反采生单接口-生单失败-未支付重复提交",
    "02_012_F06001_0007": "异步生单-运价直连生单-反采生单接口-生单失败-支付重复提交",
    "02_012_F06001_0008": "异步生单-运价直连生单-反采生单接口-生单失败-无效booking",
    "02_012_F06001_0009": "异步生单-运价直连生单-反采生单接口-生单失败-B3B错误",
    "02_012_F06001_0010": "异步生单-运价直连生单-反采生单接口-生单失败-填写的航程类型和接口不匹配",
    "02_012_F06001_0401": "异步生单-运价直连生单-反采生单接口-生单失败-特殊票,不支持的证件类型",
    "02_012_F06001_0402": "异步生单-运价直连生单-反采生单接口-生单失败-特殊票,乘机人年龄不符合购买年龄限制",
    "02_012_F06001_0403": "异步生单-运价直连生单-反采生单接口-生单失败-不满足售卖条件",
    "02_012_F06001_0404": "异步生单-运价直连生单-反采生单接口-生单失败-儿童、婴儿须由18岁以上成人陪同登机",
    "02_012_F06001_0406": "异步生单-运价直连生单-反采生单接口-生单失败-此条报价不支持儿童购买成人票",
    "02_012_F06001_0407": "异步生单-运价直连生单-反采生单接口-生单失败-此条报价不支持购买儿童票",
    "02_012_F06001_0500": "异步生单-运价直连生单-反采生单接口-生单失败-订单包含失信人",
    "02_012_F06001_0901": "异步生单-运价直连生单-反采生单接口-生单失败-航班列表错误",
    "02_012_F06001_0902": "异步生单-运价直连生单-反采生单接口-生单失败-航班OTA错误",
    "02_012_F06001_0903": "异步生单-运价直连生单-反采生单接口-生单失败-航班Booking错误",
    "02_012_F06001_0904": "异步生单-运价直连生单-反采生单接口-生单失败-航班生单错误",
    "02_012_F06001_0905": "异步生单-运价直连生单-反采生单接口-生单失败-航班支付前错误",
    "02_012_F06001_0906": "异步生单-运价直连生单-反采生单接口-生单失败-航班支付错误",
    "02_012_F06001_0907": "异步生单-运价直连生单-反采生单接口-生单失败-回帖票号错误",
    "02_013_F06003_0001": "异步生编-运价直连生编-反采生编接口-生编失败-检查参数",
    "02_013_F06003_0002": "异步生编-运价直连生编-反采生编接口-生编失败-保存订单错误",
    "02_013_F06003_0003": "异步生编-运价直连生编-反采生编接口-生编失败-NULL,错误码保留暂不启用",
    "02_013_F06003_0004": "异步生编-运价直连生编-反采生编接口-生编失败-参数错误",
    "02_013_F06003_0005": "异步生编-运价直连生编-反采生编接口-生编失败-未知错误",
    "02_013_F06003_0006": "异步生编-运价直连生编-反采生编接口-生编失败-未支付重复提交",
    "02_013_F06003_0007": "异步生编-运价直连生编-反采生编接口-生编失败-支付重复提交",
    "02_013_F06003_0008": "异步生编-运价直连生编-反采生编接口-生编失败-无效booking",
    "02_013_F06003_0009": "异步生编-运价直连生编-反采生编接口-生编失败-B3B错误",
    "02_013_F06003_0010": "异步生编-运价直连生编-反采生编接口-生编失败-填写的航程类型和接口不匹配",
    "02_013_F06003_0401": "异步生编-运价直连生编-反采生编接口-生编失败-特殊票,不支持的证件类型",
    "02_013_F06003_0402": "异步生编-运价直连生编-反采生编接口-生编失败-特殊票,乘机人年龄不符合购买年龄限制",
    "02_013_F06003_0403": "异步生编-运价直连生编-反采生编接口-生编失败-不满足售卖条件",
    "02_013_F06003_0404": "异步生编-运价直连生编-反采生编接口-生编失败-儿童、婴儿须由18岁以上成人陪同登机",
    "02_013_F06003_0406": "异步生编-运价直连生编-反采生编接口-生编失败-此条报价不支持儿童购买成人票",
    "02_013_F06003_0407": "异步生编-运价直连生编-反采生编接口-生编失败-此条报价不支持购买儿童票",
    "02_013_F06003_0500": "异步生编-运价直连生编-反采生编接口-生编失败-订单包含失信人",
    "02_013_F06003_0901": "异步生编-运价直连生编-反采生编接口-生编失败-航班列表错误",
    "02_013_F06003_0902": "异步生编-运价直连生编-反采生编接口-生编失败-航班OTA错误",
    "02_013_F06003_0903": "异步生编-运价直连生编-反采生编接口-生编失败-航班Booking错误",
    "02_013_F06003_0904": "异步生编-运价直连生编-反采生编接口-生编失败-航班生单错误",
    "02_013_F06003_0905": "异步生编-运价直连生编-反采生编接口-生编失败-航班支付前错误",
    "02_013_F06003_0906": "异步生编-运价直连生编-反采生编接口-生编失败-航班支付错误",
    "02_013_F06003_0907": "异步生编-运价直连生编-反采生编接口-生编失败-回帖票号错误",
    "03_001_000000_0001": "支付-异步生单结果检查-无接口依赖-异步生单检查失败",
    "03_001_000000_0002": "支付-异步生单结果检查-无接口依赖-异步生单结果为空",
    "03_001_000000_0003": "支付-异步生单结果检查-无接口依赖-异步生单未完成（超时）",
    "03_001_000000_0004": "支付-异步生单结果检查-无接口依赖-异步生编失信人拦截",
    "03_001_000000_0005": "支付-异步生单结果检查-无接口依赖-特殊产品校验失败未兜底",
    "03_002_000000_0001": "支付-订单状态检查-无接口依赖-不是待支付状态",
    "03_002_000000_0002": "支付-订单状态检查-无接口依赖-已存在支付成功记录",
    "03_002_000000_0003": "支付-订单状态检查-无接口依赖-支付前校验加锁失败",
    "03_002_000000_0004": "支付-订单查询-查询订单失败",
    "03_003_000000_0001": "支付-www重复订单拦截-无接口依赖-重复订单强拦截",
    "03_003_000000_0002": "支付-www重复订单拦截-无接口依赖-重复订单软拦截",
    "03_004_000000_0001": "支付-销售时间检查-无接口依赖-代理商禁售时间校验不通过",
    "03_005_F02011_0001": "支付-爆单禁售-爆单禁售接口-航线被禁售爆单拦截",
    "03_005_F02011_0002": "支付-报价禁售-blockPrice接口-报价已被禁售无法生单拦截",
    "03_006_000000_0001": "支付-失信人校验-失信人接口&生编接口-失信人拦截",
    "03_007_000000_0001": "支付-pnr一致性校验-无接口依赖-rtPat缓存获取为空",
    "03_007_000000_0002": "支付-pnr一致性校验-无接口依赖-pnr乘机人航班信息跟订单不一致",
    "03_008_F07003_0001": "支付-供应单验价-自营bt2验价接口-价格获取失败",
    "03_008_F07003_0002": "支付-供应单验价-自营bt2验价接口-获取的价格无效",
    "03_008_F07003_0003": "支付-供应单验价-自营bt2验价接口-变价",
    "03_008_F07004_0001": "支付-供应单验价-PEK950pata价格-价格获取失败",
    "03_008_F07004_0002": "支付-供应单验价-PEK950pata价格-获取的价格无效",
    "03_008_F07004_0003": "支付-供应单验价-PEK950pata价格-pata价格高于报价标识价格",
    "03_008_F07004_0004": "支付-供应单验价-PEK950pata价格-获取价格超过失效时间",
    "03_009_000000_0001": "支付-供应单验价-无接口依赖-供应单验价失败",
    "03_009_F02002_0001": "支付-供应单验价-pat缓存-获取pat价格为空",
    "03_009_F02002_0002": "支付-供应单验价-pat缓存-pat价格非法|价格获取失败",
    "03_009_F02002_0003": "支付-供应单验价-pat缓存-变价",
    "03_009_F02002_0004": "支付-供应单验价-pat缓存-验价失败",
    "03_009_F02002_0005": "支付-供应单验价-pat缓存-业务不支持pid降级",
    "03_009_F02002_0006": "支付-供应单验价-pat缓存-价格获取内部异常",
    "03_009_F02002_0007": "支付-供应单验价-pat缓存-儿童基建燃油变价",
    "03_009_F02002_0008": "支付-供应单验价-pat缓存-价格明细为空",
    "03_009_F02002_0009": "支付-供应单验价-pat缓存-成人基建燃油变价",
    "03_009_F02002_0010": "支付-供应单验价-pat缓存-儿童价格明细为空",
    "03_009_F02002_0011": "支付-供应单验价-pat缓存-RT成功Pata失败降级走验价队列",
    "03_009_F02002_0012": "支付-供应单验价-pat缓存-RT成功节省pata走组合验价",
    "03_009_F02002_0013": "支付-供应单验价-pat缓存-SS成功Pata失败降级走验价队列",
    "03_009_F02002_0014": "支付-供应单验价-pat缓存-SSPata特殊航司强制降级走验价队列",
    "03_009_000000_0006": "支付-供应单验价-无接口依赖-系统异常",
    "03_009_F05001_0001": "支付-供应单验价-旗舰店booking接口-价格获取失败",
    "03_009_F05001_0004": "支付-供应单验价-旗舰店booking接口-获取的价格无效",
    "03_009_F05001_0005": "支付-供应单验价-旗舰店booking接口-变价",
    "03_009_F05001_0006": "支付-供应单验价-旗舰店中转包booking接口-价格获取失败",
    "03_009_F05001_0007": "支付-供应单验价-旗舰店中转包booking接口-获取的价格无效",
    "03_009_F05001_0008": "支付-供应单验价-旗舰店中转包booking接口-变价",
    "03_009_F03001_0001": "支付-供应单验价-基础数据旗舰店运价库接口-价格获取失败",
    "03_009_F03001_0004": "支付-供应单验价-基础数据旗舰店运价库接口-获取的价格无效",
    "03_009_F03001_0005": "支付-供应单验价-基础数据旗舰店运价库接口-变价",
    "03_009_F03002_0001": "支付-供应单验价-官网运价接口-价格获取失败",
    "03_009_F03002_0004": "支付-供应单验价-官网运价接口-获取的价格无效",
    "03_009_F03002_0005": "支付-供应单验价-官网运价接口-变价",
    "03_009_F03003_0001": "支付-供应单验价-航司B2B价接口-获取的价格无效",
    "03_009_F03003_0002": "支付-供应单验价-航司B2B价接口-价格获取失败",
    "03_009_F03003_0003": "支付-供应单验价-航司B2B价接口-变价",
    "03_009_F03004_0001": "支付-供应单验价-PEK950接口-获取的价格无效",
    "03_009_F03004_0002": "支付-供应单验价-PEK950接口-价格获取失败",
    "03_009_F03004_0003": "支付-供应单验价-PEK950接口-变价",
    "03_009_F05004_0001": "支付-供应单验价-运价直连验价接口-接口返回失败",
    "03_009_F05004_0002": "支付-供应单验价-运价直连验价接口-接口调用超时",
    "03_009_F05004_0003": "支付-供应单验价-运价直连验价接口-接口调用异常",
    "03_009_F05004_0004": "支付-供应单验价-运价直连验价接口-航段一致性校验失败",
    "03_009_F05004_0005": "支付-供应单验价-运价直连验价接口-会员注册失败变价",
    "03_009_F05004_0006": "支付-供应单验价-运价直连验价接口-变价",
    "03_009_F05004_0007": "支付-供应单验价-运价直连验价接口-接口返回价格非法",
    "03_009_F05004_1201": "支付-供应单验价-运价直连验价接口-price请求参数为空",
    "03_009_F05004_1202": "支付-供应单验价-运价直连验价接口-price获取代理商接口返回为空",
    "03_009_F05004_1203": "支付-供应单验价-运价直连验价接口-price获取代理商接口错误",
    "03_009_F05004_1204": "支付-供应单验价-运价直连验价接口-price代理商接口返回错误",
    "03_009_F05004_1205": "支付-供应单验价-运价直连验价接口-price代理商接口返回超时",
    "03_009_F05004_1206": "支付-供应单验价-运价直连验价接口-price代理商接口返回其他错误",
    "03_009_F05004_1207": "支付-供应单验价-运价直连验价接口-price代理商结果版本错误",
    "03_009_F05004_1208": "支付-供应单验价-运价直连验价接口-价格校验重要参数为空或者改变",
    "03_009_F05004_1209": "支付-供应单验价-运价直连验价接口-price解析接口数据错误",
    "03_009_F05004_1210": "支付-供应单验价-运价直连验价接口-price接口加密错误",
    "03_009_F05007_0001": "支付-供应单验价-往返运价直连验价接口-接口返回失败",
    "03_009_F05007_0002": "支付-供应单验价-往返运价直连验价接口-接口调用超时",
    "03_009_F05007_0003": "支付-供应单验价-往返运价直连验价接口-接口调用异常",
    "03_009_F05007_0004": "支付-供应单验价-往返运价直连验价接口-接口返回价格为空",
    "03_009_F05007_0007": "支付-供应单验价-往返运价直连验价接口-接口返回价格非法",
    "03_009_F06002_0001": "支付-供应单验价-运价直连反采分销验价接口-获取异步生单结果为空",
    "03_009_F06002_0002": "支付-供应单验价-运价直连反采分销验价接口-接口调用超时",
    "03_009_F06002_0003": "支付-供应单验价-运价直连反采分销验价接口-接口调用异常",
    "03_009_F06002_0004": "支付-供应单验价-运价直连反采分销验价接口-反采单支付前校验变价",
    "03_009_F06002_0005": "支付-供应单验价-运价直连反采分销验价接口-反采单支付前校验失败",
    "03_009_F06002_0006": "支付-供应单验价-运价直连反采分销验价接口-反采单支付前校验返回为空",
    "03_009_F06002_0007": "支付-供应单验价-运价直连反采分销验价接口-未取到反采单域名",
    "03_009_F08001_0001": "支付-供应单验价-自营深航btb验价接口-价格获取失败",
    "03_009_F08001_0004": "支付-供应单验价-自营深航btb验价接口-获取的价格无效",
    "03_009_F08001_0005": "支付-供应单验价-自营深航btb验价接口-变价",
    "03_010_F01003_0001": "支付-政策校验-政策查询接口-未查到政策",
    "03_011_F05003_0001": "支付-运价直连pnr校验-单程运价直连接口-pnr校验失败",
    "03_011_F05003_0002": "支付-运价直连pnr校验-单程运价直连接口-pnr校验接口调用超时",
    "03_011_F05003_0003": "支付-运价直连pnr校验-单程运价直连接口-接口调用异常",
    "03_011_F05003_0004": "支付-运价直连pnr校验-单程运价直连接口-失信人拦截",
    "03_011_F05003_0005": "支付-运价直连pnr校验-单程运价直连接口-pnr校验返回为空",
    "03_011_F05003_0006": "支付-运价直连pnr校验-单程运价直连接口-pnr降级无效",
    "03_011_F05003_0007": "支付-运价直连pnr校验-单程运价直连接口-代理接口返回的错误信息中提示重复拦截",
    "03_011_F05003_0008": "支付-运价直连pnr校验-单程运价直连接口-代理接口返回乘机人不可购买当前航司产品",
    "03_011_F05003_0299": "支付-运价直连pnr校验-单程运价直连接口-运价直连生单失败",
    "03_011_F05003_0357": "支付-运价直连pnr校验-单程运价直连接口-代理重复预订拦截",
    "03_011_F05003_0363": "支付-运价直连pnr校验-单程运价直连接口-疫情拦截",
    "03_011_F05003_1301": "支付-运价直连pnr校验-单程运价直连接口-pnr获取代理商接口错误",
    "03_011_F05003_1302": "支付-运价直连pnr校验-单程运价直连接口-pnr代理商接口返回错误",
    "03_011_F05003_1303": "支付-运价直连pnr校验-单程运价直连接口-pnr代理商接口返回超时",
    "03_011_F05003_1304": "支付-运价直连pnr校验-单程运价直连接口-pnr代理商接口返回其他异常",
    "03_011_F05003_1305": "支付-运价直连pnr校验-单程运价直连接口-pnr代理商接口数据解析错误",
    "03_011_F05003_1306": "支付-运价直连pnr校验-单程运价直连接口-pnr代理商接口加密错误",
    "03_011_F05006_0001": "支付-运价直连pnr校验-往返运价直连接口-pnr校验失败",
    "03_011_F05006_0003": "支付-运价直连pnr校验-往返运价直连接口-pnr校验返回为空",
    "03_011_F05006_0004": "支付-运价直连pnr校验-往返运价直连接口-失信人拦截",
    "03_011_F05006_0005": "支付-运价直连pnr校验-往返运价直连接口-接口调用异常",
    "03_012_D01001_0001": "支付-用户标签匹配验证-用户标签接口-用户标签不匹配",
    "03_013_F04010_0001": "支付-摩萨德标签匹配验证-用户标签接口-摩萨德标签拦截",
    "03_014_F04004_0001": "支付-库存产品av库存校验-pidAv接口-av库存不足",
    "03_014_F04004_0003": "支付-库存产品av库存校验-pidAv接口-库存校验失败",
    "03_014_F04004_0002": "支付-库存产品av库存校验-pidAv接口-库存校验av接口异常",
    "03_014_F04004_0004": "支付-支付前校验实时pid验舱-pidAv接口-av库存不足",
    "03_016_F01004_0001": "支付-库存产品库存校验-报价统一库存校验接口-库存校验失败",
    "03_016_F01004_0002": "支付-库存产品库存校验-报价统一库存校验接口-接口调用异常",
    "03_016_F01004_0003": "支付-库存产品库存校验-报价统一库存校验接口-库存无效",
    "03_016_F01004_0004": "支付-库存产品库存校验-报价统一库存校验接口-库存不存在",
    "03_016_F01004_0005": "支付-库存产品库存校验-报价统一库存校验接口-可售数量不足",
    "03_016_F01004_0006": "支付-库存产品库存校验-报价统一库存校验接口-库存系统返回系统异常",
    "03_017_F01005_0001": "支付-包机产品库存校验-包机库存校验接口-库存校验失败",
    "03_017_F01005_0002": "支付-包机产品库存校验-包机库存校验接口-接口调用异常",
    "03_017_F01005_0003": "支付-包机产品库存校验-包机库存校验接口-库存已下架",
    "03_017_F01005_0004": "支付-包机产品库存校验-包机库存校验接口-库存不存在",
    "03_017_F01005_0005": "支付-包机产品库存校验-包机库存校验接口-可售数量不足",
    "03_018_000000_0001": "支付-运价直连恶意订单验座-无接口依赖-异步生单结果失败",
    "03_018_000000_0002": "支付-运价直连恶意订单验座-无接口依赖-异步生单结果为空",
    "03_018_000000_0003": "支付-运价直连恶意订单验座-无接口依赖-异步生单未完成（超时）",
    "03_018_F05001_0004": "支付-运价直连恶意订单验座-运价直连booking接口-运价直连恶意订单验座失败",
    "03_015_000000_0001": "支付-同步验价-无接口依赖-超过最大变价阈值",
    "03_015_000000_0002": "支付-同步验价-无接口依赖-变价",
    "03_015_000000_0003": "支付-同步验价-无接口依赖-运价直连验价变舱(旗舰店变舱变价场景",
    "03_015_000000_0004": "支付-同步验价-无接口依赖-运价直连会员注册失败变价",
    "03_015_000000_0005": "支付-同步验价-无接口依赖-运价直连航司活动失败变价",
    "03_015_000000_0006": "支付-同步验价-无接口依赖-基建燃油变价",
    "03_015_000000_0011": "支付-同步验价-无接口依赖-www变价导致订立减变价拦截",
    "03_015_000000_0012": "支付-同步验价-异步生单兜底-变价直接失败",
    "03_015_000000_0013": "支付-同步验价-无接口依赖-分销短时间内变价重复下单拦截",
    "03_015_000000_0014": "支付-同步验价-无接口依赖-换供应订单满足配置支付前变价直接失败",
    "03_019_F07002_0001": "支付-特殊库存产品-自营库存校验接口-接口返回为空",
    "03_019_F07002_0002": "支付-特殊库存产品-自营库存校验接口-接口调用超时",
    "03_019_F07002_0003": "支付-特殊库存产品-自营库存校验接口-接口调用异常",
    "03_019_F07002_0004": "支付-特殊库存产品-自营库存校验接口-库存锁定失败拦截",
    "03_020_000000_0001": "支付-订单风险校验-无接口依赖-子单包含其他付费产品",
    "03_021_D01002_0001": "支付前校验-资损检测-数据组接口-数据组判定此报价为异常报价",
    "03_021_000000_0002": "支付前校验-资损检测-无接口依赖-机票价格过低,判断此报价为异常报价",
    "03_021_000000_0003": "支付前校验-资损检测-无接口依赖-NF单价差过大",
    "03_021_000000_0004": "支付前校验-资损检测-无接口依赖-统计窗口内NF单价差过大超过阈值",
    "03_022_000000_0001": "支付前校验-一口价权益权益次数检测-无接口依赖-用户一口价权益权益次数超过阈值",
}



# 第二部分：分析请求相关
def build_analysis_payload(
    order_list: List[str], submitTraceIds: List[str], start_date: str, end_date: str
) -> Dict[str, Any]:
    """构建分析请求体"""

    def format_date(date_str: str, is_start: bool) -> str:
        """日期格式转换"""
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return (
                date_obj.strftime("%Y-%m-%d 00:00:00")
                if is_start
                else date_obj.strftime("%Y-%m-%d 23:59:59")
            )
        except ValueError:
            raise ValueError(f"无效的日期格式: {date_str}")

    orderNoCondition = None
    submitTraceIdsCondition = None
    if order_list:
        orderNoCondition = {
            "function": "equal",
            "params": order_list,
            "field": "event.$Anything.orderNo",
        }
    if submitTraceIds:
        submitTraceIdsCondition = {
            "function": "equal",
            "params": submitTraceIds,
            "field": "event.$Anything.qtraceid",
        }

    filter = None
    if orderNoCondition and submitTraceIdsCondition:
        conditions = [orderNoCondition, submitTraceIdsCondition]
        filter = {"conditions": conditions, "relation": "or"}
    elif orderNoCondition:
        conditions = [orderNoCondition]
        filter = {"conditions": conditions}
    elif submitTraceIdsCondition:
        conditions = [submitTraceIdsCondition]
        filter = {"conditions": conditions}

    return {
        "dateOffset": None,
        "customDateOffset": None,
        "measures": [
            {
                "measureFilter": None,
                "eventName": "traceTradeResult",
                "eventName_ZH": "跟踪交易阶段结果\n",
                "aggregator_ZH": "总次数",
                "isExpression": False,
                "alias": "",
                "aggregator": "general",
            }
        ],
        "byFields": [
            "event.$Anything.qtraceid",
            "event.$Anything.orderNo",
            "event.$Anything.tradeStage",
            "event.$Anything.errCode",
            "event.$Anything.oriBizErrorCode",
            "event.$Anything.bizErrorCode",
            "event.$Anything.interceptType",
            "event.$Anything.interceptRemark",
            "event.$Anything.fallbackStageNew",
            "event.$Anything.createTime",
            "event.$Anything.payErrorCode",
            "event.$Anything.interceptReason",
        ],
        "filter": filter,
        "fromDate": format_date(start_date, is_start=True),
        "toDate": format_date(end_date, is_start=False),
        "customFromDate": format_date(start_date, is_start=True),
        "customToDate": format_date(end_date, is_start=False),
        "unit": "day",
        "cunit": "line",
        "queryModel": 0,
        "samplingFactor": "0",
        "timeColumn": "",
        "useCache": True,
    }


def send_analysis_request(
    order_list: List[str], submitTraceIds: List[str], start_date: str, end_date: str
) -> Dict[str, Any]:
    """发送分析请求"""
    if not order_list and not submitTraceIds:
        return {"warning": "空orderList跳过分析请求"}

    url = "http://qlibra.corp.qunar.com/api/evtAnalyze/query/"
    params = {"projectId": "1472", "source": "search"}
    headers = {
        "Cookie": "sensor_7896=f522d549419540487e022d2cb5b841af390f480154a3927e19ed826931fbfb0888ce60dbbbcff266d9cb1337a6f102b88369ee059ac7eb4fb9b4fa1087654c8734ce2a425139897b36b4a930bd4ef7ac2170c0495a73ea78f4b4d1dc8d12a64d; QN1=00014b00247c6c3c37e0c9fa",
        "Content-Type": "application/json",
    }

    try:
        payload = build_analysis_payload(
            order_list, submitTraceIds, start_date, end_date
        )
        response = requests.post(
            url, params=params, headers=headers, json=payload, timeout=15
        )
        response.raise_for_status()
        print("----------------response", json.dumps(response.json(), indent=4,ensure_ascii=False))
        return response.json()
    except Exception as e:
        return {"error": f"分析请求失败: {str(e)}"}


def get_stage_priority(stage: str) -> int:
    """定义阶段排序优先级"""
    priority_order = {"生单阶段": 1, "异步生单阶段": 2, "支付前校验": 3}
    return priority_order.get(stage.strip(), 99)  # 未知阶段排最后


def process_analysis_data(analysis_response: Dict[str, Any]):
    """处理分析结果数据并排序"""
    orderNoMap = {}
    qtraceidMap = {}

    # 防御性处理数据结构
    data_list = analysis_response.get("data", [])
    if not isinstance(data_list, list):
        return {}

    # 第一遍：收集数据
    for data_item in data_list:
        rows = data_item.get("rows", [])
        if not isinstance(rows, list):
            continue

        for row in rows:
            by_values = row.get("byValues", [])
            if not isinstance(by_values, list) or len(by_values) < 6:
                continue

            try:
                # 提取原始错误码
                ori_biz_error_code = str(by_values[4]).strip()
                biz_error_code = str(by_values[5]).strip()

                # 构建标准化数据结构
                order_info = {
                    "qtraceid": str(by_values[0]).strip(),
                    "orderNo": str(by_values[1]).strip(),
                    "stage": str(by_values[2]).strip(),
                    "errCode": (
                        int(by_values[3]) if str(by_values[3]).strip().isdigit() else 0
                    ),
                    "oriBizErrorCodeDesc": ERROR_CODE_MAPPING.get(ori_biz_error_code, ori_biz_error_code),
                    "bizErrorCodeDesc": ERROR_CODE_MAPPING.get(biz_error_code, biz_error_code),
                    "interceptType": str(by_values[6]).strip(),
                    "interceptRemark": str(by_values[7]).strip(),
                    "fallbackStageNew": str(by_values[8]).strip(),
                    "createTime": str(by_values[9]).strip(),
                    "payErrorCode": str(by_values[10]).strip(),
                    "interceptReason": str(by_values[11]).strip(),
                }
                # 按orderNo分组
                order_no = order_info["orderNo"]
                if order_no:
                    if order_no not in orderNoMap:
                        orderNoMap[order_no] = []
                    orderNoMap[order_no].append(order_info)
                # 按qtraceid分组
                qtraceid = order_info["qtraceid"]
                if qtraceid:
                    if qtraceid not in qtraceidMap:
                        qtraceidMap[qtraceid] = []
                    qtraceidMap[qtraceid].append(order_info)
            except Exception as e:
                # 记录错误但继续处理其他数据
                print(f"数据处理异常: {str(e)}")
                continue

    # 第二遍：排序数据
    for order_no in orderNoMap:
        try:
            orderNoMap[order_no].sort(
                key=lambda x: (
                    get_stage_priority(x["stage"]),
                    (
                        datetime.strptime(x["createTime"], "%Y-%m-%d %H:%M:%S")
                        if x.get("createTime")
                        else datetime.min
                    ),
                )
            )
        except (KeyError, ValueError):
            # 处理缺失stage字段或时间格式错误的情况
            continue
    for qtraceid in qtraceidMap:
        try:
            qtraceidMap[qtraceid].sort(
                key=lambda x: (
                    get_stage_priority(x["stage"]),
                    (
                        datetime.strptime(x["createTime"], "%Y-%m-%d %H:%M:%S")
                        if x.get("createTime")
                        else datetime.min
                    ),
                )
            )
        except (KeyError, ValueError):
            # 处理缺失stage字段或时间格式错误的情况
            continue
    return orderNoMap, qtraceidMap


def extract_date_from_order(order_no):
    """
    Extract date from order number in format dvbYYMMDD...
    Example: dvb250321112040552 -> 2025-03-21
    """
    if not order_no or len(order_no) < 10:
        return None
    
    try:
        # Extract YYMMDD from order number (positions 3-8)
        date_str = order_no[3:9]
        year = "20" + date_str[:2]  # Add "20" prefix for full year
        month = date_str[2:4]
        day = date_str[4:6]
        return f"{year}-{month}-{day}"
    except Exception:
        return None

# 第三部分：主流程整合
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """主处理流程"""
    try:
        orderNo = param.get("orderNo", "")
        startDate = extract_date_from_order(orderNo)
        endDate = extract_date_from_order(orderNo)
        # 发送分析请求
        analysis_result = send_analysis_request(
            order_list=[orderNo],
            submitTraceIds=[],
            start_date=startDate,
            end_date=endDate,
        )
        print("----------------analysis_result", json.dumps(analysis_result, indent=4,ensure_ascii=False))
        # 处理分析结果
        orderNoMap = {}
        qtraceidMap = {}
        if "error" not in analysis_result:
            orderNoMap, qtraceidMap = process_analysis_data(analysis_result)

        return {
            "error": "",
            "results": orderNoMap.get(orderNo, []),
        }
    except Exception as e:
        stack_trace = traceback.format_exc()
        error_msg = f"获取数据失败: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {
            "error": f"获取数据失败: {error_msg}",
            "results": [],
        }



if __name__ == "__main__":
    param = {
        "orderNo": "sjd250413181747685",
    }
    result = main(param)
    print("----------------result", json.dumps(result, indent=4,ensure_ascii=False))
