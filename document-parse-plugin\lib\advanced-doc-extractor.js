/**
 * 高级DOC文本提取器
 * 专门处理复杂的.doc文件文本提取
 */
class AdvancedDocExtractor {
  constructor() {
    this.patterns = {
      // 常见的文本模式
      textPatterns: [
        /[\x20-\x7E\u4e00-\u9fff]{10,}/g, // ASCII + 中文字符
        /[a-zA-Z\s]{20,}/g, // 英文文本
        /[\u4e00-\u9fff\s]{10,}/g // 中文文本
      ],
      
      // Word文档特有的结构标识
      wordMarkers: [
        'Microsoft Word',
        'Word.Document',
        'Normal.dot',
        'Times New Roman',
        'Arial'
      ]
    };
  }

  // 主要的文本提取方法
  extractText(arrayBuffer) {
    console.log('开始高级文本提取...');
    
    const results = [];
    
    // 方法1: 多编码尝试
    results.push(this.multiEncodingExtraction(arrayBuffer));
    
    // 方法2: 模式匹配提取
    results.push(this.patternBasedExtraction(arrayBuffer));
    
    // 方法3: 结构化扫描
    results.push(this.structuredScan(arrayBuffer));
    
    // 方法4: 字节序列分析
    results.push(this.byteSequenceAnalysis(arrayBuffer));
    
    // 选择最佳结果
    const bestResult = this.selectBestResult(results);
    
    console.log(`提取完成，最佳结果长度: ${bestResult.length}`);
    return bestResult;
  }

  // 多编码提取
  multiEncodingExtraction(arrayBuffer) {
    const encodings = [
      { name: 'utf-16le', weight: 3 },
      { name: 'utf-8', weight: 2 },
      { name: 'windows-1252', weight: 1 },
      { name: 'iso-8859-1', weight: 1 }
    ];
    
    let bestText = '';
    let maxScore = 0;
    
    for (const encoding of encodings) {
      try {
        const decoder = new TextDecoder(encoding.name, { fatal: false });
        const text = decoder.decode(arrayBuffer);
        const cleanText = this.cleanText(text);
        const score = this.scoreText(cleanText) * encoding.weight;
        
        if (score > maxScore) {
          maxScore = score;
          bestText = cleanText;
        }
      } catch (e) {
        console.warn(`编码 ${encoding.name} 失败:`, e.message);
      }
    }
    
    return bestText;
  }

  // 模式匹配提取
  patternBasedExtraction(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let allText = '';
    
    // 转换为字符串进行模式匹配
    let rawText = '';
    for (let i = 0; i < uint8Array.length; i++) {
      rawText += String.fromCharCode(uint8Array[i]);
    }
    
    // 应用各种模式
    for (const pattern of this.patterns.textPatterns) {
      const matches = rawText.match(pattern);
      if (matches) {
        allText += matches.join(' ') + ' ';
      }
    }
    
    return this.cleanText(allText);
  }

  // 结构化扫描
  structuredScan(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    const textSegments = [];
    
    // 扫描不同的偏移量
    const offsets = [0, 512, 1024, 2048, 4096];
    
    for (const offset of offsets) {
      if (offset >= uint8Array.length) continue;
      
      // UTF-16LE扫描
      const utf16Text = this.scanUTF16LE(uint8Array, offset);
      if (utf16Text.length > 10) {
        textSegments.push(utf16Text);
      }
      
      // ASCII扫描
      const asciiText = this.scanASCII(uint8Array, offset);
      if (asciiText.length > 10) {
        textSegments.push(asciiText);
      }
    }
    
    return this.mergeTextSegments(textSegments);
  }

  // UTF-16LE扫描
  scanUTF16LE(uint8Array, startOffset) {
    let text = '';
    let consecutiveValid = 0;
    
    for (let i = startOffset; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if (this.isValidTextChar(char)) {
        text += String.fromCharCode(char);
        consecutiveValid++;
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
        consecutiveValid = 0;
      } else if (char === 0x09) {
        text += '\t';
        consecutiveValid = 0;
      } else if (char === 0x20) {
        text += ' ';
        consecutiveValid = 0;
      } else {
        consecutiveValid = 0;
        // 如果连续遇到无效字符，可能需要停止
        if (text.length > 100 && consecutiveValid === 0) {
          // 检查是否应该继续
          let shouldContinue = false;
          for (let j = i; j < Math.min(i + 100, uint8Array.length - 1); j += 2) {
            const nextChar = uint8Array[j] | (uint8Array[j + 1] << 8);
            if (this.isValidTextChar(nextChar)) {
              shouldContinue = true;
              break;
            }
          }
          if (!shouldContinue) break;
        }
      }
      
      // 限制扫描长度
      if (text.length > 10000) break;
    }
    
    return this.cleanText(text);
  }

  // ASCII扫描
  scanASCII(uint8Array, startOffset) {
    let text = '';
    let consecutiveValid = 0;
    
    for (let i = startOffset; i < uint8Array.length; i++) {
      const char = uint8Array[i];
      
      if (char >= 0x20 && char <= 0x7E) {
        text += String.fromCharCode(char);
        consecutiveValid++;
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
        consecutiveValid = 0;
      } else if (char === 0x09) {
        text += '\t';
        consecutiveValid = 0;
      } else {
        consecutiveValid = 0;
        if (text.length > 100 && consecutiveValid === 0) {
          // 检查是否应该继续
          let shouldContinue = false;
          for (let j = i; j < Math.min(i + 50, uint8Array.length); j++) {
            const nextChar = uint8Array[j];
            if (nextChar >= 0x20 && nextChar <= 0x7E) {
              shouldContinue = true;
              break;
            }
          }
          if (!shouldContinue) break;
        }
      }
      
      // 限制扫描长度
      if (text.length > 5000) break;
    }
    
    return this.cleanText(text);
  }

  // 字节序列分析
  byteSequenceAnalysis(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    const sequences = this.findTextSequences(uint8Array);
    
    let bestSequence = '';
    let maxScore = 0;
    
    for (const sequence of sequences) {
      const text = this.extractFromSequence(uint8Array, sequence);
      const score = this.scoreText(text);
      
      if (score > maxScore) {
        maxScore = score;
        bestSequence = text;
      }
    }
    
    return bestSequence;
  }

  // 查找文本序列
  findTextSequences(uint8Array) {
    const sequences = [];
    let currentSequence = null;
    
    for (let i = 0; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if (this.isValidTextChar(char)) {
        if (!currentSequence) {
          currentSequence = { start: i, end: i, score: 1 };
        } else {
          currentSequence.end = i;
          currentSequence.score++;
        }
      } else {
        if (currentSequence && currentSequence.score > 10) {
          sequences.push(currentSequence);
        }
        currentSequence = null;
      }
    }
    
    if (currentSequence && currentSequence.score > 10) {
      sequences.push(currentSequence);
    }
    
    return sequences.sort((a, b) => b.score - a.score);
  }

  // 从序列提取文本
  extractFromSequence(uint8Array, sequence) {
    let text = '';
    
    for (let i = sequence.start; i <= sequence.end && i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);
      
      if (this.isValidTextChar(char)) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }
    }
    
    return this.cleanText(text);
  }

  // 检查是否为有效文本字符
  isValidTextChar(char) {
    return (char >= 0x20 && char <= 0x7E) || // ASCII可打印字符
           (char >= 0x4e00 && char <= 0x9fff) || // 中文字符
           (char >= 0x00A0 && char <= 0x00FF); // 扩展ASCII
  }

  // 合并文本段落
  mergeTextSegments(segments) {
    const uniqueSegments = [...new Set(segments)];
    const sortedSegments = uniqueSegments.sort((a, b) => b.length - a.length);
    
    // 选择最长的几个段落
    const topSegments = sortedSegments.slice(0, 3);
    return topSegments.join('\n\n');
  }

  // 选择最佳结果
  selectBestResult(results) {
    let bestResult = '';
    let maxScore = 0;
    
    for (const result of results) {
      if (result && typeof result === 'string') {
        const score = this.scoreText(result);
        if (score > maxScore) {
          maxScore = score;
          bestResult = result;
        }
      }
    }
    
    return bestResult;
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;
    
    let score = 0;
    
    // 基础长度分
    score += Math.min(text.length / 10, 100);
    
    // 可读字符比例
    const readableChars = text.match(/[\x20-\x7E\u4e00-\u9fff]/g);
    if (readableChars) {
      score += (readableChars.length / text.length) * 50;
    }
    
    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    if (chineseChars) {
      score += chineseChars.length * 2;
    }
    
    // 常见单词加分
    const commonWords = text.match(/\b(the|and|or|but|in|on|at|to|for|of|with|by|是|的|了|在|有|我|你|他|她|它)\b/gi);
    if (commonWords) {
      score += commonWords.length * 3;
    }
    
    // 句子结构加分
    const sentences = text.match(/[.!?。！？]/g);
    if (sentences) {
      score += sentences.length * 5;
    }
    
    return score;
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';
    
    return text
      // 移除控制字符
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // 移除过多的空白
      .replace(/\s{3,}/g, '  ')
      // 移除过多的换行
      .replace(/\n{3,}/g, '\n\n')
      // 清理行
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .trim();
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdvancedDocExtractor;
} else if (typeof window !== 'undefined') {
  window.AdvancedDocExtractor = AdvancedDocExtractor;
}
