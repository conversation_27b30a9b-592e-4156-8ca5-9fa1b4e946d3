import json
from typing import List, Dict, Any, Optional


def method1(model_item: Dict, price_list: List[Dict]) -> Optional[Dict]:
    """
    根据 model_item 的 (flightNo, tag, tSource) 匹配 price_list 中的元素
    1. 优先按 flightNo+tag 精确匹配
    2. 未匹配且 tSource=list 时，用 flightNo 再次匹配
    """
    # 输入类型校验
    if not isinstance(model_item, dict) or not isinstance(price_list, list):
        return None

    # 安全获取关键字段
    flight_no = model_item.get("flightNo", "")
    tag = model_item.get("tag", "")
    t_source = model_item.get("tSource", "")

    # 遍历 price_list 匹配
    for price_item in price_list:
        if not isinstance(price_item, dict):
            continue

        p_flight = price_item.get("flightNo", "")
        p_tag = price_item.get("tag", "")

        # 优先精确匹配
        if p_flight == flight_no and p_tag == tag:
            return price_item

    # 二次匹配逻辑
    if t_source.lower() == "list":
        for price_item in price_list:
            if not isinstance(price_item, dict):
                continue

            p_flight = price_item.get("flightNo", "")
            if p_flight == flight_no:
                return price_item

    return None


def method2(model_list: List[Dict]) -> List[Dict]:
    """过滤 model_list 中 matchQuestion != 匹配 的元素"""
    if not isinstance(model_list, list):
        return []

    return [
        item
        for item in model_list
        if isinstance(item, dict) and item.get("matchQuestion") == "匹配"
    ]


def method3(model_list: List[Dict], price_list: List[Dict]) -> List[Dict]:
    """组合方法1+方法2生成填充后的价格列表"""
    filled_list = []

    # 前置校验
    if not isinstance(model_list, list) or not isinstance(price_list, list):
        return filled_list

    # 获取匹配的 model_list
    matched_list = method2(model_list)

    # 遍历填充价格信息
    for model_item in matched_list:
        if not isinstance(model_item, dict):
            continue

        # 获取匹配的 price_item
        price_item = method1(model_item, price_list)
        if not price_item:
            continue

        price_item["matchQuestion"] = model_item.get("matchQuestion", "")
        price_item["desc"] = model_item.get("desc", "")
        filled_list.append(price_item)

    return filled_list


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    try:
        model_list = param.get("modelList")
        price_list = param.get("priceList")

        if price_list is None:
            return {
                "error": "庖丁查询明细结果过滤后为空!",
                "data": [],
                "modelOriRes": "",
            }

        if model_list is None:
            return {"error": "模型过滤返回结果为空!", "data": [], "modelOriRes": ""}

        if isinstance(model_list, dict):
            if "flightNo" in model_list:
                model_list = [model_list]  # 将单个字典转换为列表
            else:
                return {
                    "error": "模型过滤返回结果为数据格式不对!",
                    "data": [],
                    "modelOriRes": json.dumps(model_list, indent=2, ensure_ascii=False),
                }
        elif not isinstance(model_list, list):
            return {
                "error": "模型过滤返回结果为数据格式不对!",
                "data": [],
                "modelOriRes": json.dumps(model_list, indent=2, ensure_ascii=False),
            }

        if (
            price_list is None
            or not isinstance(price_list, list)
            or len(price_list) <= 0
        ):
            return {
                "error": "庖丁查询明细结果过滤后为空!",
                "data": [],
                "modelOriRes": "",
            }

        if (
            model_list is None
            or not isinstance(model_list, list)
            or len(model_list) <= 0
        ):
            return {"error": "模型过滤返回结果为空!", "data": [], "modelOriRes": ""}
        # 方法1测试
        # print("Method1 Test:")
        matched = method1(model_list[0], price_list)
        # print(f"匹配结果: {matched['price'] if matched else '无'}")  # 应返回 550

        # 方法2测试
        # print("\nMethod2 Test:")
        filtered = method2(model_list)
        # print(f"过滤后数量: {len(filtered)}")  # 应返回 1

        # 方法3测试
        # print("\nMethod3 Test:")
        filled = method3(model_list, price_list)
        return {"data": filled}
    except KeyError as e:
        return {"error": f"缺少必要参数: {str(e)}", "data": [], "modelOriRes": ""}
    except ValueError as e:
        return {"error": f"数据异常: {str(e)}", "data": [], "modelOriRes": ""}
    except Exception as e:
        return {"error": f"系统异常: {str(e)}", "data": [], "modelOriRes": ""}


# ----------------------------
# 测试用例
# ----------------------------
if __name__ == "__main__":
    # 测试数据
    model_list = [
        {
            "matchQuestion": "匹配",
            "flightNo": "CZ3924",
            "cut": "21",
            "coupon": "10",
            "oriTag": "",
            "price": "749",
            "tSource": "list",
            "tag": "YCP1",
            "xCut": "",
            "cabinType": "",
            "desc": "该记录的航班号CZ3924与用户描述的航班号匹配，起飞日期2025-03-09与用户描述一致，且起飞时间22:30也符合用户描述。价格749元在用户描述的价格区间（600多至700多）内，因此满足所有匹配条件。",
        }
    ]

    price_list = [
        {
            "flightNo": "CZ3924",
            "tSource": "list",
            "cabinType": "",
            "price": 749,
            "coupon": 10,
            "cut": 21,
            "tag": "YCP1",
            "expVendor": 3,
            "expansionType": "ALLOW",
            "poison": "False",
            "basicLabels": "ZSJ6HEI,ZSJ6",
            "filters": "ECONOMY,,PRICE_ASC",
            "passengers": "",
            "wrapperId": "ttsgnd04112",
            "productMark": "894",
            "cabin": "K",
            "packagePrice": 770,
            "basePrice": 740,
            "viewPrice": 770,
            "policyId": 228409486,
            "autoPriceDecreaseAmount": "30.0",
            "secondPrice": "745.8",
            "CPT": "1741250648",
            "allGoodItemPrice": 0,
            "depDate": "2025-03-09",
            "depTime": "22:30",
        }
    ]

    param = {"modelList": model_list, "priceList": price_list}
    # print(json.dumps(param, indent=2, ensure_ascii=False))
    output = main(param)
    print(json.dumps(output, indent=2, ensure_ascii=False))
