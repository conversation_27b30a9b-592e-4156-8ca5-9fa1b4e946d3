import numbers
from typing import Any


def is_valid_number(value: Any) -> bool:
    """检查值是否为有效的数字（整数或浮点数）"""
    if value is None:
        return False
    return isinstance(value, numbers.Number) and value > 0


def convertToNumeric(priceValue):
    """
    将字典中的 price 字段转为数值类型

    Args:
        priceValue: 要转换的价格值

    Returns:
        tuple: (是否转换成功, 转换后的值)
            - 如果转换成功，返回 (True, 转换后的数值)
            - 如果转换失败，返回 (False, 原始值)
    """
    try:
        if priceValue is None:
            return False, priceValue

        if isinstance(priceValue, (int, float)):
            # 如果已经是正数字，直接返回成功
            return True, priceValue

        if isinstance(priceValue, str):
            # 先尝试转整数（例如 "100" → 100）
            if priceValue.isdigit():
                value = int(priceValue)
                return True, value
            else:
                # 再尝试转浮点数（例如 "99.9" → 99.9）
                try:
                    price = float(priceValue)

                    # 如果是整数型浮点数（例如 "100.0" → 100）
                    if price.is_integer():
                        return True, int(price)
                    return True, price
                except:
                    return False, priceValue

        return False, priceValue
    except (ValueError, TypeError, AttributeError, Exception):
        # 捕获所有可能的转换异常，返回转换失败
        return False, priceValue


def checkIsPriceMatchWithDeviation(
    priceNumber, comparePriceList, deviationDuration=None
):
    """
    检查价格是否匹配比较价格列表中的任意一个价格，支持误差区间

    Args:
        priceNumber: 要检查的价格数值
        comparePriceList: 通过getPriceByPriceClassify获取的价格列表
        deviationDuration: 误差区间，格式如 {"minDeviation":-10, "maxDeviation": 10}
                          如果为None，则进行精准匹配

    Returns:
        tuple: (是否匹配, 匹配的价格项)
            - 如果匹配成功，返回 (True, 匹配的价格项)
            - 如果匹配失败，返回 (False, None)
    """
    # 首先确保priceNumber是数值类型
    success, price_value = convertToNumeric(priceNumber)
    if not success:
        return False, None

    # 如果comparePriceList为空，直接返回不匹配
    if not comparePriceList or not isinstance(comparePriceList, list):
        return False, None

    # 提取误差范围
    min_deviation = 0
    max_deviation = 0

    if deviationDuration is not None and isinstance(deviationDuration, dict):
        min_deviation = deviationDuration.get("minDeviation", 0)
        max_deviation = deviationDuration.get("maxDeviation", 0)

        # 确保误差值是数值类型
        if isinstance(min_deviation, str):
            success, min_deviation = convertToNumeric(min_deviation)
            if not success:
                min_deviation = 0
            if min_deviation > 0:
                min_deviation = 0

        if isinstance(max_deviation, str):
            success, max_deviation = convertToNumeric(max_deviation)
            if not success:
                max_deviation = 0
            if max_deviation < 0:
                max_deviation = 0

    # 遍历比较价格列表，判断是否匹配
    for price_item in comparePriceList:
        # 判断精准价格匹配 (含误差)
        if "price" in price_item:
            compare_price = price_item["price"]

            # 应用误差范围
            lower_bound = compare_price + min_deviation
            upper_bound = compare_price + max_deviation

            # 检查价格是否在允许误差的范围内
            if lower_bound <= price_value <= upper_bound:
                return True, price_item

        # 判断区间价格匹配 (含误差)
        if "leftPrice" in price_item and "rightPrice" in price_item:
            left_price = price_item["leftPrice"]
            right_price = price_item["rightPrice"]

            # 应用误差范围扩展区间
            left_with_deviation = left_price + min_deviation
            right_with_deviation = right_price + max_deviation

            # 检查价格是否在扩展的区间内
            if left_with_deviation <= price_value <= right_with_deviation:
                return True, price_item

    # 遍历完所有价格项都没有匹配成功，返回不匹配
    return False, None


def getPriceByPriceClassify(param, indexKey, priceClassifyList):
    """
    获取按照价格分类的价格信息

    Args:
        param: 包含价格参数的字典
        indexKey: 通常是'preSurPriceGroups'
        priceClassifyList: 价格分类类型列表，可以包含'prePrice', 'surPrice', 'changePrice'

    Returns:
        列表，包含符合条件的价格信息
    """
    result = []

    # 检查参数有效性
    if not param or indexKey not in param:
        return result

    group_list = param.get(indexKey)
    if not group_list or not isinstance(group_list, list):
        return result

    # 将单个字符串转换为列表处理
    if isinstance(priceClassifyList, str):
        priceClassifyList = [priceClassifyList]

    # 确保priceClassifyList是列表
    if not isinstance(priceClassifyList, list):
        return result

    # 遍历每个价格分类类型
    for priceClassify in priceClassifyList:
        # 根据分类确定要处理的精准价格和区间价格列表名称
        precise_key = ""
        duration_key = ""

        if priceClassify == "prePrice":
            precise_key = "prePrecisePriceList"
            duration_key = "preDurationPriceList"
        elif priceClassify == "surPrice":
            precise_key = "surPrecisePriceList"
            duration_key = "surDurationPriceList"
        elif priceClassify == "changePrice":
            precise_key = "changePrecisePriceList"
            duration_key = "changeDurationPriceList"
        else:
            continue  # 跳过不支持的价格分类

        for group in group_list:
            # 处理精准价格
            if precise_key in group and isinstance(group[precise_key], list):
                for price_info in group[precise_key]:
                    try:
                        if "price" in price_info:
                            # 使用改进的转换函数
                            success, price_value = convertToNumeric(price_info["price"])
                            if success:
                                price_data = {
                                    "priceType": priceClassify + "Precise",
                                    "parseType": price_info.get("parseType", "精准"),
                                    "price": price_value,
                                }
                                result.append(price_data)
                    except (ValueError, AttributeError, TypeError, Exception):
                        # 跳过无效的价格条目
                        pass

            # 处理区间价格
            if duration_key in group and isinstance(group[duration_key], list):
                for price_info in group[duration_key]:
                    try:
                        if "leftPrice" in price_info and "rightPrice" in price_info:
                            # 处理左右价格值，使用改进的转换函数
                            left_success, left_price = convertToNumeric(
                                price_info["leftPrice"]
                            )
                            right_success, right_price = convertToNumeric(
                                price_info["rightPrice"]
                            )

                            # 只有左右价格都转换成功才添加到结果
                            if left_success and right_success:
                                price_data = {
                                    "priceType": priceClassify + "Duration",
                                    "parseType": price_info.get(
                                        "parseType", "模糊的区间价格"
                                    ),
                                    "leftPrice": left_price,
                                    "rightPrice": right_price,
                                }
                                result.append(price_data)
                    except (ValueError, AttributeError, TypeError, Exception):
                        # 跳过无效的价格条目
                        pass

    return result


if __name__ == "__main__":
    param = {
        "searchDateList": "2025-03-18 11:00:00",
        "isPriceChangeIntent": "是",
        "departureDateList": "2025-03-20 08:40",
        "departureCityName": "长沙",
        "departureCity": "CSX",
        "arrivalCityName": "厦门",
        "arrivalCity": "XMN",
        "carrierList": "MF",
        "flightNumber": "MF8838",
        "feedbackChangeType": "上涨",
        "preSurPriceGroups": [
            {
                "prePrecisePriceList": [{"parseType": "精准", "price": "275"}],
                "preDurationPriceList": [
                    {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"}
                ],
                "surPrecisePriceList": [{"parseType": "精准", "price": "405"}],
                "surDurationPriceList": [
                    {"leftPrice": 400, "rightPrice": 409, "parseType": "模糊的区间价格"}
                ],
                "changePrecisePriceList": [{"parseType": "精准", "price": "130"}],
                "changeDurationPriceList": [
                    {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"}
                ],
            }
        ],
    }
    price_list = getPriceByPriceClassify(param, "preSurPriceGroups", "prePrice")
    print(price_list)

    priceNumber = 275
    is_match, match_price = checkIsPriceMatchWithDeviation(priceNumber, price_list)
    print(is_match, match_price)
