import json
import re
import html
from urllib.parse import unquote_to_bytes
from typing import Dict, Any, List, Optional, Union, Tuple
import uuid
from datetime import datetime, timedelta

# 提取otherInterestsCardMsg模板 - 改为紧凑型
INTERESTS_CARD_TEMPLATE = """一、产品信息<br/>1、产品名称：{product_name}<br/>2、产品价格：{priceLevel}（一套），不同航线对应不同价位组，产品价格均不含民航发展基金和燃油附加费。<br/>3、产品权益：购买产品后，在适用范围内享受 {usage_times}次 {travel_class} {flight_type}飞行权益，具体以活动主办方提供的带有"{product_name}"标签的可兑换行程为准。<br/>4、产品适用航班：{airline_code}指定带有可兑换标签的航班，其中包机以及代码共享航班除外。活动页航线公示表仅供参考，实际可兑换航班和航班日期以实际查询为准。<br/>5、行李额：{luggage_amount}。<br/><br/>二、产品购买<br/>1、购买渠道：去哪儿旅行APP-机票首页-活动页面。<br/>2、购买日期：{sale_date}。<br/>3、购买规则：<br/>1）每个去哪儿网用户限购{purchase_limit}套产品。购买多套需分多次下单，一单仅支持一套，一套产品只能绑定一位乘机人。购买时需填写联系人信息以及实际乘机人信息，且乘机人信息（姓名及身份证号）一旦填写不允许修改、增减，乘机人权益不允许转让。如果乘机人信息填写错误导致无法乘机出行的责任需用户和乘机人自行承担。<br/>2）适用旅客：成人（12周岁及以上）适用，儿童婴儿等特殊旅客不适用。<br/><br/>三、产品兑换<br/>1、兑换渠道：去哪儿旅行APP<br/>2、产品兑换日期：{use_date}。<br/>3、航班日期：{depart_date_str}。<br/>4、其他兑换规则：<br/>1）本产品为机会型产品，兑换库存数量有限，实际以航空公司提供为准，可能出现售罄情况。<br/>2）本产品乘机证件为中国大陆地区居民身份证。<br/>3）可兑换标识：购买该产品的旅客，可选择带有"{product_name}"标签的航班进行兑换；每个订单仅可兑换一个乘机人的一个权益。<br/>4）购买本次产品权益与购买旅客绑定。需使用购买时的账户兑换当时购买的实际使用人权益；变更登录账户将导致无法正常兑换。<br/>5）本产品不享受航司里程及航段累积。<br/>6）本产品不得与其他产品同时享受优惠。<br/>{round_trip_rule}<br/>四、产品退订<br/>1、购买后产品一旦兑换（包含兑换后未实际乘机、兑换后机票自愿退改等情形），则不允许退订。<br/>2、购买后产品兑换权益未进行兑换，兑换期内可在去哪儿旅行APP-我的-订单里申请自助退款。<br/>3、产品到期从未发生兑换的视为放弃兑换，航班日期结束后（即取{use_date}的最后一天）后7个工作日内，活动主办方将对未兑换旅客发起自动退款。<br/><br/>五、机票退改签<br/>(一)自愿退改签规则<br/>{sign_and_transfer_rules}<br/>{refund_rules}<br/>{change_rules}<br/>(二)非自愿退改签规则<br/>{sign_and_transfer_rules}<br/>{refund_rules}<br/>{change_rules}<br/>(三)次卡产品兑换机票与平台内常规流程购票的退款流程存在差异，任何退款问题您可联系去哪儿网客服电话95117，核实可退金额后人工操作处理。<br/><br/>六、报销凭证：产品全部兑换出票且行程结束后，可通过去哪儿旅行APP-我的-查看全部订单-订单详情-开具发票路径自助申请开具行程单。行程单票面为产品权益价及各项税费总和。<br/><br/>七、个人信息授权声明：<br/>1、活动产品由{airline_code}提供最终承运服务，请您理解，为订立、履行您或者您指定个人作为一方当事人的服务合同所必需，去哪儿网需将订单信息确认页中涉及的您选购的产品信息与您输入的个人信息提供给服务提供方和代理销售方，以供其核实您的身份、确认您的订单，并最终为您提供预订和所预订的服务。在提供有关信息前，去哪儿网将严格执行《中华人民共和国个人信息保护法》以及去哪儿网《隐私政策》的规定，要求有关服务提供方和代理销售方切实履行个人信息保护义务，仅就提供服务之目的处理您的个人信息，不得擅自转移、共享、提供或基于其他目的处理您的个人信息。如果您对您的个人信息处理有任何疑问，请依照去哪儿网《隐私政策》指引与我们取得联系，您可以随时删除、修改、撤回您的个人信息。<br/>2、请您再次审慎核实确认您选择的产品信息与您输入的个人信息。若您点击【立即预订/下一步/去支付（不同旅游产品预订时展示的名称可能不同）】下单预订，表示您同意去哪儿网基于为您提供服务之目的，向有关服务提供方和代理销售方提供您的个人信息。<br/>3、在您通过去哪儿旅行app-机票首页-活动页面预订活动产品时，您的个人信息会提供给为您提供预订服务的服务提供方、代理销售方。为了顺利完成预订服务和使用所预订的服务，当您预订服务产品时，会收集并提供您的姓名、身份证号码（或航司指定的其他同等有效证件号码，具体以您输入为准）、手机号码（用于接收航班信息）、订单信息（用于核实您的订单情况）；含有个人信息的相关订单信息会依照法律规定的最短期限进行保存。<br/><br/>八、其他重要提示<br/>1、本产品为机会型产品，部分航线存在部分航班不可兑换的情况，具体可兑换航班和日期以实际查询为准。<br/>2、因市场原因机票价格实时变动，该产品价格可能存在高于实时查询机票价格的情况，请理解并理性购买。<br/>3、该产品兑换出票时需支付民航发展基金及燃油附加费等税费。<br/>4、该产品不得以任何形式转卖。<br/>5、同一用户账号、手机号、证件号、终端设备号、银行卡号、第三方支付账号，符合以上任一条件均视为同一用户。<br/>6、活动主办方可根据活动的实际举办情况对活动规则进行变更或调整，相关变动或调整将公布在活动页面或以合适的方式及时通知。<br/>7、如遇不可抗力（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障，活动不能正常进行的），活动主办方可取消或暂停本活动。<br/>8、参与活动过程中，如用户存在任何违法或违反诚实信用原则的行为（包括不限于作弊、洗钱、套现、违规账号、虚假交易、扰乱系统、实施网络攻击、转让所获得的奖励/权益等），活动主办方有权取消其活动参与资格及领取奖励资格，已发放的奖励，主办方有权予以收回。同时，保留追究其法律责任的权利。<br/>"""


# 参数校验函数
def validate_all_fields(data: Dict[str, Any]) -> List[str]:
    """
    校验所有字段的函数

    Args:
        data: 待校验的数据字典

    Returns:
        List[str]: 校验错误信息列表
    """
    messages = []

    # 校验 carrier (航司)
    carrier = data.get("carrier")
    if carrier is None or carrier == "":
        messages.append("carrier: 参数未解析")
    elif carrier == "需人工确认":
        messages.append("carrier: 需人工确认")
    elif not isinstance(carrier, str) or not re.match(r'^[A-Z]{2}$', carrier):
        messages.append("carrier: 不符合参数要求")

    # 校验 airline_code (航司简称)
    airline_code = data.get("airline_code")
    if airline_code is None or airline_code == "":
        messages.append("airline_code: 参数未解析")
    elif airline_code == "需人工确认":
        messages.append("airline_code: 需人工确认")
    elif not isinstance(airline_code, str) or not re.match(r'^[\u4e00-\u9fa5]+$', airline_code):
        messages.append("airline_code: 不符合参数要求")

    # 校验 od (活动航线)
    od = data.get("od")
    if od is None or od == "":
        messages.append("od: 参数未解析")
    elif od == "需人工确认":
        messages.append("od: 需人工确认")
    else:
        # 处理字符串或数组
        if isinstance(od, str):
            od_list = [od]
        elif isinstance(od, list):
            od_list = od
        else:
            messages.append("od: 不符合参数要求")
            od_list = []

        # 校验每个航线格式
        for route in od_list:
            if not isinstance(route, str) or not re.match(r'^[A-Z]{3}-[A-Z]{3}$', route):
                messages.append("od: 不符合参数要求")
                break

    # 校验 flight_type (航程类型)
    flight_type = data.get("flight_type")
    valid_flight_types = ["单程", "中转", "往返", "通程", "联程"]
    if flight_type is None or flight_type == "":
        messages.append("flight_type: 参数未解析")
    elif flight_type == "需人工确认":
        messages.append("flight_type: 需人工确认")
    elif flight_type not in valid_flight_types:
        messages.append("flight_type: 不符合参数要求")

    # 校验 usage_times (使用次数)
    usage_times = data.get("usage_times")
    valid_usage_times = ["1", "2", "多次"]
    if usage_times is None or usage_times == "":
        messages.append("usage_times: 参数未解析")
    elif usage_times == "需人工确认":
        messages.append("usage_times: 需人工确认")
    elif usage_times not in valid_usage_times:
        messages.append("usage_times: 不符合参数要求")

    # 校验 product_name (产品名称)
    product_name = data.get("product_name")
    if product_name is None or product_name == "":
        messages.append("product_name: 参数未解析")
    elif product_name == "需人工确认":
        messages.append("product_name: 需人工确认")
    elif not isinstance(product_name, str) or not re.match(r'^[\u4e00-\u9fa5]+$', product_name):
        messages.append("product_name: 不符合参数要求")

    # 校验 product_show (权益卡展示文案)
    product_show = data.get("product_show")
    if product_show is None or product_show == "":
        messages.append("product_show: 参数未解析")
    elif product_show == "需人工确认":
        messages.append("product_show: 需人工确认")
    elif not isinstance(product_show, str) or not re.match(r'^[\u4e00-\u9fa5]+$', product_show):
        messages.append("product_show: 不符合参数要求")

    # 校验 product_type (产品类型)
    product_type = data.get("product_type")
    valid_product_types = ["单程单次卡", "往返单次卡", "单程多次卡", "中转次卡"]
    if product_type is None or product_type == "":
        messages.append("product_type: 参数未解析")
    elif product_type == "需人工确认":
        messages.append("product_type: 需人工确认")
    elif product_type not in valid_product_types:
        messages.append("product_type: 不符合参数要求")

    # 校验 product_price (产品价格)
    product_price = data.get("product_price")
    if product_price is None or product_price == "":
        messages.append("product_price: 参数未解析")
    elif product_price == "需人工确认":
        messages.append("product_price: 需人工确认")
    else:
        try:
            float(product_price)
        except (ValueError, TypeError):
            messages.append("product_price: 不符合参数要求")

    # 校验 price_deduction (营销立减金额)
    price_deduction = data.get("price_deduction")
    if price_deduction is None or price_deduction == "":
        messages.append("price_deduction: 参数未解析")
    elif price_deduction == "需人工确认":
        messages.append("price_deduction: 需人工确认")
    else:
        try:
            float(price_deduction)
        except (ValueError, TypeError):
            messages.append("price_deduction: 不符合参数要求")

    # 校验 cabin (舱位)
    cabin = data.get("cabin")
    if cabin is None or cabin == "":
        messages.append("cabin: 参数未解析")
    elif cabin == "需人工确认":
        messages.append("cabin: 需人工确认")
    elif not isinstance(cabin, str) or not re.match(r'^[A-Z]$', cabin):
        messages.append("cabin: 不符合参数要求")

    # 校验 travel_class (舱等)
    travel_class = data.get("travel_class")
    valid_travel_classes = ["经济舱", "公务舱"]
    if travel_class is None or travel_class == "":
        messages.append("travel_class: 参数未解析")
    elif travel_class == "需人工确认":
        messages.append("travel_class: 需人工确认")
    elif travel_class not in valid_travel_classes:
        messages.append("travel_class: 不符合参数要求")

    # 校验 sale_date (销售日期)
    sale_date = data.get("sale_date")
    if sale_date is None or sale_date == "":
        messages.append("sale_date: 参数未解析")
    elif sale_date == "需人工确认":
        messages.append("sale_date: 需人工确认")
    elif not isinstance(sale_date, str) or not (
        re.match(r'^\d{4}-\d{2}-\d{2}$', sale_date) or
        re.match(r'^\d{4}-\d{2}-\d{2}至\d{4}-\d{2}-\d{2}$', sale_date)
    ):
        messages.append("sale_date: 不符合参数要求")

    # 校验 depart_date (航班日期)
    depart_date = data.get("depart_date")
    if depart_date is None or depart_date == "":
        messages.append("depart_date: 参数未解析")
    elif depart_date == "需人工确认":
        messages.append("depart_date: 需人工确认")
    elif isinstance(depart_date, list):
        for date_item in depart_date:
            if not isinstance(date_item, str) or not (
                re.match(r'^\d{4}-\d{2}-\d{2}$', date_item) or
                re.match(r'^\d{4}-\d{2}-\d{2}至\d{4}-\d{2}-\d{2}$', date_item)
            ):
                messages.append("depart_date: 不符合参数要求")
                break
    else:
        messages.append("depart_date: 不符合参数要求")

    # 校验 beside_time (旅行除外日期)
    beside_time = data.get("beside_time")
    if beside_time is not None and beside_time != "":
        if beside_time == "需人工确认":
            messages.append("beside_time: 需人工确认")
        elif isinstance(beside_time, list):
            for date_item in beside_time:
                if not isinstance(date_item, str) or not (
                    re.match(r'^\d{4}-\d{2}-\d{2}$', date_item) or
                    re.match(r'^\d{4}-\d{2}-\d{2}至\d{4}-\d{2}-\d{2}$', date_item)
                ):
                    messages.append("beside_time: 不符合参数要求")
                    break
        else:
            messages.append("beside_time: 不符合参数要求")
    else:
        messages.append("beside_time: 参数未解析")

    # 校验 use_limit (兑换限制)
    use_limit = data.get("use_limit")
    if use_limit is None or use_limit == "":
        messages.append("use_limit: 参数未解析")
    elif use_limit == "需人工确认":
        messages.append("use_limit: 需人工确认")
    elif not isinstance(use_limit, str) or not (
        re.match(r'^D\d+$', use_limit) or
        re.match(r'^D\d+-D\d+$', use_limit) or
        re.match(r'^可兑换\d+日后的航班$', use_limit)
    ):
        messages.append("use_limit: 不符合参数要求")

    # 校验 use_begin (兑换开启时间)
    use_begin = data.get("use_begin")
    if use_begin is None or use_begin == "":
        messages.append("use_begin: 参数未解析")
    elif use_begin == "需人工确认":
        messages.append("use_begin: 需人工确认")
    else:
        try:
            int(use_begin)
        except (ValueError, TypeError):
            messages.append("use_begin: 不符合参数要求")

    # 校验 use_date (兑换日期)
    use_date = data.get("use_date")
    if use_date is None or use_date == "":
        messages.append("use_date: 参数未解析")
    elif use_date == "需人工确认":
        messages.append("use_date: 需人工确认")
    elif not isinstance(use_date, str) or not (
        re.match(r'^\d{4}-\d{2}-\d{2}$', use_date) or
        re.match(r'^\d{4}-\d{2}-\d{2}至\d{4}-\d{2}-\d{2}$', use_date) or
        re.match(r'^即日起至\d{4}-\d{2}-\d{2}$', use_date)
    ):
        messages.append("use_date: 不符合参数要求")

    # 校验 refund_time (产品未兑换退款日期)
    refund_time = data.get("refund_time")
    if refund_time is None or refund_time == "":
        messages.append("refund_time: 参数未解析")
    elif refund_time == "需人工确认":
        messages.append("refund_time: 需人工确认")
    elif not isinstance(refund_time, str) or not re.match(r'^\d{4}-\d{2}-\d{2}$', refund_time):
        messages.append("refund_time: 不符合参数要求")

    # 校验 use_code (兑换标识)
    use_code = data.get("use_code")
    if use_code is None or use_code == "":
        messages.append("use_code: 参数未解析")
    elif use_code == "需人工确认":
        messages.append("use_code: 需人工确认")
    elif not isinstance(use_code, str):
        messages.append("use_code: 不符合参数要求")

    # 校验 passenger_type (旅客类型)
    passenger_type = data.get("passenger_type")
    if passenger_type is None or passenger_type == "":
        messages.append("passenger_type: 参数未解析")
    elif passenger_type == "需人工确认":
        messages.append("passenger_type: 需人工确认")
    elif passenger_type != "成人":
        messages.append("passenger_type: 不符合参数要求")

    # 校验 age_limit (年龄限制)
    age_limit = data.get("age_limit")
    if age_limit is None or age_limit == "":
        messages.append("age_limit: 参数未解析")
    elif age_limit == "需人工确认":
        messages.append("age_limit: 需人工确认")
    elif not isinstance(age_limit, str) or not re.match(r'^\d+-\d+$', age_limit):
        messages.append("age_limit: 不符合参数要求")

    # 校验 young_old (青老年)
    young_old = data.get("young_old")
    if young_old is not None and young_old != "":
        if young_old == "需人工确认":
            messages.append("young_old: 需人工确认")
        elif not isinstance(young_old, str):
            messages.append("young_old: 不符合参数要求")
    else:
        messages.append("young_old: 参数未解析")

    # 校验 sign_and_transfer_rules (签转规则)
    sign_and_transfer_rules = data.get("sign_and_transfer_rules")
    if sign_and_transfer_rules is None or sign_and_transfer_rules == "":
        messages.append("sign_and_transfer_rules: 参数未解析")
    elif sign_and_transfer_rules == "需人工确认":
        messages.append("sign_and_transfer_rules: 需人工确认")
    elif not isinstance(sign_and_transfer_rules, str):
        messages.append("sign_and_transfer_rules: 不符合参数要求")

    # 校验 refund_rules (退票规则)
    refund_rules = data.get("refund_rules")
    if refund_rules is None or refund_rules == "":
        messages.append("refund_rules: 参数未解析")
    elif refund_rules == "需人工确认":
        messages.append("refund_rules: 需人工确认")
    elif not isinstance(refund_rules, str):
        messages.append("refund_rules: 不符合参数要求")

    # 校验 change_rules (变更规则)
    change_rules = data.get("change_rules")
    if change_rules is None or change_rules == "":
        messages.append("change_rules: 参数未解析")
    elif change_rules == "需人工确认":
        messages.append("change_rules: 需人工确认")
    elif not isinstance(change_rules, str):
        messages.append("change_rules: 不符合参数要求")

    # 校验 luggage_amount (行李额)
    luggage_amount = data.get("luggage_amount")
    if luggage_amount is not None and luggage_amount != "":
        if luggage_amount == "需人工确认":
            messages.append("luggage_amount: 需人工确认")
        elif not isinstance(luggage_amount, str) or not re.match(r'^\d+\+\d+$', luggage_amount):
            messages.append("luggage_amount: 不符合参数要求")
    else:
        messages.append("luggage_amount: 参数未解析")

    # 校验 realname_limit (实名限制)
    realname_limit = data.get("realname_limit")
    if realname_limit is None or realname_limit == "":
        messages.append("realname_limit: 参数未解析")
    elif realname_limit == "需人工确认":
        messages.append("realname_limit: 需人工确认")
    elif not isinstance(realname_limit, bool):
        messages.append("realname_limit: 不符合参数要求")

    # 校验 purchase_limit (购买上限)
    purchase_limit = data.get("purchase_limit")
    if purchase_limit is None or purchase_limit == "":
        messages.append("purchase_limit: 参数未解析")
    elif purchase_limit == "需人工确认":
        messages.append("purchase_limit: 需人工确认")
    else:
        try:
            int(purchase_limit)
        except (ValueError, TypeError):
            messages.append("purchase_limit: 不符合参数要求")

    # 校验 use_together (统一兑换)
    use_together = data.get("use_together")
    if use_together is None or use_together == "":
        messages.append("use_together: 参数未解析")
    elif use_together == "需人工确认":
        messages.append("use_together: 需人工确认")
    elif not isinstance(use_together, bool):
        messages.append("use_together: 不符合参数要求")

    return messages

def validate_partial_fields(data: Dict[str, Any]) -> List[str]:
    """
    校验部分字段的函数（只校验od和product_price）

    Args:
        data: 待校验的数据字典

    Returns:
        List[str]: 校验错误信息列表
    """
    messages = []

    # 校验 od (活动航线)
    od = data.get("od")
    if od is None or od == "":
        messages.append("od: 参数未解析")
    elif od == "需人工确认":
        messages.append("od: 需人工确认")
    else:
        # 处理字符串或数组
        if isinstance(od, str):
            od_list = [od]
        elif isinstance(od, list):
            od_list = od
        else:
            messages.append("od: 不符合参数要求")
            od_list = []

        # 校验每个航线格式
        for route in od_list:
            if not isinstance(route, str) or not re.match(r'^[A-Z]{3}-[A-Z]{3}$', route):
                messages.append("od: 不符合参数要求")
                break

    # 校验 product_price (产品价格)
    product_price = data.get("product_price")
    if product_price is None or product_price == "":
        messages.append("product_price: 参数未解析")
    elif product_price == "需人工确认":
        messages.append("product_price: 需人工确认")
    else:
        try:
            float(product_price)
        except (ValueError, TypeError):
            messages.append("product_price: 不符合参数要求")

    return messages


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON
    6. 包含转义字符的JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None
    """
    if not text or not isinstance(text, str):
        return None

    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*([\s\S]*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                # 处理转义字符并尝试解析
                return json.loads(content)
            except json.JSONDecodeError:
                # 如果简单解析失败，尝试进一步清理内容
                cleaned_content = re.sub(r'[\r\n\t]+', ' ', content)  # 替换所有换行和制表符为空格
                cleaned_content = re.sub(r'\s{2,}', ' ', cleaned_content)  # 压缩多个空格
                cleaned_content = cleaned_content.replace('\\n', ' ')  # 替换所有\n为空格
                cleaned_content = re.sub(r'\\(.)', r'\1', cleaned_content)  # 去除转义字符

                try:
                    return json.loads(cleaned_content)
                except json.JSONDecodeError:
                    continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text, re.DOTALL)

        for match in matches:
            try:
                content = match.group(0)
                return json.loads(content)
            except json.JSONDecodeError:
                # 尝试清理内容
                cleaned_content = re.sub(r'[\r\n\t]+', ' ', content)  # 替换所有换行和制表符为空格
                cleaned_content = re.sub(r'\s{2,}', ' ', cleaned_content)  # 压缩多个空格
                try:
                    return json.loads(cleaned_content)
                except json.JSONDecodeError:
                    continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记和注释
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^\s*//.*$", "", cleaned_text, flags=re.MULTILINE)  # 移除JavaScript风格注释
        cleaned_text = re.sub(r"^\s*#.*$", "", cleaned_text, flags=re.MULTILINE)   # 移除Python风格注释

        # 移除空行并整理格式
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)
        cleaned_text = re.sub(r'[\r\n\t]+', ' ', cleaned_text)  # 替换所有换行和制表符为空格
        cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)     # 压缩多个空格

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        # 5. 最后尝试修复可能的格式问题
        # 尝试找到并修复无效的控制字符和错误的转义
        try:
            # 移除控制字符
            control_char_pattern = r'[\x00-\x1F\x7F]'
            sanitized_text = re.sub(control_char_pattern, '', text)

            # 尝试修复常见的JSON格式错误
            sanitized_text = sanitized_text.replace("'", '"')  # 将单引号替换为双引号
            sanitized_text = re.sub(r',\s*([}\]])', r'\1', sanitized_text)  # 移除JSON对象或数组末尾的逗号

            # 尝试重新提取和解析JSON
            json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
            matches = re.finditer(json_pattern, sanitized_text, re.DOTALL)

            for match in matches:
                try:
                    return json.loads(match.group(0))
                except json.JSONDecodeError:
                    continue
        except Exception:
            pass

        return None

    except Exception as e:
        print(f"JSON提取错误: {str(e)}")
        return None


def try_parse_json(text: str) -> Optional[Union[dict, list]]:
    """
    尝试解析JSON字符串，支持处理转义字符

    Args:
        text (str): 要解析的JSON字符串

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None
    """
    if not isinstance(text, str):
        return None

    if not text.strip():
        return None

    # 首先尝试直接解析
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    # 清理文本，处理常见问题
    # 1. 移除前后的引号（如果是被额外引号包围的）
    cleaned_text = text.strip()
    if (cleaned_text.startswith('"') and cleaned_text.endswith('"')) or \
       (cleaned_text.startswith("'") and cleaned_text.endswith("'")):
        cleaned_text = cleaned_text[1:-1]

    # 2. 处理常见转义序列
    try:
        # 修复JSON中常见的转义问题
        cleaned_text = cleaned_text.replace('\\"', '"')
        cleaned_text = cleaned_text.replace('\\\\', '\\')
        cleaned_text = cleaned_text.replace('\\/', '/')
        cleaned_text = cleaned_text.replace('\\n', '\n')
        cleaned_text = cleaned_text.replace('\\r', '\r')
        cleaned_text = cleaned_text.replace('\\t', '\t')

        # 处理换行和多余空白
        cleaned_text = re.sub(r'[\r\n\t]+', ' ', cleaned_text)  # 替换换行制表符为空格
        cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)     # 压缩多个空格

        # 将单引号替换为双引号(常见错误)
        cleaned_text = cleaned_text.replace("'", '"')

        # 移除尾部逗号(常见格式错误)
        cleaned_text = re.sub(r',\s*([}\]])', r'\1', cleaned_text)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            # 如果仍然失败，尝试用extract_json_from_text函数
            return extract_json_from_text(text)
    except Exception:
        # 如果处理过程出错，尝试用extract_json_from_text作为后备方案
        return extract_json_from_text(text)

def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。
    支持处理转义字符的JSON字符串。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default

    if isinstance(text, (dict, list, tuple)):
        return text

    if isinstance(text, str):
        # 1. 尝试使用改进后的try_parse_json函数
        result = try_parse_json(text)
        if result is not None:
            return result

        # 2. 如果上面失败，尝试extract_json_from_text
        result = extract_json_from_text(text)
        if result is not None:
            return result

        # 3. 如果都失败了，尝试一些额外的处理
        # 处理多行文本和可能的注释
        cleaned_text = re.sub(r'^\s*//.*$', '', text, flags=re.MULTILINE)  # 移除JS风格注释
        cleaned_text = re.sub(r'^\s*#.*$', '', cleaned_text, flags=re.MULTILINE)  # 移除Python风格注释
        cleaned_text = re.sub(r'/\*[\s\S]*?\*/', '', cleaned_text)  # 移除C风格块注释

        # 尝试提取可能的JSON部分
        json_pattern = r'(\{[\s\S]*\}|\[[\s\S]*\])'
        matches = re.finditer(json_pattern, cleaned_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

    # 所有尝试失败，返回默认值
    return default


def extract_json_from_codeblock(data_str: str) -> str:
    """
    从代码块中提取JSON字符串

    :param data_str: 包含代码块的字符串
    :return: 提取出的JSON字符串
    """
    if not data_str or not isinstance(data_str, str):
        return data_str

    # 1. 查找```json后的内容
    json_start = data_str.find("```json")
    if json_start != -1:
        # 跳过```json标记
        json_start += 7

        # 找到下一个```标记
        json_end = data_str.find("```", json_start)
        if json_end != -1:
            # 返回```json和下一个```之间的内容
            return data_str[json_start:json_end].strip()
        else:
            # 没有找到结束标记，返回剩余字符串
            return data_str[json_start:].strip()

    # 2. 查找```后的内容 (可能是其他语言或无语言标记的代码块)
    code_block_pattern = r"```(?:\w+)?\s*([\s\S]*?)\s*```"
    matches = re.finditer(code_block_pattern, data_str, re.DOTALL)

    for match in matches:
        content = match.group(1).strip()
        # 尝试看是否是有效的JSON
        try:
            json.loads(content)
            return content  # 如果是有效JSON，直接返回
        except json.JSONDecodeError:
            continue  # 不是有效JSON，检查下一个匹配

    # 3. 如果没有找到代码块或代码块中没有有效JSON，检查是否整个字符串是JSON
    try:
        json.loads(data_str)
        return data_str  # 整个字符串是有效的JSON
    except json.JSONDecodeError:
        pass

    # 4. 尝试提取可能的JSON部分
    json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
    matches = re.finditer(json_pattern, data_str, re.DOTALL)

    for match in matches:
        content = match.group(0)
        try:
            json.loads(content)
            return content  # 找到有效的JSON
        except json.JSONDecodeError:
            continue

    # 如果所有尝试都失败，返回原始字符串
    return data_str

def format_date_range(date_str: str) -> str:
    """
    将"YYYY-MM-DD至YYYY-MM-DD"格式的日期转换为"YYYY-MM-DD 00:00:00,YYYY-MM-DD 23:59:59"格式
    如果日期包含"即日起"，则使用当前日期替换

    :param date_str: 原始日期字符串，如"2025-05-01至2025-06-01"或"即日起至2025-06-01"
    :return: 转换后的日期字符串，如"2025-05-01 00:00:00,2025-06-01 23:59:59"
    """
    if not date_str:
        return date_str

    # 处理包含"即日起"的情况
    if "即日起" in date_str:
        # 获取当前日期，格式为YYYY-MM-DD
        today = datetime.now().strftime('%Y-%m-%d')
        # 替换"即日起"为当前日期
        date_str = date_str.replace("即日起", today)

    # 处理包含"至"的情况
    if "至" in date_str:
        try:
            start_date, end_date = date_str.split("至")
            return f"{start_date} 00:00:00,{end_date} 23:59:59"
        except Exception:
            return date_str

    return date_str

def calculate_date_range_days(date_str: str) -> int:
    """
    计算日期范围的天数，如果超过30天则返回30

    :param date_str: 日期范围字符串，格式如"2025-05-01至2025-06-01"或"即日起至2025-06-01"
    :return: 日期范围的天数，上限为30
    """
    if not date_str:
        return 30  # 默认返回最大值

    # 处理包含"即日起"的情况
    if "即日起" in date_str:
        # 获取当前日期
        today = datetime.now().date()
        # 替换"即日起"为当前日期
        date_str = date_str.replace("即日起", today.strftime('%Y-%m-%d'))

    # 处理包含"至"的情况
    if "至" in date_str:
        try:
            start_date_str, end_date_str = date_str.split("至")
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()

            # 计算日期差
            delta = (end_date - start_date).days + 1  # 包含起始日期

            # 如果超过30天，返回30
            return min(delta, 30)
        except Exception:
            return 30  # 解析失败时返回最大值

    return 30  # 无法解析日期范围时返回最大值

def process_ticket_data(ticket_list: List[Dict], param: Dict = None) -> List[Dict]:
    """
    处理机票数据，将所有价格组合到第一条数据的公共字段下

    :param ticket_list: 机票数据列表
    :param param: 额外的参数字典，包含模板信息等
    :return: 处理后的结果列表
    """
    # 如果列表为空，直接返回空列表
    if not ticket_list:
        return []

    # 如果param为None，初始化为空字典
    if param is None:
        param = {}

    # 从第一条票中提取公共属性
    first_ticket = ticket_list[0]
    common_attrs = {k: v for k, v in first_ticket.items() if k not in ["od", "od-origin", "product_price", "price_deduction"]}

    #增加uuid
    common_attrs["uuid"] = str(uuid.uuid4())

    # 1. valid_date: 取use_date的结束日期
    use_date = first_ticket.get("use_date", "")
    # 确保use_date不为None且为字符串类型
    if use_date is None:
        use_date = ""
    elif not isinstance(use_date, str):
        use_date = str(use_date) if use_date else ""

    if use_date and "至" in use_date:
        date_parts = use_date.split("至")
        if len(date_parts) >= 2 and date_parts[1]:
            common_attrs["valid_date"] = date_parts[1]
        else:
            common_attrs["valid_date"] = use_date
    else:
        common_attrs["valid_date"] = use_date

    # 2 & 3. age_start和age_end: 从age_limit解析
    age_limit = first_ticket.get("age_limit")
    # 确保age_limit不为None且为字符串类型
    if age_limit is None:
        age_limit = ""
    elif not isinstance(age_limit, str):
        age_limit = str(age_limit) if age_limit else ""

    if age_limit and "-" in age_limit:
        age_parts = age_limit.split("-")
        if len(age_parts) >= 2:
            common_attrs["age_start"] = age_parts[0] if age_parts[0] else "12"
            common_attrs["age_end"] = age_parts[1] if age_parts[1] else "200"
        else:
            common_attrs["age_start"] = "12"  # 默认值
            common_attrs["age_end"] = "200"   # 默认值
    else:
        common_attrs["age_start"] = "12"  # 默认值
        common_attrs["age_end"] = "200"   # 默认值

    # 4. activityDomain: 根据airline_code判断
    airline_code = first_ticket.get("airline_code", "")
    carrier = first_ticket.get("carrier", "")
    common_attrs["activityDomain"] = "" if airline_code == "深圳航空" or carrier == "ZH" else "xep.trade.qunar.com"

    # 5. 从param中获取模板信息
    # 提取carrier和产品类型信息
    carrier = first_ticket.get("carrier", "")
    product_type = first_ticket.get("product_type", "")
    # 处理product_type为数组或集合的情况
    if isinstance(product_type, (list, set)):
        product_type = product_type[0] if product_type else ""
    # 处理product_type为None的情况
    if product_type is None:
        product_type = ""

    # 获取flight_type和usage_times
    flight_type = first_ticket.get("flight_type", [])
    if isinstance(flight_type, list) and flight_type:
        flight_type = flight_type[0]
    else:
        flight_type =  first_ticket.get("flight_type", "")

    usage_times = first_ticket.get("usage_times", "")

    # 从param中获取template参数
    template_param = param.get("template", "")
    # 确保template_param不为None且为字符串类型
    if template_param is None:
        template_param = ""
    elif not isinstance(template_param, str):
        template_param = str(template_param) if template_param else ""

    if template_param:
        # URL解码template_param
        import urllib.parse
        template_param = urllib.parse.unquote(template_param)

        # 解析template_param中的特殊
        template_param = template_param.replace("~~*~~,", "~~*~~")

    # 初始化模板值
    mall_template = ""
    activity_template = ""
    package_ticket_template = ""

    # 解析template参数
    if template_param:
        # 按~~*~~分割不同的模板组
        template_groups = template_param.split("~~*~~")

        for group in template_groups:
            if not group.strip():
                continue

            # 解析每个模板组的属性
            template_attrs = {}
            attrs = group.split("#*#")

            for attr in attrs:
                # 确保attr不为None且为字符串类型
                if attr is None:
                    continue
                elif not isinstance(attr, str):
                    attr = str(attr) if attr else ""

                if attr and ":" in attr:
                    attr_parts = attr.split(":", 1)
                    if len(attr_parts) >= 2:
                        key, value = attr_parts[0], attr_parts[1]
                        template_attrs[key.strip()] = value.strip()

            # 检查是否匹配当前票的flight_type和usage_times
            if (template_attrs.get("次卡类型", "") == flight_type and
                template_attrs.get("兑换次数", "") == usage_times):
                # 找到匹配的模板，提取模板值
                mall_template = template_attrs.get("超商模板", "")
                activity_template = template_attrs.get("营销模板", "")
                package_ticket_template = template_attrs.get("套票模板", "")
                break

    # 更新公共属性
    common_attrs.update({
        "mallTemplate": mall_template,
        "activityTemplate": activity_template,
        "packageTicketTemplate": package_ticket_template
    })

    # 创建价格组列表
    price_group = []
    price_list = []  # 用于收集所有价格，生成priceLevel字段

    for ticket in ticket_list:
        # 确保ticket不为None
        if ticket is None:
            ticket = {}

        # 安全获取od字段
        ticket_od = ticket.get("od", [])
        if ticket_od is None:
            ticket_od = []
        elif not isinstance(ticket_od, list):
            ticket_od = [ticket_od] if ticket_od else []

        common_od = common_attrs.get("od", [])
        if common_od is None:
            common_od = []
        elif not isinstance(common_od, list):
            common_od = [common_od] if common_od else []

        # 安全获取od-origin字段
        ticket_od_origin = ticket.get("od-origin", [])
        if ticket_od_origin is None:
            ticket_od_origin = []
        elif not isinstance(ticket_od_origin, list):
            ticket_od_origin = [ticket_od_origin] if ticket_od_origin else []

        common_od_origin = common_attrs.get("od-origin", [])
        if common_od_origin is None:
            common_od_origin = []
        elif not isinstance(common_od_origin, list):
            common_od_origin = [common_od_origin] if common_od_origin else []

        # 安全构建od_str
        final_od = ticket_od if ticket_od else common_od
        od_str_parts = []
        for od in final_od:
            if od and isinstance(od, str):
                od_str_parts.append(od.replace("-", "_"))

        price_info = {
            "od": final_od,
            "od_origin": ticket_od_origin if ticket_od_origin else common_od_origin,
            "od_str": ",".join(od_str_parts),
            "product_price": ticket.get("product_price", ""),
            "price_deduction": ticket.get("price_deduction", ""),
            "uuid": common_attrs.get("uuid", ""),
            "cabin": ticket.get("cabin", "") if ticket.get("cabin", "") else common_attrs.get("cabin", ""),
            "travel_class": ticket.get("travel_class", "") if ticket.get("travel_class", "") else common_attrs.get("travel_class", "")
        }
        price_group.append(price_info)

        # 收集价格信息
        product_price = ticket.get("product_price", "")
        if product_price and product_price not in price_list:
            price_list.append(product_price)

    # 1. 添加priceLevel字段: 价格从小到大排序拼接
    if price_list:
        # 尝试将价格转换为数字进行排序
        try:
            price_list = sorted([int(p) for p in price_list])
            common_attrs["priceLevel"] = "/".join([f"{p}元" for p in price_list])
        except (ValueError, TypeError):
            # 如果价格无法转换为数字，直接拼接
            common_attrs["priceLevel"] = "/".join([f"{p}元" for p in price_list])
    else:
        common_attrs["priceLevel"] = ""

    # 2. 添加depart_date_str字段
    depart_dates = first_ticket.get("depart_date", [])
    beside_times = first_ticket.get("beside_time", [])

    # 处理出发日期范围
    min_start_date = None
    max_end_date = None

    for date_range in depart_dates:
        # 确保date_range不为None且为字符串类型
        if date_range is None:
            continue
        elif not isinstance(date_range, str):
            date_range = str(date_range) if date_range else ""

        if date_range and "至" in date_range:
            date_parts = date_range.split("至")
            if len(date_parts) >= 2:
                start_date, end_date = date_parts[0], date_parts[1]

                # 更新最小开始日期和最大结束日期
                if min_start_date is None or start_date < min_start_date:
                    min_start_date = start_date

                if max_end_date is None or end_date > max_end_date:
                    max_end_date = end_date

    # 处理除外日期
    except_dates = []
    if beside_times and isinstance(beside_times, list):
        for beside_time in beside_times:
            # 确保beside_time不为None且为字符串类型
            if beside_time is None:
                continue
            elif not isinstance(beside_time, str):
                beside_time = str(beside_time) if beside_time else ""

            if beside_time:
                if "至" in beside_time:
                    except_dates.append(beside_time)
                else:
                    except_dates.append(beside_time)

    # 格式化日期: 将YYYY-MM-DD转换为YYYY年MM月DD日
    def format_date(date_str):
        if not date_str or not isinstance(date_str, str):
            return ""

        try:
            parts = date_str.split("-")
            if len(parts) == 3:
                return f"{parts[0]}年{parts[1]}月{parts[2]}日"
            return date_str
        except:
            return date_str

    # 构建最终日期字符串
    date_str = ""
    if min_start_date and max_end_date:
        date_str = f"{format_date(min_start_date)}-{format_date(max_end_date)}"

        # 添加除外日期（如果有）
        if except_dates:
            formatted_except_dates = []

            for date in except_dates:
                # 确保date不为None且为字符串类型
                if date is None:
                    continue
                elif not isinstance(date, str):
                    date = str(date) if date else ""

                if date and "至" in date:
                    date_parts = date.split("至")
                    if len(date_parts) >= 2:
                        start, end = date_parts[0], date_parts[1]
                        formatted_except_dates.append(f"{format_date(start)}-{format_date(end)}")
                    else:
                        formatted_except_dates.append(format_date(date))
                else:
                    formatted_except_dates.append(format_date(date))

            # 确保formatted_except_dates中的元素都是字符串
            str_except_dates = [str(date) for date in formatted_except_dates if date is not None]
            except_str = "、".join(str_except_dates)
            date_str += f"（{except_str}除外）"

    common_attrs["depart_date_str"] = date_str

    # 添加otherInterestsCardMsg字段，替换模板中的占位符

    # 对于往返或中转航程，需要添加额外规则
    use_together = common_attrs.get("use_together", "")
    round_trip_rule = ""
    if use_together:
        round_trip_rule = "7）本产品机票需一同兑换，且需要按顺序使用，不得跳程使用；<br/>"

    # 从common_attrs中获取占位符对应的值，如果不存在则使用空字符串
    field_values = {
        "product_name": common_attrs.get("product_name", ""),
        "priceLevel": common_attrs.get("priceLevel", ""),
        "usage_times": common_attrs.get("usage_times", ""),
        "travel_class": common_attrs.get("travel_class", ""),
        "flight_type": common_attrs.get("flight_type", ""),
        "airline_code": common_attrs.get("airline_code", ""),
        "luggage_amount": common_attrs.get("luggage_amount", "") or "以航司公布规定为准",
        "sale_date": common_attrs.get("sale_date", ""),
        "purchase_limit": common_attrs.get("purchase_limit", ""),
        "use_date": common_attrs.get("use_date", ""),
        "depart_date_str": common_attrs.get("depart_date_str", ""),
        "sign_and_transfer_rules": common_attrs.get("sign_and_transfer_rules", ""),
        "refund_rules": common_attrs.get("refund_rules", ""),
        "change_rules": common_attrs.get("change_rules", ""),
        "round_trip_rule": round_trip_rule
    }

    # 替换模板中的占位符
    message = INTERESTS_CARD_TEMPLATE.format(**field_values)
    common_attrs["otherInterestsCardMsg"] = message

    # 3. 添加depart_date_all字段: 格式为YYYYMMDD:YYYYMMDD
    if min_start_date and max_end_date and isinstance(min_start_date, str) and isinstance(max_end_date, str):
        min_date_no_dash = min_start_date.replace("-", "")
        max_date_no_dash = max_end_date.replace("-", "")
        common_attrs["depart_date_all"] = f"{min_date_no_dash}:{max_date_no_dash}"
    else:
        common_attrs["depart_date_all"] = ""

    # 4. 修改sale_date和use_date格式
    if "sale_date" in common_attrs:
        common_attrs["sale_date"] = format_date_range(common_attrs["sale_date"])

    if "use_date" in common_attrs:
        common_attrs["use_date"] = format_date_range(common_attrs["use_date"])

    # 5. 计算营销总金额
    # 获取营销时间区间（天数）
    marketing_days = calculate_date_range_days(first_ticket.get("sale_date", ""))

    # 确定兑换率
    conversion_rate = 0.1  # 默认为经济舱10%
    travel_class = common_attrs.get("travel_class", "")
    flight_type = common_attrs.get("flight_type", "")

    if travel_class == "公务舱":
        conversion_rate = 0.01  # 公务舱1%
    elif flight_type != "单程":
        conversion_rate = 0.05  # 往返中转5%

    # 计算价格总和
    total_price_sum = 0
    for price_info in price_group:
        # 确保price_info不为None
        if price_info is None:
            continue

        product_price = price_info.get("product_price", "0")
        # 确保product_price不为None
        if product_price is None:
            product_price = "0"

        try:
            price = int(product_price)
            # 获取该价格对应的航线数量
            od_list = price_info.get("od", [])
            # 确保od_list不为None且为列表
            if od_list is None:
                od_list = []
            elif not isinstance(od_list, list):
                od_list = [od_list] if od_list else []

            od_count = len(od_list)
            if od_count == 0:
                od_count = 1  # 如果没有航线，默认为1

            # 累加价格*航线数
            total_price_sum += price * od_count
        except (ValueError, TypeError):
            continue

    # 计算营销总金额
    marketing_total_price = total_price_sum * marketing_days * conversion_rate * 0.89
    # 四舍五入到小数点后1位
    common_attrs["marketingTotalPrice"] = round(marketing_total_price, 1)

    # 创建结果对象
    result = {
        **common_attrs,
        "priceGroup": price_group
    }

    # 返回包含单个对象的列表
    return [result]

def parse_data(data_str: str, param: Dict = None) -> Tuple[Union[List[Dict], None], Dict]:
    """
    解析字符串中的JSON数据并处理成指定格式

    :param data_str: 包含JSON数据的字符串
    :param param: 额外的参数字典，包含模板信息等
    :return: 处理后的数据结构和状态信息
    """
    # 处理空值情况
    if not data_str or not data_str.strip():
        return None, {"status": "error", "message": "Empty input"}

    # 如果param为None，初始化为空字典
    if param is None:
        param = {}

    try:
        # 直接解析JSON字符串
        json_data = safe_json_parse(data_str)

        # 步骤一：校验JSON格式
        if json_data is None:
            return None, {"status": "error", "message": "转化json有误"}

        # 步骤二：参数校验（不阻塞后续流程）
        validation_messages = []

        # 校验JSON数据中的参数
        if isinstance(json_data, dict):
            # 检查是否有single字段
            if "single" in json_data and isinstance(json_data["single"], list):
                for i, item in enumerate(json_data["single"]):
                    if i == 0:
                        # 第0个元素进行完整校验
                        item_messages = validate_all_fields(item)
                    else:
                        # 第1个及以后的元素只校验od和product_price
                        item_messages = validate_partial_fields(item)
                    for msg in item_messages:
                        validation_messages.append(f"single[{i}] - {msg}")

            # 检查是否有round字段
            if "round" in json_data and isinstance(json_data["round"], list):
                for i, item in enumerate(json_data["round"]):
                    if i == 0:
                        # 第0个元素进行完整校验
                        item_messages = validate_all_fields(item)
                    else:
                        # 第1个及以后的元素只校验od和product_price
                        item_messages = validate_partial_fields(item)
                    for msg in item_messages:
                        validation_messages.append(f"round[{i}] - {msg}")

            # 如果没有single、business或round字段，直接校验整个对象
            if "single" not in json_data and "business" not in json_data and "round" not in json_data:
                validation_messages = validate_all_fields(json_data)

        # 初始化结果列表
        result = []

        # 循环处理所有类型的数据，而不是硬编码"single"和"round"
          # 处理单程数据
        if "single" in json_data and isinstance(json_data["single"], list):
            single_data = process_ticket_data(json_data["single"])
            result.extend(single_data)

        # 处理往返数据
        if "round" in json_data and isinstance(json_data["round"], list):
            round_data = process_ticket_data(json_data["round"])
            result.extend(round_data)

        # 步骤三：如果有校验信息，将其包含在返回结果中
        if validation_messages:
            return result, {"status": "success", "validation_messages": validation_messages}
        else:
            return result, {"status": "success"}

    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}

def main(param: dict) -> dict:
    """
    主处理函数，解析输入字符串并返回处理结果

    :param param: 包含content(JSON数据的原始字符串)和template(模板信息)的参数字典
    :return: 处理结果
    """
    try:
        raw_str = param.get("content")
        result, status = parse_data(raw_str, param)
        if status["status"] != "success":
            return {
                "data": None,
                "priceGroup": [],
                "message": status["message"],
                "success": False
            }

        # 提取所有priceGroup数据到顶层
        all_price_groups = []
        for item in result:
            if "priceGroup" in item:
                all_price_groups.extend(item["priceGroup"])
                # 移除内部的priceGroup
                item.pop("priceGroup", None)

        # 检查是否有校验信息
        validation_messages = status.get("validation_messages", [])
        if validation_messages:
            message = f"解析成功，校验信息: {'; '.join(validation_messages)}"
        else:
            message = "解析成功"

        return {
            "data": result,
            "priceGroup": all_price_groups,
            "message": message,
            "success": True,
            "validation_messages": validation_messages
        }
    except Exception as e:
        return {
            "data": None,
            "priceGroup": [],
            "message": str(e),
            "success": False
        }

def test():
    """
    测试函数，使用示例数据测试解析逻辑
    """
    # 测试数据
    test_data = '''
```json
{
  "single": [
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "BAV-TYN",
        "DLC-TNA",
        "HET-INC",
        "HUZ-WUH",
        "NKG-TAO",
        "TAO-HFE",
        "SHE-TYN",
        "SHE-YNT",
        "SJW-YNT",
        "WEH-TNA",
        "WUH-TNA",
        "XIY-TNA",
        "YNT-SJW",
        "YNT-SHE",
        "YNT-TNA"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "200",
      "price_deduction": "200",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "DLC-TAO",
        "DLC-TNA",
        "FOC-TAO",
        "KWE-XMN",
        "HGH-TAO",
        "HFE-TAO",
        "HFE-KWL",
        "HET-TSN",
        "HET-NKG",
        "TNA-WUH",
        "TNA-INC",
        "TNA-LHW",
        "TNA-DLC",
        "TNA-SHA",
        "SWA-CSX",
        "KHG-URC",
        "LHW-URC",
        "MDG-DLC",
        "NKG-KWL",
        "NDG-DLC",
        "TAO-HGH",
        "TAO-TYN",
        "TAO-SHA",
        "TAO-PVG",
        "TAO-SHE",
        "JJN-HSN",
        "JJN-TNA",
        "XMN-XIY",
        "XMN-CSX",
        "XMN-HSN",
        "SHE-TNA",
        "SHE-YNT",
        "SHE-TAO",
        "SHE-CGO",
        "TYN-BAV",
        "TSN-HET",
        "URC-KHG",
        "WUH-TNA",
        "WUH-TAO",
        "WUH-XMN",
        "WUH-YNT",
        "XIY-TNA",
        "YNT-SHE",
        "YNT-SHA",
        "YNT-PVG",
        "YNT-WUH",
        "YNT-PEK",
        "INC-URC",
        "CGQ-TAO",
        "CGQ-YNT",
        "CGQ-TNA",
        "CGO-TAO",
        "CGO-INC",
        "HSN-XMN",
        "HSN-JJN"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "300",
      "price_deduction": "300",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-YNT",
        "TFU-TAO",
        "DLC-TNA",
        "DLC-TAO",
        "DLC-HGH",
        "CAN-TNA",
        "KWE-XIY",
        "KWE-CGO",
        "KWE-WDS",
        "KWL-CGO",
        "KWL-XMN",
        "KWL-HFE",
        "KWL-NKG",
        "HAK-CSX",
        "HGH-TAO",
        "HFE-YNT",
        "HFE-CKG",
        "HET-TNA",
        "HET-TAO",
        "HET-URC",
        "TNA-HET",
        "TNA-DLC",
        "TNA-SHA",
        "KHG-URC",
        "LHW-TAO",
        "MDG-DLC",
        "KHN-TNA",
        "KHN-TAO",
        "KHN-ZUH",
        "NKG-YNT",
        "NKG-TAO",
        "NKG-HET",
        "NNG-WUH",
        "NDG-TAO",
        "TAO-XMN",
        "TAO-HGH",
        "TAO-LHW",
        "TAO-INC",
        "TAO-KHN",
        "TAO-WUH",
        "TAO-CSX",
        "TAO-SHA",
        "TAO-SHE",
        "XMN-SHA",
        "XMN-KWL",
        "XMN-NKG",
        "XMN-HGH",
        "PVG-YNT",
        "SHE-XUZ",
        "SHE-XMN",
        "SJW-URC",
        "TYN-SHE",
        "TYN-KWE",
        "TYN-URC",
        "TYN-TAO",
        "WNZ-ZUH",
        "WUH-YNT",
        "WUH-TAO",
        "WUH-XMN",
        "XIY-TAO",
        "XUZ-SHE",
        "YNT-HFE",
        "YNT-CKG",
        "YNT-NKG",
        "YNT-WUH",
        "YNT-SHA",
        "YNT-PEK",
        "CGQ-TNA",
        "CGO-SHE",
        "CGO-XMN",
        "CKG-TNA",
        "ZUH-CKG"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "400",
      "price_deduction": "400",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-TAO",
        "TFU-TAO",
        "TFU-TNA",
        "CAN-TNA",
        "CAN-TAO",
        "KWE-TYN",
        "KWE-CZX",
        "KWE-TAO",
        "KWL-TAO",
        "KWL-NKG",
        "HRB-TNA",
        "HRB-TAO",
        "HRB-TSN",
        "HAK-KWL",
        "HAK-TNA",
        "HGH-XMN",
        "HGH-DLC",
        "HET-CKG",
        "TNA-CKG",
        "TNA-MIG",
        "TNA-DLC",
        "TNA-URC",
        "JMU-YNT",
        "MIG-LJG",
        "MDG-TAO",
        "KHN-TNA",
        "NKG-SHE",
        "TAO-PEK",
        "TAO-TFU",
        "TAO-CKG",
        "TAO-CAN",
        "TAO-XMN",
        "TAO-CSX",
        "TAO-HET",
        "TAO-XIY",
        "XMN-CGO",
        "XMN-KWE",
        "XMN-TYN",
        "SHA-YNT",
        "SHA-TNA",
        "SHE-XMN",
        "SHE-CGO",
        "HYN-TNA",
        "TYN-XMN",
        "TYN-CGQ",
        "TYN-CKG",
        "TSN-XMN",
        "WNZ-TNA",
        "URC-KHG",
        "URC-INC",
        "XIY-XMN",
        "XIY-KWE",
        "YNT-PEK",
        "INC-TAO",
        "CGQ-TYN",
        "CKG-TAO"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "500",
      "price_deduction": "500",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-ZUH",
        "PEK-TAO",
        "PEK-YNT",
        "TFU-TNA",
        "TFU-XMN",
        "DLC-XMN",
        "DLC-CKG",
        "FOC-PEK",
        "CAN-TNA",
        "CAN-TAO",
        "HRB-TNA",
        "HRB-TAO",
        "HAK-KHN",
        "HAK-HYN",
        "HAK-TNA",
        "HAK-NGB",
        "HAK-TAO",
        "HGH-XMN",
        "HGH-TYN",
        "HET-TNA",
        "HET-XMN",
        "TNA-TFU",
        "TNA-HET",
        "TNA-KWE",
        "TNA-KWL",
        "TNA-URC",
        "JMU-YNT",
        "MIG-TNA",
        "MDG-TAO",
        "NKG-KMG",
        "NNG-TAO",
        "TAO-TFU",
        "TAO-SZX",
        "TAO-XMN",
        "TAO-KWL",
        "TAO-XNN",
        "TAO-ZUH",
        "TAO-XIY",
        "TAO-KWE",
        "XMN-CGO",
        "SHA-XMN",
        "SZX-TAO",
        "SHE-TNA",
        "SHE-URC",
        "TYN-XMN",
        "TYN-HGH",
        "TSN-HRB",
        "URC-TYN",
        "XIY-KRL",
        "XIY-TAO",
        "YNT-CKG",
        "YNT-KWL",
        "ZUH-PEK"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "600",
      "price_deduction": "600",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-XMN",
        "PEK-FOC",
        "TFU-XMN",
        "DLC-CKG",
        "DLC-KWE",
        "FOC-PEK",
        "CAN-YNT",
        "KWE-TNA",
        "KWL-YNT",
        "HAK-TAO",
        "HAK-CGO",
        "HGH-XMN",
        "HET-XMN",
        "TNA-TFU",
        "TNA-URC",
        "KRL-XIY",
        "NKG-URC",
        "NKG-KMG",
        "TAO-TFU",
        "TAO-CKG",
        "TAO-SZX",
        "TAO-URC",
        "TAO-ZUH",
        "XMN-TYN",
        "XMN-HGH",
        "XMN-INC",
        "SZX-TAO",
        "TSN-ZUH",
        "URC-LHW",
        "URC-NKG",
        "XIY-AKU",
        "XNN-TNA",
        "YNT-TFU",
        "YNT-XMN",
        "YNT-JMU",
        "YNT-CAN",
        "CGQ-XMN"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "700",
      "price_deduction": "700",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "HRB-CKG",
        "HGH-URC",
        "TNA-KMG",
        "TNA-LJG",
        "TNA-KHG",
        "TNA-CGQ",
        "TNA-HRB",
        "KRL-CGO",
        "NKG-JMU",
        "NNG-TNA",
        "TAO-NNG",
        "TAO-KWE",
        "TAO-KMG",
        "SYX-TNA",
        "SZX-TNA",
        "SZX-YNT",
        "SHE-KWE",
        "URC-TNA",
        "YNT-SZX",
        "YNT-URC",
        "CGQ-CKG"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "800",
      "price_deduction": "800",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "HAK-SHE",
        "TNA-KHG",
        "SYX-TNA",
        "SZX-TNA",
        "URC-TAO",
        "URC-HGH",
        "URC-SHE",
        "URC-TNA"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "900",
      "price_deduction": "900",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "AKU-XIY",
        "AKU-TAO",
        "HRB-XMN",
        "HRB-NNG",
        "HRB-ZUH",
        "KHG-TNA",
        "TAO-KMG",
        "SYX-CGO",
        "SYX-TAO",
        "XMN-URC",
        "XMN-TFU",
        "WEH-SZX",
        "URC-TNA",
        "URC-XMN",
        "URC-FOC"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "1000",
      "price_deduction": "1000",
      "cabin": "A",
      "travel_class": "经济舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：收取5%手续费；航班起飞前7天（不含）至48小时（含）：收取30%手续费；航班起飞前48小时（不含）至4小时（含）：收取50%手续费；航班起飞前4小时（不含）至航班起飞后：收取60%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件和相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    }
  ],
  "business": [
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "BAV-TYN",
        "PEK-YNT",
        "PEK-ZUH",
        "TFU-TAO",
        "TFU-TNA",
        "DLC-TNA",
        "DLC-TAO",
        "DLC-KWE",
        "DLC-XMN",
        "CAN-TNA",
        "CAN-TAO",
        "KWE-XMN",
        "KWE-XIY",
        "KWL-CGO",
        "KWL-XMN",
        "KWL-HFE",
        "KWL-TAO",
        "KWL-NKG",
        "HRB-TNA",
        "HRB-TAO",
        "HRB-TSN",
        "HAK-KWL",
        "HAK-KHN",
        "HAK-TNA",
        "HAK-CSX",
        "HGH-XMN",
        "HGH-TAO",
        "HFE-CKG",
        "HFE-YNT",
        "HFE-TAO",
        "HFE-KWL",
        "HET-TNA",
        "HET-TSN",
        "HET-INC",
        "HUZ-WUH",
        "TNA-WUH",
        "TNA-INC",
        "TNA-LHW",
        "TNA-DLC",
        "TNA-SHA",
        "SWA-CSX",
        "KHG-URC",
        "LHW-URC",
        "LHW-TAO",
        "MIG-LJG",
        "MDG-DLC",
        "KHN-TNA",
        "KHN-TAO",
        "KHN-ZUH",
        "NKG-YNT",
        "NKG-SHE",
        "NKG-HET",
        "NKG-KWL",
        "NNG-WUH",
        "NDG-DLC",
        "TAO-TFU",
        "TAO-CKG",
        "TAO-HFE",
        "TAO-HGH",
        "TAO-LHW",
        "TAO-INC",
        "TAO-KHN",
        "TAO-CSX",
        "TAO-HET",
        "TAO-WUH",
        "TAO-XIY",
        "TAO-SHA",
        "TAO-PVG",
        "TAO-SHE",
        "JJN-HSN",
        "XMN-HGH",
        "XMN-XIY",
        "XMN-CGO",
        "XMN-KWL",
        "XMN-HSN",
        "PVG-YNT",
        "SZX-TAO",
        "SHE-TAO",
        "SHE-TYN",
        "SHE-TNA",
        "SHE-XMN",
        "SHE-CGO",
        "SHE-XUZ",
        "SHE-YNT",
        "SJW-YNT",
        "SJW-URC",
        "TYN-XMN",
        "TYN-SHE",
        "TYN-KWE",
        "TSN-HET",
        "WEH-TNA",
        "WNZ-ZUH",
        "URC-KHG",
        "WUH-TNA",
        "WUH-TAO",
        "WUH-XMN",
        "WUH-YNT",
        "XIY-KWE",
        "XIY-TNA",
        "XIY-TAO",
        "XUZ-SHE",
        "YNT-TNA",
        "YNT-HFE",
        "YNT-CKG",
        "YNT-NKG",
        "YNT-SHE",
        "YNT-SHA",
        "YNT-PVG",
        "YNT-WUH",
        "YNT-PEK",
        "YNT-SJW",
        "INC-URC",
        "INC-TAO",
        "CGQ-TAO",
        "CGQ-YNT",
        "CGQ-TNA",
        "CGO-INC",
        "CGO-SHE",
        "CGO-TAO",
        "CKG-HFE",
        "CKG-YNT",
        "CKG-TNA",
        "ZUH-PEK",
        "ZUH-CKG"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "800",
      "price_deduction": "800",
      "cabin": "I",
      "travel_class": "公务舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：免费；航班起飞前7天（不含）至48小时（含）：收取10%手续费；航班起飞前48小时（不含）至4小时（含）：收取10%手续费；航班起飞前4小时（不含）至航班起飞后：收取15%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-YNT",
        "PEK-ZUH",
        "TFU-TAO",
        "TFU-TNA",
        "TFU-XMN",
        "DLC-XMN",
        "DLC-HGH",
        "DLC-CKG",
        "DLC-KWE",
        "FOC-PEK",
        "CAN-TNA",
        "CAN-TAO",
        "CAN-YNT",
        "KWE-TNA",
        "KWL-YNT",
        "KWL-TNA",
        "HRB-TNA",
        "HAK-HYN",
        "HAK-TNA",
        "HAK-TAO",
        "HAK-CGO",
        "HGH-DLC",
        "HGH-XMN",
        "HGH-TYN",
        "HET-TNA",
        "HET-XMN",
        "HET-CKG",
        "HET-URC",
        "TNA-CKG",
        "TNA-HET",
        "TNA-KMG",
        "TNA-KWE",
        "TNA-KWL",
        "TNA-LJG",
        "TNA-TFU",
        "TNA-MIG",
        "TNA-URC",
        "JMU-YNT",
        "SWA-TNA",
        "KRL-CGO",
        "MIG-TNA",
        "MDG-TAO",
        "NKG-TAO",
        "NKG-KMG",
        "NNG-TAO",
        "NDG-TAO",
        "TAO-TYN",
        "TAO-WUH",
        "TAO-TFU",
        "TAO-CKG",
        "TAO-SZX",
        "TAO-XMN",
        "TAO-KWL",
        "TAO-XNN",
        "TAO-ZUH",
        "TAO-XIY",
        "TAO-KWE",
        "XMN-CGO",
        "XMN-TYN",
        "XMN-SHA",
        "XMN-NKG",
        "XMN-KWE",
        "XMN-INC",
        "SHA-YNT",
        "SHA-TNA",
        "SHA-XMN",
        "SZX-TNA",
        "SZX-YNT",
        "SZX-TAO",
        "SHE-XMN",
        "SHE-KWE",
        "TYN-XMN",
        "TYN-URC",
        "TYN-CGQ",
        "TYN-CKG",
        "TYN-TAO",
        "TSN-XMN",
        "TSN-HRB",
        "WNZ-TNA",
        "URC-TYN",
        "WUH-TAO",
        "XIY-XMN",
        "XIY-TNA",
        "XIY-KWE",
        "XIY-TAO",
        "XNN-TNA",
        "YNT-TFU",
        "YNT-CKG",
        "YNT-XMN",
        "YNT-JMU",
        "YNT-KWL",
        "YNT-CAN",
        "YNT-SZX",
        "YNT-URC",
        "YNT-PEK",
        "CGQ-TYN",
        "CGQ-CKG",
        "CGO-XMN",
        "CKG-TNA",
        "CKG-TAO",
        "ZUH-PEK"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "1000",
      "price_deduction": "1000",
      "cabin": "I",
      "travel_class": "公务舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：免费；航班起飞前7天（不含）至48小时（含）：收取10%手续费；航班起飞前48小时（不含）至4小时（含）：收取10%手续费；航班起飞前4小时（不含）至航班起飞后：收取15%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "PEK-YNT",
        "TFU-XMN",
        "DLC-CKG",
        "DLC-KWE",
        "CAN-TNA",
        "CAN-TAO",
        "CAN-YNT",
        "KWE-TNA",
        "HRB-CKG",
        "HAK-SHE",
        "HGH-XMN",
        "HET-XMN",
        "TNA-TFU",
        "TNA-CGQ",
        "TNA-KMG",
        "NKG-JMU",
        "NKG-URC",
        "NKG-KMG",
        "TAO-TFU",
        "TAO-NNG",
        "TAO-SZX",
        "TAO-XMN",
        "TAO-URC",
        "TAO-ZUH",
        "TAO-KWE",
        "TAO-KMG",
        "XMN-TYN",
        "XMN-HGH",
        "XMN-KWE",
        "XMN-URC",
        "SZX-TNA",
        "SHE-URC",
        "TYN-XMN",
        "TYN-HGH",
        "URC-INC",
        "XIY-KRL",
        "XIY-AKU",
        "XIY-TAO",
        "YNT-PEK",
        "YNT-URC",
        "CGQ-XMN",
        "CGQ-CKG"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "1200",
      "price_deduction": "1200",
      "cabin": "I",
      "travel_class": "公务舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：免费；航班起飞前7天（不含）至48小时（含）：收取10%手续费；航班起飞前48小时（不含）至4小时（含）：收取10%手续费；航班起飞前4小时（不含）至航班起飞后：收取15%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    },
    {
      "carrier": "SC",
      "airline_code": "山航",
      "od": [
        "AKU-XIY",
        "AKU-TAO",
        "PEK-TAO",
        "PEK-XMN",
        "PEK-FOC",
        "FOC-PEK",
        "HRB-XMN",
        "HRB-NNG",
        "HAK-NGB",
        "HAK-TAO",
        "HGH-URC",
        "TNA-KHG",
        "KRL-XIY",
        "KHG-TNA",
        "NKG-KMG",
        "SYX-CGO",
        "SYX-TAO",
        "SYX-TNA",
        "SHE-HAK",
        "TSN-ZUH",
        "URC-HGH",
        "URC-LHW",
        "URC-TAO",
        "URC-NKG",
        "URC-XMN",
        "URC-FOC",
        "URC-SHE",
        "URC-TNA",
        "YNT-CAN",
        "YNT-SZX",
        "ZUH-TSN"
      ],
      "od_origin": null,
      "flight_type": "单程",
      "usage_times": "1",
      "product_name": "山航单程单次卡",
      "product_show": "山航单程单次卡",
      "product_type": "单程单次卡",
      "product_price": "1400",
      "price_deduction": "1400",
      "cabin": "I",
      "travel_class": "公务舱",
      "sale_date": "2025-05-23至2025-05-30",
      "depart_date": [
        "2025-06-11至2025-06-30"
      ],
      "beside_time": null,
      "use_limit": "D5",
      "use_date": null,
      "refund_time": "2025-06-30",
      "use_code": "山航暖冬畅游1月版国内机票次卡产品",
      "passenger_type": "成人",
      "age_limit": "2-200",
      "young_old": null,
      "sign_and_transfer_rules": "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "refund_rules": "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。",
      "change_rules": "航班起飞前7天之前：免费；航班起飞前7天（不含）至48小时（含）：收取10%手续费；航班起飞前48小时（不含）至4小时（含）：收取10%手续费；航班起飞前4小时（不含）至航班起飞后：收取15%手续费；非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行",
      "luggage_amount": null,
      "realname_limit": true,
      "purchase_limit": "5",
      "use_together": false
    }
  ]
}
```
裁判解析依据:
carrier: 采纳所有 ML 的一致结果 "SC"。依据：文件名称包含“山东航空”，IATA 二字码匹配规则，三个模型均正确识别。
airline_code: 采纳所有 ML 的一致结果 "山航"。依据：文件名称包含“山航”，三个模型均正确识别。
od: 采纳 LLM A 的 od 列表，并补充 LLM B 和 C 中遗漏但原文包含的航线。依据：三个模型都基于航线列表提取，但存在交叉重复和遗漏。根据完整性原则，合并所有模型提取到的有效航线，并进行去重和格式修正。经济舱和公务舱的航线列表分开提取，按价格档位分组，保证同一价格档位下的航线列表的准确性。
od_origin: 采纳所有 ML 的一致结果 null。依据：原文未提及原始航线信息，三个模型判断一致且符合规则。
flight_type: 采纳所有 ML 的一致结果 "单程"。依据：虽然原文未明确标注航程类型，但所有航线均为城市对，且根据业务知识“次卡”通常为单程，三个模型判断一致且依据合理。
usage_times: 采纳所有 ML 一致的推断结果 "1"。依据：原文未明确说明可使用次数，但“每张次卡仅可添加一个受益人”以及产品名称“单次卡”推断为单次，三个模型判断一致且依据合理。
product_name: 采纳 LLM A 的结果 "山航单程单次卡"。依据：LLM A 和 B 均输出此名称且依据合理。LLM C 增加了舱等信息，与原始提取逻辑中“product_name 的拼接规则”（航司简称 + 航程类型 + 次数 + 卡）不完全一致，且“product_show”字段已经体现舱等区分。
product_show: 采纳 LLM A 和 B 的结果 "山航单程单次卡"。依据：原文未提及权益卡展示文案，按照规则，与产品名称 product_name 保持一致。LLM C 的结果与 product_name 不一致。
product_type: 采纳所有 ML 的一致结果 "单程单次卡"。依据：结合航程类型和使用次数的推断，产品类型为单程单次卡，三个模型判断一致且依据合理。
product_price: 采纳三个 ML 提取的价格信息。依据：三个模型都从原文中提取了价格信息，并按照经济舱和公务舱的价格档位进行了分组，提取结果一致且依据明确。
price_deduction: 采纳三个 ML 一致的结果，其值等于 product_price。依据：根据price_deduction=product_price/usage_times，由于 usage_times=1，所以 price_deduction 等于 product_price，三个模型计算一致且依据合理。
cabin: 采纳三个 ML 一致的结果。依据：原文“适用舱位 ：经济舱A舱，公务舱I舱”明确给出了舱位代码，三个模型提取一致且依据准确。
travel_class: 采纳三个 ML 一致的结果 "经济舱" 和 "公务舱"。依据：原文明确区分了经济舱和公务舱及其不同的价格和规则，三个模型提取一致且依据准确。
sale_date: 采纳三个 ML 一致的结果 "2025-05-23至2025-05-30"。依据：原文“购买日期 ：2025年 05月23日- 2025年05月30日”有明确的日期范围，三个模型提取一致且依据准确。
depart_date: 采纳三个 ML 一致的结果 ["2025-06-11至2025-06-30"]。依据：原文“旅行日期 ：2025年 6月11日-6月30日”有明确的日期范围，三个模型提取一致且依据准确。
beside_time: 采纳 LLM A 和 C 的一致结果 null。依据：原文未明确提及旅行除外日期。LLM B 输出空数组，但 null 更符合未提及的场景。
use_limit: 采纳所有 ML 一致的结果 "D5"。依据：原文“需提前5天出票”明确了兑换限制，三个模型提取一致且依据准确。
use_date: 采纳 LLM A 的结果 null。依据：原文未明确给出兑换日期的具体时间范围。LLM B 和 C 推断了兑换开始时间或与旅行日期一致，但规则要求严格基于原文提取，原文未给出明确范围，故 null 更准确。
refund_time: 采纳 LLM B 和 C 的一致结果 "2025-06-30"。依据：原文“购买成功但超过兑换日期未兑换，产品自动失效并全额退款”结合旅行日期截止日推断最晚退款日期为旅行日期截止日。LLM A 未能推断出具体日期。
use_code: 采纳 LLM A 的结果 "山航暖冬畅游1月版国内机票次卡产品"。依据：原文明确提及了产品标识名称。LLM B 和 C 使用了 product_name 作为 use_code，与原文不符。
passenger_type: 采纳三个 ML 推断一致的结果 "成人"。依据：原文“适用旅客： 成人/儿童”，但提取规则可能要求默认提取成人，或者业务知识推断次卡通常针对成人。采纳一致推断结果。
age_limit: 采纳所有 ML 一致的结果 "2-200"。依据：原文“支持2岁以上儿童与成人购买”，推断最小年龄为2岁，最大年龄未提及故默认200，三个模型提取一致且依据合理。
young_old: 采纳所有 ML 一致的结果 null。依据：原文未提及青年或老年，三个模型判断一致且符合规则。
sign_and_transfer_rules: 采纳 LLM A 的结果 "非自愿按照《山东航空股份有限公司旅客行李运输总条件》与《山东航空股份有限公司国内票价使用条件》相关条款执行"。依据：LLM A 的依据直接引用了原文中非自愿情况的处理规则，更完整准确。LLM B 输出“需人工确认”依据不足。LLM C 增加了“不得自愿签转”的推断，但原文未明确禁止自愿签转，且退改规则中只列举了自愿变更和自愿退票，未提及自愿签转手续费，可能意味着不允许自愿签转，但直接引用非自愿规则更符合原文。
refund_rules: 整合 LLM A、B 和 C 的有效信息。采纳结果 "未兑换可全额退款；超过兑换日期未兑换，产品自动失效并全额退款；兑换机票后不允许申请产品退款；非自愿情况，可办理机票非自愿全退。"。依据：LLM A 和 B 对未兑换和兑换后非自愿退款规则的提取更完整且一致。LLM C 遗漏了“超过兑换日期未兑换，产品自动失效并全额退款”的关键信息，且“已兑换仅退机建燃油费”是自愿退票规则而非产品退款规则中的非自愿退票。因此，整合 LLM A 和 B 的完整且准确的规则描述。
change_rules: 采纳 LLM A 的经济舱变更规则和 LLM A 的公务舱变更规则，并结合 LLM A 提取的非自愿变更规则。依据：LLM A 完整提取了经济舱和公务舱的自愿变更手续费标准，并将非自愿变更规则（与签转规则一致）一并提取。LLM B 对自愿变更规则的描述不够详细，非自愿变更规则输出“需人工确认”。LLM C 提取了自愿变更规则，但格式为列表，且非自愿变更规则只提到“按保障细则”，不如 LLM A 直接引用具体条款描述准确。
luggage_amount: 采纳所有 ML 一致的结果 null。依据：原文未提及行李额信息，三个模型判断一致且符合规则。
realname_limit: 采纳所有 ML 一致的结果 true。依据：原文明确要求录入乘机人信息且不得更换，符合实名制要求，三个模型提取一致且依据准确。
purchase_limit: 采纳 LLM A 和 C 的一致推断结果 "5"。依据：原文未明确提及购买上限，推断为默认值 5，符合规则。LLM B 输出“人工确认”依据不足。
use_together: 采纳所有 ML 一致的推断结果 false。依据：航程类型为单程，无需统一兑换，三个模型判断一致且符合规则。
```
'''

    # 调用主函数解析数据
    param = {
        "content": test_data,
        "template": "%E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A1%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%E5%8D%95%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E, %E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A2%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%E4%B8%A4%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E, %E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A%E5%A4%9A%E6%AC%A1%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%8D%95%E7%A8%8B%E5%A4%9A%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E, %E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A1%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%E5%8D%95%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E, %E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A2%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%E4%B8%A4%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E, %E5%85%91%E6%8D%A2%E6%AC%A1%E6%95%B0%3A%E5%A4%9A%E6%AC%A1%23%2A%23%E6%AC%A1%E5%8D%A1%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%23%2A%23%E7%B1%BB%E5%9E%8B%3A%E5%BE%80%E8%BF%94%E5%A4%9A%E6%AC%A1%23%2A%23%E8%B6%85%E5%95%86%E6%A8%A1%E6%9D%BF%3A002056156618%23%2A%23%E8%90%A5%E9%94%80%E6%A8%A1%E6%9D%BF%3A90075776%23%2A%23%E5%A5%97%E7%A5%A8%E6%A8%A1%E6%9D%BF%3A90075777%7E%7E%2A%7E%7E"
    }
    print(json.dumps(param, ensure_ascii=False, indent=2))
    result = main(param)

    # 打印结果
    print("解析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 示例：访问解析后的数据结构

    if result["success"] and result["data"]:
        print("\n解析后的数据条目数:", len(result["data"]))
        print(f"价格组数量: {len(result['priceGroup'])}")
        #价格组按照uuid分组
        price_group_dict = {}
        for item in result["priceGroup"]:
            price_group_dict[item["uuid"]] = item
        print(f"价格分组数: {len(price_group_dict)}")

        for i, item in enumerate(result["data"]):
            print(f"\n条目 {i+1}:")
            print(f"航司: {item.get('carrier')} ({item.get('airline_code')})")
            print(f"产品: {item.get('product_name')}")



if __name__ == "__main__":
        test()
