import requests
import json
import time
import uuid
from typing import Dict, List, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def search_flow(
    orderNo: str = "",
    flowStatus: str = "",
    date: str = "",
    appCode: str = "",
    appToken: str = ""
) -> Tuple[List[Dict[str, Any]], int, List[int]]:
    """
    搜索工单流
    Args:
        orderNo: 订单号
        flowStatus: 工单状态
        date: 日期，格式为YYYY-MM-DD
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        Tuple[List[Dict[str, Any]], int, List[int]]: 工单列表, 总页数, 页数数组
    """
    base_url = "https://hcallcenter.corp.qunar.com/callcenter/flow/search/advanced"
    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    
    # 处理日期，默认为当前日期
    if not date:
        start_time =""
        end_time=""
    else:
        current_date = date
        # 设置开始时间为当天的0点，结束时间为当天的23:59:59
        start_time = f"{current_date} 00:00:00"
        end_time = f"{current_date} 23:59:59"
    
         # URL encode the time strings
        start_time = requests.utils.quote(start_time)
        end_time = requests.utils.quote(end_time)

    all_results = []
    page_num = 1
    page_size = 10
    total_pages = 0
    
    # 先查询第一页获取总数
    proxyData = {
        "method": "get",
        "url": f"{base_url}?bizLine=FLIGHT&flowNo=&orderNo={orderNo}&contactNumber=&agentName=&currentHandlerName=&channel=&createStartTime={start_time}&createEndTime={end_time}&createTimeDesc=&closedTimeDesc=&flowNodeIdList=&flowStatus={flowStatus}&problem1Id=Mlhshda9&problem2Id=BJDP5GU4&closerName=&userIdentity=&pageNum={page_num}&pageSize={page_size}",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        if "error" in result:
            return all_results, 0, []

        response_data = json.loads(result.get("data", "{}"))
        
        if not response_data or response_data.get("ret") is False:
            return all_results, 0, []

        data = response_data.get("data", {})
        current_page_data = data.get("data", [])
        
        # 处理当前页的数据，只保留需要的字段
        for item in current_page_data:
            create_time = item.get("createTime", "")
            # 将createTime格式化为yyyy-mm-dd
            create_date = ""
            if create_time:
                try:
                    # 假设createTime格式为 "yyyy-mm-dd HH:MM:SS"
                    create_date = create_time.split()[0]
                except:
                    create_date = create_time
            
            processed_item = {
                "uniqKey": str(uuid.uuid4()),
                "flowNo": item.get("flowNo", ""),
                "orderNo": item.get("orderNo", ""),
                "createTime": create_time,
                "createDate": create_date,
                "flowStatus": item.get("flowStatus", "")
            }
            all_results.append(processed_item)

        total_count = data.get("totalCount", 0)
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        # 生成页数数组
        page_array = list(range(1, total_pages + 1)) if total_pages > 0 else []
        

    except Exception as e:
        print(f"查询出错: {str(e)}")
        return all_results, 0, []

    return all_results, total_pages, page_array


def get_order_info(orderNo: str, appCode: str, appToken: str) -> Dict[str, Any]:
    """
    获取订单的详细信息
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        Dict: 包含订单domain、航班日期和乘客信息
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/gongdan/order/search?orderNo={orderNo}&domain=callcenter.qunar.com",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        # 检查是否有错误
        if "error" in result:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": []}

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": []}

        # 获取list中的第一个订单信息
        order_list = response_data.get("data", {}).get("list", [])
        if not order_list:
            return {"rawDomain": "", "airlineDate": [], "passengerNames": []}
            
        order_info = order_list[0]
        
        # 提取所需信息
        raw_domain = order_info.get("rawDomain", "")
        airline_date = order_info.get("airlineDate", [])
        
        # 提取乘客姓名
        passenger_names = []
        passenger_vos = order_info.get("passengerVos", [])
        for passenger in passenger_vos:
            name = passenger.get("name", "")
            if name:
                passenger_names.append(name)
                
        return {
            "rawDomain": raw_domain,
            "airlineDate": airline_date,
            "passengerNames": passenger_names
        }

    except Exception as e:
        return {"rawDomain": "", "airlineDate": [], "passengerNames": []}

def get_carrier_info(orderNo: str, appCode: str, appToken: str) -> str:
    """
    获取订单的承运人信息
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        str: 承运人代码
    """
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/qsna/message/type/queryResidentRepresentCarrier?orderNo={orderNo}&domain=callcenter.qunar.com",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        
        if "error" in result:
            return ""

        response_data = json.loads(result.get("data", "{}"))
        
        if not response_data or response_data.get("ret") is False:
            return ""
            
        return response_data.get("data", "")

    except Exception as e:
        return ""

def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含orderNo、flowStatus、date、invokeAppCode和invokeToken的参数字典
    Returns:
        Dict: 包含总页数和页数数组的结果
    """
    orderNo = param.get("orderNo", "")
    flowStatus = param.get("flowStatus", "")
    date = param.get("date", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    
    # 验证参数
    if not orderNo and not date:
        return {
            "error": "当订单号为空时，日期不能为空",
            "totalPages": 0,
            "pageArray": []
        }
    
    try:
        results, total_pages, page_array = search_flow(orderNo, flowStatus, date, appCode, appToken)
        
        return {
            "error": "",
            "totalPages": total_pages,
            "pageArray": page_array
        }
    except Exception as e:
        return {
            "error": f"处理失败: {str(e)}", 
            "totalPages": 0,
            "pageArray": []
        }

def test():
    param = {
        "orderNo": "",
        "flowStatus": "",
        "date": "2025-05-12",  
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 打印总页数和页数数组
    print(f"总页数: {result.get('totalPages', 0)}")
    print(f"页数数组: {result.get('pageArray', [])}")

if __name__ == "__main__":
    test() 