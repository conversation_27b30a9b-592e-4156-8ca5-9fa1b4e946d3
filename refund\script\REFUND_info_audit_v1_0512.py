from urllib.parse import unquote_to_bytes
import json
from typing import Dict, List, Tuple
import hashlib


def validate_id_card_any(materials: List[Dict], name_to_md5_map: Dict[str, str]) -> Tuple[bool, str]:
    """
    身份证（任一）校验器
    检查是否至少有一个乘客有对应的身份证明图片
    """
    id_cards = [m for m in materials if m.get("docType") == "身份证明"]
    if not id_cards:
        return False, "未找到身份证明"
    
    # 获取所有身份证明中的姓名MD5值
    id_card_names = set()
    for card in id_cards:
        img_content = card.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        name = img_content.get("姓名_md5", "")
        if name:
            id_card_names.add(name)
    
    # 检查是否至少有一个乘客有对应的身份证明
    for passenger_name in name_to_md5_map.values():
        if passenger_name in id_card_names:
            return True, ""
            
    return False, "缺少身份证明文件"

def validate_id_card_companion(materials: List[Dict], name_to_md5_map: Dict[str, str]) -> Tuple[bool, str]:
    """
    身份证（陪同）校验器
    检查每个乘客是否都有对应的身份证明图片
    """
    id_cards = [m for m in materials if m.get("docType") == "身份证明"]
    if not id_cards:
        return False, "未找到身份证明"
    
    # 获取所有身份证明中的姓名MD5值
    id_card_names = set()
    for card in id_cards:
        img_content = card.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        name = img_content.get("姓名_md5", "")
        if name:
            id_card_names.add(name)
    
    # 创建MD5值到原始名称的反向映射
    md5_to_name_map = {md5: name for name, md5 in name_to_md5_map.items()}
    
    # 检查每个乘客是否都有对应的身份证明
    for passenger_name in name_to_md5_map.values():
        if passenger_name not in id_card_names:
            # 使用反向映射获取原始乘客名称，如果没有则使用MD5值
            original_name = md5_to_name_map.get(passenger_name, passenger_name)
            return False, f"乘客 {original_name} 缺少身份证明文件"
            
    return True, ""

def validate_id_card_companion_with_type(materials: List[Dict], name_to_md5_map: Dict[str, str], cert_type: str) -> Tuple[bool, str]:
    """
    身份证（陪同）校验器 - 带证件类型校验
    检查每个乘客是否都有对应的身份证明图片，并且证件类型符合要求
    
    Args:
        materials: 材料列表
        name_to_md5_map: 姓名到MD5值的映射字典
        cert_type: 需要校验的证件类型，例如"身份证"
        
    Returns:
        Tuple[bool, str]: (是否通过, 失败原因)
    """
    id_cards = [m for m in materials if m.get("docType") == "身份证明"]
    if not id_cards:
        return False, "未找到身份证明"
    
    # 创建MD5值到原始名称的反向映射
    md5_to_name_map = {md5: name for name, md5 in name_to_md5_map.items()}
    
    # 记录每个乘客是否有符合要求的证件
    passenger_valid_cert = {passenger_md5: False for passenger_md5 in name_to_md5_map.values()}
    
    # 检查所有身份证明
    for card in id_cards:
        img_content = card.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        name_md5 = img_content.get("姓名_md5", "")
        actual_cert_type = img_content.get("证件类型", "")
        
        # 检查名称MD5是否在乘客列表中，且证件类型是否符合要求
        if name_md5 in passenger_valid_cert and actual_cert_type == cert_type:
            passenger_valid_cert[name_md5] = True
    
    # 检查是否有乘客没有符合要求的证件
    for passenger_md5, has_valid_cert in passenger_valid_cert.items():
        if not has_valid_cert:
            original_name = md5_to_name_map.get(passenger_md5, passenger_md5)
            return False, f"乘客 {original_name} 缺少{cert_type}"
            
    return True, ""

def validate_diagnosis_signature(materials: List[Dict]) -> Tuple[bool, str]:
    """
    诊断证明（签字）过滤器
    检查诊断证明是否有医生签字
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
        has_signature = img_content.get("是否有医生签字", "")
        if has_signature == "是":
            return True, ""
            
    return False, "未找到医生签字"

def validate_diagnosis_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    诊断证明（盖章）过滤器
    检查诊断证明是否有盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        has_stamp = img_content.get("是否有盖章", "")
        if has_stamp == "是":
            return True, ""
            
    return True, ""

def validate_check_material_type(materials: List[Dict], material_type: str) -> Tuple[bool, str]:
    """
    检查报告过滤器
    检查是否有检查报告类型的诊断证明
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        
        if actual_material_type == material_type:
            return True, ""
            
    return False, "未找到检查报告"

def validate_invoice_type(materials: List[Dict], invoice_type: str) -> Tuple[bool, str]:
    """
    发票类型过滤器
    检查发票类型是否为指定类型
    """
    diagnoses = [m for m in materials if m.get("docType") == "发票证明"]
    if not diagnoses:
        return False, "未找到发票证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        if actual_material_type == invoice_type:
            return True, ""
            
    return False, "未找到发票或押金类证明"


def validate_invoice(materials: List[Dict]) -> Tuple[bool, str]:
    """
    发票过滤器
    检查是否有发票证明
    """
    invoices = [m for m in materials if m.get("docType") == "发票证明"]
    if not invoices:
        return False, "未找到发票证明"
    return True, ""

def validate_invoice_amount(materials: List[Dict], required_amount: float) -> Tuple[bool, str]:
    """
    发票金额过滤器
    检查发票金额是否大于指定金额
    """
    invoices = [m for m in materials if m.get("docType") == "发票证明"]
    if not invoices:
        return False, "未找到发票证明"
        
    for invoice in invoices:
        img_content = invoice.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        amount_str = img_content.get("发票金额", "")
        if not amount_str:
            continue
            
        try:
            amount = float(amount_str)
            if amount >= required_amount:
                return True, ""
        except ValueError:
            continue
            
    return False, f"未找到金额大于 {required_amount} 的发票"

def validate_id_card_front(materials: List[Dict]) -> Tuple[bool, str]:
    """
    是否含身份证-正面过滤器
    检查是否有包含姓名和身份证号的身份证
    """
    id_cards = [m for m in materials if m.get("docType") == "身份证明"]
    if not id_cards:
        return False, "未找到身份证明"
        
    for card in id_cards:
        img_content = card.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        cert_type = img_content.get("证件类型", "")
        if cert_type != "身份证":
            continue
            
        name = img_content.get("姓名", "")
        id_number = img_content.get("身份证号", "")
        
        if name and id_number:
            return True, ""
            
    return False, "未找到有效的身份证正面信息"


def validate_id_card_back(materials: List[Dict]) -> Tuple[bool, str]:
    """
    是否含身份证-背面过滤器
    检查是否有包含有效期限和签发机关的身份证
    """
    id_cards = [m for m in materials if m.get("docType") == "身份证明"]
    if not id_cards:
        return False, "未找到身份证明"
        
    for card in id_cards:
        img_content = card.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        cert_type = img_content.get("证件类型", "")
        if cert_type != "身份证":
            continue
            
        valid_period = img_content.get("有效期限", "")
        issue_authority = img_content.get("签发机关", "")
        
        if valid_period and issue_authority:
            return True, ""
            
    return False, "未找到有效的身份证背面信息"


def validate_not_suitable_for_flying(materials: List[Dict]) -> Tuple[bool, str]:
    """
    不宜乘机过滤器
    检查诊断证明中是否标记为不宜乘机
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:

        not_suitable = diagnosis.get("notSuitableForFlying", "")
        if not_suitable == "是":
            return True, ""
            
    return False, "未找到不宜乘机证明"

def validate_medical_record(materials: List[Dict]) -> Tuple[bool, str]:
    """
    病历过滤器
    检查是否有病历类型的诊断证明
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        if actual_material_type == "病历":
            return True, ""
            
    return False, "未找到病历"

def validate_medical_record_with_signature_and_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    病历（签字盖章）过滤器
    检查是否有病历类型的诊断证明，且必须有医生签字和盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        has_signature = img_content.get("是否有医生签字", "")
        has_stamp = img_content.get("是否有盖章", "")
        
        if actual_material_type == "病历" and has_signature == "是" and has_stamp == "是":
            return True, ""
            
    return False, "未找到有效的病历（需包含医生签字和盖章）"

def validate_medical_document(materials: List[Dict], material_type_need: str, require_signature: bool = False, require_stamp: bool = False, signature_relation: str = "and") -> Tuple[bool, str]:
    """
    医疗文档验证器
    检查是否有指定类型的诊断证明，并根据要求验证签字和盖章
    
    Args:
        materials: 材料列表
        material_type_need: 材料类型，支持单个类型、逗号分隔的多个类型，或留空（不校验材料类型）
        require_signature: 是否要求医生签字
        require_stamp: 是否要求盖章
        signature_relation: 签字和盖章的关系，"and"表示都需要，"or"表示满足一个即可
    
    Returns:
        Tuple[bool, str]: (是否通过, 失败原因)
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    # 处理材料类型需求
    material_types = [t.strip() for t in material_type_need.split(",")] if material_type_need else []
    
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        
        # 如果指定了材料类型，则检查是否匹配任一类型
        if material_types and actual_material_type not in material_types:
            continue
            
        # 如果不需要验证签字和盖章，直接返回通过
        if not require_signature and not require_stamp:
            return True, ""
            
        # 获取签字和盖章状态
        has_signature = img_content.get("是否有医生签字", "") == "是"
        has_stamp = img_content.get("是否有盖章", "") == "是"
        
        # 根据关系判断是否通过
        if signature_relation == "and":
            if has_signature and has_stamp:
                return True, ""
        else:  # "or"
            if has_signature or has_stamp:
                return True, ""
    
    # 构建失败原因
    reason_parts = []
    if material_types:
        reason_parts.append(f"未找到{', '.join(material_types)}")
    if require_signature and require_stamp:
        if signature_relation == "and":
            reason_parts.append("需同时包含医生签字和盖章")
        else:
            reason_parts.append("需包含医生签字或盖章")
    elif require_signature:
        reason_parts.append("需包含医生签字")
    elif require_stamp:
        reason_parts.append("需包含盖章")
        
    return False, "，".join(reason_parts)

def validate_diagnosis_certificate_with_signature_and_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    诊断证明（签字盖章）过滤器
    检查是否有诊断证明类型的诊断证明，且必须有医生签字和盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        has_signature = img_content.get("是否有医生签字", "")
        has_stamp = img_content.get("是否有盖章", "")
        
        if (actual_material_type == "诊断证明" or actual_material_type == "诊断书") and has_signature == "是" and has_stamp == "是":
            return True, ""
            
    return False, "未找到有效的诊断证明（需包含医生签字和盖章）"


def validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable(materials: List[Dict]) -> Tuple[bool, str]:
    """
    诊断证明（签字盖章）过滤器
    检查是否有诊断证明类型的诊断证明，且必须有医生签字和盖章，且不宜乘机为否
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        has_signature = img_content.get("是否有医生签字", "")
        has_stamp = img_content.get("是否有盖章", "")
        not_suitable = diagnosis.get("notSuitableForFlying", "")
        if (actual_material_type == "诊断证明" or actual_material_type == "诊断书") and has_signature == "是" and has_stamp == "是" and not_suitable == "是":
            return True, ""
            
    return False, "未找到有效的诊断证明（需包含医生签字和盖章，需含不宜乘机）"


# 用药清单（盖章）过滤器,修改方法名
def validate_medical_drug_record_with_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    用药清单（盖章）过滤器
    检查是否有用药明细清单类型的诊断证明，且必须有盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        has_stamp = img_content.get("是否有盖章", "")
        
        if actual_material_type == "用药明细清单" and has_stamp == "是":
            return True, ""
            
    return False, "未找到有效的用药明细清单（需包含盖章）"


# 病历（盖章）过滤器,修改方法名
def validate_medical_record_with_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    病历（盖章）过滤器
    检查是否有病历类型的诊断证明，且必须有盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        actual_material_type = img_content.get("材料类型", "") if img_content.get("材料类型", "") else img_content.get("证件类型", "")
        has_stamp = img_content.get("是否有盖章", "")
        
        if actual_material_type == "病历" and has_stamp == "是":
            return True, ""
            
    return False, "未找到有效的病历（需包含盖章）"    

# 病历（盖章）过滤器,修改方法名
def validate_medical_check_report_with_stamp(materials: List[Dict]) -> Tuple[bool, str]:
    """
    检查报告（盖章）过滤器
    检查是否有检查报告类型的诊断证明，且必须有盖章
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        has_stamp = img_content.get("是否有盖章", "")
        
        if has_stamp == "是":
            return True, ""
            
    return False, "未找到有效的病历（需包含盖章）"   

# 诊断证明（盖章和医生签字）过滤器,修改方法名
def validate_diagnosis_certificate_with_stamp_and_signature(materials: List[Dict]) -> Tuple[bool, str]:
    """
    诊断证明（盖章和医生签字）过滤器
    检查是否有诊断证明类型的诊断证明，且必须有盖章和医生签字
    """
    diagnoses = [m for m in materials if m.get("docType") == "诊断证明"]
    if not diagnoses:
        return False, "未找到诊断证明"
        
    for diagnosis in diagnoses:
        img_content = diagnosis.get("imgContent", {})
        if not isinstance(img_content, dict):
            continue
            
        has_stamp = img_content.get("是否有盖章", "")
        has_signature = img_content.get("是否有医生签字", "")
        
        if has_stamp == "是" and has_signature == "是":
            return True, ""
            
    return False, "未找到有效的诊断证明（需包含盖章和医生签字）"  

#过滤器映射，key:汉字描述，value:函数
FILTER_FUNCTIONS = {
    "身份证（任一）": validate_id_card_any,
    "身份证（陪同）": validate_id_card_companion,
    "身份证（陪同带类型）": validate_id_card_companion_with_type,
    "诊断证明（签字）": validate_diagnosis_signature,
    "诊断证明（盖章）": validate_diagnosis_stamp,
    "诊断证明（检查报告）": validate_check_material_type,
    "发票类型": validate_invoice_type,
    "发票": validate_invoice,
    "发票金额": validate_invoice_amount,
    "身份证-正面": validate_id_card_front,
    "身份证-反面": validate_id_card_back,
    "不宜乘机": validate_not_suitable_for_flying,
    "病历": validate_medical_record,
    "病历（签字盖章）": validate_medical_record_with_signature_and_stamp,
    "诊断证明（签字盖章）": validate_diagnosis_certificate_with_signature_and_stamp,
    "诊断证明（签字盖章不宜乘机）": validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable,
    "用药清单（盖章）": validate_medical_drug_record_with_stamp,
    "病历（盖章）": validate_medical_record_with_stamp,
    "检查报告（盖章）": validate_medical_check_report_with_stamp,
    "诊断证明（盖章和医生签字）": validate_diagnosis_certificate_with_stamp_and_signature,
    "诊断证明文档验证": validate_medical_document
}

# 航司过滤器映射
carrier_filter_map = {
    "CA": [
        # 第一组：身份证明校验
        [validate_id_card_companion], # type: ignore
        # 第二组：诊断证明、病历、检查报告校验签字或者盖章
        [validate_invoice,lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历,检查报告", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
    ],
    "3U": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 50)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "8L": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 50)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "PN": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 50)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "MU": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明,出院小结/记录", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "FM": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明,出院小结/记录", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "9D": [
        [validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "DR": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "JD": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "Y8": [
        # 第一组：发票金额校验
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "A6": [
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        # 第二组：诊断证明、病历校验签字或者盖章
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        # 第三组：身份证明校验
        [validate_id_card_companion] # type: ignore
    ],
    "AQ": [
        [validate_diagnosis_certificate_with_signature_and_stamp], # type: ignore
        [validate_medical_record_with_signature_and_stamp], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion], # type: ignore
        [validate_medical_drug_record_with_stamp] # type: ignore
    ],
    "BK": [
        [lambda materials: validate_invoice_amount(materials, 200)], # type: ignore
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "HU": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明,出院小结/记录", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "CN": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "FU": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "GS": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "GX": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "UQ": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "CZ": [
        [validate_invoice, lambda materials:validate_medical_document(materials,"押金", require_signature=False, require_stamp=True)], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "DZ": [
        [validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "EU": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=False, require_stamp=True,signature_relation="and")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [validate_id_card_any], # type: ignore
        [validate_id_card_front], # type: ignore
        [validate_id_card_back] # type: ignore
    ],
    "G5": [
        [lambda materials:validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 200)], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "GJ": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable], # type: ignore
        [validate_invoice, lambda materials: validate_invoice_type(materials, "押金")], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "GT": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明,检查报告", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 200)], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "GY": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 200)], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "HO": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [lambda materials: validate_medical_document(materials, "病历,",require_signature=False, require_stamp=True)], # type: ignore
        [lambda materials: validate_check_material_type(materials, "挂号单")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "JR": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [lambda materials: validate_medical_document(materials, "病历",require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [lambda materials: validate_check_material_type(materials, "挂号单")], # type: ignore
        [validate_id_card_any] # type: ignore
        [validate_id_card_front], # type: ignore
        [validate_id_card_back] 
    ],
    "KN": [
        [validate_invoice],
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书,病历,住院证明", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_id_card_companion] # type: ignore

    ],
    "KY": [
        [validate_invoice,lambda materials: validate_medical_document(materials,"诊断证明,诊断书,检查报告", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
    ],
    "SC": [
        [validate_invoice,lambda materials: validate_medical_document(materials,"诊断证明,诊断书,检查报告", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
    ],
    "ZH": [
        [validate_invoice,lambda materials: validate_medical_document(materials,"诊断证明,诊断书,检查报告", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
    ],
    "LT": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_medical_document(materials,"检查报告", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [validate_invoice] # type: ignore
    ],
    "MF": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [lambda materials: validate_medical_document(materials,"病历,住院证明", require_signature=True, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "NS": [
        [validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable], # type: ignore
        [lambda materials: validate_medical_document(materials,"病历,住院证明", require_signature=False, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "QW": [
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore
        [validate_invoice], # type: ignore
        [validate_id_card_any] # type: ignore
    ],
    "RY": [
        [validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable], # type: ignore
        [lambda materials: validate_medical_document(materials,"病历,住院证明", require_signature=False, require_stamp=True, signature_relation="or")], # type: ignore
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [validate_id_card_companion] # type: ignore
    ],
    "TV": [
        [lambda materials: validate_invoice_amount(materials, 100)], # type: ignore
        [validate_id_card_companion], # type: ignore
        [lambda materials: validate_medical_document(materials,"诊断证明,诊断书,病历", require_signature=True, require_stamp=True, signature_relation="and")], # type: ignore

    ]
}

def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def process_passenger_names(passenger_names: str) -> List[str]:
    """
    处理乘客名单，支持多种输入格式
    
    Args:
        passenger_names: 乘客名单字符串，可以是JSON字符串或逗号分隔的字符串
        
    Returns:
        List[str]: 处理后的乘客名单列表
    """
    if isinstance(passenger_names, str):
        if ',' in passenger_names:
            return [name.strip() for name in passenger_names.split(',') if name.strip()]
        elif passenger_names.strip():
            try:
                return json.loads(passenger_names)
            except json.JSONDecodeError:
                return [passenger_names.strip()]
    return []

def process_material_list(material_list: List[Dict]) -> List[Dict]:
    """
    处理材料列表，确保必要的字段存在并格式正确
    
    Args:
        material_list: 原始材料列表
        
    Returns:
        List[Dict]: 处理后的材料列表
    """
    filtered_material_list = []
    for material in material_list:
        # 确保inconsistencies字段存在
        if "inconsistencies" not in material:
            material["inconsistencies"] = []
            
        # 处理imgContent
        if "imgContent" in material:
            if material["imgContent"] == "{}":
                material["imgContent"] = {}
            elif isinstance(material["imgContent"], str) and material["imgContent"]:
                material["imgContent"] = json.loads(material["imgContent"])
        else:
            material["imgContent"] = {}
            
        # 处理invoice_validation_content
        if "invoice_validation_content" in material:
            if material["invoice_validation_content"] == "{}":
                material["invoice_validation_content"] = {}
            elif isinstance(material["invoice_validation_content"], str) and material["invoice_validation_content"]:
                material["invoice_validation_content"] = json.loads(material["invoice_validation_content"])
        else:
            material["invoice_validation_content"] = {}
            
        filtered_material_list.append(material)
    
    return filtered_material_list

def expand_passenger_names(passenger_names: List[str]) -> Tuple[List[str], Dict[str, str]]:
    """
    扩展乘客名单，添加MD5加密版本
    
    Args:
        passenger_names: 原始乘客名单
        
    Returns:
        Tuple[List[str], Dict[str, str]]: 
            - 扩展后的乘客名单（包含原始名称和MD5值）
            - 原始名称到MD5值的映射字典
    """
    expanded_names = passenger_names.copy()
    name_to_md5_map = {}
    
    for name in passenger_names:
        if name:
            md5_name = hashlib.md5(name.encode('utf-8')).hexdigest()
            expanded_names.append(md5_name)
            name_to_md5_map[name] = md5_name
            
    return expanded_names, name_to_md5_map

def get_carrier_requirements_summary(carrier: str) -> str:
    """
    获取航司退票要求的汇总描述
    
    Args:
        carrier: 航司代码
        
    Returns:
        str: 航司退票要求描述
    """
    carrier_requirements_map = {
        "CA": [
            "需提供以下①②③④⑤文件之一作为证明材料（原件、复印件、扫描件或照片均可）",
            "①由医疗机构出具的医疗诊断证明书；",
            "②由医师签署的医疗诊断证明书；",
            "③由医疗机构出具的检查报告；",
            "④由医疗机构出具的药费或治疗费的收费票据；",
            "⑤由医疗机构出具的病历证明..."
        ],
        "3U": [
            "①诊断证明：出具认可的医疗机构出具有医生签字及医院盖章的诊断证明或疾病诊断书；",
            "②发票：医药费或医疗检查在50元（含）以上的就诊费发票（病费、治疗费、检查费、住院费等，仅提供挂号费视为资料不齐）；", 
            "③证件：提供旅客购买的机票件原照片或彩色扫描件（快递材料必须为原件）",
            "④大合照：患病旅客本人及陪行人员有效身份证件复印或电子扫描件或照片"
        ],
        "8L/PN": [
            "①转机航空认可的医疗机构出具的有主治医生签字或医院盖章的真实有效的诊断证明（诊断书或病历等）",
            "②转机航空认可的医疗机构出具的收费票据且金额须大于50元（含），发票需真实有效，发票类可包含费用、治疗费、检查费、住院费；",
            "③证件：提供旅客购票时提供的购票凭证（姓名+证件号面即可）"
        ],
        "MU/FM": [
            "①在航班起飞前21天之内出具的，可证明旅客在客票列明的航班飞行期间不适宜乘机的真实有效合理的诊疗证明原件复印（如诊断书、病历证明，病假单等）",
            "②可验证的收费票据及复印件（包括挂号、门急诊、医药费、住院费，仅提供挂号费视为资料不齐），军人在部队医院就医，无法提供挂号或收费凭据，可提供相应军医证明文件原件或复印件代替",
            "③证件：提供旅客购票时所用的身份证件原件"
        ],
        "9D": [
            "①诊断证明：县级（含）以上医疗单位出具有主治医生签字，医院盖章的诊断证明书原件（需注明不宜乘机）",
            "②证件：提供旅客购票时所用的身份证件原件"
        ],
        "DR": [
            "①医疗机构出具的有主治医生签字及医院盖章的诊断证明或疾病原件(扫描件或照片)；",
            "②同一医疗机构电脑打印的100元（含）以上医药费收费票据（部分无法打印的医院需提供原件且盖有医院公章）；发票类型可包含医药费、治疗费、检查费、住院费等(仅提供挂号费视为资料不齐)；",
            "③提供旅客购票时提供的购票凭证(扫描件或照片)"
        ],
        "JD/Y8": [
            "①Y8认可医疗机构出具的有主治医生签字及医院盖章的正规诊断证明或疾病；",
            "②同一医疗机构出具的收费发票（发票不包括医院收费票据，如缴费票据），发票金额不低于100元（含），包含药费、治疗费、检查费、住院费、诊查费、挂号费等，但仅提供挂号费视为资料不齐",
            "③提供旅客身份证件原件或扫描件（证件要求包含照片、姓名及证件号码面即可）"
        ],
        "A6": [
            "①诊断证明：县级（含）以上医疗单位出具的有主治医生签字的正规诊断证明原件（需注明不宜乘机）；",
            "②发票：100元（含）以上的医药发票原件或复印件 （需要与诊断证明同一天开具）",
            "③大合照：需要乘机人以及陪同人证件、医药发票，诊断证明放在一起的清晰大合照",
            "④证件：乘机人身份证件原件"
        ],
        "AQ": [
            "①诊断证明：由县级以上医院开具，主治医生签字，医院盖章；",
            "②病历：住院旅客无法提供病历，可打印住院病案，加盖医院公章",
            "③医药发票：需加盖医院公章",
            "④用药明细清单原件：需加盖医院公章",
            "⑤证件：乘机人身份证件原件"
        ],
        "BK": [
            "①诊断证明：县级（含）以上医疗单位有主治医生签字及医院盖章的诊断证明，（建议体检时间需要乘机日期）；",
            "②医药发票：医院电脑打印不少于200元的医药费凭据或住院流水水单据；",
            "③证件：提供旅客购票时身份证件原件；",
            "④陪同人员提供购票凭证照片；",
            "⑤以上材料需要单独拍照，并放在一起拍摄清晰照片"
        ],
        "HU/CN/FU/GS/GX/UQ": [
            "①大新华航空认可的医疗机构出具的有主治医生签字或医院盖章的真实有效医疗诊断证明或病历或出院小结（住院证明）",
            "②发票：大新华航空认可的医疗机构开具的收费发票（包括挂号费，住院费等），住院旅客可提供住院押金充值收据或医院统一打印收费明细单",
            "③证件：提供旅客购票时所用的有效身份证件"
        ],
        "CZ": [
            "①医疗缴费发票（发票包含检查费、药费、住院费等，仅提供挂号发票视为资料不齐）",
            "②住院押金证明（需加盖公章）",
            "③证件：提供旅客购票时所用的有效身份证件"
        ],
        "DZ": [
            "①国家卫健委网站可查询的医院出具的有主治医生签字和医院盖章的不适宜乘机的诊断证明书原件；",
            "②医药发票原件（包括但不限于带有效收费专用章的医疗收费票据）",
            "③证件：乘机人身份证件原件"
        ],
        "EU": [
            "①由认可医疗机构出具，需加盖医院公章的医疗诊断证明（诊断书或病历等）",
            "②100元以上票费发票（仅提供电子发票）",
            "③证件：乘机人身份证件正反面照片"
        ],
        "G5": [
            "①县级（含）以上医疗单位出具的正规诊断证明原件或病历原件；",
            "②医药发票：200元（含）以上；",
            "③证件：乘机人身份证件复印件（需要在身份证复印件上与上办理旅退的客票票号）"
        ],
        "GJ": [
            "①卫健委列明的医院及下属医疗机构在旅客购票后且在始发日日（含）之前出具的医生诊断证明或病历（二选一）",
            "②卫健委列明的医院及下属医疗机构出具的不适宜乘机样样的证明（建议体检日期最好是航班计划起飞日目前也可），需要有医生签字并加盖医院公章",
            "③上述医药费发票或住院费证明（二选一）",
            "④乘机人身份证原件及其陪同人员的票件证件"
        ],
        "GT": [
            "①县级（含）以上医疗单位出具的有主治医生签字的正规诊断证明原件（正规诊断证明包括诊断书、病历、住院通知单或检查报告单）",
            "②发票：医院电脑打印的200元（含）以上有效的医药费发票/收据（部分无电脑打印医药费收费单的住院旅客，可出具医院电脑打印收费明细后加盖医院收费章用章）；",
            "③证件：乘机人身份证原件拍照；",
            "④以上全部凭据放在一起合照"
        ],
        "GY": [
            "①医疗单位出具的有主治医生签字并加盖公章的正规诊断证明（或病历）",
            "②医药发票：医院电脑打印不少于200元的医药费发票（部分无电脑打印医药费收费单的住院旅客，可出具医院电脑打印收费明细后加盖医院公章）；",
            "③提供旅客身份证原件"
        ],
        "HO": [
            "①诊断证明：二级（含）以上医疗单位出具的有主治医生签字，医院盖章的诊断证明原件；",
            "②挂号单：原件；",
            "③病历：原件；",
            "④医药发票：原件（上述材料①②③都符合的情况下，发票不强制要求）；",
            "⑤证件：乘机人身份证件原件照片（复印件不可）"
        ],
        "JR": [
            "①诊断证明：患病旅客提供县级（含）以上医疗单位出具的有主治医生签字，医院盖章的正规诊断证明原件；",
            "②病历：原件（住院旅客无法提供病历，可打印住院病案，加盖医院公章）",
            "③医药发票：医院电脑打印的100元（含）以上的医药费收费原件；",
            "④挂号单：医院电脑打印的挂号发票；",
            "⑤证件：乘机人身份证原件"
        ],
        "KN": [
            "①①在选择一：二级甲等（含）以上院，在飞行期间不适宜乘机且盖章的《诊断证明书》",
            "②二级甲等（含）以上院，能够证明旅客在客票列明的航班飞行期间不适宜乘机的真实有效的诊疗证明原件或复印件（如诊断书、病历、住院证明）和医疗机构收费票据或复印件（如挂号、门急诊、住院）；",
            "③证件：提供旅客证件正反面照片；"
        ],
        "KY/SC/ZH": [
            "①由县级以上医疗机构出具的医疗诊断证明书；",
            "②由县级以上医疗机构出具的检查报告；",
            "③由县级以上医疗机构出具的医药费或治疗费的收费票据",
            "④①②③任选一即可"
        ],
        "LT": [
            "①由医疗机构出具的医疗诊断证明书",
            "②由医师签署的医疗诊断证明书",
            "③由医疗机构出具的检查报告",
            "④由医疗机构出具的药费或治疗费的收费据"
        ],
        "MF": [
            "①诊断证明：县级以上医疗机构出具的不适宜乘机的诊断证明书（疾病证明）原件(有主治医生签字，医院盖章)；",
            "②病历：原件（住院旅客无法提供病历，可打印住院病案，加盖医院公章）",
            "③医药发票：医院电脑打印的100元以上原件（部分无电脑打印医药费收费单的住院旅客，可出具医院电脑打印收费明细后加盖医院公章）；",
            "④证件：乘机人身份证件原件"
        ],
        "NS": [
            "①诊断证明：县（市）级以上医疗单位出具的有主治医生签字，医院盖章注明不宜乘机的诊断证明（疾病证明/医疗证明）",
            "②病历：原件（住院旅客无法提供病历，可打印住院病案，加盖医院公章）",
            "③医药发票：医院电脑打印100元以上医药发",
            "④证件：乘机人身份证原件"
        ],
        "QW": [
            "①诊断证明：县（市）级以上医院开具由主治医生签字，医院盖章的正规诊断证明原件",
            "②医药发票：医院电脑打印的医药费收费单",
            "③证件：乘机人身份证件原件"
        ],
        "RY": [
            "①诊断证明：县级（含）以上医疗单位出具，有主治医生签字，医院盖章（需含有不宜乘机）的诊断证明原件（疾病证明/医疗证明）",
            "②病历：原件（住院旅客无法提供病历，可打印住院病案，加盖医院公章）",
            "③医药发票：医院电脑打印的100元以上医药费收费单（部分无电脑打印医药费收费单的住院旅客，可出具医院电脑打印收费明细后加盖医院公章）；",
            "④证件：乘机人身份证件原件"
        ],
        "TV": [
            "1. 由县级或二甲等及以上医疗单位出具的医疗证明或诊断证明",
            "2. 有医生签名或盖性名及医疗单位盖章（境外由医生签章）",
            "3. 100元以上就费单（军人、警察、消防救援人员申请退的材料中医疗单位开具的费用金额可不受最低 100 元的限制）",
            "4. 旅客购票所用的身份证件"
        ]
    }
    
    # 特殊处理航司代码
    if carrier == "PN" or carrier == "8L":
        result_list = carrier_requirements_map.get("8L/PN", [])
    # 处理HU系列航司
    elif carrier in ["HU", "CN", "FU", "GS", "GX", "UQ"]:
        result_list = carrier_requirements_map.get("HU/CN/FU/GS/GX/UQ", [])
    # 处理KY相关航司
    elif carrier in ["KY", "SC", "ZH"]:
        result_list = carrier_requirements_map.get("KY/SC/ZH", [])
    # 处理MU/FM相关航司
    elif carrier in ["MU", "FM"]:
        result_list = carrier_requirements_map.get("MU/FM", [])  
    # 处理3D/Y8相关航司
    elif carrier in ["3D", "Y8"]:
        result_list = carrier_requirements_map.get("3D/Y8", [])        
    else:
        result_list = carrier_requirements_map.get(carrier, ["无该航司退票材料要求信息"])
    
    # 将列表用换行符连接成一个字符串
    return "\n".join(result_list)

def main(param: dict) -> dict:
    """
    主函数
    :param param: 参数字典
    :return: 结果字典
    """
    orderNo = param.get("orderNo", "")
    uniqKey = param.get("uniqKey", "")
    flowNo = param.get("flowNo", "")
    carrier = param.get("carrier","")
    if not orderNo or not carrier:
        return {"error": "订单号/航司不能为空", "results": {}}

    try:
        # 处理allData
        allData = param.get("allData", [])
        if isinstance(allData, str):
            parsed_data, parseStatus = parse_urlencoded_structured_data({"allData": allData}, "allData")
            if parsed_data:
                allData = parsed_data
        
        if not allData:
            return {"error": "获取材料列表失败", "results": []}
        
        # 处理材料列表
        material_list = allData
        filtered_material_list = process_material_list(material_list)
        
        # 处理乘客名单
        passengerNames = process_passenger_names(param.get("passengerNames", ""))
        passengerNames, name_to_md5_map = expand_passenger_names(passengerNames)
        
        # 获取航司对应的过滤器组
        filter_groups = carrier_filter_map.get(carrier, [])
        #如果航司对应的过滤器未查到，则不通过，返回原因
        
        if filter_groups == []:
            return {
                "error": "",
                "results": {
                    "orderNo": orderNo,
                    "uniqKey": uniqKey,
                    "flowNo": flowNo,
                    "orderCheckResult": "不通过",
                    "orderCheckBlockReason": f"该航司({carrier})暂无对应的退票审核规则",
                    "carrier_requirements": get_carrier_requirements_summary(carrier),
                    "passenger_name_md5_map": name_to_md5_map
                }
            }
        
        # 遍历并执行过滤器，组与组之间为且的关系，组内为或的关系
        order_check_result = ""
        order_check_block_reason = ""
        
        # 默认设置为通过，除非有过滤器组不通过
        all_groups_passed = True
        
        for group_index, filter_group in enumerate(filter_groups):
            group_passed = False
            group_reasons = []
            
            # 遍历组内的过滤器（或的关系）
            for filter_func in filter_group:
                # 检查函数签名并适配不同参数
                try:
                    # 有些函数只需要材料列表参数
                    if filter_func.__name__ in ["validate_diagnosis_signature", "validate_diagnosis_stamp", 
                                              "validate_invoice", "validate_not_suitable_for_flying",
                                              "validate_medical_record", "validate_diagnosis_certificate_with_signature_and_stamp",
                                              "validate_diagnosis_certificate_with_signature_and_stamp_and_not_suitable",
                                              "validate_medical_drug_record_with_stamp", "validate_medical_record_with_stamp",
                                              "validate_medical_check_report_with_stamp", "validate_diagnosis_certificate_with_stamp_and_signature",
                                              "validate_id_card_front", "validate_id_card_back"]:
                        success, reason = filter_func(filtered_material_list)
                    # 有些函数需要材料列表和乘客姓名
                    elif filter_func.__name__ in ["validate_id_card_any", "validate_id_card_companion"]:
                        success, reason = filter_func(filtered_material_list, name_to_md5_map)
                    # 特殊处理validate_check_material_type和validate_invoice_type，它们需要额外参数
                    elif filter_func.__name__ == "validate_check_material_type":
                        # 由于缺少material_type参数，这里默认检查"检查报告"
                        success, reason = filter_func(filtered_material_list, "检查报告")
                    elif filter_func.__name__ == "validate_invoice_type":
                        # 默认检查普通发票
                        success, reason = filter_func(filtered_material_list, "发票")
                    elif filter_func.__name__ == "validate_invoice_amount":
                        # 默认检查金额大于等于100的发票
                        success, reason = filter_func(filtered_material_list, 100)
                    # 使用lambda包装的函数，直接调用
                    else:
                        success, reason = filter_func(filtered_material_list)
                except Exception as e:
                    success = False
                    reason = f"过滤器执行错误: {str(e)}"
                
                if success:
                    group_passed = True
                    break
                else:
                    group_reasons.append(reason)
            
            # 如果组内没有一个过滤器通过，整个订单不通过
            if not group_passed:
                all_groups_passed = False
                if group_reasons:
                    # 添加拦截原因编号，按照1, 2, 3的顺序
                    reject_index = len(order_check_block_reason.split("；")) + 1 if order_check_block_reason else 1
                    
                    # 对组内原因进行去重和细化处理
                    unique_reasons = []
                    for reason in group_reasons:
                        # 检查是否与已有原因重复（简单字符串包含检查）
                        is_duplicate = False
                        for existing_reason in unique_reasons:
                            if reason in existing_reason or existing_reason in reason:
                                # 如果当前原因更详细，替换已有原因
                                if len(reason) > len(existing_reason):
                                    unique_reasons.remove(existing_reason)
                                    unique_reasons.append(reason)
                                is_duplicate = True
                                break
                        if not is_duplicate:
                            unique_reasons.append(reason)
                    
                    # 组内的原因使用"或"连接，并添加组类型描述
                    group_message = f"{reject_index}. " + " 或 ".join(unique_reasons)
                    
                    # 检查与已有原因的重复性
                    if order_check_block_reason:
                        # 避免与全局原因重复
                        if not any(group_message.strip() in block for block in order_check_block_reason.split("；")):
                            order_check_block_reason += "；"
                            order_check_block_reason += group_message
                    else:
                        order_check_block_reason += group_message
                
                # 不中断，继续检查其他规则组，收集所有拦截原因
                # 移除了之前的 break 注释
        
        # 获取航司退票材料审核要求
        carrier_requirements = get_carrier_requirements_summary(carrier)
        
        # 根据验证结果设置最终的订单检查结果
        if not all_groups_passed:
            # 检查是否只有一个拦截原因且为"未找到发票证明"
            if order_check_block_reason == "1. 未找到发票证明":
                order_check_result = "待人工确认"
            else:
                order_check_result = "不通过"
        else:
            order_check_result = "通过"
        
        return {
            "error": "",
            "results": {
                "orderNo": orderNo,
                "uniqKey": uniqKey,
                "flowNo": flowNo,
                "orderCheckResult": order_check_result,
                "orderCheckBlockReason": order_check_block_reason,
                "carrier_requirements": carrier_requirements,
                "passenger_name_md5_map": name_to_md5_map
            }
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "results": []}

def test():
    param = {"carrier":"3U","orderNo":"fzy250412220531198","uniqKey":"dc407d89-208f-46b9-bf04-4c2c21e9f925","allData":"orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A8d84df8aa3f482c628d69583a8d078bd%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3Ac569b448-4eb0-4dd7-9cd7-7fb44cdd0021%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ae7e63da161c40f43add590e7508a0a44%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%E9%95%BF%E6%9C%9F%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%226531%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A6623%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%220afc2f1e6728dfad1094df8ae36d1dc9%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222006%E5%B9%B45%E6%9C%8828%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E9%99%88%2A%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%2257ff13b463f65929b950f4c8641b7bc7%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%7D%23%2A%23traceId%3A8a384498-f26c-4942-aa57-e9ac7f97eeb7%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A40db405e9d5c40cef34d7cda03382b3c%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3A7bd973e8-b074-4d48-a902-57a04a02deec%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3Ad0b70954bb71ee6022f2c3a7adc1159f%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3A44942da4-cc30-49f8-8f7b-575bff00f08e%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A168OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3A33dc137c1869066ca80a0d261e83f9a4%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E7%94%9F%E7%AD%BE%E5%AD%97%22%3A%22%E6%98%AF%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%22e00243e1b51304a8fb6db044cf8106db%22%2C%22%E6%9D%90%E6%96%99%E7%B1%BB%E5%9E%8B%22%3A%22%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E9%98%BF%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E7%97%85%E5%8E%86%E5%8F%B7%22%3A%22%22%2C%22%E5%B9%B4%E9%BE%84%22%3A%2218%22%2C%22%E7%97%85%E5%8E%86%E5%86%85%E5%AE%B9%22%3A%22%E7%94%B2%E6%B5%81%EF%BC%88H1N1%EF%BC%89%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22OP0%2A%2A%2A%2A%2A%2A062%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%221453e2a02a5ffad009732d1e4f265473%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%222025-04-14%22%2C%22%E5%8C%BB%E7%94%9F%E5%A7%93%E5%90%8D%22%3A%22%E8%A2%81%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%85%A5%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22%22%2C%22%E6%98%AF%E5%90%A6%E6%9C%89%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%22%3A%22%E6%B2%B3%E5%8C%97%E7%9C%81%E4%B8%AD%E5%8C%BB%E9%99%A2%22%2C%22%E5%BC%80%E5%85%B7%E6%97%B6%E9%97%B4%22%3A%222025-04-14%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E5%91%BC%E5%90%B8%E5%86%85%E7%A7%91%22%7D%23%2A%23traceId%3A9d3b3f1b-d7b1-4044-863b-467f17d768ea%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A168OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A2ae629fddf211f0fc2e8ee78deb52bde%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3Abafef7d8-f716-454e-bcf6-b1483970bbe0%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A179OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A456c880b199baf86198e1641ba1bf0b3%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%22requestId%22%3A%229df97a6597a48eb0a0d44aa800238814%22%2C%22success%22%3Atrue%2C%22code%22%3A0%2C%22data%22%3A%7B%22kprq%22%3A%222025-04-14%2000%3A00%3A00%22%2C%22ch%22%3Afalse%2C%22jkr_md5%22%3A%22e00243e1b51304a8fb6db044cf8106db%22%2C%22czbmyzbh%22%3A%22001%22%2C%22fphm%22%3A%22007%2A%2A%2A%2A322%22%2C%22cysj%22%3A%222025-05-08%2020%3A06%3A47%22%2C%22fhr%22%3A%22wechat001%22%2C%22rz%22%3Afalse%2C%22xmmx%22%3A%5B%7B%22xmmc%22%3A%22%E6%A1%91%E5%8F%B6%22%2C%22zfje_bl%22%3A%22%22%2C%22sl%22%3A%22100.0%22%2C%22xmbh%22%3A%22Y310120405%22%2C%22ggbz%22%3A%22%20%22%2C%22lx%22%3A%22%22%2C%22bz%22%3A%22%22%2C%22je%22%3A%221.98%22%2C%22zfje%22%3A%22%22%2C%22dw%22%3A%22g%22%7D%2C%7B%22lx%22%3A%22%22%2C%22xmbh%22%3A%22Y310810524%22%2C%22xmmc%22%3A%22%E7%8E%AB%E7%91%B0%E8%8A%B1%22%2C%22sl%22%3A%2250.0%22%2C%22zfje%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22bz%22%3A%22%22%2C%22dw%22%3A%22g%22%2C%22je%22%3A%2210.35%22%2C%22ggbz%22%3A%22%20%22%7D%5D%2C%22jkr%22%3A%22%E9%98%BF%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22skr%22%3A%2210219%22%2C%22czbmyz%22%3A%22%E8%B4%A2%E6%94%BF%E7%9B%91%E5%88%B6%E7%AB%A0%22%2C%22chrq%22%3A%22%22%2C%22chyy%22%3A%22%22%2C%22skdw%22%3A%22%E6%B2%B3%E5%8C%97%E7%9C%81%E4%B8%AD%E5%8C%BB%E9%99%A2%22%2C%22cycs%22%3A%221%22%2C%22pjmc%22%3A%22%E6%B2%B3%E5%8C%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22chsj%22%3A%22%22%2C%22fphm_md5%22%3A%22d6c7b5cea2c3c726bb492158ec7699f0%22%2C%22jehjcn%22%3A%22%E5%A3%B9%E6%8B%BE%E8%B4%B0%E5%9C%86%E5%8F%81%E8%A7%92%E5%8F%81%E5%88%86%22%2C%22dy%22%3Afalse%2C%22qtxx%22%3A%5B%7B%22mc%22%3A%22%E4%B8%9A%E5%8A%A1%E6%B5%81%E6%B0%B4%E5%8F%B7%22%2C%22name%22%3A%22ywlsh%22%2C%22value%22%3A%2232B2AC682706028CE0630A20000BF4B3%22%7D%2C%7B%22mc%22%3A%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%2C%22name%22%3A%22mzh%22%2C%22value%22%3A%22OP0013652062%22%7D%2C%7B%22mc%22%3A%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%2C%22name%22%3A%22jzrq%22%2C%22value%22%3A%2220250414%22%7D%2C%7B%22value%22%3A%22%E4%B8%AD%E5%8C%BB%E5%8C%BB%E9%99%A2%22%2C%22mc%22%3A%22%E5%8C%BB%E7%96%97%E6%9C%BA%E6%9E%84%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yljglx%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%B1%BB%E5%9E%8B%22%2C%22name%22%3A%22yblx%22%2C%22value%22%3A%22%E8%87%AA%E8%B4%B9%22%7D%2C%7B%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BC%96%E5%8F%B7%22%2C%22name%22%3A%22ybbh%22%2C%22value%22%3A%22%22%7D%2C%7B%22mc%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22name%22%3A%22xb%22%2C%22value%22%3A%22%E5%A5%B3%22%7D%2C%7B%22name%22%3A%22ybtcjjzf%22%2C%22value%22%3A%220.0%22%2C%22mc%22%3A%22%E5%8C%BB%E4%BF%9D%E7%BB%9F%E7%AD%B9%E5%9F%BA%E9%87%91%E6%94%AF%E4%BB%98%22%7D%2C%7B%22mc%22%3A%22%E5%85%B6%E4%BB%96%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22qtzf%22%2C%22value%22%3A%220.0%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%B4%A6%E6%88%B7%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grzhzf%22%2C%22value%22%3A%220.0%22%7D%2C%7B%22value%22%3A%2212.33%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E7%8E%B0%E9%87%91%E6%94%AF%E4%BB%98%22%2C%22name%22%3A%22grxjzf%22%7D%2C%7B%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E4%BB%98%22%2C%22name%22%3A%22grzf%22%2C%22value%22%3A%220.0%22%7D%2C%7B%22name%22%3A%22grzfe%22%2C%22value%22%3A%220.0%22%2C%22mc%22%3A%22%E4%B8%AA%E4%BA%BA%E8%87%AA%E8%B4%B9%22%7D%5D%2C%22xmqd%22%3A%5B%7B%22zfje%22%3A%22%22%2C%22zfje_bl%22%3A%22%22%2C%22xmbh%22%3A%22%22%2C%22je%22%3A%2212.33%22%2C%22xmmc%22%3A%22%E4%B8%AD%E8%8D%89%E8%8D%AF%22%2C%22ggbz%22%3A%22%22%2C%22bz%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22dw%22%3A%22%22%2C%22sl%22%3A%22%22%7D%2C%7B%22zfje_bl%22%3A%22%22%2C%22zfje%22%3A%22%22%2C%22bz%22%3A%22%22%2C%22xmbh%22%3A%22%22%2C%22sl%22%3A%22100.0%2Fg%22%2C%22ggbz%22%3A%22%22%2C%22lx%22%3A%22%22%2C%22dw%22%3A%22100.0%2Fg%22%2C%22je%22%3A%221.98%22%2C%22xmmc%22%3A%22%E6%A1%91%E5%8F%B6%22%7D%5D%2C%22jkrnsrsbh%22%3A%22653125%2A%2A%2A%2A%2A%2A%2A%2A6620%22%2C%22jym%22%3A%228527%2A%2A%22%2C%22jehj%22%3A%2212.33%22%2C%22fpdm_md5%22%3A%229c6d154a16c3ce5eb6dbfe87dd841d1d%22%2C%22fpdm%22%3A%22130%2A%2A025%22%2C%22jym_md5%22%3A%221a3c55523abbe05e1d090213deb33701%22%7D%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%94%B6%E6%AC%BE%E5%8D%95%E4%BD%8D%EF%BC%88%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0%EF%BC%89%22%3A%22%E6%B2%B3%E5%8C%97%E7%9C%81%E4%B8%AD%E5%8C%BB%E9%99%A2%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81%22%3A%228527%2A%2A%22%2C%22%E4%BD%8F%E9%99%A2%E7%A7%91%E5%88%AB%22%3A%22%E7%AE%80%E6%98%93%E9%97%A8%E8%AF%8A%EF%BC%88%E4%BA%92%E8%81%94%E7%BD%91%E5%8C%BB%E9%99%A2%E9%99%A2%E5%86%85%E7%BB%AD%E6%96%B9%EF%BC%89%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81%22%3A%22130%2A%2A025%22%2C%22%E5%B0%B1%E8%AF%8A%E6%97%A5%E6%9C%9F%22%3A%2220250414%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%2212.33%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%2220250414%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7_md5%22%3A%22d813d89e0be4a8df3de81fe9443d6749%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81%22%3A%22007%2A%2A%2A%2A322%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA%22%3A%22%E9%98%BF%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22%E7%A5%A8%E6%8D%AE%E4%BB%A3%E7%A0%81_md5%22%3A%229c6d154a16c3ce5eb6dbfe87dd841d1d%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8%E3%80%81%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%22%2C%22%E7%A5%A8%E6%8D%AE%E5%8F%B7%E7%A0%81_md5%22%3A%22d6c7b5cea2c3c726bb492158ec7699f0%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7_md5%22%3A%221453e2a02a5ffad009732d1e4f265473%22%2C%22%E5%B0%B1%E8%AF%8A%E5%8F%B7%22%3A%22001%2A%2A%2A%2A062%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E6%B2%B3%E5%8C%97%E7%9C%81%E5%8C%BB%E7%96%97%E9%97%A8%E8%AF%8A%E6%94%B6%E8%B4%B9%E7%A5%A8%E6%8D%AE%EF%BC%88%E7%94%B5%E5%AD%90%EF%BC%89%22%2C%22%E6%A0%A1%E9%AA%8C%E7%A0%81_md5%22%3A%221a3c55523abbe05e1d090213deb33701%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E9%97%A8%E8%AF%8A%E5%8F%B7%22%3A%22OP0%2A%2A%2A%2A%2A%2A062%22%2C%22%E4%BD%8F%E9%99%A2%E6%97%B6%E9%97%B4%22%3A%22%22%2C%22%E4%BA%A4%E6%AC%BE%E4%BA%BA_md5%22%3A%22e00243e1b51304a8fb6db044cf8106db%22%7D%23%2A%23traceId%3Ac9fa2e1d-133d-41ce-95e4-edf7e353a610%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E6%88%90%E5%8A%9F%EF%BC%8C%E4%BA%A4%E6%AC%BE%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%8C%B9%E9%85%8D%23%2A%23invoice_validation_result%3A%E6%88%90%E5%8A%9F%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%293%23%2A%23imgPathMd5%3A05454afd049b8ecc80d83076efbd2ccc%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%222024.03.06-2034.03.06%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%22%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%22%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E6%80%A7%E5%88%AB%22%3A%22%22%7D%23%2A%23traceId%3Af543de50-321f-4402-8c09-98ef5099ffc0%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%292%23%2A%23imgPathMd5%3Aa2e95ba82fc81223cd53875db6980606%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%80%A7%E5%88%AB%22%3A%22%E5%A5%B3%22%2C%22%E6%9C%89%E6%95%88%E6%9C%9F%E9%99%90%22%3A%22%E9%95%BF%E6%9C%9F%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E8%BA%AB%E4%BB%BD%E8%AF%81%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7%22%3A%226531%2A%2A%2A%2A%2A%2A%2A%2A%2A%2A6623%22%2C%22%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%8F%B7_md5%22%3A%220afc2f1e6728dfad1094df8ae36d1dc9%22%2C%22%E5%87%BA%E7%94%9F%E6%97%A5%E6%9C%9F%22%3A%222006%E5%B9%B45%E6%9C%8828%E6%97%A5%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%E9%99%88%2A%2A%22%2C%22%E5%A7%93%E5%90%8D_md5%22%3A%2257ff13b463f65929b950f4c8641b7bc7%22%7D%23%2A%23traceId%3Aa047caab-9d5d-48fe-a009-47033e22d341%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A144OCR%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A1271d9972dff0d38c426dc645553782a%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3A26ce63de-b38c-4ae0-979a-b4d8a1b932c4%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23materialStringType%3A168OCR%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A4ca584249ecca781b22421ec0d51465f%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E8%AF%8A%E6%96%AD%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%7D%23%2A%23traceId%3A1ddde0d8-4582-48fe-bb6b-b0b31e94eb81%23%2A%23invoice_validation_msg%3A%23%2A%23invoice_validation_result%3A%7E%7E%2A%7E%7E, orderNo%3Afzy250412220531198%23%2A%23materialType%3A%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%23%2A%23materialStringType%3A179OCR%E5%8C%BB%E8%8D%AF%E5%8F%91%E7%A5%A8%28%E5%BF%85%E5%A1%AB%291%23%2A%23imgPathMd5%3A2de64d23cd2bdc1f1a7d5142b601299f%23%2A%23uniqKey%3Adc407d89-208f-46b9-bf04-4c2c21e9f925%23%2A%23docType%3A%E5%8F%91%E7%A5%A8%E8%AF%81%E6%98%8E%23%2A%23invoice_validation_content%3A%7B%7D%23%2A%23flowNoNIMF20250413100524594336%23%2A%23imgContent%3A%7B%22%E6%98%AF%E5%90%A6%E6%9C%89%E5%8C%BB%E9%99%A2%E7%9B%96%E7%AB%A0%22%3A%22%E6%98%AF%22%2C%22%E9%87%91%E9%A2%9D%EF%BC%88%E5%B0%8F%E5%86%99%EF%BC%89%22%3A%222271.25%22%2C%22%E4%BD%8F%E9%99%A2%E5%8F%B7%22%3A%22%22%2C%22%E5%8C%BB%E9%99%A2%E5%90%8D%E7%A7%B0F%E5%8D%95%E4%BD%8D%22%3A%22%E6%96%B0%E7%96%86%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%A7%93%E5%90%8D%22%3A%22%22%2C%22%E7%A5%A8%E6%8D%AE%E6%8A%AC%E5%A4%B4%22%3A%22%E6%96%B0%E7%96%86%E5%8C%BB%E7%A7%91%E5%A4%A7%E5%AD%A6%E7%AC%AC%E4%B8%80%E9%99%84%E5%B1%9E%E5%8C%BB%E9%99%A2%22%2C%22%E5%BC%80%E7%A5%A8%E6%97%A5%E6%9C%9F%22%3A%222024%E5%B9%B411%E6%9C%8816%E6%97%A5%22%2C%22%E8%AF%81%E4%BB%B6%E7%B1%BB%E5%9E%8B%22%3A%22%E9%83%A8%E5%88%86%E7%BA%B8%E8%B4%A8%E5%8F%91%E7%A5%A8%E3%80%81%E9%A2%84%E4%BA%A4%E9%87%91%22%2C%22%E7%A7%91%E5%AE%A4%22%3A%22%E5%BF%83%E7%90%86%E7%A7%91%22%7D%23%2A%23traceId%3A13445e03-8c3b-4f12-8d20-443c29b07776%23%2A%23invoice_validation_msg%3A%E5%8F%91%E7%A5%A8%E8%AF%86%E5%88%AB%E5%A4%B1%E8%B4%A5%23%2A%23invoice_validation_result%3A%E5%A4%B1%E8%B4%A5%7E%7E%2A%7E%7E","airlineDate":"2025-04-15 16:35-18:30,2025-04-16 12:20-16:55","passengerNames":"阿丽亚阿布力米提,陈美伶"}
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 测试扩展乘客名单功能
    test_names = ["张三", "李四"]
    expanded_names, name_map = expand_passenger_names(test_names)
    print("扩展后的乘客名单:")
    print(expanded_names)
    print("姓名到MD5映射:")
    print(name_map)

if __name__ == "__main__":
    test() 
