/**
 * DOC智能解析器
 * 根据软件类型自动选择最佳解析策略
 */
class DocSmartParser {
  constructor() {
    this.detector = null;
    this.wordEnhancedParser = null;
    this.initializeDetector();
  }

  // 初始化检测器
  initializeDetector() {
    if (typeof DocSoftwareDetector !== 'undefined') {
      this.detector = new DocSoftwareDetector();
      console.log('软件检测器初始化成功');
    } else {
      console.warn('软件检测器未加载');
    }

    if (typeof WordEnhancedParser !== 'undefined') {
      this.wordEnhancedParser = new WordEnhancedParser();
      console.log('Word增强解析器初始化成功');
    } else {
      console.warn('Word增强解析器未加载');
    }
  }

  // 智能解析DOC文件
  async smartParse(arrayBuffer, fileName) {
    console.log('开始智能解析DOC文件:', fileName);

    try {
      // 第一步：检测软件类型
      const rawText = this.getRawText(arrayBuffer);
      const detection = this.detector ?
        this.detector.detectSoftware(arrayBuffer, rawText) :
        { software: 'unknown', recommendedStrategy: 'hybrid' };

      console.log(`检测结果: ${detection.software}`);

      // 第二步：获取解析策略
      const strategy = this.detector ?
        this.detector.getParsingStrategy(detection) :
        this.getDefaultStrategy();

      console.log(`使用策略: ${strategy.name}`);

      // 第三步：根据策略解析
      const parseResult = await this.parseWithStrategy(arrayBuffer, strategy, detection);

      // 第四步：生成报告
      const report = this.detector ?
        this.detector.generateDetectionReport(detection, strategy) :
        '检测器不可用';

      return {
        success: parseResult.success,
        text: parseResult.text,
        html: parseResult.html,
        method: `智能解析 (${detection.software})`,
        detection: detection,
        strategy: strategy.name,
        report: report
      };

    } catch (error) {
      console.error('智能解析失败:', error);
      return {
        success: false,
        text: '智能解析失败: ' + error.message,
        html: '<p>智能解析失败</p>',
        method: '智能解析失败'
      };
    }
  }

  // 根据策略解析
  async parseWithStrategy(arrayBuffer, strategy, detection) {
    const results = [];

    // 方法1: 特殊处理
    if (strategy.specialHandling !== 'standard') {
      const specialResult = await this.applySpecialHandling(arrayBuffer, strategy.specialHandling, detection);
      if (specialResult.text.length > 0) {
        results.push(specialResult);
      }
    }

    // 方法2: 多编码尝试（按策略顺序）
    for (const encoding of strategy.encodings) {
      try {
        const decoder = new TextDecoder(encoding, { fatal: false });
        const text = decoder.decode(arrayBuffer);
        const processed = this.processTextWithStrategy(text, strategy);

        if (processed.length > 10) {
          results.push({
            text: processed,
            score: this.scoreText(processed),
            method: `${encoding}编码`
          });
        }
      } catch (e) {
        // 忽略编码错误
      }
    }

    // 方法3: 字节偏移扫描
    for (const offset of strategy.byteOffsets) {
      const offsetResult = this.scanFromOffset(arrayBuffer, offset, strategy);
      if (offsetResult.text.length > 10) {
        results.push(offsetResult);
      }
    }

    // 选择最佳结果
    if (results.length > 0) {
      const bestResult = results.reduce((best, current) =>
        current.score > best.score ? current : best
      );

      return {
        success: true,
        text: bestResult.text,
        html: this.textToHtml(bestResult.text)
      };
    }

    return {
      success: false,
      text: '',
      html: ''
    };
  }

  // 应用特殊处理
  async applySpecialHandling(arrayBuffer, handlingType, detection) {
    switch (handlingType) {
      case 'wps_specific':
        return this.handleWPSSpecific(arrayBuffer, detection);
      case 'word_specific':
        return this.handleWordSpecific(arrayBuffer, detection);
      case 'comprehensive':
        return this.handleComprehensive(arrayBuffer, detection);
      default:
        return { text: '', score: 0, method: '无特殊处理' };
    }
  }

  // WPS特殊处理
  handleWPSSpecific(arrayBuffer, detection) {
    console.log('应用WPS特殊处理...');

    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';

    // WPS可能使用GB2312编码
    try {
      const decoder = new TextDecoder('gb2312', { fatal: false });
      text = decoder.decode(arrayBuffer);
    } catch (e) {
      // 如果GB2312失败，尝试UTF-16LE
      try {
        const decoder = new TextDecoder('utf-16le', { fatal: false });
        text = decoder.decode(arrayBuffer);
      } catch (e2) {
        text = '';
      }
    }

    // WPS特殊字符处理
    text = this.cleanWPSText(text);

    // 提取WPS特有信息
    const wpsInfo = this.extractWPSInfo(text);
    if (wpsInfo.length > 0) {
      text = wpsInfo + '\n\n' + text;
    }

    return {
      text: text,
      score: this.scoreText(text),
      method: 'WPS特殊处理'
    };
  }

  // Word特殊处理
  async handleWordSpecific(arrayBuffer, detection) {
    console.log('应用Word特殊处理...');

    // 优先使用Word增强解析器
    if (this.wordEnhancedParser) {
      try {
        console.log('使用Word增强解析器...');
        const enhancedResult = await this.wordEnhancedParser.parseWordDocument(arrayBuffer, 'document.doc');

        if (enhancedResult.success && enhancedResult.text.length > 10) {
          return {
            text: enhancedResult.text,
            score: this.scoreText(enhancedResult.text),
            method: 'Word增强解析器'
          };
        }
      } catch (enhancedError) {
        console.warn('Word增强解析器失败:', enhancedError.message);
      }
    }

    // 回退到传统Word处理
    let text = '';

    // Word通常使用UTF-16LE
    try {
      const decoder = new TextDecoder('utf-16le', { fatal: false });
      text = decoder.decode(arrayBuffer);
    } catch (e) {
      try {
        const decoder = new TextDecoder('utf-8', { fatal: false });
        text = decoder.decode(arrayBuffer);
      } catch (e2) {
        text = '';
      }
    }

    // Word特殊字符处理
    text = this.cleanWordText(text);

    // 提取Word特有信息
    const wordInfo = this.extractWordInfo(text);
    if (wordInfo.length > 0) {
      text = wordInfo + '\n\n' + text;
    }

    return {
      text: text,
      score: this.scoreText(text),
      method: 'Word传统处理'
    };
  }

  // 综合处理
  handleComprehensive(arrayBuffer, detection) {
    console.log('应用综合处理...');

    const wpsResult = this.handleWPSSpecific(arrayBuffer, detection);
    const wordResult = this.handleWordSpecific(arrayBuffer, detection);

    // 选择更好的结果
    if (wpsResult.score > wordResult.score) {
      return {
        text: wpsResult.text,
        score: wpsResult.score,
        method: '综合处理 (WPS优先)'
      };
    } else {
      return {
        text: wordResult.text,
        score: wordResult.score,
        method: '综合处理 (Word优先)'
      };
    }
  }

  // 清理WPS文本
  cleanWPSText(text) {
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fff\u0020-\u007E\s]/g, '') // 只保留中文和ASCII
      .replace(/\s{3,}/g, '  ') // 合并空格
      .replace(/(.)\1{5,}/g, '$1') // 移除重复字符
      .trim();
  }

  // 清理Word文本
  cleanWordText(text) {
    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fff\u0020-\u007E\s]/g, '') // 只保留中文和ASCII
      .replace(/\s{3,}/g, '  ') // 合并空格
      .trim();
  }

  // 提取WPS信息
  extractWPSInfo(text) {
    const info = [];

    if (text.includes('WPS Office')) {
      info.push('软件: WPS Office');
    }
    if (text.includes('专业版')) {
      info.push('版本: 专业版');
    }
    if (text.includes('KSOProductBuildVer')) {
      const versionMatch = text.match(/KSOProductBuildVer(\d+[\d\.\-]+)/);
      if (versionMatch) {
        info.push('构建版本: ' + versionMatch[1]);
      }
    }

    return info.join('\n');
  }

  // 提取Word信息
  extractWordInfo(text) {
    const info = [];

    if (text.includes('Microsoft Word')) {
      info.push('软件: Microsoft Word');
    }
    if (text.includes('Microsoft Office')) {
      info.push('套件: Microsoft Office');
    }

    const versionMatch = text.match(/(\d{2}\.\d+)/);
    if (versionMatch) {
      info.push('版本: ' + versionMatch[1]);
    }

    return info.join('\n');
  }

  // 从偏移量扫描
  scanFromOffset(arrayBuffer, offset, strategy) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';

    // UTF-16LE扫描
    for (let i = offset; i < uint8Array.length - 1; i += 2) {
      const char = uint8Array[i] | (uint8Array[i + 1] << 8);

      if ((char >= 0x4e00 && char <= 0x9fff) || (char >= 0x20 && char <= 0x7E)) {
        text += String.fromCharCode(char);
      } else if (char === 0x0D || char === 0x0A) {
        text += '\n';
      } else if (char === 0x09) {
        text += '\t';
      } else if (char === 0x20) {
        text += ' ';
      }

      if (text.length > 1000) break; // 限制长度
    }

    return {
      text: this.cleanText(text),
      score: this.scoreText(text),
      method: `偏移扫描 (0x${offset.toString(16)})`
    };
  }

  // 处理文本（根据策略）
  processTextWithStrategy(text, strategy) {
    let processed = text;

    // 应用模式匹配
    if (strategy.textPatterns && strategy.textPatterns.length > 0) {
      const matches = [];
      strategy.textPatterns.forEach(pattern => {
        const patternMatches = text.match(pattern) || [];
        matches.push(...patternMatches);
      });

      if (matches.length > 0) {
        processed = matches.join(' ') + '\n\n' + processed;
      }
    }

    return this.cleanText(processed);
  }

  // 获取原始文本
  getRawText(arrayBuffer) {
    try {
      const decoder = new TextDecoder('utf-8', { fatal: false });
      return decoder.decode(arrayBuffer);
    } catch (e) {
      return '';
    }
  }

  // 获取默认策略
  getDefaultStrategy() {
    return {
      name: '默认策略',
      encodings: ['utf-16le', 'utf-8', 'gb2312', 'windows-1252'],
      byteOffsets: [0x200, 0x400, 0x800, 0x1000],
      textPatterns: [],
      specialHandling: 'comprehensive'
    };
  }

  // 文本评分
  scoreText(text) {
    if (!text || typeof text !== 'string') return 0;

    let score = 0;

    // 基础长度分
    score += Math.min(text.length / 10, 50);

    // 中文字符加分
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    if (chineseChars) {
      score += chineseChars.length * 3;
    }

    // 有意义的英文单词加分
    const englishWords = text.match(/\b[a-zA-Z]{3,}\b/g);
    if (englishWords) {
      score += englishWords.length * 2;
    }

    // 软件特征加分
    if (text.includes('WPS') || text.includes('Word') || text.includes('Office')) {
      score += 20;
    }

    return score;
  }

  // 清理文本
  cleanText(text) {
    if (!text) return '';

    return text
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      .replace(/\s{3,}/g, '  ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  // 文本转HTML
  textToHtml(text) {
    if (!text) return '<p>无内容</p>';

    const paragraphs = text.split('\n\n');
    let html = '';

    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        html += `<p>${this.escapeHtml(paragraph.trim())}</p>`;
      }
    });

    return html || '<p>无法提取内容</p>';
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocSmartParser;
} else if (typeof window !== 'undefined') {
  window.DocSmartParser = DocSmartParser;
}
