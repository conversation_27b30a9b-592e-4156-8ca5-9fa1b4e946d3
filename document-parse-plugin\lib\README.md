# 第三方库文件

请将以下库文件放入此目录：

1. [xlsx.full.min.js](https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js) - Excel处理库
2. [mammoth.browser.min.js](https://cdn.jsdelivr.net/npm/mammoth@1.4.0/mammoth.browser.min.js) - Word处理库
3. [turndown.min.js](https://cdn.jsdelivr.net/npm/turndown@7.1.2/dist/turndown.min.js) - HTML到Markdown转换库

下载后请确保文件名完全一致。

## 功能说明

- **xlsx.full.min.js**: 用于读取和处理Excel文件(.xlsx, .xls)
- **mammoth.browser.min.js**: 用于读取和处理Word文档(.docx, .doc)
- **turndown.min.js**: 用于将HTML内容转换为Markdown格式，支持高质量的格式转换
- **table-formatter.js**: 专业表格格式化库，提供智能表格分析和美化功能
- **enhanced-markdown-formatter.js**: 🆕 增强Markdown格式化器，专注于结构化和AI友好的输出

## 表格格式化功能

**table-formatter.js** 提供以下高级功能：

### 🔍 智能数据分析
- **自动类型检测**: 数字、百分比、货币、日期、URL、邮箱等
- **数据统计**: 数值列的总和、平均值、最大最小值
- **完整度分析**: 空单元格统计和数据完整度计算

## 🆕 增强Markdown格式化功能

**enhanced-markdown-formatter.js** 是专门为AI友好和结构化输出设计的高级格式化器：

### 🎯 核心特性
- **🤖 AI优化**: 专门针对AI理解和处理优化的Markdown结构
- **📊 智能表格分析**: 自动识别表格类型（财务、时间安排、联系人、库存、绩效等）
- **🔍 高级数据类型检测**: 支持20+种数据类型的智能识别
- **📈 数据质量分析**: 完整度评估、数据密度分析、质量评级
- **💡 使用建议**: 基于表格内容自动生成使用建议和优化提示

### 📋 表格类型识别
- **💰 财务表格**: 自动识别包含金额、价格、费用等财务数据的表格
- **📅 时间安排表格**: 识别包含时间、日期、计划等时间相关的表格
- **👥 联系人表格**: 识别包含姓名、电话、邮箱等联系信息的表格
- **📦 库存表格**: 识别包含数量、库存、产品等库存管理的表格
- **📊 绩效表格**: 识别包含指标、完成率、目标等绩效数据的表格

### 🔢 数据类型检测
- **数字类型**: 整数、小数、科学计数法
- **货币类型**: 人民币(¥)、美元($)、欧元(€)、英镑(£)
- **百分比**: 百分比数据和比率数据
- **日期时间**: ISO格式、斜杠格式、中文格式、时间戳
- **联系信息**: 邮箱地址、手机号码、国际电话、固定电话
- **网络相关**: URL链接、IP地址
- **标识符**: 身份证号、产品编码、序列号

### 📊 输出格式特色
- **简洁清晰**: 去除不必要的图标和特殊符号，专注内容本身
- **结构化说明**: 详细的表格结构分析和列属性说明
- **智能建议**: 基于数据特征的使用建议和优化提示
- **统计摘要**: 数值列的统计信息（总和、平均值、范围等）
- **数据类型标注**: 明确的数据类型标识，便于理解和处理

### 🤖 AI友好特性
- **清晰的层次结构**: 使用标准Markdown标题层级组织内容
- **标准化表格格式**: 符合Markdown规范的表格格式，便于解析
- **元数据注释**: 丰富的表格元数据和说明信息
- **结构化描述**: 便于AI理解的表格特征描述
- **简洁的格式**: 避免过多装饰性元素，提高可读性和解析效率

### 📊 表格美化
- **智能对齐**: 根据数据类型自动设置列对齐方式
  - 数字/货币: 右对齐
  - 日期: 居中对齐
  - 文本: 左对齐
- **数值格式化**: 千分位分隔符、货币符号保留
- **列宽优化**: 自动计算最佳列宽，避免内容截断

### 📋 结构化输出
- **表格分析报告**: 数据维度、类型分布、统计信息
- **数据总结**: 数值统计、完整度分析
- **AI友好格式**: 标准化的Markdown表格，便于机器解析
