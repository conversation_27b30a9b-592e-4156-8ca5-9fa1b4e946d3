import cv2
import numpy as np


def segment_image_by_contours(
    image_path, min_contour_area=100, max_contour_area_ratio=0.5
):
    """
    Segments an image by detecting contours and drawing bounding boxes around them.

    Args:
        image_path (str): The path to the input image.
        min_contour_area (int): Minimum area for a contour to be considered.
        max_contour_area_ratio (float): Maximum area for a contour relative to image size.
                                        Used to filter out very large contours (e.g., page border).
    Returns:
        tuple: (original_image, image_with_contours)
               Returns None, None if the image cannot be loaded.
    """
    # 1. 加载图像
    # 1. Load the image
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"错误：无法加载图像，请检查路径： {image_path}")
        print(f"Error: Could not load image. Please check the path: {image_path}")
        return None, None

    print(f"图像加载成功，尺寸: {original_image.shape}")
    print(f"Image loaded successfully, dimensions: {original_image.shape}")

    # 创建一个副本用于绘制轮廓
    # Create a copy to draw contours on
    image_with_contours = original_image.copy()

    # 2. 预处理
    # 2. Preprocessing
    # 转换为灰度图
    # Convert to grayscale
    gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)

    # 高斯模糊以减少噪声，并帮助边缘检测
    # Apply Gaussian blur to reduce noise and help edge detection
    blurred_image = cv2.GaussianBlur(gray_image, (5, 5), 0)

    # 3. 边缘检测 (使用 Canny 算法)
    # 3. Edge Detection (using Canny algorithm)
    # Canny 算法的阈值可以根据图像特性调整
    # The thresholds for Canny can be adjusted based on image characteristics
    edged_image = cv2.Canny(blurred_image, 50, 150)
    # 您可以尝试不同的阈值, 例如:
    # You can try different thresholds, e.g.:
    # edged_image = cv2.Canny(blurred_image, 30, 200)

    print("边缘检测完成。")
    print("Edge detection complete.")

    # 4. 查找轮廓
    # 4. Find Contours
    # cv2.RETR_EXTERNAL 只检测外部轮廓
    # cv2.RETR_LIST 会检测所有轮廓，包括嵌套的
    # cv2.CHAIN_APPROX_SIMPLE 压缩水平、垂直和对角线段，只留下它们的端点
    # cv2.RETR_EXTERNAL retrieves only the extreme outer contours.
    # cv2.RETR_LIST retrieves all of the contours without establishing any hierarchical relationships.
    # cv2.CHAIN_APPROX_SIMPLE compresses horizontal, vertical, and diagonal segments and leaves only their end points.
    contours, hierarchy = cv2.findContours(
        edged_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )

    print(f"找到 {len(contours)} 个初始轮廓。")
    print(f"Found {len(contours)} initial contours.")

    # 5. 筛选轮廓并绘制边界框
    # 5. Filter contours and draw bounding boxes
    image_height, image_width = original_image.shape[:2]
    max_contour_area = image_width * image_height * max_contour_area_ratio

    valid_contours_count = 0
    for contour in contours:
        # 计算轮廓面积
        # Calculate contour area
        area = cv2.contourArea(contour)

        # 根据面积筛选轮廓
        # Filter contours based on area
        if min_contour_area < area < max_contour_area:
            valid_contours_count += 1
            # 获取边界框坐标
            # Get bounding box coordinates
            x, y, w, h = cv2.boundingRect(contour)

            # 在图像上绘制边界框 (绿色，厚度为2)
            # Draw bounding box on the image (green color, thickness 2)
            cv2.rectangle(image_with_contours, (x, y), (x + w, y + h), (0, 255, 0), 2)

    print(f"筛选后剩下 {valid_contours_count} 个有效轮廓。")
    print(f"Found {valid_contours_count} valid contours after filtering.")

    return original_image, image_with_contours


# --- 主程序 ---
if __name__ == "__main__":
    # 重要提示：请将下面的路径替换为您自己的图像文件路径
    # IMPORTANT: Replace the path below with the path to your own image file
    # 例如: image_file_path = "path/to/your/image.jpg"
    # e.g., image_file_path = "path/to/your/image.jpg"
    # 对于您提供的图片: "cdc250511204953254_1099894796_noise_20250522_002638.jpg"
    # For the image you provided: "cdc250511204953254_1099894796_noise_20250522_002638.jpg"
    image_file_path = (
        "D:\\work\\ps_check_data\\cdc250511204953254_1099894796_for_segment.jpeg"
    )
    # 如果图片不在脚本同目录下，请提供完整路径
    # If the image is not in the same directory as the script, provide the full path

    original, segmented = segment_image_by_contours(
        image_file_path, min_contour_area=50, max_contour_area_ratio=0.8
    )

    if original is not None and segmented is not None:
        # 显示结果
        # Display the results
        # 为了在不同环境下都能较好地显示，可以调整窗口大小
        # To display well in different environments, you can resize windows

        # 创建可调整大小的窗口
        # Create resizable windows
        cv2.namedWindow("Original Image", cv2.WINDOW_NORMAL)
        cv2.namedWindow("Segmented Image with Contours", cv2.WINDOW_NORMAL)

        cv2.imshow("Original Image", original)
        cv2.imshow("Segmented Image with Contours", segmented)

        print("请按任意键关闭图像窗口...")
        print("Press any key to close the image windows...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
