<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>JSZip DOC解析器测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .upload-area {
      border: 2px dashed #ccc;
      padding: 40px;
      text-align: center;
      border-radius: 10px;
      margin: 20px 0;
      cursor: pointer;
    }
    .upload-area:hover {
      border-color: #007bff;
      background-color: #f8f9ff;
    }
    .file-input {
      display: none;
    }
    .result-area {
      margin-top: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #f9f9f9;
      min-height: 200px;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .status.warning {
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    .content-preview {
      margin-top: 15px;
      padding: 15px;
      background: white;
      border-radius: 5px;
      border: 1px solid #ddd;
      max-height: 300px;
      overflow-y: auto;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #0056b3;
    }
    .library-status {
      margin-bottom: 20px;
      padding: 15px;
      background: #e9ecef;
      border-radius: 5px;
    }
    .library-item {
      margin: 5px 0;
      padding: 5px;
      border-radius: 3px;
    }
    .library-item.loaded {
      background: #d4edda;
      color: #155724;
    }
    .library-item.missing {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 JSZip DOC解析器测试</h1>

    <!-- 库状态检查 -->
    <div class="library-status">
      <h3>📚 库加载状态</h3>
      <div id="libraryStatus"></div>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-area" id="uploadArea">
      <p>📁 点击或拖拽.doc文件到这里</p>
      <p style="font-size: 14px; color: #666;">专门测试JSZip解析器</p>
      <input type="file" id="fileInput" class="file-input" accept=".doc">
    </div>

    <!-- 测试按钮 -->
    <div style="text-align: center;">
      <button onclick="testJSZipParser()">🧪 测试JSZip解析器</button>
      <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <!-- 结果显示区域 -->
    <div class="result-area" id="resultArea">
      <p style="color: #666; text-align: center;">等待文件上传或测试...</p>
    </div>
  </div>

  <!-- 加载必要的库 -->
  <script src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
  <script src="lib/doc-encoding-fixer.js"></script>
  <script src="lib/doc-jszip-parser.js"></script>

  <script>
    // 检查库加载状态
    function checkLibraryStatus() {
      const libraries = [
        { name: 'JSZip', check: () => typeof JSZip !== 'undefined' },
        { name: 'DocEncodingFixer', check: () => typeof DocEncodingFixer !== 'undefined' },
        { name: 'DocJSZipParser', check: () => typeof DocJSZipParser !== 'undefined' }
      ];

      const statusDiv = document.getElementById('libraryStatus');
      statusDiv.innerHTML = '';

      libraries.forEach(lib => {
        const div = document.createElement('div');
        div.className = `library-item ${lib.check() ? 'loaded' : 'missing'}`;
        div.textContent = `${lib.name}: ${lib.check() ? '✅ 已加载' : '❌ 未加载'}`;
        statusDiv.appendChild(div);
      });
    }

    // 初始化JSZip解析器
    let docJSZipParser = null;
    try {
      docJSZipParser = new DocJSZipParser();
      console.log('JSZip DOC解析器初始化成功');
    } catch (error) {
      console.error('JSZip DOC解析器初始化失败:', error);
    }

    // 文件上传处理
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const resultArea = document.getElementById('resultArea');

    uploadArea.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽支持
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#007bff';
      uploadArea.style.backgroundColor = '#f8f9ff';
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.style.borderColor = '#ccc';
      uploadArea.style.backgroundColor = '';
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#ccc';
      uploadArea.style.backgroundColor = '';
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFile(files[0]);
      }
    });

    function handleFileSelect(e) {
      const file = e.target.files[0];
      if (file) {
        handleFile(file);
      }
    }

    async function handleFile(file) {
      showStatus('info', `开始处理文件: ${file.name}`);

      try {
        const arrayBuffer = await file.arrayBuffer();
        const result = await parseDocFileWithJSZip(arrayBuffer, file.name);
        displayResult(result, file.name);
      } catch (error) {
        showStatus('error', `处理失败: ${error.message}`);
        console.error('文件处理错误:', error);
      }
    }

    async function parseDocFileWithJSZip(arrayBuffer, fileName) {
      if (!docJSZipParser) {
        throw new Error('JSZip DOC解析器未初始化');
      }

      showStatus('info', '使用JSZip解析器处理文件...');
      const result = await docJSZipParser.parseDocFile(arrayBuffer, fileName);

      if (result.success) {
        showStatus('success', `JSZip解析成功! 方法: ${result.method}`);
      } else {
        showStatus('warning', `JSZip解析失败: ${result.error || '未知错误'}`);
      }

      return result;
    }

    function displayResult(result, fileName) {
      const html = `
        <h3>📄 ${fileName} - JSZip解析结果</h3>

        <div style="margin: 15px 0; padding: 10px; background: ${result.success ? '#d4edda' : '#fff3cd'}; border-radius: 5px;">
          <strong>解析状态:</strong> ${result.success ? '✅ 成功' : '⚠️ 失败'}<br>
          <strong>使用方法:</strong> ${result.method || '未知'}<br>
          <strong>文本长度:</strong> ${result.text ? result.text.length : 0} 字符
        </div>

        <h4>📝 提取的文本内容</h4>
        <div class="content-preview">
          <pre style="white-space: pre-wrap; font-family: inherit;">${result.text || '无文本内容'}</pre>
        </div>

        <h4>🌐 HTML内容</h4>
        <div class="content-preview">
          ${result.html || '<p>无HTML内容</p>'}
        </div>

        <div style="margin-top: 15px;">
          <button onclick="copyToClipboard('${(result.text || '').replace(/'/g, "\\'")}')">📋 复制文本</button>
          <button onclick="downloadAsText('${fileName}', '${(result.text || '').replace(/'/g, "\\'")}')">💾 下载文本</button>
        </div>
      `;

      resultArea.innerHTML = html;
    }

    function showStatus(type, message) {
      const statusDiv = document.createElement('div');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;

      resultArea.insertBefore(statusDiv, resultArea.firstChild);

      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.parentNode.removeChild(statusDiv);
        }
      }, 5000);
    }

    function testJSZipParser() {
      showStatus('info', '开始测试JSZip解析器...');

      if (!docJSZipParser) {
        showStatus('error', 'JSZip DOC解析器未初始化');
        return;
      }

      // 创建一个包含测试文本的模拟DOC文件
      const testData = new Uint8Array(2048);

      // DOC文件头
      const docHeader = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
      docHeader.forEach((byte, index) => {
        testData[index] = byte;
      });

      // 添加测试文本
      const testText = "Hello World! 这是JSZip解析器测试。";
      const textOffset = 1024;

      for (let i = 0; i < testText.length; i++) {
        const charCode = testText.charCodeAt(i);
        testData[textOffset + i * 2] = charCode & 0xFF;
        testData[textOffset + i * 2 + 1] = (charCode >> 8) & 0xFF;
      }

      console.log('测试数据创建完成，开始解析...');

      docJSZipParser.parseDocFile(testData.buffer, 'test.doc')
        .then(result => {
          showStatus('success', '测试完成 - JSZip解析器工作正常');
          console.log('解析结果:', result);
          displayResult(result, 'test.doc');
        })
        .catch(error => {
          showStatus('error', `测试失败: ${error.message}`);
          console.error('测试错误:', error);
        });
    }

    function clearResults() {
      resultArea.innerHTML = '<p style="color: #666; text-align: center;">等待文件上传或测试...</p>';
    }

    function copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        showStatus('success', '文本已复制到剪贴板');
      }).catch(() => {
        showStatus('error', '复制失败，请手动选择文本复制');
      });
    }

    function downloadAsText(originalName, text) {
      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = originalName.replace('.doc', '.txt');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showStatus('success', '文件下载已开始');
    }

    // 页面加载完成后检查库状态
    window.addEventListener('load', () => {
      checkLibraryStatus();
      showStatus('info', '页面加载完成，可以开始测试JSZip DOC解析器');
    });
  </script>
</body>
</html>
