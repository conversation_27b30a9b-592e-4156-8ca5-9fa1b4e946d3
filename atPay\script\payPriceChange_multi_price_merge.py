from typing import Optional, Dict, Any


def format_block_info(third_reason: str, reason: str, reason_desc: str) -> dict:
    """将三个原始参数格式化为包含 block 和 blockDesc 的对象

    Args:
        third_reason: 第三方原因（备选原因）
        reason: 主原因（优先使用）
        reason_desc: 原因描述

    Returns:
        包含统一字段名的字典，结构示例：
        {
            "block": "具体原因",
            "blockDesc": "详细描述"
        }
    """
    # 清理并标准化输入参数
    reason_clean = (reason or "").strip()
    third_reason_clean = (third_reason or "").strip()
    reason_desc_clean = (reason_desc or "").strip()

    # 根据业务规则选择 block 的值
    block_value = reason_clean if reason_clean else third_reason_clean

    return {"block": block_value, "blockDesc": reason_desc_clean}


def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """主处理流程"""
    if "needInvokeAi" in param and not param.get("needInvokeAi"):
        return {
            "block": param.get("thirdReason", ""),
            "blockDesc": "",
        }
    # 获取原始数据
    return format_block_info(
        third_reason=param.get("thirdReason", ""),  # 改为下划线命名
        reason=param.get("reason", ""),
        reason_desc=param.get("reasonDesc", ""),  # 改为下划线命名
    )
