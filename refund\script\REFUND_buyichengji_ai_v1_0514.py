import json
import datetime
from typing import Any, Tuple, Optional, Dict, List


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj == ""
    return False


def parse_to_dict(data: Any) -> Tuple[bool, Optional[Dict], str]:
    """
    将输入数据解析为字典格式
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否成功, 解析后的字典, 错误信息)
    """
    try:
        # 如果是字符串，尝试JSON解析
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                return False, None, "JSON解析失败"

        # 如果是字典，直接返回
        if isinstance(data, dict):
            return True, data, ""

        # 如果是列表，查找包含inductionInfo的元素
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and "inductionInfo" in item:
                    return True, item, ""
            return False, None, "列表中未找到包含inductionInfo的元素"

        return False, None, f"不支持的数据类型: {type(data)}"
    except Exception as e:
        return False, None, f"解析异常: {str(e)}"


def is_date_in_range(date_str: str, time_range: str) -> bool:
    """
    检查日期是否在指定的时间范围内
    Args:
        date_str: 日期字符串，格式为 yyyy-mm-dd hh:mm:ss
        time_range: 时间范围字符串，格式为 "yyyy-mm-dd，yyyy-mm-dd" 或 "yyyy-mm-dd,yyyy-mm-dd"
    Returns:
        bool: 日期是否在时间范围内
    """
    try:
        # 解析输入日期
        if isinstance(date_str, datetime.datetime):
            date_obj = date_str
        else:
            date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        
        # 解析时间范围，支持中文逗号和英文逗号
        if "，" in time_range:
            range_dates = time_range.split("，")
        else:
            range_dates = time_range.split(",")
            
        if len(range_dates) != 2:
            return False
        
        start_date = datetime.datetime.strptime(range_dates[0].strip(), "%Y-%m-%d")
        end_date = datetime.datetime.strptime(range_dates[1].strip(), "%Y-%m-%d")
        
        # 调整结束日期到当天的最后一刻
        end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # 检查日期是否在范围内
        return start_date <= date_obj <= end_date
    except Exception as e:
        print(f"日期范围检查异常: {e}")
        return False


def validate_dict_structure(data: Dict) -> Tuple[bool, Optional[Dict], str]:
    """
    验证字典结构是否符合要求
    Args:
        data: 输入字典
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    try:
        if data is None:
            return False, None, "大模型返回数据为空"
        # 检查字典有效性
        if not isinstance(data, dict):
            return False, None, "大模型返回数据不是有效的字典"

        # 检查inductionInfo字段
        if "inductionInfo" not in data:
            return False, None, "大模型返回数据缺少inductionInfo字段"

        induction_info = data["inductionInfo"]
        if not isinstance(induction_info, dict):
            return False, None, "大模型返回数据inductionInfo不是有效的字典"

        # 检查必需字段
        if "not_suitable_for_flying" not in induction_info or is_deep_empty(
            induction_info.get("not_suitable_for_flying")
        ):
            return False, None, "大模型返回数据缺少not_suitable_for_flying字段"

        # 设置默认值
        induction_info.setdefault("not_suitable_for_flying", "-")
        induction_info.setdefault("not_suitable_timeRange", "-")


        return True, data, ""
    except Exception as e:
        return False, None, f"大模型返回数据格式/内容验证异常: {str(e)}"


def check_data_structure(data: Any) -> Tuple[bool, Optional[Dict], str]:
    """
    检查数据结构并进行解析验证
    Args:
        data: 输入数据，可能是字典、列表或字符串
    Returns:
        Tuple[bool, Optional[Dict], str]: (是否有效, 处理后的字典, 错误信息)
    """
    if data is None:
        return False, None, "大模型返回数据为空"
    # 第一步：解析成字典
    success, result_dict, error_msg = parse_to_dict(data)
    if not success:
        return False, None, error_msg

    # 第二步：验证字典结构
    return validate_dict_structure(result_dict)


def get_earliest_flight_time(airline_date: List[str]) -> Optional[datetime.datetime]:
    """
    获取最早的航班时间
    
    Args:
        airline_date: 航班时间列表，格式如 ["2025-04-15 16:35-18:30", "2025-04-16 12:20-16:55"]
    
    Returns:
        datetime.datetime: 最早的航班时间
    """
    if not airline_date:
        return None
        
    earliest_time = None
    for flight_time in airline_date:
        try:
            # 先找到日期和时间的部分（格式为：yyyy-MM-dd HH:mm）
            datetime_part = flight_time.split(" ")[0] + " " + flight_time.split(" ")[1].split("-")[0]
            # 解析时间
            flight_datetime = datetime.datetime.strptime(datetime_part, "%Y-%m-%d %H:%M")
            if earliest_time is None or flight_datetime < earliest_time:
                earliest_time = flight_datetime
        except Exception as e:
            print(f"解析航班时间异常: {e}")
            continue
            
    return earliest_time


def main(param: dict) -> dict:
    try:
        alResult = param.get("alResult")
        airlineDate = param.get("airlineDate", "")

        # 处理airlineDate
        if isinstance(airlineDate, str) and airlineDate:
            airlineDate = [date.strip() for date in airlineDate.split(',') if date.strip()]
        else:
            airlineDate = []

        # 获取最早的航班时间
        earliest_flight_time = get_earliest_flight_time(airlineDate)

        success, result_dict, error_msg = check_data_structure(alResult)
        if not success:
            return {
                "aIResult": {
                    "inductionInfo": {
                    "not_suitable_for_flying": "",
                    "not_suitable_timeRange": "",
                    "date_treatment": "",
                    "issuance_time": "",
                    "admission_time": "",
                    "hospital_grade": "",
                    "county_level_or_above_hospital": ""
                    }
                },
                "status": "失败",
                "errMsg": error_msg,
            }
        else:
            induction_info = result_dict["inductionInfo"]
            time_range = induction_info.get("not_suitable_timeRange", "-")
            
            # 检查时间范围是否为空且airlineDate是否提供
            if time_range != "-" and earliest_flight_time:
                # 将datetime对象转换为字符串格式，以便与时间范围进行比较
                earliest_flight_str = earliest_flight_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 根据是否在时间范围内设置not_suitable_for_flying
                if is_date_in_range(earliest_flight_time, time_range):
                    induction_info["not_suitable_for_flying"] = "是"
                else:
                    induction_info["not_suitable_for_flying"] = "否"
            
            return {
                "aIResult": result_dict,
                "status": "成功",
                "errMsg": "大模型返回数据验证成功",
            }
    except KeyError as e:
        # 处理字典中缺少必需键的情况
        result = {
            "aIResult": {
                "inductionInfo": {
                    "not_suitable_for_flying": "",
                    "not_suitable_timeRange": "",
                    "date_treatment": "",
                    "issuance_time": "",
                    "admission_time": "",
                    "hospital_grade": "",
                    "county_level_or_above_hospital": ""
                }
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result
    except Exception as e:
        # 处理其他所有异常
        result = {
            "aIResult": {
                "inductionInfo": {
                    "not_suitable_for_flying": "",
                    "not_suitable_timeRange": "",
                    "date_treatment": "",
                    "issuance_time": "",
                    "admission_time": "",
                    "hospital_grade": "",
                    "county_level_or_above_hospital": ""
                }
            },
            "status": "失败",
            "errMsg": f"检查大模型返回数据结构异常: {e}",
        }
        return result

# 测试代码
if __name__ == "__main__":
    # 测试用例
    test_data = {
        "alResult": {
            "inductionInfo": {
                "not_suitable_for_flying": "是",
                "not_suitable_timeRange": "2025-05-09,2025-05-15",
                "date_treatment": "2025-05-09",
                "issuance_time": "2025-05-09",
                "admission_time": "2025-05-09",
                "hospital_grade": "三级甲等",
                "county_level_or_above_hospital": "是"
            }
        },
        "airlineDate": "2025-05-10 22:25-00:40,2025-05-12 07:50-10:05"
    }
    
    # 运行测试
    result = main(test_data)
    print("测试结果:", result)
    
    # 验证结果
    is_correct = result["aIResult"]["inductionInfo"]["not_suitable_for_flying"] == "是"
    print(f"结果验证: {'成功' if is_correct else '失败'}")
