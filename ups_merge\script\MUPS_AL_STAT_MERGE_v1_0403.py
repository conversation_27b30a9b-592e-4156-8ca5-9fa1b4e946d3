from collections import defaultdict
import copy
import traceback
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from datetime import datetime
from string import Formatter
import re
from typing import Union


def parse_search_time(time_str: str) -> datetime:
    """统一处理带空格的时间格式"""
    # 替换空格为 'T' 使其符合 ISO 格式
    return datetime.fromisoformat(time_str.replace(" ", "T"))


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串

    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(
            obj,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe="", encoding="utf-8", errors="strict")
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "Missing required parameter: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


supported_scenes = {"多次搜索", "支付时变价", "页面间变价-D2B", "页面间变价-S2D"}
# 定义场景优先级
scenarioPriority = {
    "支付时变价": 3,
    "多次搜索": 2,
    "页面间变价-D2B": 1,
    "页面间变价-S2D": 0,
}


def doAlSceneBriefDataMerge(
    alScene, callBackData, oirAlRelateData
) -> Tuple[bool, str, dict]:
    """
    根据alScene调用具体的策略merge结论数据

    Args:
        alScene: 分析场景
        callBackData: 回调数据
        oirAlRelateData: 原始分析相关数据

    Returns:
        Tuple[bool, str, dict]: (是否成功, 错误信息, 合并后的场景简要数据)
    """
    try:
        # 根据不同的场景调用不同的合并策略
        if alScene == "多次搜索":
            return mergeMultipleSearchData(callBackData, oirAlRelateData)
        elif alScene == "支付时变价":
            return mergePaymentPriceChangeData(callBackData, oirAlRelateData)
        elif alScene == "页面间变价-D2B":
            return mergePagePriceChangeData(callBackData, oirAlRelateData)
        elif alScene == "页面间变价-S2D":
            return mergePagePriceS2DChangeData(callBackData, oirAlRelateData)
        else:
            return False, f"不支持的分析场景: {alScene}", {}
    except Exception as e:
        return False, f"生成串联数据结果发生异常: {str(e)}", {}


def checkAndUpdateGlobalCtrlScene(briefData, alScene, canCtrl):
    """
    检查并更新全局可控类型和可控变价场景字段

    Args:
        briefData: 简要数据字典
        alScene: 当前分析场景
        canCtrl: 是否可控场景

    Returns:
        None
    """
    if canCtrl == "是":
        # 获取当前场景
        # 检查是否可控类型是否为空或特定值
        currentControllableType = briefData.get("是否可控类型", "")
        if not currentControllableType or currentControllableType in [
            "-",
            "否",
            "未知",
        ]:
            briefData["是否可控类型"] = "是"
            briefData["可控变价场景"] = alScene
        else:
            # 根据可控变价场景的值决定是否回写
            currentScenario = briefData.get("可控变价场景", "")

            # 获取当前场景和新场景的优先级
            currentPriority = scenarioPriority.get(currentScenario, 0)
            newPriority = scenarioPriority.get(alScene, 0)

            # 只有新场景优先级更高时才回写
            if newPriority > currentPriority:
                briefData["是否可控类型"] = "是"
                briefData["可控变价场景"] = alScene
    else:
        currentControllableType = briefData.get("是否可控类型", "")
        if not currentControllableType or currentControllableType in [
            "-",
            "未知",
        ]:
            briefData["是否可控类型"] = "否"
            briefData["可控变价场景"] = "-"


def mergeMultipleSearchData(callBackData, oirAlRelateData) -> Tuple[bool, str, dict]:
    """合并多次搜索数据"""
    try:
        # 提取必要的数据
        alResultHead = callBackData.get("alResultHead", {})
        alResultBody = callBackData.get("alResultBody", {})

        # 构建简要数据
        briefData = copy.deepcopy(oirAlRelateData)

        # 检查alResultHead是否有效
        if not alResultHead or not isinstance(alResultHead, dict):
            return False, "alResultHead无效", briefData

        # 解析encodeJoinData
        encodeJoinData = alResultBody.get("encodeJoinData", "")
        parsedData = []
        if encodeJoinData:
            parsedData, _ = parse_urlencoded_structured_data(
                alResultBody, "encodeJoinData"
            )

        # 一、回写多次搜索分析字段
        # 1. 根据alResultHead可回写的字段
        briefData["多次搜索-分析标识"] = "已分析"

        # 处理多次搜索-无需归因原因
        needExecAl = alResultHead.get("needExecAl", "")
        notExecAlReason = alResultHead.get("notExecAlReason", "")
        processStage = alResultHead.get("processStage", "")

        if needExecAl == "否" and notExecAlReason:
            briefData["多次搜索-无需归因原因"] = (
                f"阶段:{processStage}, 不分析原因：{notExecAlReason}"
            )
        elif needExecAl == "是" and not notExecAlReason:
            # 检查encodeJoinData解析内容是否为空或所有元素的needExecAl=否
            allNeedExecAlNo = True
            if parsedData:
                for item in parsedData:
                    if item.get("needExecAl") != "否":
                        allNeedExecAlNo = False
                        break

            if not parsedData or allNeedExecAlNo:
                briefData["多次搜索-无需归因原因"] = (
                    "阶段:变价数据分组和过滤, 不分析原因：无需归因的变价数据"
                )

        # 2. 回写需要encodeJoinData才能回写的字段
        if not parsedData:
            # 如果encodeJoinData解析内容为空
            briefData["多次搜索-AI定责说明"] = "平台无责"
            briefData["多次搜索-AI变价原因归因"] = "-"
            briefData["多次搜索-AI变价判断描述"] = "-"
            briefData["多次搜索-是否可控场景"] = "-"
            briefData["多次搜索-一级原因"] = "-"
            briefData["多次搜索-二级原因"] = "-"
        else:
            # 按照needExecAl=是进行过滤
            needAlDetails = [
                item for item in parsedData if item.get("needExecAl") == "是"
            ]

            if not needAlDetails:
                # needAlDetails为空
                briefData["多次搜索-AI定责说明"] = "平台无责"
                briefData["多次搜索-AI变价原因归因"] = "-"
                briefData["多次搜索-AI变价判断描述"] = "-"
                briefData["多次搜索-是否可控场景"] = "-"
                briefData["多次搜索-一级原因"] = "-"
                briefData["多次搜索-二级原因"] = "-"
            else:
                # 按照attributeResp包含"平台策略"过滤
                platRespDetails = [
                    item
                    for item in needAlDetails
                    if "平台策略" == item.get("attributeFirstLevel", "")
                    or "平台责任" == item.get("attributeFirstLevel", "")
                ]

                if platRespDetails:
                    # platRespDetails不为空
                    briefData["多次搜索-AI定责说明"] = "平台有责"
                    briefData["多次搜索-一级原因"] = json.dumps(
                        [
                            item.get("attributeFirstLevel", "")
                            for item in platRespDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-AI变价判断描述"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-是否可控场景"] = "是"
                else:
                    # platRespDetails为空
                    briefData["多次搜索-AI定责说明"] = "平台无责"
                    briefData["多次搜索-一级原因"] = json.dumps(
                        [item.get("attributeFirstLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-AI变价判断描述"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["多次搜索-是否可控场景"] = "否"

                    # 定义attributeResp的优先级顺序
                    resp_priority = {
                        "平台责任": 1,
                        "疑似平台责任": 2,
                        "平台无责-用户行为导致": 3,
                        "平台无责-航班基础数据变化": 4,
                        "其他": 5,
                        "平台无责-未变价": 6,
                    }

                    # 从needAlDetails中找出最高优先级的attributeResp
                    highest_priority_resp = None
                    highest_priority = float("inf")

                    for item in needAlDetails:
                        resp = item.get("attributeResp", "")
                        if (
                            resp in resp_priority
                            and resp_priority[resp] < highest_priority
                        ):
                            highest_priority = resp_priority[resp]
                            highest_priority_resp = resp

                    if highest_priority_resp:
                        briefData["多次搜索-AI定责说明"] = highest_priority_resp

        # 二、回写是否可控类型和可控变价场景字段
        alScene = alResultHead.get("alScene", "")
        canCtrl = briefData.get("多次搜索-是否可控场景", "")
        checkAndUpdateGlobalCtrlScene(briefData, alScene, canCtrl)

        return True, "", briefData
    except Exception as e:
        return False, f"合并多次搜索数据时发生异常: {str(e)}", {}


def mergePaymentPriceChangeData(
    callBackData, oirAlRelateData
) -> Tuple[bool, str, dict]:
    """合并支付时变价数据"""
    try:
        # 提取必要的数据
        alResultHead = callBackData.get("alResultHead", {})

        # 构建简要数据
        briefData = copy.deepcopy(oirAlRelateData)

        # 检查alResultHead是否有效
        if not alResultHead or not isinstance(alResultHead, dict):
            return False, "alResultHead无效", briefData

        # 一、回写支付时场景分析字段
        # 1. 根据alResultHead可回写的字段
        briefData["支付时场景-分析标识"] = "已分析"

        # 处理多次搜索-无需归因原因
        firstLevelResp = alResultHead.get("firstLevelResp", "")
        sceLevelResp = alResultHead.get("sceLevelResp", "")
        thirdLevelResp = alResultHead.get("thirdLevelResp", "")

        briefData["支付分析-一级原因"] = firstLevelResp
        briefData["支付分析-二级原因"] = sceLevelResp
        briefData["支付分析-三级原因"] = thirdLevelResp

        if firstLevelResp and "点击支付时-平台有责" in firstLevelResp:
            briefData["支付分析-是否可控场景"] = "是"

        # 二、回写是否可控类型和可控变价场景字段
        alScene = alResultHead.get("alScene", "")
        canCtrl = briefData.get("支付分析-是否可控场景", "")
        checkAndUpdateGlobalCtrlScene(briefData, alScene, canCtrl)
        return True, "", briefData
    except Exception as e:
        return False, f"合并支付时变价数据时发生异常: {str(e)}", {}


def mergePagePriceChangeData(callBackData, oirAlRelateData) -> Tuple[bool, str, dict]:
    """合并不同页面间变价数据"""
    try:
        # 提取必要的数据
        alResultHead = callBackData.get("alResultHead", {})
        alResultBody = callBackData.get("alResultBody", {})

        # 构建简要数据
        briefData = copy.deepcopy(oirAlRelateData)

        # 检查alResultHead是否有效
        if not alResultHead or not isinstance(alResultHead, dict):
            return False, "alResultHead无效", briefData

        # 一、回写页面间变价分析字段
        # 1. 根据alResultHead可回写的字段
        briefData["页面间变价-分析标识"] = "已分析"

        # 处理页面间变价-无需归因原因
        needExecAl = alResultHead.get("needExecAl", "")
        notExecAlReason = alResultHead.get("notExecAlReason", "")
        processStage = alResultHead.get("processStage", "")

        if needExecAl == "否" and notExecAlReason:
            briefData["页面间变价-无需归因原因"] = (
                f"阶段:{processStage}, 不分析原因：{notExecAlReason}"
            )
        elif needExecAl == "是" and not notExecAlReason:
            # 检查encodeJoinData解析内容是否为空或所有元素的needExecAl=否
            encodeJoinData = alResultBody.get("encodeJoinData", "")
            parsedData = []
            if encodeJoinData:
                parsedData, _ = parse_urlencoded_structured_data(
                    alResultBody, "encodeJoinData"
                )

            allNeedExecAlNo = True
            if parsedData:
                for item in parsedData:
                    if item.get("needExecAl") != "否":
                        allNeedExecAlNo = False
                        break

            if not parsedData or allNeedExecAlNo:
                briefData["页面间变价-无需归因原因"] = (
                    "阶段:变价数据分组和过滤, 不分析原因：无需归因的变价数据"
                )

        # 2. 回写需要encodeJoinData才能回写的字段
        if not parsedData:
            # 如果encodeJoinData解析内容为空
            briefData["页面间变价-AI定责"] = "平台无责"
            briefData["页面间变价-一级原因"] = "-"
            briefData["页面间变价-二级原因"] = "-"
            briefData["页面间变价-三级原因"] = "-"
            briefData["页面间变价-AI变价原因归因"] = "-"
            briefData["页面间变价-AI变价判断说明"] = "-"
            briefData["页面间变价-是否可控"] = "-"
        else:
            # 按照needExecAl=是进行过滤
            needAlDetails = [
                item for item in parsedData if item.get("needExecAl") == "是"
            ]

            if not needAlDetails:
                # needAlDetails为空
                briefData["页面间变价-AI定责"] = "平台无责"
                briefData["页面间变价-一级原因"] = "-"
                briefData["页面间变价-二级原因"] = "-"
                briefData["页面间变价-三级原因"] = "-"
                briefData["页面间变价-AI变价原因归因"] = "-"
                briefData["页面间变价-AI变价判断说明"] = "-"
                briefData["页面间变价-是否可控"] = "-"
            else:
                # 按照attributeResp包含"平台策略"过滤
                platRespDetails = [
                    item
                    for item in needAlDetails
                    if "平台策略" in item.get("attributeResp", "")
                    or "平台责任" in item.get("attributeResp", "")
                ]

                if platRespDetails:
                    # platRespDetails不为空
                    briefData["页面间变价-AI定责"] = "平台有责"
                    briefData["页面间变价-一级原因"] = json.dumps(
                        [
                            item.get("attributeFirstLevel", "")
                            for item in platRespDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    # 当三级原因为空时，使用二级原因
                    briefData["页面间变价-三级原因"] = json.dumps(
                        [
                            item.get("attributeThirdLevel", "")
                            or item.get("attributeSecLevel", "")
                            for item in platRespDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-AI变价判断说明"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-是否可控"] = "是"
                else:
                    # platRespDetails为空
                    briefData["页面间变价-AI定责"] = "平台无责"
                    briefData["页面间变价-一级原因"] = json.dumps(
                        [item.get("attributeFirstLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    # 当三级原因为空时，使用二级原因
                    briefData["页面间变价-三级原因"] = json.dumps(
                        [
                            item.get("attributeThirdLevel", "")
                            or item.get("attributeSecLevel", "")
                            for item in needAlDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-AI变价判断说明"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["页面间变价-是否可控"] = "否"

                    # 定义attributeResp的优先级顺序
                    resp_priority = {
                        "平台责任": 1,
                        "疑似平台责任": 2,
                        "平台无责-用户行为导致": 3,
                        "平台无责-航班基础数据变化": 4,
                        "其他": 5,
                        "平台无责-未变价": 6,
                    }

                    # 从needAlDetails中找出最高优先级的attributeResp
                    highest_priority_resp = None
                    highest_priority = float("inf")

                    for item in needAlDetails:
                        resp = item.get("attributeResp", "")
                        if (
                            resp in resp_priority
                            and resp_priority[resp] < highest_priority
                        ):
                            highest_priority = resp_priority[resp]
                            highest_priority_resp = resp

                    if highest_priority_resp:
                        briefData["页面间变价-AI定责"] = highest_priority_resp

        # 二、回写是否可控类型和可控变价场景字段
        alScene = alResultHead.get("alScene", "")
        canCtrl = briefData.get("页面间变价-是否可控", "")
        checkAndUpdateGlobalCtrlScene(briefData, alScene, canCtrl)

        return True, "", briefData
    except Exception as e:
        return False, f"合并不同页面间变价数据时发生异常: {str(e)}", {}


def mergePagePriceS2DChangeData(
    callBackData, oirAlRelateData
) -> Tuple[bool, str, dict]:
    """合并S2D页面间变价数据"""
    try:
        # 提取必要的数据
        alResultHead = callBackData.get("alResultHead", {})
        alResultBody = callBackData.get("alResultBody", {})

        # 构建简要数据
        briefData = copy.deepcopy(oirAlRelateData)

        # 检查alResultHead是否有效
        if not alResultHead or not isinstance(alResultHead, dict):
            return False, "alResultHead无效", briefData

        # 一、回写S2D页面间变价分析字段
        # 1. 根据alResultHead可回写的字段
        briefData["S2D-分析标识"] = "已分析"

        # 处理S2D-无需归因说明
        needExecAl = alResultHead.get("needExecAl", "")
        notExecAlReason = alResultHead.get("notExecAlReason", "")
        processStage = alResultHead.get("processStage", "")

        if needExecAl == "否" and notExecAlReason:
            briefData["S2D-无需归因说明"] = (
                f"阶段:{processStage}, 不分析原因：{notExecAlReason}"
            )
        elif needExecAl == "是" and not notExecAlReason:
            # 检查encodeJoinData解析内容是否为空或所有元素的needExecAl=否
            encodeJoinData = alResultBody.get("encodeJoinData", "")
            parsedData = []
            if encodeJoinData:
                parsedData, _ = parse_urlencoded_structured_data(
                    alResultBody, "encodeJoinData"
                )

            allNeedExecAlNo = True
            if parsedData:
                for item in parsedData:
                    if item.get("needExecAl") != "否":
                        allNeedExecAlNo = False
                        break

            if not parsedData or allNeedExecAlNo:
                briefData["S2D-无需归因说明"] = (
                    "阶段:变价数据分组和过滤, 不分析原因：无需归因的变价数据"
                )

        # 2. 回写需要encodeJoinData才能回写的字段
        if not parsedData:
            # 如果encodeJoinData解析内容为空
            briefData["S2D-AI定责"] = "平台无责"
            briefData["S2D-一级原因"] = "-"
            briefData["S2D-二级原因"] = "-"
            briefData["S2D-三级原因"] = "-"
            briefData["S2D-AI变价原因归因"] = "-"
            briefData["S2D-AI变价描述"] = "-"
            briefData["S2D-是否可控"] = "-"
        else:
            # 按照needExecAl=是进行过滤
            needAlDetails = [
                item for item in parsedData if item.get("needExecAl") == "是"
            ]

            if not needAlDetails:
                # needAlDetails为空
                briefData["S2D-AI定责"] = "平台无责"
                briefData["S2D-一级原因"] = "-"
                briefData["S2D-二级原因"] = "-"
                briefData["S2D-三级原因"] = "-"
                briefData["S2D-AI变价原因归因"] = "-"
                briefData["S2D-AI变价描述"] = "-"
                briefData["S2D-是否可控"] = "-"
            else:
                # 按照attributeResp包含"平台策略"过滤
                platRespDetails = [
                    item
                    for item in needAlDetails
                    if "平台策略" == item.get("attributeResp", "")
                    or "平台责任" == item.get("attributeResp", "")
                ]

                if platRespDetails:
                    # platRespDetails不为空
                    briefData["S2D-AI定责"] = "平台有责"
                    briefData["S2D-一级原因"] = json.dumps(
                        [
                            item.get("attributeFirstLevel", "")
                            for item in platRespDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["S2D-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    # 当三级原因为空时，使用二级原因
                    briefData["S2D-三级原因"] = json.dumps(
                        [
                            item.get("attributeThirdLevel", "")
                            or item.get("attributeSecLevel", "")
                            for item in platRespDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["S2D-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["S2D-AI变价描述"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in platRespDetails],
                        ensure_ascii=False,
                    )
                    briefData["S2D-是否可控"] = "是"
                else:
                    # platRespDetails为空
                    briefData["S2D-AI定责"] = "平台无责"
                    briefData["S2D-一级原因"] = json.dumps(
                        [item.get("attributeFirstLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["S2D-二级原因"] = json.dumps(
                        [item.get("attributeSecLevel", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    # 当三级原因为空时，使用二级原因
                    briefData["S2D-三级原因"] = json.dumps(
                        [
                            item.get("attributeThirdLevel", "")
                            or item.get("attributeSecLevel", "")
                            for item in needAlDetails
                        ],
                        ensure_ascii=False,
                    )
                    briefData["S2D-AI变价原因归因"] = json.dumps(
                        [item.get("attributeReason", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["S2D-AI变价描述"] = json.dumps(
                        [item.get("priceChageDesc", "") for item in needAlDetails],
                        ensure_ascii=False,
                    )
                    briefData["S2D-是否可控"] = "否"

                    # 定义attributeResp的优先级顺序
                    resp_priority = {
                        "平台责任": 1,
                        "疑似平台责任": 2,
                        "平台无责-用户行为导致": 3,
                        "平台无责-航班基础数据变化": 4,
                        "其他": 5,
                        "平台无责-未变价": 6,
                    }

                    # 从needAlDetails中找出最高优先级的attributeResp
                    highest_priority_resp = None
                    highest_priority = float("inf")

                    for item in needAlDetails:
                        resp = item.get("attributeResp", "")
                        if (
                            resp in resp_priority
                            and resp_priority[resp] < highest_priority
                        ):
                            highest_priority = resp_priority[resp]
                            highest_priority_resp = resp

                    if highest_priority_resp:
                        briefData["S2D-AI定责"] = highest_priority_resp

        # 二、回写是否可控类型和可控变价场景字段
        alScene = alResultHead.get("alScene", "")
        canCtrl = briefData.get("S2D-是否可控", "")
        checkAndUpdateGlobalCtrlScene(briefData, alScene, canCtrl)

        return True, "", briefData
    except Exception as e:
        return False, f"合并S2D页面间变价数据时发生异常: {str(e)}", {}


def isValidAlScene(alScene, alResultHead) -> Tuple[bool, str]:
    """
    验证alScene参数是否有效

    Args:
        alScene: 要验证的alScene参数，字符串类型
        alResultHead: 分析结果头部信息

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    # 支持的分析场景

    # 检查alScene是否为空
    if not alScene:
        return False, "alScene参数为空"

    # 检查alScene是否为字符串类型
    if not isinstance(alScene, str):
        return False, f"alScene参数类型错误，期望str，实际{type(alScene)}"

    # 检查alScene是否为支持的分析场景
    if alScene in supported_scenes:
        return True, ""
    else:
        return False, f"不支持的回调场景: {alScene}"


def main(param: dict) -> dict:
    try:
        callBackData = safe_json_parse(param.get("callBackData"))

        oirAlRelateData = safe_json_parse(param.get("alRelateData"))
        username = param.get("username")

        if not oirAlRelateData or not isinstance(oirAlRelateData, dict):
            return {
                "username": username,
                "errorMsg": "请求参数中alRelateData字段解析失败",
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        if not callBackData or not isinstance(callBackData, dict):
            return {
                "username": username,
                "errorMsg": "请求参数中callBackData字段解析失败",
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        alResultHead = callBackData.get("alResultHead")
        if not alResultHead or not isinstance(alResultHead, dict):
            return {
                "username": username,
                "errorMsg": "请求参数中alResultHead字段解析失败",
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        alScene = alResultHead.get("alScene")
        validScene, msg = isValidAlScene(alScene, alResultHead)
        if not validScene:
            return {
                "username": username,
                "errorMsg": msg,
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        isSuccess, errorMsg, alSceneBriefData = doAlSceneBriefDataMerge(
            alScene, callBackData, oirAlRelateData
        )
        if not isSuccess:
            return {
                "username": username,
                "errorMsg": errorMsg,
                "isSuccess": "false",
                "alSceneBriefData": {},
            }

        return {
            "username": username,
            "isSuccess": "true",
            "alSceneBriefData": alSceneBriefData,
            "errorMsg": "",
        }
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"聚合变价数据分析结果异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        result = {
            "username": username,
            "errorMsg": error_msg,
            "isSuccess": "false",
            "alSceneBriefData": {},
        }
        return result


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# 使用示例
if __name__ == "__main__":

    input = {
        "username": "khffsmq1352",
        "ursDate": "2025-05-15",
        "uniqKey": "295eae10-0ebe-45ad-a895-fb854126a652",
        "alRelateData": {
            "用户名": "khffsmq1352",
            "uniqKey": "295eae10-0ebe-45ad-a895-fb854126a652",
            "urs-日期": "2025-05-15",
            "是否可控类型": "",
            "可控变价场景": "",
            "支付时场景-分析标识": "",
            "支付分析-一级原因": "",
            "支付分析-二级原因": "",
            "支付分析-三级原因": "",
            "支付分析-是否可控场景": "",
            "多次搜索-分析标识": "",
            "多次搜索-是否可控场景": "",
            "多次搜索-AI定责说明": "",
            "多次搜索-AI变价原因归因": "",
            "多次搜索-AI变价判断描述": "",
            "多次搜索-无需归因原因": "",
            "页面间变价-分析标识": "",
            "页面间变价-是否可控": "",
            "页面间变价-AI定责": "",
            "页面间变价-一级原因": "",
            "页面间变价-二级原因": "",
            "页面间变价-三级原因": "",
            "页面间变价-AI变价原因归因": "",
            "页面间变价-AI变价判断说明": "",
            "页面间变价-无需归因原因": "",
            "多次搜索-一级原因": "",
            "多次搜索-二级原因": "",
            "S2D-分析标识": "",
            "S2D-是否可控": "",
            "S2D-AI定责": "",
            "S2D-一级原因": "",
            "S2D-二级原因": "",
            "S2D-三级原因": "",
            "S2D-AI变价原因归因": "",
            "S2D-AI变价描述": "",
            "S2D-无需归因说明": "",
        },
        "callBackData": {
            "alResultBody": {
                "encodeJoinData": "userName%3Akhffsmq1352%23%2A%23needExecAl%3A%E6%98%AF%23%2A%23notExecAlReason%3A%23%2A%23alStatus%3A%E6%88%90%E5%8A%9F%23%2A%23attributeResp%3A%E5%B9%B3%E5%8F%B0%E8%B4%A3%E4%BB%BB%23%2A%23attributeReason%3AbasePrice%E4%BB%8E549%E2%86%92550%EF%BC%88%2B1%E5%85%83%EF%BC%89%EF%BC%8CpackagePrice%E4%BB%8E549%E2%86%92550%EF%BC%88%2B1%E5%85%83%EF%BC%89%EF%BC%8CviewPrice%E6%9C%AA%E5%8F%98%EF%BC%88550%E2%86%92550%EF%BC%89%E3%80%82%E5%BA%95%E4%BB%B7%E5%8F%98%E5%8C%96%E7%94%B1policyId%E5%8F%98%E6%9B%B4%EF%BC%8820036801253%E2%86%9281856233%EF%BC%89%E5%8F%8AwrapperId%E5%8F%98%E6%9B%B4%EF%BC%88ttsgnd00111%E2%86%92ttsgnd03846%EF%BC%89%E5%AF%BC%E8%87%B4%23%2A%23priceChageDesc%3A%E5%B1%95%E7%A4%BA%E4%BB%B7%E4%BB%8E519%E5%8F%98%E6%9B%B4%E4%B8%BA520%EF%BC%88%2B1%E5%85%83%EF%BC%89%23%2A%23compareType%3Alist-list%23%2A%23priceDiff%3A1%23%2A%23prePrice%3A519%23%2A%23surPrice%3A520%23%2A%23searchDiffSec%3A78%23%2A%23preFlightNo%3AZH8383%23%2A%23preTradeId%3Aops_slugger_250515.000925.10.90.75.73.920929.6373241518_1%23%2A%23surTradeId%3Aops_slugger_250515.001043.10.95.133.35.4193021.2033747054_1%23%2A%23attributeFirstLevel%3A%E5%B9%B3%E5%8F%B0%E7%AD%96%E7%95%A5%23%2A%23attributeSecLevel%3A%E4%BB%A3%E7%90%86%E6%88%96%E6%94%BF%E7%AD%96%E5%8F%98%E5%8C%96%23%2A%23surLockForceFailTag%3A-%2B-%7E%7E%2A%7E%7E, userName%3Akhffsmq1352%23%2A%23needExecAl%3A%E6%98%AF%23%2A%23notExecAlReason%3A%23%2A%23alStatus%3A%E6%88%90%E5%8A%9F%23%2A%23attributeResp%3A%E5%B9%B3%E5%8F%B0%E6%97%A0%E8%B4%A3-%E8%88%AA%E7%8F%AD%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%8F%98%E5%8C%96%23%2A%23attributeReason%3A%E5%BA%95%E4%BB%B7%E5%8F%98%E5%8C%96%E5%BD%92%E5%9B%A0%EF%BC%9ApolicyId%E4%B8%8D%E4%B8%80%E8%87%B4%EF%BC%88174595%20vs%203589550856%EF%BC%89%E4%B8%94wrapperId%E4%B8%8D%E4%B8%80%E8%87%B4%EF%BC%88ttsgndy0067%20vs%20ttsgnd04084%EF%BC%89%EF%BC%8C%E6%AC%A1%E4%BD%8E%E4%BB%B7%28secondPrice%29%E4%BB%8E495.5%E5%85%83%E4%B8%8A%E6%B6%A8%E8%87%B3520%E5%85%83%E5%AF%BC%E8%87%B4%E8%BF%BD%E4%BB%B7%E9%87%91%E9%A2%9D%28autoPriceDecreaseAmount%29%E4%BB%8E41.6%E5%85%83%E9%99%8D%E8%87%B324.5%E5%85%83%23%2A%23priceChageDesc%3A%E5%B1%95%E7%A4%BA%E4%BB%B7%E4%BB%8E472%E5%85%83%E4%B8%8A%E6%B6%A8%E8%87%B3478%E5%85%83%EF%BC%88%2B6%E5%85%83%EF%BC%89%23%2A%23compareType%3Alist-list%23%2A%23priceDiff%3A6%23%2A%23prePrice%3A472%23%2A%23surPrice%3A478%23%2A%23searchDiffSec%3A74%23%2A%23preFlightNo%3AEU1927%23%2A%23preTradeId%3Aops_slugger_250515.001043.10.95.133.35.4193021.2033747054_1%23%2A%23surTradeId%3Aops_slugger_250515.001157.10.90.5.81.548799.9957088746_1%23%2A%23attributeFirstLevel%3A%E8%88%AA%E7%8F%AD%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%8F%98%E5%8C%96%23%2A%23attributeSecLevel%3A%E7%A5%A8%E9%9D%A2%E4%BB%B7%E5%8F%98%E5%8C%96%E4%B8%94%E4%BB%A3%E7%90%86%E6%94%BF%E7%AD%96%E5%8F%98%E5%8C%96%23%2A%23surLockForceFailTag%3A2%2B-%7E%7E%2A%7E%7E"
            },
            "alResultHead": {
                "processStage": "变价数据分组和过滤",
                "urs_page": "ota",
                "ursDate": "2025-05-15",
                "user_name": "khffsmq1352",
                "urs_flightNos": "FM9446",
                "needExecAl": "是",
                "hasDirectPriceChange": "是",
                "alScene": "多次搜索",
                "hasRoundTripPriceChange": "是",
                "notExecAlReason": "",
            },
        },
    }
    print(json.dumps(input, indent=2, ensure_ascii=False))

    finalData = main(input)
    # print("执行结果：" + json.dumps(finalData, indent=2, ensure_ascii=False))
    write_json_to_file(
        finalData,
        file_path="ups_merge/data/mergeMultiSearch.json",
    )
