/**
 * CFB Lite - 简化的复合文档二进制文件格式解析器
 * 专门用于解析.doc文件的基本结构
 */
class CFBLite {
  constructor() {
    this.HEADER_SIZE = 512;
    this.SECTOR_SIZE = 512;
    this.MINI_SECTOR_SIZE = 64;
  }

  // 读取复合文档
  read(data) {
    const buffer = data instanceof ArrayBuffer ? data : data.buffer;
    const view = new DataView(buffer);
    const uint8Array = new Uint8Array(buffer);

    // 验证文件头
    if (!this.validateHeader(uint8Array)) {
      throw new Error('不是有效的复合文档格式');
    }

    // 解析文件头
    const header = this.parseHeader(view);

    // 读取目录
    const directory = this.readDirectory(uint8Array, header);

    return {
      header: header,
      directory: directory,
      FileIndex: directory,
      data: uint8Array
    };
  }

  // 验证文件头
  validateHeader(data) {
    if (data.length < 8) return false;

    // 检查OLE签名
    const signature = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
    for (let i = 0; i < 8; i++) {
      if (data[i] !== signature[i]) return false;
    }
    return true;
  }

  // 解析文件头
  parseHeader(view) {
    const header = {
      signature: new Uint8Array(view.buffer, 0, 8),
      minorVersion: view.getUint16(24, true),
      majorVersion: view.getUint16(26, true),
      byteOrder: view.getUint16(28, true),
      sectorSize: Math.pow(2, view.getUint16(30, true)),
      miniSectorSize: Math.pow(2, view.getUint16(32, true)),
      directorySectors: view.getUint32(44, true),
      fatSectors: view.getUint32(48, true),
      directoryFirstSector: view.getUint32(48, true),
      miniStreamCutoff: view.getUint32(56, true),
      miniFatFirstSector: view.getUint32(60, true),
      miniFatSectors: view.getUint32(64, true),
      difatFirstSector: view.getUint32(68, true),
      difatSectors: view.getUint32(72, true)
    };

    // 读取DIFAT数组
    header.difat = [];
    for (let i = 0; i < 109; i++) {
      const sector = view.getUint32(76 + i * 4, true);
      if (sector !== 0xFFFFFFFF) {
        header.difat.push(sector);
      }
    }

    return header;
  }

  // 读取目录
  readDirectory(data, header) {
    const directory = [];

    try {
      // 尝试多种方法读取目录

      // 方法1: 使用头部信息
      let directoryStart = header.directoryFirstSector * header.sectorSize + this.HEADER_SIZE;

      if (directoryStart < data.length) {
        console.log(`尝试从偏移量 ${directoryStart} 读取目录`);
        this.readDirectoryFromOffset(data, directoryStart, directory);
      }

      // 方法2: 如果方法1失败，扫描整个文件寻找目录条目
      if (directory.length === 0) {
        console.log('头部目录读取失败，尝试扫描文件');
        this.scanForDirectoryEntries(data, directory);
      }

      // 方法3: 尝试常见的目录位置
      if (directory.length === 0) {
        const commonOffsets = [0x200, 0x400, 0x800, 0x1000, 0x2000];
        for (const offset of commonOffsets) {
          if (offset < data.length) {
            console.log(`尝试从常见偏移量 ${offset} 读取目录`);
            this.readDirectoryFromOffset(data, offset, directory);
            if (directory.length > 0) break;
          }
        }
      }

    } catch (error) {
      console.error('读取目录失败:', error);
    }

    console.log(`找到 ${directory.length} 个目录条目`);
    return directory;
  }

  // 从指定偏移量读取目录
  readDirectoryFromOffset(data, startOffset, directory) {
    for (let offset = startOffset; offset < data.length - 128; offset += 128) {
      const entry = this.parseDirectoryEntry(data, offset);
      if (entry && entry.name) {
        directory.push(entry);
        console.log(`找到目录条目: ${entry.name}`);
      }

      // 限制读取的条目数量，避免无限循环
      if (directory.length > 50) break;
    }
  }

  // 扫描文件寻找目录条目
  scanForDirectoryEntries(data, directory) {
    // 每128字节扫描一次，寻找可能的目录条目
    for (let offset = 0; offset < data.length - 128; offset += 128) {
      const entry = this.parseDirectoryEntry(data, offset);
      if (entry && entry.name && this.isValidDirectoryEntry(entry)) {
        directory.push(entry);
        console.log(`扫描找到目录条目: ${entry.name}`);
      }

      // 限制扫描范围
      if (offset > 0x10000 || directory.length > 20) break;
    }
  }

  // 验证目录条目是否有效
  isValidDirectoryEntry(entry) {
    // 检查名称是否合理
    if (!entry.name || entry.name.length === 0 || entry.name.length > 32) {
      return false;
    }

    // 检查是否包含常见的Word文档流名称
    const commonNames = ['WordDocument', '1Table', '0Table', 'Data', 'CompObj', 'SummaryInformation'];
    if (commonNames.includes(entry.name)) {
      return true;
    }

    // 检查名称是否只包含有效字符
    return /^[a-zA-Z0-9_\-\.]+$/.test(entry.name);
  }

  // 解析目录条目
  parseDirectoryEntry(data, offset) {
    try {
      const view = new DataView(data.buffer, offset, 128);

      // 读取名称（UTF-16LE编码）
      const nameLength = view.getUint16(64, true);
      let name = '';

      if (nameLength > 0 && nameLength <= 64) {
        for (let i = 0; i < nameLength - 2; i += 2) {
          const char = view.getUint16(i, true);
          if (char > 0 && char < 0xFFFF) {
            name += String.fromCharCode(char);
          }
        }
      }

      // 如果名称为空或包含无效字符，尝试ASCII解析
      if (!name || name.length === 0) {
        // 尝试ASCII解析
        for (let i = 0; i < 32; i++) {
          const char = view.getUint8(i);
          if (char >= 0x20 && char <= 0x7E) {
            name += String.fromCharCode(char);
          } else if (char === 0) {
            break;
          }
        }
      }

      // 如果还是没有名称，跳过
      if (!name || name.length === 0) {
        return null;
      }

      // 清理名称中的无效字符
      name = name.replace(/[\x00-\x1F\x7F]/g, '').trim();

      if (!name) {
        return null;
      }

      const entry = {
        name: name,
        type: view.getUint8(66),
        color: view.getUint8(67),
        leftSibling: view.getUint32(68, true),
        rightSibling: view.getUint32(72, true),
        child: view.getUint32(76, true),
        startSector: view.getUint32(116, true),
        size: view.getUint32(120, true)
      };

      // 验证条目的合理性
      if (entry.size > data.length || entry.startSector > 0x1000000) {
        console.warn(`目录条目 ${name} 的参数可能无效: size=${entry.size}, startSector=${entry.startSector}`);
        // 仍然返回条目，但不读取内容
        return entry;
      }

      // 读取内容
      if (entry.startSector !== 0xFFFFFFFF && entry.size > 0 && entry.size < data.length) {
        try {
          entry.content = this.readStream(data, entry.startSector, entry.size);
          console.log(`成功读取流 ${name}, 大小: ${entry.content.length} 字节`);
        } catch (streamError) {
          console.warn(`读取流 ${name} 失败:`, streamError.message);
          entry.content = new Uint8Array(0);
        }
      }

      return entry;
    } catch (error) {
      console.error('解析目录条目失败:', error);
      return null;
    }
  }

  // 读取流数据
  readStream(data, startSector, size) {
    try {
      if (startSector === 0xFFFFFFFF || size === 0) {
        return new Uint8Array(0);
      }

      const sectorSize = this.SECTOR_SIZE;
      const content = new Uint8Array(size);
      let contentOffset = 0;
      let currentSector = startSector;

      while (contentOffset < size && currentSector !== 0xFFFFFFFF) {
        const sectorOffset = this.HEADER_SIZE + currentSector * sectorSize;

        if (sectorOffset >= data.length) {
          console.warn('扇区偏移超出文件范围');
          break;
        }

        const bytesToRead = Math.min(sectorSize, size - contentOffset);
        const sectorData = data.slice(sectorOffset, sectorOffset + bytesToRead);

        content.set(sectorData, contentOffset);
        contentOffset += bytesToRead;

        // 简化的链表遍历（实际应该读取FAT表）
        currentSector++;

        // 防止无限循环
        if (currentSector > 1000) break;
      }

      return content.slice(0, contentOffset);
    } catch (error) {
      console.error('读取流数据失败:', error);
      return new Uint8Array(0);
    }
  }

  // 查找指定名称的流
  findEntry(cfb, name) {
    if (!cfb || !cfb.FileIndex) return null;

    return cfb.FileIndex.find(entry =>
      entry && entry.name && entry.name.toLowerCase() === name.toLowerCase()
    );
  }

  // 获取流内容
  getStreamContent(cfb, streamName) {
    const entry = this.findEntry(cfb, streamName);
    return entry ? entry.content : null;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CFBLite;
} else if (typeof window !== 'undefined') {
  window.CFBLite = CFBLite;
}
