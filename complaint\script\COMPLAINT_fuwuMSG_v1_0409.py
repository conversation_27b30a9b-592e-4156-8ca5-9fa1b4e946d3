import requests
import json
from typing import Dict, List, Any

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def queryMessageHistory(orderNo: str, appCode: str, appToken: str) -> str:
    """
    查询消息历史
    Args:
        orderNo: 订单号
        appCode: 应用代码
        appToken: 应用令牌
    Returns:
        str: 拼接后的消息历史
    """
    base_url = "https://fuwu.qunar.com/callcenter/sopmsg/msg/all"
    url = f"{base_url}?bizLine=FLIGHT&channelWay=AUTO&domain=callcenter.qunar.com&orderNo={orderNo}&bizNo={orderNo}&bizNoType=order&intl=false&flowNo="
    
    proxyData = {
        "method": "get",
        "url": url,
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"

    try:
        result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
        print("----------------response:", result)
        
        # 检查是否有错误
        if "error" in result:
            return ""

        # 尝试解析data字段为JSON
        response_data = json.loads(result.get("data", "{}"))

        # 检查ret字段和data字段
        if not response_data or response_data.get("ret") is False:
            return ""

        # 获取内层data
        inner_data = response_data.get("data", {})
        if not inner_data:
            return ""

        conversations = inner_data.get("currentOrderNoConversation", [])
        quality_inspection_results = []
        
        for idx, conv in enumerate(conversations, 1):
            outline = conv.get("outline", "")
            if outline and any(c.isdigit() for c in outline):
                quality_inspection_results.append(f"{idx}.{outline}")
        
        if quality_inspection_results:
            return "".join(quality_inspection_results) + "；"
        
        return ""
    except Exception as e:
        print(f"查询消息历史失败: {str(e)}")
        return ""

def formatPromptWithMessage(prompt: str, messageHistory: str) -> str:
    """
    将消息历史填充到prompt的占位符中
    Args:
        prompt: 包含{msg}占位符的提示文本
        messageHistory: 消息历史
    Returns:
        str: 填充后的提示文本
    """
    if not prompt:
        return messageHistory
    return prompt.replace("{msg}", messageHistory)

def main(param: Dict[str, str]) -> Dict[str, Any]:
    """
    主函数
    Args:
        param: 包含orderNo和vDate的参数字典
    Returns:
        Dict: 处理结果
    """
    orderNo = param.get("orderNo", "")
    prompt = param.get("prompt", "")
    appCode = param.get("invokeAppCode", "")
    appToken = param.get("invokeToken", "")
    
    if not orderNo:
        return {"error": "订单号不能为空", "result": []}
    
    try:
        # 查询订单详情
        messageHistory = queryMessageHistory(orderNo, appCode, appToken)
        if not messageHistory:
            return {"error": "未查询到消息历史", "processStage": "查询工单会话消息", "notExecAlReason": "", "needExecAl": "是", "result": {
                "orderNo": orderNo,
                "messageHistory": "无",
                "prompt": "" 
            }}
        
        # 处理prompt中的占位符
        formattedPrompt = formatPromptWithMessage(prompt, messageHistory)
        
        return {
            "error": "",
            "processStage": "查询工单会话消息",
            "notExecAlReason": "",
            "needExecAl": "是",
            "result": {
                "orderNo": orderNo,
                "messageHistory": messageHistory,
                "prompt": formattedPrompt    
            }
        }
    except Exception as e:
        return {"error": f"处理失败: {str(e)}", "processStage": "查询工单会话消息", "notExecAlReason": f"处理失败: {str(e)}", "needExecAl": "否", "result": []}

def test():
    param = {
        "orderNo": "jfx250309002848283",
        "invokeAppCode": "f_pangu",
        "invokeToken": "V3AMURod43wuWwSgvYutQlKpZnvl7lTLaV8RXYqedFviEkfCsI+vNemVFXg6wMWTrB+XdAtoeILXgTqEbl+JisRRrfZl4FAyf8G0w3RChYf30KcnxhjFtx+mz2oiuaPkBW6eO0FoImWjeeCqV9OJHAJ12/Cvr9ur25rRlC+1Tfg="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test() 