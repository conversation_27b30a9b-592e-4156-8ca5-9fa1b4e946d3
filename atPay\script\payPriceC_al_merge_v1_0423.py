from collections import defaultdict
import traceback
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List, Dict, Any
from datetime import datetime
from string import Formatter
import re
from typing import Union
import uuid
from dataclasses import dataclass
from enum import Enum


class AttributionLevel(Enum):
    FIRST = 1
    SECOND = 2
    THIRD = 3


@dataclass
class AttributionNode:
    description: str
    priority: int
    children: List["AttributionNode"] = None
    parent: Optional["AttributionNode"] = None

    def __post_init__(self):
        if self.children is None:
            self.children = []


class AttributionSystem:
    def __init__(self):
        self.root = AttributionNode("Root", 0)
        self._initialize_attribution_tree()

    def _initialize_attribution_tree(self):
        # First level attributions
        platform_responsible = AttributionNode("点击支付时-平台有责", 0)
        platform_not_responsible = AttributionNode("点击支付时-平台无责", 1)
        unmatched_scenario = AttributionNode("未匹配用户场景", 2)

        # Second level attributions for platform_responsible
        pre_payment_change = AttributionNode("支付前校验变价", 0)
        order_product_change = AttributionNode("生单商品发生变化", 1)
        platform_responsible.children = [pre_payment_change, order_product_change]

        # Second level attributions for platform_not_responsible
        second_purchase = AttributionNode("挽留二次加购", 2)
        multi_quote_change = AttributionNode("多报价生单变价", 1)
        platform_not_responsible.children = [second_purchase, multi_quote_change]

        # Second level attributions for unmatched_scenario
        no_price_change = AttributionNode("用户下单未变价", 0)
        no_order = AttributionNode("用户未下单", 1)
        unmatched_scenario.children = [no_order, no_price_change]

        # Third level attributions for multi_quote_change
        age_limit = AttributionNode("年龄限制", 0)
        passenger_limit = AttributionNode("乘机人数限制", 1)
        insufficient_seats = AttributionNode("余位不足", 2)
        multi_fromSecondLevel = AttributionNode("多报价生单变价", 1000)
        multi_quote_change.children = [
            insufficient_seats,
            passenger_limit,
            age_limit,
            multi_fromSecondLevel,
        ]

        # Third level attributions for order_product_change
        marketing_loss = AttributionNode("辅营商品变化", 0)
        ancillary_loss = AttributionNode("营销商品变化", 1)
        order_product_change.children = [marketing_loss, ancillary_loss]

        # Set up the tree structure
        self.root.children = [
            platform_responsible,
            platform_not_responsible,
            unmatched_scenario,
        ]

    def get_priority(self, level: AttributionLevel, description: str) -> int:
        """Get the priority of a specific attribution description at a given level"""
        if level == AttributionLevel.FIRST:
            for node in self.root.children:
                if node.description == description:
                    return node.priority
        elif level == AttributionLevel.SECOND:
            for parent in self.root.children:
                for node in parent.children:
                    if node.description == description:
                        return node.priority
        elif level == AttributionLevel.THIRD:
            for parent in self.root.children:
                for second_level in parent.children:
                    for node in second_level.children:
                        if node.description == description:
                            return node.priority
        return 999  # Default priority for unmatched descriptions

    def get_highest_priority_attribution(
        self, level: AttributionLevel, descriptions: List[str]
    ) -> str:
        """Get the highest priority attribution description from a list of descriptions at a given level"""
        if not descriptions:
            return None

        min_priority = float("inf")
        result_description = None

        for description in descriptions:
            priority = self.get_priority(level, description)
            if priority < min_priority:
                min_priority = priority
                result_description = description

        return result_description


def parse_structured_data(data_str: str) -> List[Dict[str, str]]:
    """
    解析结构化字符串数据为字典列表

    参数：
    data_str : str - 输入的原生字符串数据

    返回：
    List[Dict[str, str]] - 解析后的字典列表
    """
    result = []

    # 第一阶段：分割并清洗原始数据
    raw_entries = [
        entry.strip(", ") for entry in data_str.split("~~*~~") if entry.strip()
    ]

    for entry in raw_entries:
        record = {}
        # 第二阶段：字段级解析
        fields = [f.strip() for f in entry.split("#*#") if f.strip()]

        for field in fields:
            # 第三阶段：键值对解析
            if ":" not in field:
                continue  # 跳过无效字段

            key, value = field.split(":", 1)
            key = key.strip()
            value = value.strip()

            # 第四阶段：值清洗
            if len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]  # 去除包裹的引号
            elif value.lower() == "null":
                value = None  # 处理空值
            elif value == '""':
                value = ""  # 处理空字符串标识

            record[key] = value

        if record:  # 跳过空记录
            result.append(record)

    return result


def parse_urlencoded_structured_data(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码structured字符串的字典参数

    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {
            "status": "error",
            "message": "无需要执行分析的价格数据: {}".format(paramKey),
        }

    raw_param = data[paramKey]

    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {
            "status": "error",
            "message": "Invalid parameter type, expected string",
        }

    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode("utf-8")
        except UnicodeDecodeError:
            return None, {
                "status": "error",
                "message": "Failed to decode bytes parameter",
            }

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode("utf-8")
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {"status": "error", "message": "Empty parameter after decoding"}

    # 尝试解析JSON
    try:
        json_obj = parse_structured_data(decoded_str)
        return json_obj, {"status": "success"}
    except json.JSONDecodeError as e:
        return None, {"status": "error", "message": f"JSON decode error: {str(e)}"}
    except Exception as e:
        return None, {"status": "error", "message": f"Unexpected error: {str(e)}"}


def findHighestPriorityCase(caseJoinDatas: List[Dict]) -> Optional[Dict]:
    """
    从caseJoinDatas中找到优先级最高的case

    Args:
        caseJoinDatas: 待处理的case数据列表

    Returns:
        Optional[Dict]: 优先级最高的case，如果没有则返回None
    """
    if not caseJoinDatas:
        return None

    # Step 1: 过滤掉一级原因为空的记录
    valid_cases = [case for case in caseJoinDatas if case.get("firstReason")]
    if not valid_cases:
        return None

    # Step 2: 如果只有一个元素，直接返回
    if len(valid_cases) == 1:
        return valid_cases[0]

    # Step 3: 按照一级归因优先级分组，取优先级最高的那组
    attribution_system = AttributionSystem()
    first_level_groups = defaultdict(list)
    for case in valid_cases:
        first_level = case.get("firstReason")
        priority = attribution_system.get_priority(AttributionLevel.FIRST, first_level)
        first_level_groups[priority].append(case)

    if not first_level_groups:
        return None

    # 获取优先级最高的组
    first_highest_priority = min(first_level_groups.keys())
    first_highest_level_cases = first_level_groups[first_highest_priority]

    if len(first_highest_level_cases) == 1:
        return first_highest_level_cases[0]

    # Step 4: 按照二级归因分组，取优先级最高的那组
    second_level_groups = defaultdict(list)
    for case in first_highest_level_cases:
        second_level = case.get("secondReason")
        priority = attribution_system.get_priority(
            AttributionLevel.SECOND, second_level
        )
        second_level_groups[priority].append(case)

    if not second_level_groups:
        return None

    # 获取优先级最高的组
    second_highest_priority = min(second_level_groups.keys())
    sec_highest_level_cases = second_level_groups[second_highest_priority]

    if len(sec_highest_level_cases) == 1:
        return sec_highest_level_cases[0]

    # Step 5: 按照三级归因分组，取优先级最高的那组
    third_level_groups = defaultdict(list)
    for case in sec_highest_level_cases:
        third_level = case.get("thirdReason")
        priority = attribution_system.get_priority(AttributionLevel.THIRD, third_level)
        third_level_groups[priority].append(case)

    if not third_level_groups:
        return None

    # 获取优先级最高的组
    third_highest_priority = min(third_level_groups.keys())
    third_highest_level_cases = third_level_groups[third_highest_priority]

    if len(third_highest_level_cases) == 1:
        return third_highest_level_cases[0]

    # Step 6: 如果还有多个，按照orderTime逆序排序，取第一个
    def parse_order_time(case):
        order_time = case.get("orderTime")
        try:
            return (
                datetime.strptime(order_time, "%Y-%m-%d %H:%M:%S")
                if order_time
                else datetime.min
            )
        except:
            return datetime.min

    return sorted(third_highest_level_cases, key=parse_order_time, reverse=True)[0]


def mergeAlResultFromCaseToUser(
    userAlJoinData: Dict, caseJoinDatas: List[Dict]
) -> None:
    """
    将caseJoinDatas中优先级最高的case的1,2,3级原因设置给userAlJoinData

    Args:
        userAlJoinData: 用户数据
        caseJoinDatas: case数据列表
    """
    # Step 1: 如果caseJoinDatas为空，直接返回
    if not caseJoinDatas:
        return

    # Step 2: 找到优先级最高的case
    highest_priority_case = findHighestPriorityCase(caseJoinDatas)
    if not highest_priority_case:
        return

    # Step 3: 将最高优先级case的1,2,3级原因设置给userAlJoinData
    userAlJoinData["firstReason"] = highest_priority_case.get("firstReason", "")
    userAlJoinData["secondReason"] = highest_priority_case.get("secondReason", "")
    userAlJoinData["thirdReason"] = highest_priority_case.get("thirdReason", "")


def main(param: dict) -> dict:
    try:
        # 解析参数
        userAlJoinDatas, userDataStatus = parse_urlencoded_structured_data(
            param, "userAlJoinData"
        )
        caseJoinDatas, caseDataStatus = parse_urlencoded_structured_data(
            param, "caseJoinData"
        )

        if not userAlJoinDatas:
            return {
                "status": "error",
                "message": "userAlJoinData is None",
            }

        userAlJoinData = userAlJoinDatas[0]
        if not caseJoinDatas:
            return {"status": "", "message": "caseJoinData is None", **userAlJoinData}

        mergeAlResultFromCaseToUser(userAlJoinData, caseJoinDatas)
        return {"status": "", "message": "success", **userAlJoinData}
    except Exception as e:
        stack_trace = traceback.format_exc()
        return {
            "status": "error",
            "message": f"Unexpected error: {stack_trace}",
        }


if __name__ == "__main__":
    param = {
        "userName": "kpegrko8895",
        "userAlJoinData": "questionTime%3A2025-04-21%2000%3A03%3A13%23%2A%23firstReason%3A%23%2A%23secondReason%3A%23%2A%23thirdReason%3A%23%2A%23username%3Akpegrko8895%23%2A%23needExecAl%3A%E6%98%AF%23%2A%23notExecAlReason%3A%23%2A%23processStage%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%7E%7E%2A%7E%7E",
        "caseJoinData": "orderTime%3A2025-04-21%2006%3A22%3A20%23%2A%23%E7%94%A8%E6%88%B7%E5%90%8D%3Akpegrko8895%23%2A%23%E4%BA%A4%E6%98%93%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A820.0%23%2A%23%E6%98%AF%E5%90%A6%E9%9C%80%E8%A6%81%E5%88%86%E6%9E%90%3A%E6%98%AF%23%2A%23%E9%97%AE%E9%A2%98%E5%8F%91%E7%94%9F%E6%97%B6%E9%97%B4%3A2025-04-21%2000%3A03%3A13%23%2A%23%E8%AE%A2%E5%8D%95%E5%8F%B7%3Ajeu250421062220898%23%2A%23goPay%E4%BB%B7%E6%A0%BC%3A820.0%23%2A%23firstReason%3A%E6%9C%AA%E5%8C%B9%E9%85%8D%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%23%2A%23%E6%97%A0%E9%9C%80%E5%88%86%E6%9E%90%E5%8E%9F%E5%9B%A0%3A%23%2A%23%E5%88%86%E6%9E%90%E6%89%A7%E8%A1%8C%E9%98%B6%E6%AE%B5%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E6%97%A5%E5%BF%97%3A%23%2A%23%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E4%BB%B7%E6%A0%BC%3A820.0%23%2A%23uniqId%3Afa25dde0-e622-450d-a775-8f7f1e70a491%23%2A%23%E9%97%AE%E5%8D%B7id%3A2584%23%2A%23thirdReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E4%BB%B7%E6%A0%BC%3A%23%2A%23secondReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23booking%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A820.0%23%2A%23urs%E6%97%A5%E6%9C%9F%3A2025-04-21%7E%7E%2A%7E%7E, orderTime%3A2025-04-20%2023%3A48%3A37%23%2A%23%E7%94%A8%E6%88%B7%E5%90%8D%3Akpegrko8895%23%2A%23%E4%BA%A4%E6%98%93%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23%E6%98%AF%E5%90%A6%E9%9C%80%E8%A6%81%E5%88%86%E6%9E%90%3A%E6%98%AF%23%2A%23%E9%97%AE%E9%A2%98%E5%8F%91%E7%94%9F%E6%97%B6%E9%97%B4%3A2025-04-21%2000%3A03%3A13%23%2A%23%E8%AE%A2%E5%8D%95%E5%8F%B7%3Abyv250420234838882%23%2A%23goPay%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23firstReason%3A%E6%9C%AA%E5%8C%B9%E9%85%8D%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%23%2A%23%E6%97%A0%E9%9C%80%E5%88%86%E6%9E%90%E5%8E%9F%E5%9B%A0%3A%23%2A%23%E5%88%86%E6%9E%90%E6%89%A7%E8%A1%8C%E9%98%B6%E6%AE%B5%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E6%97%A5%E5%BF%97%3A%23%2A%23%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23uniqId%3A93fbe52f-d123-469a-bbcb-5bddad30896d%23%2A%23%E9%97%AE%E5%8D%B7id%3A2584%23%2A%23thirdReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E4%BB%B7%E6%A0%BC%3A%23%2A%23secondReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23booking%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23urs%E6%97%A5%E6%9C%9F%3A2025-04-21%7E%7E%2A%7E%7E, orderTime%3A2025-04-20%2023%3A48%3A12%23%2A%23%E7%94%A8%E6%88%B7%E5%90%8D%3Akpegrko8895%23%2A%23%E4%BA%A4%E6%98%93%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23%E6%98%AF%E5%90%A6%E9%9C%80%E8%A6%81%E5%88%86%E6%9E%90%3A%E6%98%AF%23%2A%23%E9%97%AE%E9%A2%98%E5%8F%91%E7%94%9F%E6%97%B6%E9%97%B4%3A2025-04-21%2000%3A03%3A13%23%2A%23%E8%AE%A2%E5%8D%95%E5%8F%B7%3Abyv250420234812773%23%2A%23goPay%E4%BB%B7%E6%A0%BC%3A643.0%2C643.0%23%2A%23firstReason%3A%E6%9C%AA%E5%8C%B9%E9%85%8D%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%23%2A%23%E6%97%A0%E9%9C%80%E5%88%86%E6%9E%90%E5%8E%9F%E5%9B%A0%3A%23%2A%23%E5%88%86%E6%9E%90%E6%89%A7%E8%A1%8C%E9%98%B6%E6%AE%B5%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E6%97%A5%E5%BF%97%3A%23%2A%23%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23uniqId%3Ae25483f5-3e5a-430e-99a8-49a6ab1e935a%23%2A%23%E9%97%AE%E5%8D%B7id%3A2584%23%2A%23thirdReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E4%BB%B7%E6%A0%BC%3A%23%2A%23secondReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23booking%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A643.0%23%2A%23urs%E6%97%A5%E6%9C%9F%3A2025-04-21%7E%7E%2A%7E%7E, orderTime%3A2025-04-15%2001%3A14%3A17%23%2A%23%E7%94%A8%E6%88%B7%E5%90%8D%3Akpegrko8895%23%2A%23%E4%BA%A4%E6%98%93%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23%E6%98%AF%E5%90%A6%E9%9C%80%E8%A6%81%E5%88%86%E6%9E%90%3A%E6%98%AF%23%2A%23%E9%97%AE%E9%A2%98%E5%8F%91%E7%94%9F%E6%97%B6%E9%97%B4%3A2025-04-21%2000%3A03%3A13%23%2A%23%E8%AE%A2%E5%8D%95%E5%8F%B7%3Asub250415011418934%23%2A%23goPay%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23firstReason%3A%E6%9C%AA%E5%8C%B9%E9%85%8D%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%23%2A%23%E6%97%A0%E9%9C%80%E5%88%86%E6%9E%90%E5%8E%9F%E5%9B%A0%3A%23%2A%23%E5%88%86%E6%9E%90%E6%89%A7%E8%A1%8C%E9%98%B6%E6%AE%B5%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E6%97%A5%E5%BF%97%3A%23%2A%23%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23uniqId%3Aaf3a5f8c-1d05-44b8-92ef-c12eeb41305f%23%2A%23%E9%97%AE%E5%8D%B7id%3A2584%23%2A%23thirdReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E4%BB%B7%E6%A0%BC%3A%23%2A%23secondReason%3A%E7%94%A8%E6%88%B7%E4%B8%8B%E5%8D%95%E6%9C%AA%E5%8F%98%E4%BB%B7%23%2A%23booking%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23urs%E6%97%A5%E6%9C%9F%3A2025-04-21%7E%7E%2A%7E%7E, orderTime%3A2025-04-15%2000%3A02%3A08%23%2A%23%E7%94%A8%E6%88%B7%E5%90%8D%3Akpegrko8895%23%2A%23%E4%BA%A4%E6%98%93%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23%E6%98%AF%E5%90%A6%E9%9C%80%E8%A6%81%E5%88%86%E6%9E%90%3A%E6%98%AF%23%2A%23%E9%97%AE%E9%A2%98%E5%8F%91%E7%94%9F%E6%97%B6%E9%97%B4%3A2025-04-21%2000%3A03%3A13%23%2A%23%E8%AE%A2%E5%8D%95%E5%8F%B7%3Asub250415000208523%23%2A%23goPay%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23firstReason%3A%E7%82%B9%E5%87%BB%E6%94%AF%E4%BB%98%E6%97%B6-%E5%B9%B3%E5%8F%B0%E6%9C%89%E8%B4%A3%23%2A%23%E6%97%A0%E9%9C%80%E5%88%86%E6%9E%90%E5%8E%9F%E5%9B%A0%3A%23%2A%23%E5%88%86%E6%9E%90%E6%89%A7%E8%A1%8C%E9%98%B6%E6%AE%B5%3A%E5%BA%96%E4%B8%81case%E6%9F%A5%E8%AF%A2%E9%98%B6%E6%AE%B5%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E6%97%A5%E5%BF%97%3A%23%2A%23%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E4%BB%B7%E6%A0%BC%3A610.0%23%2A%23uniqId%3Ad1dc658e-4235-4b59-809f-27aab56fef7a%23%2A%23%E9%97%AE%E5%8D%B7id%3A2584%23%2A%23thirdReason%3A%E7%A5%A8%E4%BB%B7%E7%94%B1500.0%E5%8F%98%E6%9B%B4%E4%B8%BA560.0%23%2A%23%E5%A4%9A%E6%8A%A5%E4%BB%B7%E4%BB%B7%E6%A0%BC%3A%23%2A%23secondReason%3A%E6%94%AF%E4%BB%98%E5%89%8D%E6%A0%A1%E9%AA%8C%E5%8F%98%E4%BB%B7%23%2A%23booking%E7%94%9F%E5%8D%95%E4%BB%B7%E6%A0%BC%3A550.0%23%2A%23urs%E6%97%A5%E6%9C%9F%3A2025-04-21%7E%7E%2A%7E%7E",
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False))
