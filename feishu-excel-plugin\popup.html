<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>飞书Excel助手</title>
  <style>
    body {
      width: 400px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .section {
      padding: 10px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
    }

    .auth-status {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px;
      border-radius: 4px;
    }

    .auth-status.logged-in {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .auth-status.logged-out {
      background-color: #fbe9e7;
      color: #d32f2f;
    }

    .upload-area {
      border: 2px dashed #ccc;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }

    .upload-area:hover {
      border-color: #1976d2;
    }

    .upload-area.drag-over {
      border-color: #1976d2;
      background-color: #e3f2fd;
    }

    .preview-area {
      max-height: 200px;
      overflow-y: auto;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
    }

    .sheet-list {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 10px;
    }

    .sheet-item {
      padding: 4px 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      cursor: pointer;
    }

    .sheet-item.active {
      background-color: #1976d2;
      color: white;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s;
    }

    .primary-button {
      background-color: #1976d2;
      color: white;
    }

    .primary-button:hover {
      background-color: #1565c0;
    }

    .secondary-button {
      background-color: #e0e0e0;
      color: #333;
    }

    .secondary-button:hover {
      background-color: #d5d5d5;
    }

    .button-group {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    .status-message {
      padding: 8px;
      border-radius: 4px;
      margin-top: 10px;
    }

    .status-message.success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    .status-message.error {
      background-color: #fbe9e7;
      color: #d32f2f;
    }

    .loading {
      display: none;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 10px;
    }

    .loading.active {
      display: flex;
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1976d2;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 认证状态 -->
    <div class="auth-status logged-out">
      <span class="status-icon">⚠️</span>
      <span class="status-text">未登录飞书</span>
      <button class="secondary-button" id="authButton">登录</button>
    </div>

    <!-- 文件上传区域 -->
    <div class="section">
      <div class="upload-area" id="uploadArea">
        <p>点击或拖拽Excel文件到此处</p>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
      </div>
    </div>

    <!-- Sheet列表 -->
    <div class="section">
      <h3>工作表</h3>
      <div class="sheet-list" id="sheetList">
        <!-- 动态填充 -->
      </div>
    </div>

    <!-- 数据预览 -->
    <div class="section">
      <h3>数据预览</h3>
      <div class="preview-area" id="previewArea">
        <!-- 动态填充 -->
      </div>
    </div>

    <!-- 飞书表格选择 -->
    <div class="section">
      <h3>目标飞书表格</h3>
      <select id="spreadsheetSelect" class="full-width">
        <option value="">选择表格...</option>
      </select>
    </div>

    <!-- 操作按钮 -->
    <div class="button-group">
      <button class="secondary-button" id="previewButton">预览数据</button>
      <button class="primary-button" id="importButton">导入到飞书</button>
      <button class="secondary-button" id="exportButton">导出为文本</button>
    </div>

    <!-- 加载状态 -->
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <span>处理中...</span>
    </div>

    <!-- 状态消息 -->
    <div class="status-message" id="statusMessage" style="display: none;">
      <!-- 动态填充 -->
    </div>
  </div>

  <!-- 引入必要的脚本 -->
  <script src="./lib/xlsx.full.min.js"></script>
  <script src="./excelProcessor.js"></script>
  <script src="./feishuApi.js"></script>
  <script src="./popup.js"></script>
</body>
</html>