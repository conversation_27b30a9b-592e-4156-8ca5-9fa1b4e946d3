class DocumentProcessor {
  constructor() {
    this.files = {
      word: [],
      excel: []
    };
    this.maxFiles = {
      word: 5,
      excel: 10
    };

    // 初始化表格格式化器
    this.tableFormatter = typeof TableFormatter !== 'undefined' ? new TableFormatter() : null;

    // 初始化增强Markdown格式化器
    this.enhancedFormatter = typeof EnhancedMarkdownFormatter !== 'undefined' ? new EnhancedMarkdownFormatter() : null;

    // 初始化表格结构分析器
    this.tableAnalyzer = typeof TableStructureAnalyzer !== 'undefined' ? new TableStructureAnalyzer() : null;

    // 初始化数据压缩器
    this.dataCompressor = typeof DataCompressor !== 'undefined' ? new DataCompressor() : null;

    // 初始化DOC解析器
    this.docParser = typeof DocParser !== 'undefined' ? new DocParser() : null;

    // 初始化CFB库
    this.cfbLite = typeof CFBLite !== 'undefined' ? new CFBLite() : null;

    // 初始化DOC转换助手
    this.docConversionHelper = typeof DocConversionHelper !== 'undefined' ? new DocConversionHelper() : null;

    // 初始化JSZip DOC解析器
    this.docJSZipParser = typeof DocJSZipParser !== 'undefined' ? new DocJSZipParser() : null;

    // 初始化智能DOC解析器
    this.docSmartParser = typeof DocSmartParser !== 'undefined' ? new DocSmartParser() : null;

    // 初始化纯正文提取器
    this.pureTextExtractor = typeof PureTextExtractor !== 'undefined' ? new PureTextExtractor() : null;
    
    // 初始化文档编码修复器
    this.docEncodingFixer = typeof DocEncodingFixer !== 'undefined' ? new DocEncodingFixer() : null;

    // Check if required libraries are loaded
    if (typeof mammoth === 'undefined') {
      console.error('Mammoth library not loaded');
    }
    if (typeof XLSX === 'undefined') {
      console.error('XLSX library not loaded');
    }
    if (typeof TableFormatter === 'undefined') {
      console.warn('TableFormatter library not loaded, using basic table formatting');
    }
    if (typeof TableStructureAnalyzer === 'undefined') {
      console.warn('TableStructureAnalyzer library not loaded, using basic table processing');
    }
    if (typeof DataCompressor === 'undefined') {
      console.warn('DataCompressor library not loaded, data will not be compressed');
    }
    if (typeof DocParser === 'undefined') {
      console.warn('DocParser library not loaded, .doc file support will be limited');
    }
    if (typeof CFBLite === 'undefined') {
      console.warn('CFBLite library not loaded, .doc file parsing will use fallback method');
    }
    if (typeof AdvancedDocExtractor === 'undefined') {
      console.warn('AdvancedDocExtractor library not loaded, .doc file text extraction will be basic');
    }
    if (typeof JSZip === 'undefined') {
      console.warn('JSZip library not loaded, .doc file parsing will be limited');
    }
    if (typeof DocJSZipParser === 'undefined') {
      console.warn('DocJSZipParser library not loaded, advanced .doc parsing unavailable');
    }
  }

  // 检查文件类型
  getFileType(file) {
    if (!file || !file.name) {
      throw new Error('无效的文件对象');
    }
    const ext = file.name.split('.').pop().toLowerCase();
    if (['docx', 'doc'].includes(ext)) return 'word';
    if (['xlsx', 'xls'].includes(ext)) return 'excel';
    return 'unknown';
  }

  // 检查DOCX文件有效性
  isValidDocx(arrayBuffer) {
    if (!arrayBuffer || arrayBuffer.byteLength < 4) return false;
    const header = new Uint8Array(arrayBuffer, 0, 4);
    const zipHeader = [0x50, 0x4B, 0x03, 0x04]; // PK.. ZIP header
    return header.every((val, i) => val === zipHeader[i]);
  }

  // 检查DOC文件有效性
  isValidDoc(arrayBuffer) {
    if (!arrayBuffer || arrayBuffer.byteLength < 8) return false;
    const header = new Uint8Array(arrayBuffer, 0, 8);
    // DOC文件头特征 - Microsoft Office文档的OLE复合文档格式
    const docHeader = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
    return header.every((val, i) => val === docHeader[i]);
  }

  // 检查是否为.doc文件（旧格式）
  isLegacyDocFile(file) {
    return file && file.name && file.name.toLowerCase().endsWith('.doc');
  }

  // 基本文本提取方法（最后的回退方案）
  extractBasicTextFromDoc(arrayBuffer) {
    try {
      // 尝试使用专门的中文文本提取方法
      const chineseText = this.extractChineseText(arrayBuffer);
      if (chineseText && chineseText.length > 10) {
        console.log('使用专门的中文文本提取方法成功');
        return chineseText;
      }
      
      // 尝试使用编码检测器自动检测编码
      if (typeof DocEncodingFixer !== 'undefined') {
        const encodingFixer = new DocEncodingFixer();
        const detectionResult = encodingFixer.detectAndConvertEncoding(arrayBuffer);
        
        if (detectionResult.success) {
          console.log(`自动检测到编码: ${detectionResult.encoding}`);
          return detectionResult.text;
        }
      }
      
      // 如果自动检测失败，尝试多种编码方式
      const results = [];
      
      // 1. 尝试UTF-16LE编码（常见于Word文档）
      results.push(this.extractUTF16LEText(arrayBuffer));
      
      // 2. 尝试GBK/GB18030编码（常见于中文文档）
      results.push(this.extractGBKText(arrayBuffer));
      
      // 3. 尝试UTF-8编码
      results.push(this.extractUTF8Text(arrayBuffer));
      
      // 4. 原始方法作为回退
      results.push(this.extractLegacyText(arrayBuffer));
      
      // 5. 尝试暴力扫描方法
      results.push(this.bruteForceExtractText(arrayBuffer));
      
      // 评分并选择最佳结果
      let bestText = '';
      let bestScore = 0;
      
      results.forEach(result => {
        const score = this.scoreExtractedText(result);
        if (score > bestScore) {
          bestScore = score;
          bestText = result;
        }
      });
      
      // 清理提取的文本
      bestText = bestText
        .replace(/\s{3,}/g, '  ') // 移除过多的空格
        .replace(/\n{3,}/g, '\n\n') // 移除过多的换行
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .join('\n')
        .trim();

      return bestText || '无法从.doc文件中提取文本内容，建议将文件转换为.docx格式';
    } catch (error) {
      console.error('基本文本提取失败:', error);
      return '文件解析失败，建议将.doc文件转换为.docx格式后重新上传';
    }
  }
  
  // 专门提取中文文本的方法
  extractChineseText(arrayBuffer) {
    try {
      const uint8Array = new Uint8Array(arrayBuffer);
      let result = '';
      let chineseSegments = [];
      
      // 扫描整个文件寻找中文字符序列
      for (let i = 0; i < uint8Array.length - 1; i++) {
        // 检查是否可能是中文字符的起始字节
        if (uint8Array[i] >= 0x81 && uint8Array[i] <= 0xFE) {
          let segment = '';
          let startPos = i;
          let validChars = 0;
          let invalidChars = 0;
          
          // 尝试解析连续的中文字符
          for (let j = i; j < uint8Array.length - 1; j += 2) {
            const byte1 = uint8Array[j];
            const byte2 = uint8Array[j + 1];
            
            // 检查是否符合GBK编码规则
            if ((byte1 >= 0x81 && byte1 <= 0xFE) && 
                (byte2 >= 0x40 && byte2 <= 0xFE && byte2 !== 0x7F)) {
              try {
                // 尝试解码这个双字节
                const gbkBytes = new Uint8Array([byte1, byte2]);
                const decoder = new TextDecoder('gb18030');
                const char = decoder.decode(gbkBytes);
                
                // 检查是否是有效的中文字符
                if (/[\u4e00-\u9fff]/.test(char)) {
                  segment += char;
                  validChars++;
                } else {
                  invalidChars++;
                }
              } catch (e) {
                invalidChars++;
              }
            } else {
              // 不符合GBK编码规则，可能是段落结束
              break;
            }
            
            // 如果无效字符太多，终止解析
            if (invalidChars > 5 && validChars < 2) {
              break;
            }
          }
          
          // 如果找到有效的中文段落，保存它
          if (validChars >= 2) {
            chineseSegments.push({
              text: segment,
              start: startPos,
              length: validChars * 2,
              score: validChars * 10 - invalidChars * 2
            });
            
            // 跳过已处理的字节
            i += validChars * 2 - 1;
          }
        }
      }
      
      // 按分数排序段落
      chineseSegments.sort((a, b) => b.score - a.score);
      
      // 合并高分段落
      const highScoreSegments = chineseSegments.filter(seg => seg.score > 20);
      if (highScoreSegments.length > 0) {
        result = highScoreSegments.map(seg => seg.text).join('\n');
      }
      
      return result;
    } catch (e) {
      console.warn('中文文本提取失败:', e);
      return '';
    }
  }
  
  // 暴力扫描提取文本
  bruteForceExtractText(arrayBuffer) {
    try {
      const uint8Array = new Uint8Array(arrayBuffer);
      let text = '';
      
      // 尝试不同的扫描方式
      
      // 1. 扫描连续的ASCII字符
      let asciiText = '';
      let consecutiveAscii = 0;
      
      for (let i = 0; i < uint8Array.length; i++) {
        const byte = uint8Array[i];
        if (byte >= 0x20 && byte <= 0x7E) {
          asciiText += String.fromCharCode(byte);
          consecutiveAscii++;
        } else if (byte === 0x0D || byte === 0x0A) {
          if (consecutiveAscii > 3) {
            asciiText += '\n';
          }
          consecutiveAscii = 0;
        } else {
          if (consecutiveAscii <= 3) {
            asciiText = asciiText.slice(0, -consecutiveAscii);
          }
          consecutiveAscii = 0;
        }
      }
      
      // 2. 扫描可能的中文字符
      let gbkText = '';
      
      for (let i = 0; i < uint8Array.length - 1; i++) {
        const byte1 = uint8Array[i];
        const byte2 = uint8Array[i + 1];
        
        // 检查是否可能是中文字符
        if ((byte1 >= 0x81 && byte1 <= 0xFE) && 
            (byte2 >= 0x40 && byte2 <= 0xFE && byte2 !== 0x7F)) {
          try {
            const gbkBytes = new Uint8Array([byte1, byte2]);
            const decoder = new TextDecoder('gb18030');
            const char = decoder.decode(gbkBytes);
            
            if (/[\u4e00-\u9fff]/.test(char)) {
              gbkText += char;
              i++; // 跳过第二个字节
            }
          } catch (e) {
            // 解码失败，继续
          }
        }
      }
      
      // 选择更好的结果
      if (gbkText.length > asciiText.length / 3) {
        text = gbkText;
      } else {
        text = asciiText;
      }
      
      return text;
    } catch (e) {
      console.warn('暴力扫描提取失败:', e);
      return '';
    }
  }
  
  // 提取UTF-16LE编码文本（原始方法）
  extractLegacyText(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    let text = '';
    let consecutiveNulls = 0;

    // 跳过文件头，从偏移量512开始查找文本
    for (let i = 512; i < uint8Array.length - 1; i++) {
      const byte1 = uint8Array[i];
      const byte2 = uint8Array[i + 1];

      // 检查是否为可打印的ASCII字符（UTF-16LE编码）
      if (byte1 >= 0x20 && byte1 <= 0x7E && byte2 === 0x00) {
        text += String.fromCharCode(byte1);
        consecutiveNulls = 0;
        i++; // 跳过下一个字节（通常是0x00）
      } else if (byte1 === 0x0D && byte2 === 0x00) {
        text += '\n';
        consecutiveNulls = 0;
        i++;
      } else if (byte1 === 0x09 && byte2 === 0x00) {
        text += '\t';
        consecutiveNulls = 0;
        i++;
      } else if (byte1 === 0x20 && byte2 === 0x00) {
        text += ' ';
        consecutiveNulls = 0;
        i++;
      } else if (byte1 === 0x00) {
        consecutiveNulls++;
        // 如果连续遇到太多空字节，可能已经离开文本区域
        if (consecutiveNulls > 100) {
          // 跳过一段距离，寻找下一个文本区域
          i += 100;
          consecutiveNulls = 0;
        }
      } else {
        consecutiveNulls = 0;
      }
    }
    
    return text;
  }
  
  // 提取UTF-16LE编码文本（改进版）
  extractUTF16LEText(arrayBuffer) {
    try {
      // 使用TextDecoder直接解码
      const decoder = new TextDecoder('utf-16le');
      let text = decoder.decode(arrayBuffer);
      
      // 清理控制字符
      text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
      
      return text;
    } catch (e) {
      console.warn('UTF-16LE解码失败:', e);
      return this.extractLegacyText(arrayBuffer);
    }
  }
  
  // 提取GBK/GB18030编码文本
  extractGBKText(arrayBuffer) {
    try {
      // 尝试使用TextDecoder解码GB18030（包含GBK）
      const decoder = new TextDecoder('gb18030');
      let text = decoder.decode(arrayBuffer);
      
      // 清理控制字符
      text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
      
      return text;
    } catch (e) {
      console.warn('GBK/GB18030解码失败:', e);
      
      // 如果浏览器不支持GB18030解码，尝试手动解码
      if (typeof DocEncodingFixer !== 'undefined') {
        const encodingFixer = new DocEncodingFixer();
        return encodingFixer.fixGBKEncoding(new Uint8Array(arrayBuffer));
      }
      
      return '';
    }
  }
  
  // 提取UTF-8编码文本
  extractUTF8Text(arrayBuffer) {
    try {
      // 使用TextDecoder直接解码
      const decoder = new TextDecoder('utf-8');
      let text = decoder.decode(arrayBuffer);
      
      // 清理控制字符
      text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
      
      return text;
    } catch (e) {
      console.warn('UTF-8解码失败:', e);
      return '';
    }
  }
  
  // 评分提取的文本质量
  scoreExtractedText(text) {
    if (!text) return 0;
    let score = 0;
    
    // 检查中文字符
    const chineseChars = text.match(/[\u4e00-\u9fff]/g) || [];
    score += chineseChars.length * 2;
    
    // 检查常见中文标点
    const chinesePunct = text.match(/[。，、；：？！""''（）【】《》]/g) || [];
    score += chinesePunct.length;
    
    // 检查ASCII字符
    const asciiChars = text.match(/[a-zA-Z0-9]/g) || [];
    score += asciiChars.length;
    
    // 检查文本长度
    score += Math.min(text.length / 10, 100);
    
    // 减分项：无效字符
    const invalidChars = text.match(/[\ufffd\u0000-\u0008\u000b-\u000c\u000e-\u001f]/g) || [];
    score -= invalidChars.length * 10;
    
    return score;
  }

  // 添加文件
  async addFile(file) {
    if (!file) {
      throw new Error('文件对象不能为空');
    }

    const type = this.getFileType(file);
    if (type === 'unknown') throw new Error('不支持的文件类型');

    // 确保files[type]存在
    if (!this.files[type]) {
      this.files[type] = [];
    }

    if (!Array.isArray(this.files[type])) {
      this.files[type] = [];
    }

    if (this.files[type].length >= this.maxFiles[type]) {
      throw new Error(`最多只能上传${this.maxFiles[type]}个${type === 'word' ? 'Word' : 'Excel'}文件`);
    }

    const fileData = await this.readFile(file);
    this.files[type].push({
      name: file.name,
      type,
      data: fileData,
      file
    });
  }

  // 读取文件内容
  async readFile(file) {
    if (!file) {
      throw new Error('文件对象不能为空');
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          if (!e || !e.target || !e.target.result) {
            throw new Error('文件读取失败');
          }

          const type = this.getFileType(file);
          const arrayBuffer = e.target.result;
          const ext = file.name.split('.').pop().toLowerCase();

          if (type === 'word') {
            if (ext === 'docx') {
              // 处理新版.docx文件
              if (!this.isValidDocx(arrayBuffer)) {
                throw new Error('不是有效的.docx文件 - 文件可能已损坏');
              }

              if (typeof mammoth === 'undefined') {
                throw new Error('Word文档处理库未加载，请刷新页面重试');
              }

              try {
                // 使用convertToHtml而不是extractRawText，以保留更多格式
                const result = await mammoth.convertToHtml({arrayBuffer});
                if (!result || typeof result.value !== 'string') {
                  throw new Error('Word文档解析失败 - 无法提取内容');
                }
                // 保存HTML内容和纯文本内容
                const textResult = await mammoth.extractRawText({arrayBuffer});
                resolve({
                  html: result.value,
                  text: textResult.value
                });
              } catch (error) {
                console.error('Word文档解析错误:', error);
                throw new Error('解析.docx文件失败 - ' + (error.message || '未知错误'));
              }
            } else if (ext === 'doc') {
              // 处理旧版.doc文件 - 使用智能解析器自动检测WPS/Word
              console.log('开始处理.doc文件，使用智能解析器');

              // 优先使用智能DOC解析器（自动检测WPS/Word）
              if (this.docSmartParser) {
                try {
                  console.log('使用智能DOC解析器处理文件（自动检测软件类型）');
                  const result = await this.docSmartParser.smartParse(arrayBuffer, file.name);

                  if (result.success) {
                    console.log(`智能解析成功，检测到: ${result.detection ? result.detection.software : '未知'}, 方法: ${result.method}`);

                    // 应用纯正文提取器进一步清理
                    let finalText = result.text;
                    if (this.pureTextExtractor && result.text) {
                      try {
                        console.log('应用纯正文提取器清理结果...');
                        const cleanedText = this.pureTextExtractor.extractPureText(result.text);
                        if (cleanedText && cleanedText.length > 0) {
                          finalText = cleanedText;
                          console.log('纯正文提取成功，文本已清理');
                        }
                      } catch (cleanError) {
                        console.warn('纯正文提取失败，使用原始结果:', cleanError.message);
                      }
                    }

                    resolve({
                      html: this.textToHtml(finalText),
                      text: finalText
                    });
                    return;
                  } else {
                    console.warn('智能解析失败，尝试JSZip解析器');
                  }
                } catch (smartError) {
                  console.warn('智能解析器失败:', smartError.message);
                }
              }

              // 回退到JSZip DOC解析器
              if (this.docJSZipParser) {
                try {
                  console.log('使用JSZip DOC解析器处理文件');
                  const result = await this.docJSZipParser.parseDocFile(arrayBuffer, file.name);

                  if (result.success) {
                    console.log(`JSZip解析成功，使用方法: ${result.method}`);

                    // 应用纯正文提取器进一步清理
                    let finalText = result.text;
                    if (this.pureTextExtractor && result.text) {
                      try {
                        console.log('应用纯正文提取器清理JSZip结果...');
                        const cleanedText = this.pureTextExtractor.extractPureText(result.text);
                        if (cleanedText && cleanedText.length > 0) {
                          finalText = cleanedText;
                          console.log('纯正文提取成功，JSZip结果已清理');
                        }
                      } catch (cleanError) {
                        console.warn('纯正文提取失败，使用JSZip原始结果:', cleanError.message);
                      }
                    }

                    resolve({
                      html: this.textToHtml(finalText),
                      text: finalText
                    });
                    return;
                  } else {
                    console.warn('JSZip解析失败，尝试其他方法');
                  }
                } catch (jszipError) {
                  console.warn('JSZip解析器失败:', jszipError.message);
                }
              }

              // 回退到DOC转换助手
              if (this.docConversionHelper) {
                try {
                  console.log('使用DOC转换助手处理文件');
                  const result = await this.docConversionHelper.handleDocFile(file);

                  if (result.success) {
                    console.log(`DOC转换成功，使用方法: ${result.method}`);
                    resolve({
                      html: result.html,
                      text: result.text
                    });
                    return;
                  } else if (result.isGuidance) {
                    // 如果是用户指导，也返回结果
                    console.log('提供用户指导信息');
                    resolve({
                      html: result.html,
                      text: result.text
                    });
                    return;
                  }
                } catch (helperError) {
                  console.warn('DOC转换助手失败:', helperError.message);
                }
              }

              // 回退到原有的解析方法
              if (!this.isValidDoc(arrayBuffer)) {
                throw new Error('不是有效的.doc文件 - 文件可能已损坏');
              }

              // 尝试专门的DOC解析器
              if (this.docParser) {
                try {
                  console.log('使用专门的DOC解析器处理文件');
                  const result = await this.docParser.parseDocFile(arrayBuffer);
                  resolve({
                    html: result.html || '<p>无法提取HTML内容</p>',
                    text: result.text || '无法提取文本内容'
                  });
                  return;
                } catch (docParserError) {
                  console.warn('专门的DOC解析器失败，尝试mammoth:', docParserError.message);
                }
              }

              // 回退到mammoth解析器
              try {
                console.log('使用mammoth解析器处理.doc文件');
                const result = await mammoth.convertToHtml({arrayBuffer});
                if (!result || typeof result.value !== 'string') {
                  throw new Error('Word文档解析失败 - 无法提取内容');
                }
                const textResult = await mammoth.extractRawText({arrayBuffer});
                resolve({
                  html: result.value,
                  text: textResult.value
                });
              } catch (mammothError) {
                console.error('所有解析方法都失败了');

                // 最终回退：提供转换指导
                const guidanceHtml = `
                  <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border: 1px solid #ffeaa7;">
                    <h3 style="color: #856404; margin-top: 0;">⚠️ 无法解析.doc文件</h3>
                    <p><strong>文件：</strong> ${file.name}</p>
                    <p><strong>建议解决方案：</strong></p>
                    <ol>
                      <li>使用Microsoft Word打开文件，另存为.docx格式</li>
                      <li>使用在线转换工具将.doc转换为.docx</li>
                      <li>复制文件内容并粘贴到文本编辑器中</li>
                    </ol>
                    <p style="margin-bottom: 0;"><em>转换后的.docx文件将能够完美解析。</em></p>
                  </div>
                `;

                resolve({
                  html: guidanceHtml,
                  text: `无法解析.doc文件: ${file.name}\n\n建议：\n1. 转换为.docx格式\n2. 使用在线转换工具\n3. 手动复制内容`
                });
              }
            }
          } else if (type === 'excel') {
            if (typeof XLSX === 'undefined') {
              throw new Error('Excel处理库未加载，请刷新页面重试');
            }

            const workbook = XLSX.read(arrayBuffer, {
              type: 'array',
              cellStyles: true,
              cellDates: true
            });

            if (!workbook || !workbook.SheetNames || !Array.isArray(workbook.SheetNames) || workbook.SheetNames.length === 0) {
              throw new Error('Excel文件不包含任何工作表');
            }

            resolve(workbook);
          }
        } catch (error) {
          console.error(`处理文件 ${file.name} 失败:`, error);
          reject(new Error(`处理 ${file.name} 失败: ${error.message}`));
        }
      };

      reader.onerror = () => reject(new Error(`读取 ${file.name} 失败`));
      reader.readAsArrayBuffer(file);
    });
  }

  // 获取文件列表
  getFileList() {
    // 确保files.word和files.excel存在且是数组
    if (!this.files.word || !Array.isArray(this.files.word)) {
      this.files.word = [];
    }
    if (!this.files.excel || !Array.isArray(this.files.excel)) {
      this.files.excel = [];
    }

    return {
      word: this.files.word.map(f => ({name: f.name, type: f.type})),
      excel: this.files.excel.map(f => ({
        name: f.name,
        type: f.type,
        sheets: f.data && f.data.SheetNames ? f.data.SheetNames : []
      }))
    };
  }

  // 清理文本内容，保留表格格式
  cleanText(text) {
    if (!text) return '';

    // 检查是否包含表格格式（包含 | 和 - 字符的行）
    const hasTableFormat = /\|.*\|/.test(text) || /^-+-$/.test(text);

    if (hasTableFormat) {
      // 如果包含表格格式，只移除连续的空行，保留缩进和空格
      return text
        .replace(/\n{3,}/g, '\n\n')  // 将3个以上连续换行替换为2个
        .replace(/^\s*$\n/gm, '');   // 移除只包含空白字符的行
    } else {
      // 如果不包含表格格式，使用原来的清理方法
      return text
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .join('\n');
    }
  }

  // 生成合并的HTML内容
  generateCombinedHTML() {
    let html = '<html><head><meta charset="UTF-8"><title>文档导出</title>';
    html += '<style>';
    html += 'body { font-family: Arial, sans-serif; margin: 20px; }';
    html += 'h1, h2, h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 8px; }';
    html += 'table { border-collapse: collapse; width: 100%; margin: 15px 0; }';
    html += 'table, th, td { border: 1px solid #ddd; }';
    html += 'th, td { padding: 8px; text-align: left; }';
    html += 'th { background-color: #f2f2f2; font-weight: bold; }';
    html += 'tr:nth-child(even) { background-color: #f9f9f9; }';
    html += '.file-section { margin-bottom: 30px; border-bottom: 2px solid #eee; padding-bottom: 20px; }';
    html += '.file-title { color: #2196F3; font-size: 24px; margin-bottom: 15px; }';
    html += '</style></head><body>';

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word)) {
      this.files.word.forEach(file => {
        if (file && file.data !== undefined) {
          html += `<div class="file-section">`;
          html += `<h1 class="file-title">${file.name}</h1>`;

          if (typeof file.data === 'object' && file.data.html) {
            html += file.data.html;
          } else if (typeof file.data === 'string') {
            html += `<pre>${file.data}</pre>`;
          }
          html += `</div>`;
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel)) {
      this.files.excel.forEach(file => {
        if (!file || !file.data || !file.data.SheetNames) return;

        html += `<div class="file-section">`;
        html += `<h1 class="file-title">${file.name}</h1>`;

        file.data.SheetNames.forEach(sheetName => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (!Array.isArray(sheetData) || sheetData.length === 0) return;

          html += `<h2>${sheetName}</h2>`;
          html += '<table>';

          sheetData.forEach((row, rowIndex) => {
            if (!Array.isArray(row)) return;

            const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell.toString().trim() !== '');
            if (nonEmptyCells.length === 0) return;

            const tag = rowIndex === 0 ? 'th' : 'td';
            html += '<tr>';
            row.forEach(cell => {
              const cellContent = cell !== null && cell !== undefined ? cell.toString() : '';
              html += `<${tag}>${cellContent}</${tag}>`;
            });
            html += '</tr>';
          });

          html += '</table>';
        });
        html += `</div>`;
      });
    }

    html += '</body></html>';
    return html;
  }

  // 生成合并的Markdown内容 - 简化版本，专注内容
  generateCombinedMarkdown() {
    let markdown = '';

    // 简化的文档头部
    markdown += '# 文档内容\n\n';

    // 统计信息
    const wordCount = this.files.word ? this.files.word.length : 0;
    const excelCount = this.files.excel ? this.files.excel.length : 0;

    // 如果没有文件，简单说明
    if (wordCount === 0 && excelCount === 0) {
      markdown += '暂无文档内容\n\n';
      return markdown;
    }

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word) && this.files.word.length > 0) {
      this.files.word.forEach((file, index) => {
        if (file && file.data !== undefined) {
          // 简化的文件标题
          markdown += `## ${file.name}\n\n`;

          if (typeof file.data === 'object' && file.data.html) {
            // 使用Turndown转换HTML内容
            if (typeof TurndownService !== 'undefined') {
              const turndownService = new TurndownService({
                headingStyle: 'atx',
                codeBlockStyle: 'fenced',
                bulletListMarker: '-',
                hr: '---',
                strongDelimiter: '**',
                emDelimiter: '*'
              });

              // 增强表格处理
              const enhancedHtml = this.processWordTables(file.data.html);

              // 清理HTML并转换
              const cleanedHtml = this.cleanHtmlForMarkdown(enhancedHtml);
              let convertedContent = turndownService.turndown(cleanedHtml);

              // 处理表格Markdown标记
              convertedContent = this.processTableMarkdown(convertedContent);

              // 进一步优化内容结构
              convertedContent = this.optimizeMarkdownStructure(convertedContent);

              markdown += convertedContent + '\n\n';
            } else {
              // 回退到文本内容
              markdown += this.formatTextContent(file.data.text || '无法显示内容') + '\n\n';
            }
          } else if (typeof file.data === 'string') {
            markdown += this.formatTextContent(file.data) + '\n\n';
          }
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel) && this.files.excel.length > 0) {
      this.files.excel.forEach((file, fileIndex) => {
        if (!file || !file.data || !file.data.SheetNames) return;

        // 简化的Excel文件标题
        markdown += `## ${file.name}\n\n`;

        // 处理每个工作表
        file.data.SheetNames.forEach((sheetName, sheetIndex) => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (!Array.isArray(sheetData) || sheetData.length === 0) {
            markdown += `### ${sheetName}\n\n*工作表为空*\n\n`;
            return;
          }

          // 简化的工作表标题
          markdown += `### ${sheetName}\n\n`;

          // 生成表格 - 使用简化的表格格式
          if (sheetData.length > 0) {
            markdown += this.generateSimpleTable(sheetData);
          }
        });
      });
    }

    return markdown;
  }

  // 清理HTML内容以便更好地转换为Markdown
  cleanHtmlForMarkdown(html) {
    if (!html) return '';

    // 创建临时DOM元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 移除样式属性
    const allElements = tempDiv.querySelectorAll('*');
    allElements.forEach(el => {
      el.removeAttribute('style');
      el.removeAttribute('class');
    });

    // 确保表格有正确的结构
    const tables = tempDiv.querySelectorAll('table');
    tables.forEach(table => {
      // 确保第一行是表头
      const firstRow = table.querySelector('tr');
      if (firstRow) {
        const cells = firstRow.querySelectorAll('td');
        cells.forEach(cell => {
          const th = document.createElement('th');
          th.innerHTML = cell.innerHTML;
          cell.parentNode.replaceChild(th, cell);
        });
      }
    });

    return tempDiv.innerHTML;
  }

  // 优化Markdown内容结构
  optimizeMarkdownStructure(content) {
    if (!content) return '';

    // 清理多余的空行，但保持结构
    content = content.replace(/\n{4,}/g, '\n\n\n');

    // 确保标题前后有适当的空行
    content = content.replace(/\n(#{1,6}\s)/g, '\n\n$1');
    content = content.replace(/(#{1,6}\s[^\n]+)\n/g, '$1\n\n');

    // 确保表格前后有空行
    content = content.replace(/\n(\|[^\n]+\|)\n/g, '\n\n$1\n');
    content = content.replace(/(\|[^\n]+\|)\n([^|\n])/g, '$1\n\n$2');

    // 确保列表项结构清晰
    content = content.replace(/\n(-\s)/g, '\n\n$1');

    // 确保段落分隔清晰
    content = content.replace(/([^.\n])\n([A-Z\u4e00-\u9fa5])/g, '$1\n\n$2');

    return content.trim();
  }

  // 格式化文本内容
  formatTextContent(text) {
    if (!text) return '';

    // 清理文本，保持段落结构
    let formatted = text.trim();

    // 确保段落之间有适当的分隔
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    // 识别可能的标题行（全大写或特殊格式）
    formatted = formatted.replace(/^([A-Z\u4e00-\u9fa5]{2,}[^\n]*?)$/gm, '**$1**');

    return formatted;
  }

  // 生成简化表格格式
  generateSimpleTable(sheetData) {
    if (!Array.isArray(sheetData) || sheetData.length === 0) {
      return '*表格为空*\n\n';
    }

    let markdown = '';

    // 处理表头
    const headerRow = sheetData[0];
    if (Array.isArray(headerRow) && headerRow.length > 0) {
      // 表头行
      markdown += '| ' + headerRow.map((cell, colIndex) => {
        const cellStr = cell !== null && cell !== undefined ? cell.toString().trim() : '';
        return cellStr || `列${colIndex + 1}`;
      }).join(' | ') + ' |\n';

      // 分隔线
      markdown += '| ' + headerRow.map(() => '---').join(' | ') + ' |\n';

      // 数据行
      for (let i = 1; i < sheetData.length; i++) {
        const row = sheetData[i];
        if (!Array.isArray(row)) continue;

        // 检查是否为空行
        const nonEmptyCells = row.filter(cell =>
          cell !== null && cell !== undefined && cell.toString().trim() !== ''
        );
        if (nonEmptyCells.length === 0) continue;

        // 确保行的长度与表头一致
        const paddedRow = [...row];
        while (paddedRow.length < headerRow.length) {
          paddedRow.push('');
        }

        markdown += '| ' + paddedRow.slice(0, headerRow.length).map(cell => {
          const cellStr = cell !== null && cell !== undefined ? cell.toString().trim() : '';
          return cellStr || '-';
        }).join(' | ') + ' |\n';
      }
    }

    markdown += '\n';
    return markdown;
  }

  // 生成基本表格格式（回退方案）
  generateBasicTable(sheetData, sheetName) {
    let markdown = '**📋 表格数据**:\n\n';

    if (!Array.isArray(sheetData) || sheetData.length === 0) {
      return '> ⚠️ 表格数据为空\n\n';
    }

    // 处理表头
    const headerRow = sheetData[0];
    if (Array.isArray(headerRow) && headerRow.length > 0) {
      // 表头行
      markdown += '| ' + headerRow.map((cell, colIndex) => {
        const cellStr = cell !== null && cell !== undefined ? cell.toString().trim() : '';
        const displayStr = cellStr || `列${colIndex + 1}`;
        return displayStr;
      }).join(' | ') + ' |\n';

      // 分隔线
      markdown += '| ' + headerRow.map(() => '---').join(' | ') + ' |\n';

      // 数据行
      let validRowCount = 0;
      for (let i = 1; i < sheetData.length; i++) {
        const row = sheetData[i];
        if (!Array.isArray(row)) continue;

        // 检查是否为空行
        const nonEmptyCells = row.filter(cell =>
          cell !== null && cell !== undefined && cell.toString().trim() !== ''
        );
        if (nonEmptyCells.length === 0) continue;

        validRowCount++;

        // 确保行的长度与表头一致
        const paddedRow = [...row];
        while (paddedRow.length < headerRow.length) {
          paddedRow.push('');
        }

        markdown += '| ' + paddedRow.slice(0, headerRow.length).map(cell => {
          const cellStr = cell !== null && cell !== undefined ? cell.toString().trim() : '';
          return cellStr || '-';
        }).join(' | ') + ' |\n';
      }

      // 表格结束说明
      markdown += `\n> 📊 工作表 \`${sheetName}\` 数据展示完成，共 ${validRowCount} 行有效数据\n\n`;
    }

    return markdown;
  }

  // 增强Word表格处理
  processWordTables(html) {
    if (!html) return '';

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const tables = tempDiv.querySelectorAll('table');
    if (!tables || tables.length === 0) return html;

    tables.forEach((table, tableIndex) => {
      // 提取表格数据
      const tableData = this.extractTableData(table);

      if (tableData.length > 0) {
        let tableMarkdown = '';

        if (this.tableFormatter) {
          // 使用高级表格格式化器
          tableMarkdown = this.tableFormatter.formatTable(tableData, {
            tableName: `表格${tableIndex + 1}`,
            maxCellWidth: 25,
            minCellWidth: 4,
            emptyCell: '-',
            numberFormat: true
          });
        } else {
          // 使用基本格式化
          tableMarkdown = this.generateBasicTable(tableData, `表格${tableIndex + 1}`);
        }

        // 创建包含Markdown的容器
        const markdownContainer = document.createElement('div');
        markdownContainer.className = 'word-table-markdown';
        markdownContainer.setAttribute('data-markdown', tableMarkdown);
        markdownContainer.innerHTML = `<pre class="table-markdown">${tableMarkdown}</pre>`;

        // 替换原表格
        table.parentNode.replaceChild(markdownContainer, table);
      }
    });

    return tempDiv.innerHTML;
  }

  // 从HTML表格提取数据
  extractTableData(table) {
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach(row => {
      const cells = row.querySelectorAll('th, td');
      const rowData = [];

      cells.forEach(cell => {
        const cellText = cell.textContent.trim();
        rowData.push(cellText);
      });

      if (rowData.length > 0) {
        data.push(rowData);
      }
    });

    return data;
  }

  // 处理表格Markdown标记
  processTableMarkdown(content) {
    if (!content) return '';

    // 查找并替换表格Markdown标记
    return content.replace(/<pre class="table-markdown">([\s\S]*?)<\/pre>/g, (match, tableContent) => {
      return tableContent.trim();
    });
  }

  // 生成合并的JSON内容
  generateCombinedJSON() {
    const result = {
      exportTime: new Date().toISOString(),
      files: {
        word: [],
        excel: []
      }
    };

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word)) {
      this.files.word.forEach(file => {
        if (file && file.data !== undefined) {
          const wordData = {
            name: file.name,
            type: 'word'
          };

          if (typeof file.data === 'object' && file.data.html && file.data.text) {
            wordData.html = file.data.html;
            wordData.text = file.data.text;
          } else if (typeof file.data === 'string') {
            wordData.text = file.data;
          }

          result.files.word.push(wordData);
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel)) {
      this.files.excel.forEach(file => {
        if (!file || !file.data || !file.data.SheetNames) return;

        const excelData = {
          name: file.name,
          type: 'excel',
          sheets: {}
        };

        file.data.SheetNames.forEach(sheetName => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (Array.isArray(sheetData) && sheetData.length > 0) {
            excelData.sheets[sheetName] = sheetData;
          }
        });

        result.files.excel.push(excelData);
      });
    }

    return JSON.stringify(result, null, 2);
  }

  // 生成合并的文本内容
  generateCombinedText() {
    let text = '';

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word)) {
      this.files.word.forEach(file => {
        if (file && file.data !== undefined) {
          // 添加文件名作为标题
          text += `=== ${file.name} ===\n\n`;

          // 使用HTML内容提取文本和表格
          if (typeof file.data === 'object' && file.data.html) {
            // 创建临时DOM元素来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = file.data.html;

            // 处理文档中的所有表格，将它们转换为文本表示
            const tables = tempDiv.querySelectorAll('table');

            if (tables && tables.length > 0) {
              tables.forEach((table, tableIndex) => {
                // 获取所有行
                const rows = table.querySelectorAll('tr');
                if (!rows || rows.length === 0) return;

                // 计算每列的最大宽度
                const colWidths = [];
                rows.forEach(row => {
                  const cells = row.querySelectorAll('th, td');
                  cells.forEach((cell, i) => {
                    const cellText = cell.textContent.trim();
                    colWidths[i] = Math.max(colWidths[i] || 0, cellText.length);
                  });
                });

                // 创建表格的文本表示
                let tableText = '';

                // 处理每一行
                rows.forEach((row, rowIndex) => {
                  const cells = row.querySelectorAll('th, td');
                  if (!cells || cells.length === 0) return;

                  // 格式化每个单元格
                  const formattedRow = Array.from(cells).map((cell, i) => {
                    const cellText = cell.textContent.trim();
                    // 左对齐，并填充空格到最大宽度
                    return cellText.padEnd(colWidths[i] || 0, ' ');
                  });

                  tableText += formattedRow.join(' | ') + '\n';

                  // 在表头后添加分隔线
                  if (rowIndex === 0) {
                    const separator = colWidths.map(width => '-'.repeat(width)).join('-+-');
                    tableText += separator + '\n';
                  }
                });

                // 替换原始表格为文本表示
                const placeholder = document.createElement('pre');
                placeholder.className = 'table-text';
                placeholder.textContent = tableText;
                table.parentNode.replaceChild(placeholder, table);
              });

              // 提取处理后的文本内容（包括表格的文本表示）
              const textContent = this.extractTextNodesRecursive(tempDiv);

              // 清理文本内容
              const cleanedText = this.cleanText(textContent);
              if (cleanedText) {
                text += cleanedText + '\n\n';
              }
            } else {
              // 如果没有表格，直接使用文本内容
              const cleanedText = this.cleanText(file.data.text);
              if (cleanedText) {
                text += cleanedText + '\n\n';
              }
            }
          } else if (typeof file.data === 'string') {
            // 回退到纯文本处理
            const cleanedText = this.cleanText(file.data);
            if (cleanedText) {
              text += cleanedText + '\n\n';
            }
          }
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel)) {
      this.files.excel.forEach(file => {
        if (!file || !file.data || !file.data.SheetNames) return;

        // 添加文件名作为标题
        text += `=== ${file.name} ===\n\n`;

        file.data.SheetNames.forEach(sheetName => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          // 使用header:1选项获取包含表头的数据
          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (!Array.isArray(sheetData) || sheetData.length === 0) return;

          // 添加工作表名称
          text += `【${sheetName}】\n\n`;

          // 计算每列的最大宽度，以便对齐
          const colWidths = [];
          sheetData.forEach(row => {
            if (!Array.isArray(row)) return;
            row.forEach((cell, i) => {
              const cellStr = cell !== null && cell !== undefined ? cell.toString() : '';
              colWidths[i] = Math.max(colWidths[i] || 0, cellStr.length);
            });
          });

          // 处理每一行数据，使用固定宽度格式化
          sheetData.forEach((row, rowIndex) => {
            if (!Array.isArray(row)) return;

            // 过滤掉空行和只包含空值的行
            const nonEmptyCells = row.filter(cell => cell !== null && cell !== undefined && cell.toString().trim() !== '');
            if (nonEmptyCells.length === 0) return;

            // 格式化每个单元格，使其对齐
            const formattedRow = row.map((cell, i) => {
              const cellStr = cell !== null && cell !== undefined ? cell.toString() : '';
              // 左对齐，并填充空格到最大宽度
              return cellStr.padEnd(colWidths[i] || 0, ' ');
            });

            text += formattedRow.join(' | ') + '\n';

            // 在表头后添加分隔线
            if (rowIndex === 0) {
              const separator = colWidths.map(width => '-'.repeat(width)).join('-+-');
              text += separator + '\n';
            }
          });
          text += '\n\n';
        });
      });
    }

    // 最后清理整个文本，移除多余的空行，但保留格式
    return text.replace(/\n{3,}/g, '\n\n');
  }

  // 递归提取文本节点
  extractTextNodesRecursive(node) {
    let result = '';

    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim();
      if (text) {
        result += text + ' ';
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 特殊处理表格文本
      if (node.nodeName === 'PRE' && node.className === 'table-text') {
        // 直接添加表格文本，保持格式
        result += '\n\n' + node.textContent + '\n\n';
        return result;
      }

      // 对于段落和标题元素，添加换行
      if (['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV', 'BR', 'LI'].includes(node.nodeName)) {
        result += '\n';
      }

      // 递归处理子节点
      for (const child of node.childNodes) {
        result += this.extractTextNodesRecursive(child);
      }

      // 在块级元素后添加额外的换行
      if (['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV', 'UL', 'OL'].includes(node.nodeName)) {
        result += '\n';
      }
    } else {
      // 处理其他类型的节点
      for (const child of node.childNodes) {
        result += this.extractTextNodesRecursive(child);
      }
    }

    return result;
  }

  // 删除单个文件
  removeFile(type, name) {
    if (!type || !name || !this.files[type]) {
      return false;
    }

    const index = this.files[type].findIndex(file => file && file.name === name);
    if (index === -1) {
      return false;
    }

    // 从数组中删除文件
    this.files[type].splice(index, 1);
    return true;
  }

  // 清空所有文件
  clear() {
    this.files.word = [];
    this.files.excel = [];
  }

  // 提取Word文档中的表格数据为结构化JSON
  extractWordTablesAsJSON(html) {
    if (!html) return [];

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    const tables = tempDiv.querySelectorAll('table');
    const tablesData = [];

    tables.forEach((table, tableIndex) => {
      const tableData = this.extractTableData(table);

      if (tableData.length > 0 && this.tableAnalyzer) {
        // 使用表格分析器进行结构化处理
        const structuredTable = this.tableAnalyzer.analyzeTableStructure(
          tableData,
          `Word文档表格 ${tableIndex + 1}`
        );
        tablesData.push(structuredTable);
      } else {
        // 回退到基本格式
        tablesData.push({
          tableName: `Word文档表格 ${tableIndex + 1}`,
          rawData: tableData
        });
      }
    });

    return tablesData;
  }

  // 生成紧凑的JSON内容，专为API传输优化
  generateCompactJSON() {
    const result = {
      files: {
        word: [],
        excel: []
      }
    };

    // 处理Word文档 - 紧凑格式
    if (this.files.word && Array.isArray(this.files.word)) {
      this.files.word.forEach(file => {
        if (file && file.data !== undefined) {
          const wordData = {
            name: file.name,
            content: ''
          };

          if (typeof file.data === 'object' && file.data.text) {
            // 只保留文本内容，去除HTML
            wordData.content = file.data.text;

            // 提取表格数据的简化版本
            const tablesData = this.extractWordTablesAsJSON(file.data.html);
            if (tablesData.length > 0) {
              // 只保留核心表格数据，去除元数据
              wordData.tables = tablesData.map(table => ({
                name: table.tableName,
                data: table.rawData
              }));
            }
          } else if (typeof file.data === 'string') {
            wordData.content = file.data;
          }

          result.files.word.push(wordData);
        }
      });
    }

    // 处理Excel文档 - 紧凑格式
    if (this.files.excel && Array.isArray(this.files.excel)) {
      this.files.excel.forEach(file => {
        if (!file || !file.data || !file.data.SheetNames) return;

        const excelData = {
          name: file.name,
          sheets: {}
        };

        file.data.SheetNames.forEach(sheetName => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (Array.isArray(sheetData) && sheetData.length > 0) {
            // 只保留原始数据，去除分析信息
            excelData.sheets[sheetName] = sheetData;
          }
        });

        result.files.excel.push(excelData);
      });
    }

    return JSON.stringify(result);
  }

  // 生成增强的JSON内容，包含结构化表格（用于导出）
  generateEnhancedJSON() {
    const result = {
      exportTime: new Date().toISOString(),
      metadata: {
        totalFiles: (this.files.word?.length || 0) + (this.files.excel?.length || 0),
        wordFiles: this.files.word?.length || 0,
        excelFiles: this.files.excel?.length || 0,
        compressionEnabled: this.dataCompressor?.compressionEnabled || false
      },
      files: {
        word: [],
        excel: []
      }
    };

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word)) {
      this.files.word.forEach(file => {
        if (file && file.data !== undefined) {
          const wordData = {
            name: file.name,
            type: 'word',
            content: {}
          };

          if (typeof file.data === 'object' && file.data.html && file.data.text) {
            wordData.content.html = file.data.html;
            wordData.content.text = file.data.text;

            // 提取结构化表格数据
            const tablesData = this.extractWordTablesAsJSON(file.data.html);
            if (tablesData.length > 0) {
              wordData.content.tables = tablesData;
            }
          } else if (typeof file.data === 'string') {
            wordData.content.text = file.data;
          }

          result.files.word.push(wordData);
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel)) {
      this.files.excel.forEach(file => {
        if (!file || !file.data || !file.data.SheetNames) return;

        const excelData = {
          name: file.name,
          type: 'excel',
          sheets: {}
        };

        file.data.SheetNames.forEach(sheetName => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (Array.isArray(sheetData) && sheetData.length > 0) {
            if (this.tableAnalyzer) {
              // 使用表格分析器进行结构化处理
              const structuredSheet = this.tableAnalyzer.analyzeTableStructure(
                sheetData,
                sheetName
              );
              excelData.sheets[sheetName] = structuredSheet;
            } else {
              // 回退到基本格式
              excelData.sheets[sheetName] = {
                tableName: sheetName,
                rawData: sheetData
              };
            }
          }
        });

        result.files.excel.push(excelData);
      });
    }

    return JSON.stringify(result, null, 2);
  }

  // 生成压缩的JSON数据（用于API传输）
  generateCompressedJSON() {
    const jsonData = this.generateCompactJSON(); // 使用紧凑格式

    if (this.dataCompressor) {
      const compressionResult = this.dataCompressor.smartCompress(JSON.parse(jsonData));
      return {
        data: compressionResult.data,
        compressed: compressionResult.compressed,
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressed ?
          ((1 - compressionResult.compressedSize / compressionResult.originalSize) * 100).toFixed(1) + '%' :
          '0%'
      };
    } else {
      return {
        data: jsonData,
        compressed: false,
        originalSize: jsonData.length,
        compressedSize: jsonData.length,
        compressionRatio: '0%'
      };
    }
  }

  // 检查数据大小是否超过API限制
  checkDataSize(data, maxSize = 100000) {
    const dataSize = typeof data === 'string' ? data.length : JSON.stringify(data).length;
    return {
      size: dataSize,
      withinLimit: dataSize <= maxSize,
      maxSize: maxSize,
      usage: ((dataSize / maxSize) * 100).toFixed(1) + '%'
    };
  }

  // 生成适合API传输的数据
  generateAPIData() {
    // 首先尝试紧凑JSON
    const compactData = this.generateCompactJSON();
    const sizeCheck = this.checkDataSize(compactData);

    if (sizeCheck.withinLimit) {
      return {
        data: compactData,
        format: 'compact_json',
        size: sizeCheck.size,
        compressed: false
      };
    }

    // 如果还是太大，尝试压缩
    if (this.dataCompressor) {
      const compressionResult = this.dataCompressor.smartCompress(JSON.parse(compactData));
      const compressedSizeCheck = this.checkDataSize(compressionResult.data);

      if (compressedSizeCheck.withinLimit) {
        return {
          data: compressionResult.data,
          format: 'compressed_json',
          size: compressedSizeCheck.size,
          compressed: true,
          compressionRatio: compressionResult.compressionRatio
        };
      }
    }

    // 如果压缩后还是太大，回退到文本格式
    const textData = this.generateCombinedText();
    const textSizeCheck = this.checkDataSize(textData);

    return {
      data: textData,
      format: 'text_fallback',
      size: textSizeCheck.size,
      compressed: false,
      warning: textSizeCheck.withinLimit ? null : 'Data exceeds API limit even in text format'
    };
  }

  // 生成增强的Markdown内容，包含结构化表格说明
  generateEnhancedMarkdown() {
    let markdown = '';

    // 文档头部
    markdown += '# 文档解析结果\n\n';

    // 添加元数据
    const wordCount = this.files.word?.length || 0;
    const excelCount = this.files.excel?.length || 0;

    markdown += '## 文档统计\n\n';
    markdown += `- **Word文档**: ${wordCount} 个\n`;
    markdown += `- **Excel文档**: ${excelCount} 个\n`;
    markdown += `- **导出时间**: ${new Date().toLocaleString()}\n`;
    markdown += `- **格式化器**: 增强结构化Markdown (AI优化)\n\n`;

    if (wordCount === 0 && excelCount === 0) {
      markdown += '暂无文档内容\n\n';
      return markdown;
    }

    // 处理Word文档
    if (this.files.word && Array.isArray(this.files.word) && this.files.word.length > 0) {
      markdown += '## Word文档内容\n\n';

      this.files.word.forEach((file, index) => {
        if (file && file.data !== undefined) {
          markdown += `### ${index + 1}. ${file.name}\n\n`;

          if (typeof file.data === 'object' && file.data.html) {
            // 提取表格数据
            const tablesData = this.extractWordTablesAsJSON(file.data.html);

            if (tablesData.length > 0) {
              markdown += '#### 表格数据\n\n';

              tablesData.forEach((tableData, tableIndex) => {
                if (tableData.rawData && Array.isArray(tableData.rawData) && tableData.rawData.length > 0) {
                  // 使用增强的表格格式化器
                  if (this.enhancedFormatter) {
                    const enhancedTable = this.enhancedFormatter.formatTable(tableData.rawData, {
                      tableName: tableData.tableName || `表格${tableIndex + 1}`,
                      source: file.name, // Word文档
                      includeMetadata: true,
                      includeUsageTips: true,
                      maxDisplayRows: 30
                    });
                    markdown += enhancedTable;
                  } else {
                    // 回退到原有格式化方式
                    markdown += `##### 表格 ${tableIndex + 1}: ${tableData.tableName}\n\n`;

                    if (tableData.metadata) {
                      markdown += '**📈 表格统计信息**:\n';
                      markdown += `- 总行数: ${tableData.metadata.totalRows}\n`;
                      markdown += `- 总列数: ${tableData.metadata.totalColumns}\n`;

                      if (tableData.metadata.dataTypes && tableData.metadata.dataTypes.length > 0) {
                        markdown += '- 列数据类型:\n';
                        tableData.metadata.dataTypes.forEach(col => {
                          markdown += `  - ${col.column}: ${col.type} (置信度: ${(col.confidence * 100).toFixed(1)}%)\n`;
                        });
                      }
                      markdown += '\n';
                    }

                    // 生成表格
                    markdown += this.generateStructuredMarkdownTable(tableData.rawData, tableData.metadata);

                    // 添加表格理解说明
                    markdown += this.generateTableExplanation(tableData);
                  }
                }
              });
            }

            // 添加文本内容
            if (file.data.text) {
              markdown += '#### 文档内容\n\n';
              markdown += this.formatTextContent(file.data.text) + '\n\n';
            }
          } else if (typeof file.data === 'string') {
            markdown += this.formatTextContent(file.data) + '\n\n';
          }
        }
      });
    }

    // 处理Excel文档
    if (this.files.excel && Array.isArray(this.files.excel) && this.files.excel.length > 0) {
      markdown += '## Excel文档内容\n\n';

      this.files.excel.forEach((file, fileIndex) => {
        if (!file || !file.data || !file.data.SheetNames) return;

        markdown += `### ${fileIndex + 1}. ${file.name}\n\n`;

        file.data.SheetNames.forEach((sheetName, sheetIndex) => {
          const worksheet = file.data.Sheets[sheetName];
          if (!worksheet) return;

          const sheetData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
          if (!Array.isArray(sheetData) || sheetData.length === 0) {
            markdown += `#### ${sheetName}\n\n*工作表为空*\n\n`;
            return;
          }

          markdown += `#### ${sheetName}\n\n`;

          // 使用增强的表格格式化器
          if (this.enhancedFormatter) {
            const enhancedTable = this.enhancedFormatter.formatTable(sheetData, {
              tableName: sheetName,
              source: file.name, // Excel文档
              includeMetadata: false, // Excel不需要元数据
              includeUsageTips: false, // Excel不需要使用建议
              maxDisplayRows: 100
            });
            markdown += enhancedTable;
          } else {
            // 回退到原有格式化方式
            // 分析表格结构
            let tableAnalysis = null;
            if (this.tableAnalyzer) {
              tableAnalysis = this.tableAnalyzer.analyzeTableStructure(sheetData, sheetName);
            }

            if (tableAnalysis && tableAnalysis.metadata) {
              markdown += '**📈 工作表统计信息**:\n';
              markdown += `- 总行数: ${tableAnalysis.metadata.totalRows}\n`;
              markdown += `- 总列数: ${tableAnalysis.metadata.totalColumns}\n`;

              if (tableAnalysis.metadata.dataTypes && tableAnalysis.metadata.dataTypes.length > 0) {
                markdown += '- 列数据类型:\n';
                tableAnalysis.metadata.dataTypes.forEach(col => {
                  markdown += `  - ${col.column}: ${col.type} (置信度: ${(col.confidence * 100).toFixed(1)}%)\n`;
                });
              }

              if (tableAnalysis.metadata.statistics && Object.keys(tableAnalysis.metadata.statistics).length > 0) {
                markdown += '- 数值统计:\n';
                Object.entries(tableAnalysis.metadata.statistics).forEach(([colName, stats]) => {
                  markdown += `  - ${colName}: 平均值 ${stats.average.toFixed(2)}, 最小值 ${stats.min}, 最大值 ${stats.max}\n`;
                });
              }
              markdown += '\n';
            }

            // 生成表格
            markdown += this.generateStructuredMarkdownTable(sheetData, tableAnalysis?.metadata);

            // 添加表格理解说明
            if (tableAnalysis) {
              markdown += this.generateTableExplanation(tableAnalysis);
            }
          }
        });
      });
    }

    return markdown;
  }

  // 生成结构化Markdown表格
  generateStructuredMarkdownTable(tableData, metadata) {
    if (!Array.isArray(tableData) || tableData.length === 0) {
      return '*表格为空*\n\n';
    }

    let markdown = '';
    const headers = tableData[0] || [];
    const rows = tableData.slice(1);

    // 表头
    markdown += '| ' + headers.map((header, index) => {
      const headerText = header !== null && header !== undefined ? header.toString().trim() : '';
      const dataType = metadata?.dataTypes?.[index]?.type || '';
      const typeIcon = this.getTypeIcon(dataType);
      return `${typeIcon} ${headerText || `列${index + 1}`}`;
    }).join(' | ') + ' |\n';

    // 分隔线
    markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';

    // 数据行
    rows.forEach(row => {
      if (!Array.isArray(row)) return;

      const nonEmptyCells = row.filter(cell =>
        cell !== null && cell !== undefined && cell.toString().trim() !== ''
      );
      if (nonEmptyCells.length === 0) return;

      const paddedRow = [...row];
      while (paddedRow.length < headers.length) {
        paddedRow.push('');
      }

      markdown += '| ' + paddedRow.slice(0, headers.length).map((cell, index) => {
        const cellStr = cell !== null && cell !== undefined ? cell.toString().trim() : '';
        const dataType = metadata?.dataTypes?.[index]?.type || '';
        return this.formatCellValue(cellStr, dataType);
      }).join(' | ') + ' |\n';
    });

    markdown += '\n';
    return markdown;
  }

  // 获取数据类型图标
  getTypeIcon(dataType) {
    const icons = {
      'number': '�',
      'currency': '�',
      'percentage': '�',
      'date': '�',
      'email': '📧',
      'url': '�',
      'phone': '📞',
      'boolean': '✅',
      'text': '📝'
    };
    return icons[dataType] || '📝';
  }

  // 格式化单元格值
  formatCellValue(value, dataType) {
    if (!value) return '-';

    switch (dataType) {
      case 'currency':
        return `**${value}**`;
      case 'percentage':
        return `*${value}*`;
      case 'number':
        return `\`${value}\``;
      case 'date':
        return `📅 ${value}`;
      case 'email':
        return `📧 ${value}`;
      case 'url':
        return `🔗 ${value}`;
      case 'phone':
        return `📞 ${value}`;
      case 'boolean':
        return value.toLowerCase() === 'true' || value === '1' ? '✅ 是' : '❌ 否';
      default:
        return value;
    }
  }

  // 生成表格理解说明
  generateTableExplanation(tableData) {
    if (!tableData || !tableData.metadata) return '';

    let explanation = '> **🤖 AI理解指南**:\n';
    explanation += '> \n';
    explanation += `> 这是一个包含 ${tableData.metadata.totalRows} 行 ${tableData.metadata.totalColumns} 列的数据表格。\n`;

    if (tableData.metadata.dataTypes && tableData.metadata.dataTypes.length > 0) {
      explanation += '> \n';
      explanation += '> **列结构说明**:\n';
      tableData.metadata.dataTypes.forEach((col, index) => {
        const icon = this.getTypeIcon(col.type);
        explanation += `> - 第${index + 1}列 "${col.column}": ${icon} ${this.getTypeDescription(col.type)}\n`;
      });
    }

    if (tableData.metadata.statistics && Object.keys(tableData.metadata.statistics).length > 0) {
      explanation += '> \n';
      explanation += '> **数值分析**:\n';
      Object.entries(tableData.metadata.statistics).forEach(([colName, stats]) => {
        explanation += `> - ${colName}: 数值范围 ${stats.min}-${stats.max}, 平均值 ${stats.average.toFixed(2)}\n`;
      });
    }

    explanation += '> \n';
    explanation += '> 💡 **使用建议**: 此表格适合进行数据分析、统计计算和可视化展示。\n\n';

    return explanation;
  }

  // 获取数据类型描述
  getTypeDescription(dataType) {
    const descriptions = {
      'number': '数值型数据，可进行数学运算',
      'currency': '货币金额，适合财务分析',
      'percentage': '百分比数据，适合比例分析',
      'date': '日期时间，适合时间序列分析',
      'email': '电子邮箱地址',
      'url': '网址链接',
      'phone': '电话号码',
      'boolean': '布尔值，表示是/否状态',
      'text': '文本内容，适合文本分析'
    };
    return descriptions[dataType] || '文本数据';
  }

  // 文本转HTML
  textToHtml(text) {
    if (!text) return '<p>无内容</p>';

    // 按段落分割
    const paragraphs = text.split(/\n\s*\n/);
    let html = '';

    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        // 处理单行换行
        const lines = paragraph.split('\n').map(line => line.trim()).filter(line => line);
        if (lines.length === 1) {
          html += `<p>${this.escapeHtml(lines[0])}</p>`;
        } else if (lines.length > 1) {
          html += `<p>${lines.map(line => this.escapeHtml(line)).join('<br>')}</p>`;
        }
      }
    });

    return html || '<p>无法提取内容</p>';
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}