import json
from ParseAndValiatePriceChangeParam import (
    getPriceByPriceClassify,
    checkIsPriceMatchWithDeviation,
)

# 测试数据
test_json = {
    "preSurPriceGroups": [
        {
            "prePrecisePriceList": [{"parseType": "精准", "price": "275"}],
            "preDurationPriceList": [
                {"leftPrice": 270, "rightPrice": 279, "parseType": "模糊的区间价格"}
            ],
            "surPrecisePriceList": [{"parseType": "精准", "price": "405"}],
            "surDurationPriceList": [
                {"leftPrice": 400, "rightPrice": 409, "parseType": "模糊的区间价格"}
            ],
            "changePrecisePriceList": [{"parseType": "精准", "price": "130"}],
            "changeDurationPriceList": [
                {"leftPrice": 121, "rightPrice": 139, "parseType": "模糊的区间价格"}
            ],
        }
    ]
}


def test_price_match_with_deviation():
    print("===== 测试支持误差范围的价格匹配功能 =====")

    # 获取前序价格列表
    pre_prices = getPriceByPriceClassify(test_json, "preSurPriceGroups", "prePrice")
    print(f"前序价格列表有 {len(pre_prices)} 项:")
    for price in pre_prices:
        print(json.dumps(price, indent=2, ensure_ascii=False))

    # 1. 无误差测试 (相当于精准匹配)
    print("\n1. 无误差测试 (相当于精准匹配):")

    # 精准匹配价格275
    is_match, matched_item = checkIsPriceMatchWithDeviation(275, pre_prices, None)
    print(f"价格275匹配结果(无误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 不匹配价格290
    is_match, matched_item = checkIsPriceMatchWithDeviation(290, pre_prices, None)
    print(f"价格290匹配结果(无误差): {is_match}")

    # 2. 正向误差测试 (只允许向上偏差)
    deviation_positive = {"minDeviation": 0, "maxDeviation": 20}
    print(f"\n2. 正向误差测试 {deviation_positive}:")

    # 精准价格275，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        275, pre_prices, deviation_positive
    )
    print(f"价格275匹配结果(正向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 高于精准价格但在误差范围内，290 < 275+20，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        290, pre_prices, deviation_positive
    )
    print(f"价格290匹配结果(正向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 超出误差范围，300 > 275+20，不应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        300, pre_prices, deviation_positive
    )
    print(f"价格300匹配结果(正向误差): {is_match}")

    # 3. 负向误差测试 (只允许向下偏差)
    deviation_negative = {"minDeviation": -20, "maxDeviation": 0}
    print(f"\n3. 负向误差测试 {deviation_negative}:")

    # 精准价格275，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        275, pre_prices, deviation_negative
    )
    print(f"价格275匹配结果(负向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 低于精准价格但在误差范围内，260 > 275-20，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        260, pre_prices, deviation_negative
    )
    print(f"价格260匹配结果(负向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 超出误差范围，250 < 275-20，不应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        250, pre_prices, deviation_negative
    )
    print(f"价格250匹配结果(负向误差): {is_match}")

    # 4. 双向误差测试 (允许上下偏差)
    deviation_both = {"minDeviation": -10, "maxDeviation": 10}
    print(f"\n4. 双向误差测试 {deviation_both}:")

    # 精准价格275，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        275, pre_prices, deviation_both
    )
    print(f"价格275匹配结果(双向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 高于精准价格但在误差范围内，283 < 275+10，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        283, pre_prices, deviation_both
    )
    print(f"价格283匹配结果(双向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 低于精准价格但在误差范围内，267 > 275-10，应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        267, pre_prices, deviation_both
    )
    print(f"价格267匹配结果(双向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 超出误差范围，290 > 275+10，不应该匹配
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        290, pre_prices, deviation_both
    )
    print(f"价格290匹配结果(双向误差): {is_match}")

    # 5. 对区间价格添加误差
    print("\n5. 对区间价格添加误差:")
    # 正常情况下区间是[270, 279]

    # 使用正向误差 +20
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        299, pre_prices, deviation_positive
    )
    print(f"价格299匹配结果(区间价格+正向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 使用负向误差 -20
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        255, pre_prices, deviation_negative
    )
    print(f"价格255匹配结果(区间价格+负向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")

    # 使用双向误差 ±10
    is_match, matched_item = checkIsPriceMatchWithDeviation(
        265, pre_prices, deviation_both
    )
    print(f"价格265匹配结果(区间价格+双向误差): {is_match}")
    if is_match:
        print(f"匹配项: {json.dumps(matched_item, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    test_price_match_with_deviation()
