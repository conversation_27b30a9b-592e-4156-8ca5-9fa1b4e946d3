import json
from ParseAndValiatePriceChangeParam import convert_price_to_numeric


def test_convert_price_to_numeric():
    print("===== 测试价格数值转换函数 =====")

    # 测试有效的整数字符串
    success, value = convert_price_to_numeric("100")
    print(f"转换 '100': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试有效的浮点数字符串
    success, value = convert_price_to_numeric("99.9")
    print(f"转换 '99.9': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试整数型浮点数字符串
    success, value = convert_price_to_numeric("100.0")
    print(f"转换 '100.0': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试已经是整数类型
    success, value = convert_price_to_numeric(200)
    print(f"转换 200: 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试已经是浮点数类型
    success, value = convert_price_to_numeric(299.9)
    print(f"转换 299.9: 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试无效的字符串
    success, value = convert_price_to_numeric("abc")
    print(f"转换 'abc': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试负数
    success, value = convert_price_to_numeric("-100")
    print(f"转换 '-100': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    success, value = convert_price_to_numeric(-100)
    print(f"转换 -100: 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试零
    success, value = convert_price_to_numeric("0")
    print(f"转换 '0': 成功={success}, 结果={value}, 类型={type(value).__name__}")

    success, value = convert_price_to_numeric(0)
    print(f"转换 0: 成功={success}, 结果={value}, 类型={type(value).__name__}")

    # 测试None
    success, value = convert_price_to_numeric(None)
    print(
        f"转换 None: 成功={success}, 结果={value}, 类型={type(value).__name__ if value is not None else 'NoneType'}"
    )


if __name__ == "__main__":
    test_convert_price_to_numeric()
