import json

def format_json_placeholder(template: str, obj1, obj2) -> str:
    """
    将两个对象JSON序列化后填充到字符串模板的占位符
    
    :param template: 含两个{}占位符的字符串模板
    :param obj1: 第一个可序列化对象
    :param obj2: 第二个可序列化对象
    :return: 格式化后的字符串
    
    示例：
    >>> data = {"name": "张三", "age": 25}
    >>> lst = [1, 2, 3]
    >>> print(format_json_placeholder("用户数据：{}，订单列表：{}", data, lst))
    用户数据：{"name": "张三", "age": 25}，订单列表：[1, 2, 3]
    """
    # 序列化时保持中文可读性（ensure_ascii=False）
    json_str1 = json.dumps(obj1, ensure_ascii=False, indent=None)  # 紧凑格式
    
    
    
    try:
        return template.format(json_str1, obj2)
    except IndexError:
        raise ValueError("模板需要包含两个{}占位符") from None
    except Exception as e:
        raise RuntimeError(f"格式化失败: {str(e)}") from e
    

def genPrompt(obj1, obj2) -> str:
    template = '''你是一名资深的机票行业运营，现在用户反馈机票搜索有变价，请结合用户反馈问题描述，搜索数据明细，搜索业务规则，变价场景和变价分析规则进行搜索变价分析
# 变价分析目标
搜索变价分析目标：根据用户的反馈question，搜索数据明细，搜索业务规则，变价场景和变价分析规则分析是否发生用户反馈的变价，并且进行变价归因和变价定责

#入参
##：入参说明
question参数： 用户反馈的信息
param 参数：结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
##：入参明细
question: {}
param: {}

#机票搜索业务说明
## 搜索流程
1、搜索分为list搜索和ota搜索，用户搜索先进list，list展示用户选择的航线，起飞日期展示不同航班号的价格（一个航班号在list也只有一个航班号，通常是用户能看到的最低价）
2、用户在list页点击具体的航班会进入ota搜索，ota展示同航班，不同tag的报价，ota的tag分为(tag, oritag)，ota正常情况下有一个tag跟list tag(ota.tag = list.tag || ota.oriTag = list.tag)和价格(price)都一致的报价；从list进ota的血缘关系可以 ota.listTradeId = list.tradeId 关联
3、用户可能进行多次list搜索，也可以从同一个list点同一航班或不同航班多次进入ota搜索
4、每一次搜索事件都对应有一个唯一的tradeId

#搜索变价业务说明
## 用户视角能感知搜索变价业务场景
以下是用户视角能感知到的分维度变价场景，几个维度可以组合
### 用户视角能感知搜索流程维度变价场景
1、用户多次list搜索变价场景
2、单次list -> 单次ota， ota没有list的价格（list -> ota 具有血缘关系）
3、单次list触发多次ota，部分ota/全部ota 没有list的价格（list -> ota 具有血缘关系）

### 用户视角能感知产品维度变价场景
1、同航班变价
2、不同航班同航司变价
3、不同航班不同航变变价
4、不同航线变价

### 用户视角能感知的时间维度变价场景
1、秒级/分钟级/小时级/天级

### 用户视角变价场景定责说明
#### 平台无责场景（通常我们分析变价可以直接忽略此类场景）
1、不同航线变价
2、不同航班变价
3、不同航司变价
4、风控用户：如黄牛用户（用户标签包含 HNMX,SBHN,ZFHN,DJH,HSRY），抓取用户已经被平台识别投毒（poison=true）
5、用户搜索筛选项变化：如filters内容不一致
6、用户搜索时身份变化：用户标签（ZSJ6,PTGP,ZSJ6HEI有一个不同就算不同），乘机人不一致
7、同航班搜索时间跨度大（目前天级维度的就可判断平台无责，小时级维度的需要进一步分析原因定责

#### 同航班时间跨度范围不大的定责
1、多次搜索舱位变化，一般是航班基础数据(cabin)变化，用户平台均无责
2、票面价变化，一般是航班基础数据变化（viewPrice），用户平台均无责
3、供应（wrapperId）变化且票面没变，用户平台均无责


## 数据字段说明
### param结构说明
结构化的搜索参数，两级分组，先按航班号维度分组，再按tradeId分组
### param明细字段
searchDateTime:搜索时间
departureDate:航班起飞日期
depTime:航班起飞时间
tradeId：每次搜索事件唯一id
tSource:list/ota搜索枚举
cabinType:舱等信息，目前只有ota能拿到舱等，list可以默认经济舱价格
price:展示价格
coupon:代金券，影响展示价格
cut:营销金额，影响展示价格
xCut:辅营营销金额，影响展示价格
tag:tag
oriTag:实tag，ota搜索会有根据一个实oriTag包装出多个tag的场景，这类case理论上他们之间的viewPrice和basePrice是一样的
expVendor:膨胀金金额，跟expansionType组合判断本价格是否用了膨胀金
expansionType:膨胀金计算类型，跟expVendor组合使用，当expVendor > 0时，根据下面枚举决定是否使用膨胀金，DURING说明当前搜索使用了膨胀金
   LIMIT(0, "不允许参与"),
   ALLOW(1, "可参与"),
   DURING(2, "参与中")
poison:投毒标识,=true说明平台识别的非法用户，进行了投毒
basicLabels:用户标签，识别用户身份使用（用户身份影响价格包装）
filters:搜索筛选项，如舱等选择，乘机人类型选择都可能影响价格
passengers:乘机人信息，影响价格（如特殊产品低价只有特定乘机人能买,会员产品,不同乘机人绑定不同代金券和营销）
wrapperId:供应id，供应不一致会导致价格变化
cabin:舱位，舱位变化大概率价格变化
packagePrice:包装价
basePrice:政策价
viewPrice:票面价
policyId:政策id，政策id不一致价格计算方式可能不一样
autoPriceDecreaseAmount:追价金额，平台低价策略，追价产品可根据次低价调整追价金额，达到低价优势,追价金额影响政策价计算
secondPrice:次低价，影响追价金额取值
CPT:报价生成时间，生成时间不一致可能是底层航班基础数据变化，结合viewPrice,basePrice,wrapperId,cabin看
allGoodItemPrice:加价商品金额，价格不一致可能是加价商品金额不一致

### param里价格计算依赖顺序
viewPrice -> basePrice -> packagePrice -> price
viewPrice一致，basePrice不一致影响因素：(secondPrice，autoPriceDecreaseAmount，policyId，wrapperId）
basePrice一致，packagePrice不一致影响因素(allGoodItemPrice,policyId,wrapperId)
packagePrice一致，price不一致影响因素(coupon,cut,xCut,[expVendor,expansionType],tag)

### 变价原因归因规则
1、展示价showPrice不同，判断投毒，如果结果不同，则归因为投毒
2、展示价showPrice不同，投毒相同，判断用户身份标签，身份标签含（HNMX,SBHN,ZFHN,DJH,HSRY），则归因为异常用户，身份标签不同（ZSJ6,PTGP,ZSJ6HEI有一个不同就算不同），则归因为用户身份发生变化
3、展示价showPrice不同，投毒相同，用户身份标签相同，判断乘机人是否相同，如果乘机人不同，则归因为乘机人填写不同
4、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，判断筛选项是否相同，如果结果为不同，则归因为筛选项
5、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，判断tag是否相同，如果不同则归因为无外露同tag
6、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，判断营销，如果包装价不变或变低，则归因为营销变化
7、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，判断膨胀金计算类型，如果相同
8、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位变化，则归因为航班基础数据变化-舱位变化
9、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价，政策价变，票面价变，舱位不变，则归因为航班基础数据变化-运价变化
10、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变，票面价未变，不同代理，则归因为供应变化-代理商调整价格
11、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变，票面价未变，同代理，同政策，追价金额变化，tag内次低价变化，则归因为供应变化-tag内次低价变化
12、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-同政策-追价金额变化，则归因为平台策略-追价变化
13、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-同政策-追价金额不变，则归因为其他
14、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价变-票面价未变-同代理-非同政策-报价生成时间变化，则归因为供应变化-报价生成时间变化
15、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-同tag包装价变价-政策价变-票面价未变-同代理-非同政策-低价被过滤 ，则归因为平台策略-低价被过滤
16、展示价showPrice不同，投毒相同，用户身份标签相同，乘机人相同，筛选项相同，tag相同，营销相同，膨胀金相同，判断包装价变价-政策价不变-加价变化（SPE_ADD）、限价变化、其余商品变化，则归因为平台策略-加价等商品变化

## 基于用户问题进行搜索变价分析说明

### 分析分类
1、 同航班不同list搜索变价分析
2、 同航班具有血缘关系的list->ota搜索分析
3、 同航班不同ota价格对比（如果存在同航班有ota价格，但是找不到有血缘关系的list价格）， 则此类ota价格需要跟其他无血缘关系的同航班，同ota价格分析

### param分析数据剪枝说明
1、 结合用户的问题，如果报价航班，航司，搜索日期，航班起飞日期，时间，价格跟 param里的报价项不匹配，可提前剪枝，忽略此类数据
2、 如果某类分析分组仅有一个搜索事件（如某个航班只有一次list搜索事件），可提前剪枝，忽略此类数据（记录剪枝原因）

### 分析前置数据处理
1、list搜索事件请按照搜索时间升序排序，如果没有搜索时间可从tradeId提取，如ops_slugger_250226.181810.10.90.5.84.1867071.8637025621_1 时间是(250226.181810)

### 价格比较说明
1、都是比展示价格，即price
2、同航班不同list价格比较，比价无需区分tag，直接比同航班的price，如果有变价则进行归因
3、list -> ota 的比价说明，优先选同航班，同tag进行对比(ota.tag = list.tag || ota.oriTag = list.tag)，有一个ota的价格没变价则认为未变价；如果没相同tag但是有价格一样的，也可认为未变价；如果分析有变价则进行归因
4、ota -> ota 比价说明，优先选同航班，同tag进行对比(ota.tag = ota.tag || ota.oriTag = ota.tag)，有一个ota的价格没变价则认为未变价；如果没相同tag但是有价格一样的，也可认为未变价；如果分析有变价则进行归因


### 分析步骤（以上两类分析都是执行下面步骤）
#### step1: 组合分类数据(如同航班，list搜索数据分组； 同航班)
#### step2：遍历分类数据 -> 执行剪枝规则-> 数据前置处理-> 执行比价分析 -> 记录是否变价结果[航班号（	)，tradeId, 展示价(price), tag, 原始tag(oriTag), 搜索时间(searchDateTime)，搜索时间差(searchTimeDiff 搜索时间差，时间差用人类阅读友好的方式展示，如xx秒，xx分钟xx秒), 搜索页(tSource), 是否变价(isPriceChage)，变价判断依据说明(priceChageDesc)]
#### step3: 如step2存在变价数据，对这组数据进行变价归因，输出归因信息推理信息(attributeDesc），归因(attributeReason）和 定责（attributeResp）

# 输出
1、结合用户是问题给出是否有发生过变价，字段名:是否发生过变价 = true|false， 发生过变价的依据，字段名：变价判断说明和依据
2、如果有变价，按照markdown数组格式格式输出变价信息,如果同航班有变价且有多个list搜索，尽量这些list分析都输出
        航班号，tradeId, 展示价, tag, 原始tag, 搜索时间，搜索时间差,搜索页, 是否变价，变价判断依据, 信息推理信息，归因和定责'''
    format_json_placeholder(template, obj1, obj2)



# 使用示例
if __name__ == "__main__":
    
    param = {"server": "api.example.com", "timeout": 30}
    qe = '用户问题'
    
    result = genPrompt(
        qe,
        param
        
    )
    print(result)
