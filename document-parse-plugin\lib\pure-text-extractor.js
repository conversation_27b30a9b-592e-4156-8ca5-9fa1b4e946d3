/**
 * 纯正文提取器
 * 专门从WPS和Word的乱码中提取纯正文内容
 */
class PureTextExtractor {
  constructor() {
    // 需要过滤的软件信息关键词
    this.softwareKeywords = [
      'WPS Office', 'Microsoft Word', 'Microsoft Office', 'Normal.dot', 'Normal.dotm',
      'KSOProductBuildVer', 'Times New Roman', 'Arial', 'Symbol', 'Courier',
      'Root Entry', 'WordDocument', 'Data', 'Table', 'SummaryInformation',
      'DocumentSummaryInformation', 'CompObj', '1Table', '0Table',
      '正文', '默认段落字体', '页眉', '页脚', '普通表格', '无列表',
      'DengXian', 'Cambria Math', 'Unknown'
    ];
    
    // 需要过滤的格式信息模式
    this.formatPatterns = [
      /Root Entry[^]*?CompObj/g,
      /Microsoft [^]*?Corporation/g,
      /WPS Office[^]*?\d+\.\d+\.\d+/g,
      /Times New Roman[^]*?Arial/g,
      /Normal\.dot[^]*?Administrator/g,
      /KSOProductBuildVer[^]*?\d+/g,
      /SummaryInformation[^]*?DocumentSummaryInformation/g,
      /[\x00-\x1F\x7F-\x9F]{3,}/g, // 控制字符
      /[!@#$%^&*()]{5,}/g, // 连续特殊字符
      /\d{10,}/g, // 长数字串
      /[a-zA-Z]{15,}/g, // 长英文串（可能是乱码）
      /(.)\1{6,}/g // 重复字符
    ];
    
    // 中文标点符号
    this.chinesePunctuation = '，。！？；：""''（）【】《》、';
    
    // 英文标点符号
    this.englishPunctuation = ',.!?;:\'"()[]<>/\\';
  }

  // 主要的纯正文提取方法
  extractPureText(rawText, originalText = '') {
    console.log('开始提取纯正文，原始长度:', rawText.length);
    
    // 方法1: 如果知道原文，直接精确提取
    if (originalText && rawText.includes(originalText)) {
      const exactResult = this.extractAroundOriginal(rawText, originalText);
      if (exactResult.length > 0) {
        console.log('使用精确提取方法');
        return this.finalClean(exactResult);
      }
    }
    
    // 方法2: 智能正文识别
    const intelligentResult = this.intelligentTextExtraction(rawText);
    if (intelligentResult.length > 0) {
      console.log('使用智能识别方法');
      return this.finalClean(intelligentResult);
    }
    
    // 方法3: 模式过滤提取
    const patternResult = this.patternBasedExtraction(rawText);
    if (patternResult.length > 0) {
      console.log('使用模式过滤方法');
      return this.finalClean(patternResult);
    }
    
    // 方法4: 统计学提取
    const statisticalResult = this.statisticalExtraction(rawText);
    console.log('使用统计学方法');
    return this.finalClean(statisticalResult);
  }

  // 方法1: 围绕原文精确提取
  extractAroundOriginal(rawText, originalText) {
    const index = rawText.indexOf(originalText);
    if (index === -1) return '';
    
    // 扩展搜索范围，寻找完整句子
    let start = index;
    let end = index + originalText.length;
    
    // 向前扩展到句子开始
    while (start > 0) {
      const char = rawText[start - 1];
      if (this.isSentenceEnd(char) || this.isGarbageChar(char)) {
        break;
      }
      start--;
      if (index - start > 100) break; // 限制扩展范围
    }
    
    // 向后扩展到句子结束
    while (end < rawText.length) {
      const char = rawText[end];
      if (this.isSentenceEnd(char) || this.isGarbageChar(char)) {
        if (this.isSentenceEnd(char)) end++; // 包含句号
        break;
      }
      end++;
      if (end - index > 200) break; // 限制扩展范围
    }
    
    return rawText.substring(start, end).trim();
  }

  // 方法2: 智能正文识别
  intelligentTextExtraction(rawText) {
    // 按段落分割
    const paragraphs = this.splitIntoParagraphs(rawText);
    const validParagraphs = [];
    
    for (const paragraph of paragraphs) {
      const score = this.scoreParagraph(paragraph);
      if (score > 50) { // 阈值可调整
        validParagraphs.push({
          text: paragraph,
          score: score
        });
      }
    }
    
    // 按得分排序，取最佳段落
    validParagraphs.sort((a, b) => b.score - a.score);
    
    // 合并前几个最佳段落
    const topParagraphs = validParagraphs.slice(0, 3);
    return topParagraphs.map(p => p.text).join(' ');
  }

  // 方法3: 模式过滤提取
  patternBasedExtraction(rawText) {
    let cleaned = rawText;
    
    // 移除软件信息
    for (const keyword of this.softwareKeywords) {
      const regex = new RegExp(keyword + '[^\\u4e00-\\u9fff]*', 'gi');
      cleaned = cleaned.replace(regex, ' ');
    }
    
    // 移除格式模式
    for (const pattern of this.formatPatterns) {
      cleaned = cleaned.replace(pattern, ' ');
    }
    
    // 提取有意义的文本片段
    const meaningfulParts = this.extractMeaningfulParts(cleaned);
    return meaningfulParts.join(' ');
  }

  // 方法4: 统计学提取
  statisticalExtraction(rawText) {
    // 字符频率分析
    const charFreq = this.analyzeCharFrequency(rawText);
    
    // 识别高频有意义字符
    const meaningfulChars = new Set();
    for (const [char, freq] of Object.entries(charFreq)) {
      if (freq > 2 && this.isMeaningfulChar(char)) {
        meaningfulChars.add(char);
      }
    }
    
    // 基于高频字符提取文本
    let extracted = '';
    let consecutiveValid = 0;
    
    for (const char of rawText) {
      if (meaningfulChars.has(char) || this.isBasicPunctuation(char)) {
        extracted += char;
        consecutiveValid++;
      } else {
        if (consecutiveValid > 5) {
          extracted += ' ';
        }
        consecutiveValid = 0;
      }
    }
    
    return extracted;
  }

  // 分割段落
  splitIntoParagraphs(text) {
    // 多种分割方式
    const separators = [
      /[。！？]\s*/g,  // 中文句号
      /[.!?]\s*/g,     // 英文句号
      /\n+/g,          // 换行
      /\s{3,}/g,       // 多个空格
      /[^\u4e00-\u9fff\u0020-\u007E]{5,}/g // 连续非正常字符
    ];
    
    let paragraphs = [text];
    
    for (const separator of separators) {
      const newParagraphs = [];
      for (const paragraph of paragraphs) {
        newParagraphs.push(...paragraph.split(separator));
      }
      paragraphs = newParagraphs;
    }
    
    return paragraphs
      .map(p => p.trim())
      .filter(p => p.length > 2);
  }

  // 段落评分
  scoreParagraph(paragraph) {
    let score = 0;
    
    // 基础长度分（但不要太长）
    if (paragraph.length >= 5 && paragraph.length <= 200) {
      score += Math.min(paragraph.length / 2, 30);
    }
    
    // 中文字符比例
    const chineseChars = paragraph.match(/[\u4e00-\u9fff]/g) || [];
    const chineseRatio = chineseChars.length / paragraph.length;
    score += chineseRatio * 40;
    
    // 可读字符比例
    const readableChars = paragraph.match(/[\u4e00-\u9fff\u0020-\u007E]/g) || [];
    const readableRatio = readableChars.length / paragraph.length;
    score += readableRatio * 20;
    
    // 包含软件信息扣分
    for (const keyword of this.softwareKeywords) {
      if (paragraph.includes(keyword)) {
        score -= 30;
      }
    }
    
    // 包含过多数字或特殊字符扣分
    const specialChars = paragraph.match(/[^\u4e00-\u9fff\u0020-\u007E]/g) || [];
    if (specialChars.length > paragraph.length * 0.3) {
      score -= 20;
    }
    
    // 重复字符扣分
    if (/(.)\1{3,}/.test(paragraph)) {
      score -= 15;
    }
    
    return Math.max(score, 0);
  }

  // 提取有意义的文本片段
  extractMeaningfulParts(text) {
    const parts = [];
    const words = text.split(/\s+/);
    let currentPart = '';
    
    for (const word of words) {
      if (this.isMeaningfulWord(word)) {
        currentPart += (currentPart ? ' ' : '') + word;
      } else {
        if (currentPart.length > 3) {
          parts.push(currentPart);
        }
        currentPart = '';
      }
    }
    
    // 处理最后一部分
    if (currentPart.length > 3) {
      parts.push(currentPart);
    }
    
    return parts.filter(part => part.length > 2);
  }

  // 字符频率分析
  analyzeCharFrequency(text) {
    const freq = {};
    for (const char of text) {
      freq[char] = (freq[char] || 0) + 1;
    }
    return freq;
  }

  // 判断是否为有意义的字符
  isMeaningfulChar(char) {
    const code = char.charCodeAt(0);
    return (code >= 0x4e00 && code <= 0x9fff) || // 中文
           (code >= 0x30 && code <= 0x39) ||     // 数字
           (code >= 0x41 && code <= 0x5A) ||     // 大写字母
           (code >= 0x61 && code <= 0x7A) ||     // 小写字母
           this.chinesePunctuation.includes(char) ||
           this.englishPunctuation.includes(char);
  }

  // 判断是否为有意义的词
  isMeaningfulWord(word) {
    if (!word || word.length < 1) return false;
    
    // 过滤软件关键词
    for (const keyword of this.softwareKeywords) {
      if (word.includes(keyword)) return false;
    }
    
    // 纯中文词
    if (/^[\u4e00-\u9fff]+$/.test(word)) return true;
    
    // 中英文混合但有意义的词
    if (/[\u4e00-\u9fff]/.test(word) && word.length <= 10) return true;
    
    // 短的英文词
    if (/^[a-zA-Z]+$/.test(word) && word.length <= 8) return true;
    
    // 包含数字但不全是数字
    if (/\d/.test(word) && !/^\d+$/.test(word) && word.length <= 10) return true;
    
    return false;
  }

  // 判断是否为句子结束符
  isSentenceEnd(char) {
    return '。！？.!?'.includes(char);
  }

  // 判断是否为垃圾字符
  isGarbageChar(char) {
    const code = char.charCodeAt(0);
    return code < 0x20 || // 控制字符
           (code > 0x7E && code < 0x4e00) || // 非中文非ASCII
           code > 0x9fff; // 超出中文范围
  }

  // 判断是否为基础标点
  isBasicPunctuation(char) {
    return this.chinesePunctuation.includes(char) || 
           this.englishPunctuation.includes(char) ||
           char === ' ';
  }

  // 最终清理
  finalClean(text) {
    if (!text) return '';
    
    return text
      .replace(/[\x00-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/\s{3,}/g, ' ') // 合并多余空格
      .replace(/(.)\1{4,}/g, '$1') // 移除过多重复字符
      .replace(/^\s+|\s+$/g, '') // 移除首尾空格
      .replace(/^[^\u4e00-\u9fff\w]*/, '') // 移除开头的非正常字符
      .replace(/[^\u4e00-\u9fff\w]*$/, '') // 移除结尾的非正常字符
      .trim();
  }

  // 批量处理多个测试用例
  batchExtract(testCases) {
    const results = [];
    
    for (const testCase of testCases) {
      console.log(`处理测试用例: ${testCase.name}`);
      
      const extracted = this.extractPureText(testCase.garbledText, testCase.originalText);
      const score = this.evaluateExtraction(extracted, testCase.originalText);
      
      results.push({
        name: testCase.name,
        original: testCase.originalText,
        extracted: extracted,
        score: score,
        success: score > 80
      });
      
      console.log(`提取结果: ${extracted}`);
      console.log(`评分: ${score}`);
    }
    
    return results;
  }

  // 评估提取效果
  evaluateExtraction(extracted, original) {
    let score = 0;
    
    // 包含原文
    if (original && extracted.includes(original)) {
      score += 60;
    }
    
    // 文本质量
    const cleanRatio = this.calculateCleanRatio(extracted);
    score += cleanRatio * 30;
    
    // 长度合理性
    if (original) {
      const lengthRatio = extracted.length / original.length;
      if (lengthRatio >= 1 && lengthRatio <= 3) {
        score += 10;
      }
    }
    
    return Math.min(score, 100);
  }

  // 计算清洁度
  calculateCleanRatio(text) {
    if (!text) return 0;
    
    const cleanChars = text.match(/[\u4e00-\u9fff\u0020-\u007E]/g) || [];
    return cleanChars.length / text.length;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PureTextExtractor;
} else if (typeof window !== 'undefined') {
  window.PureTextExtractor = PureTextExtractor;
}
