import json
import requests

def build_request_url(qualityCheckNo):
    """
    构造请求的URL和参数
    """
    base_url = "http://fuwu.qunar.com/qualityCheck/taskManage/QCInfoQuery"  # 移除末尾多余空格
    params = {
        "qualityCheckNo": qualityCheckNo,
        "domain" : "callcenter.qunar.com",
        "businessType" : 1,
        "curtStep" : -1,
        "problemTypeLevel2" : -1,
        "orderSource" : -1,
        "responsible" : -1,
        "compensateFlag" : -1,
        "confirmPenalty" : -1,
        "paymentStatus" : -1,
        "paymentType" : -1,
        "curtStatus" : -1,
        "forcePenalty" : -1,
        "fenxiao" : -1,
        "limit" : 20,
        "pageIndex" : 1
    }
    return base_url, params

def query_data_from_api(url, params, headers):
    """
    发起HTTP GET请求并获取数据
    """
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": f"请求失败: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析失败: {str(e)}"}
    except Exception as e:
        return {"error": f"发生错误: {str(e)}"}

def main(param):
    """
    主入口函数
    """
    # 从参数中提取必要的字段
    qualityCheckNo = param.get("qualityCheckNo")

    # 使用单引号包裹Cookie字符串，避免双引号冲突
    cookie = 'QN271AC=register_pc; _RSG=6qKgdEnIzL0OJuN0bZcLm9; _RDG=286a3dfec6536c27230c697a8e59b84aff; _RGUID=057d4b13-a7a9-4d06-9c15-9a39405f84d8; ajs_anonymous_id=0c9f334a-b172-41eb-9a1b-a853acd1173f; UBT_VID=1694001468182.jqpofs; quinn=7f762589abeb3b5064f2c7ddc9deb343bae1e05f3a85b9f8ebdaa4fc336139c018d5d861c513041664171dc11a67c805; ctt_june=1683616182042##iK3wWSaNVuPwawPwa%3DPNEKgmasDNWs0IESDAWKjwVK2mVDWhEDXOEDj8aDkGiK3siK3saKgnVR3maKanWKgwahPwaUvt; QN601=39b558768b43147f9d0e5ebdd1480e50; fid=510174da-e450-420c-bfd5-35a1c91efcc9; QN1=00012a0011d8682a3360de2d; QN99=4820; QN48=0000f0002f10682a55d074b3; 11344=1722403391463##iK3waSD8WUPwawPwastmXsg%2BWSHRESP%2BasTDEKGhWRjmXstOaRTDERawEDDAiK3siK3saKgsas3nWKXwVRjnWUPwaUvt; 11536=1722403391463##iK3wWs2NWwPwawPwasaAWKfhaSGDEDj%3DXS3mXKvAXsg%3DWPWRWKX8EPkTasX%3DiK3siK3saKgsas3nWKXwVRjwauPwaUvt; QN621=1490067914133%2Ctestssong%3DDEFAULT%26fr%3Dflight_dom_search%261490067914133%3DDEFAULT%261490067914133%252Ctestssong%3DDEFAULT; QN668=51%2C57%2C53%2C53%2C52%2C51%2C55%2C54%2C53%2C55%2C53%2C58%2C54; _RF1=************; _bfa=1.1694001468182.jqpofs.1.1739850823164.1739882003841.22.1.10650153142; QN300=organic; _vi=QIkbk4wHQw4kRDwMuyLw3BDxjw4NztcnS0-Ae07ixfD0J4qzvHoanuG6hx-kQjCkV9k-vtQ8Xv-KzHaEa8VXOSAhE3UIEL1rki635WtF43Vsd0KFY_CXyv8QwZX7sQo4jNjAbIeEblzRnnkU1nWlDT-Q0IXyawRz71-EO6PVvmNk; QN271SL=512ae91d48ec1a758f61fbf7ee7c4c08; QN271RC=512ae91d48ec1a758f61fbf7ee7c4c08; _q=U.dyjamnb6468; csrfToken=yi2pgPbkacSjtzFSildRDJlvV6oUpbGv; _s=s_P5CMA7RXKEM5FIPM7EXFCO6HVQ; _t=29147275; _v=MS6HmrBllqFtdLoUq9FcOlUvfLzK-zbd_wuEpomIsjmxIQ_aWq8h6yN9igp4vMR-mknHrF9iIp9BJBh2CETxf5RAYPqYACBqHxOE7YWzgbvAoTthrTF8HvKw8G4bL1svYbS0c6yAIFOfmsK-fHBjqb2W6I_RNi17J4RKLLt-c4A8; QN43=""; _i=""; QN42=%E5%8E%BB%E5%93%AA%E5%84%BF%E7%94%A8%E6%88%B7; QN74=callcenter.qunar.com; QN29=bf99d0a85b784778b1d07da0d24f2c90; ctf_june=1683616182042##iK3wVRtsVhPwawPwa%3DDAasD8XKjnERjNXStmasawas2AERfhESHhW%3DWRXPPniK3siK3saKg%3DaS2AVKP8VK28WhPwaUvt; cs_june=fd28b6824058a65d6a181ce56387d533b59d9c5d115f9e2a3721b000dcc3beedd12b314f1b428b9009dc41ae2f056095fc8963c0e34840e7732bc03e50c7c198b17c80df7eee7c02a9c1a6a5b97c1179ce864755a666d095d891f55f15bb228f5a737ae180251ef5be23400b098dd8ca; _uf=hongyuan.cao; console_record="2|1:0|10:1744096506|14:console_record|180:W3siaXBfcG9ydCI6ICIxMC45NS4xMzYuMTEwXzMzMTgiLCAicGFzc3dvcmQiOiAicm50OTVVRngxbGVmRUhhayJ9LCB7ImlwX3BvcnQiOiAiMTAuODguNzIuMjAzXzMzMDciLCAicGFzc3dvcmQiOiAiUFNiejVHZkRneVc4dTdwYyJ9XQ==|6319daad2d78140cf80309110d67893a6462037a75f398ee24d212af8020088b"; userId=hongyuan.cao; meta_qsso_token=DoKoSE35GKbuubRY; QN238=zh_cn; _mdp=9BF9FB590419BE8F925B26180B7B6F6B; currentId=65363039353165656137343034313438383539623438363261353461646333622C323032352D30342D32312031313A34393A30302C31302E37372E37362E3139392C686F6E677975616E2E63616F; JSESSIONID=2D73C7141A448C4518295625091F1859; QN166=hongyuan.cao'

    # 检查参数是否完整
    if not qualityCheckNo:
        return {"error": "缺少必要的参数: qualityCheckNo", "results": []}

    # 构造请求的URL和参数
    url, params = build_request_url(qualityCheckNo)

    # 构造请求的headers，设置cookie
    headers = {
        "Cookie": cookie,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    # 发起请求并获取数据
    response_data = query_data_from_api(url, params, headers)

    # 检查返回的数据是否包含错误信息
    if "error" in response_data:
        return {"error": response_data["error"], "results": []}

    # 返回标准化的结果结构
    return {
        "error": "",
        "results": response_data
    }

