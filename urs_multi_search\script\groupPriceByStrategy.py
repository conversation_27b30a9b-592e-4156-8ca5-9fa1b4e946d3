from collections import defaultdict
from urllib.parse import unquote_to_bytes
import json
from urllib.parse import quote
from typing import Tuple, Optional, List


def json_to_urlencoded(obj: object) -> Tuple[Optional[str], Optional[str]]:
    """
    将Python对象转换为URL编码的JSON字符串
    
    :param obj: 要转换的Python对象（需可JSON序列化）
    :return: (编码后的字符串, 错误信息) 元组
    """
    try:
        # 序列化为JSON字符串（保留Unicode字符）
        json_str = json.dumps(obj, 
                            ensure_ascii=False, 
                            separators=(',', ':'),  # 移除多余空格
                            check_circular=True)
    except TypeError as e:
        return None, f"对象包含不可JSON序列化的类型: {str(e)}"
    except Exception as e:
        return None, f"JSON序列化失败: {str(e)}"

    try:
        # 进行全量URL编码（safe参数为空字符串表示编码所有非字母数字字符）
        encoded_str = quote(json_str, safe='', encoding='utf-8', errors='strict')
        return encoded_str, None
    except (UnicodeEncodeError, TypeError) as e:
        return None, f"URL编码失败: {str(e)}"
    except Exception as e:
        return None, f"未知编码错误: {str(e)}"

def parse_urlencoded_json(data: dict, paramKey) -> tuple:
    """
    解析包含URL编码JSON字符串的字典参数
    
    :param data: 包含参数的字典
    :return: tuple(解析后的对象, 错误信息)
    """
    # 检查参数存在性
    if paramKey not in data:
        return None, {'status': 'error', 'message': "Missing required parameter: {}".format(paramKey)}
    
    raw_param = data[paramKey]
    
    # 类型检查
    if not isinstance(raw_param, (str, bytes)):
        return None, {'status': 'error', 'message': 'Invalid parameter type, expected string'}
    
    # 统一转为字符串处理
    if isinstance(raw_param, bytes):
        try:
            raw_param = raw_param.decode('utf-8')
        except UnicodeDecodeError:
            return None, {'status': 'error', 'message': 'Failed to decode bytes parameter'}

    # 尝试URL解码
    try:
        # 使用unquote_to_bytes处理特殊字符
        decoded_bytes = unquote_to_bytes(raw_param)
        decoded_str = decoded_bytes.decode('utf-8')
    except Exception as e:
        decoded_str = raw_param  # 解码失败则使用原始值

    # 处理空值情况
    if not decoded_str.strip():
        return None, {'status': 'error', 'message': 'Empty parameter after decoding'}

    # 尝试解析JSON
    try:
        json_obj = json.loads(decoded_str)
        return json_obj, {'status': 'success'}
    except json.JSONDecodeError as e:
        return None, {'status': 'error', 'message': f'JSON decode error: {str(e)}'}
    except Exception as e:
        return None, {'status': 'error', 'message': f'Unexpected error: {str(e)}'}

def split_price_changes(groups: dict, group_type: str) -> tuple:
    """拆分价格变化的分组（增强版）"""
    price_change = {}
    no_change = {}
    
    for key, items in groups.items():
        # 处理航司/航班号分组
        if group_type in ('airline', 'flight'):
            # 获取所有唯一价格（字符串形式）
            prices = {str(item['price']) for item in items}
            
            if len(prices) > 1:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': list({item['flightNo'] for item in items}),  # 去重后的航班号列表
                    'prices': list(prices)  # 保证单值
                }
        
        # 处理OTA关联数据
        elif group_type == 'list2ota':
            ota_price = items['otaPrice']['price']
            list_price = items['listPrice']['price']
            
            if ota_price != list_price:
                price_change[key] = items
            else:
                no_change[key] = {
                    'group_type': group_type,
                    'group_value': key,
                    'flight_nos': [items['otaPrice']['flightNo']],
                    'prices': [ota_price]
                }
    
    return price_change, no_change

def generate_no_change_report(no_change_groups: dict) -> str:
    """生成无价格变动报告字符串"""
    if not no_change_groups:
        return ""
    
    report = []
    for key, group in no_change_groups.items():
        report.append(
            f"结论：无价格变动 | "
            f"分组类型: {group['group_type']} | "
            f"分组值: {group['group_value']}\n"
            f"  涉及航班: {', '.join(group['flight_nos'])}\n"
            f"  稳定价格: {group['prices'][0]}\n"
            f"{'-'*40}"
        )
    
    return "\n".join(report) + "\n"

# 方法一：按航司分组
def group_by_airline(data: list) -> dict:
    def get_airline(flight_no: str) -> str:
        if '/' in flight_no:
            parts = flight_no.split('/')
            return '/'.join([p[:2] for p in parts])
        return flight_no[:2]
    
    result = defaultdict(list)
    for item in data:
        airline = get_airline(item["flightNo"])
        result[airline].append(item)
    return dict(result)

# 方法三：按航班号分组
def group_by_flightno(data: list) -> dict:
    result = defaultdict(list)
    for item in data:
        result[item["flightNo"]].append(item)
    return dict(result)

# 方法二：判断是否统一航班号
def is_unified_flight(data: list) -> bool:
    flights = {item["flightNo"] for item in data}
    return len(flights) == 1



# 方法四：处理OTA数据关联
def process_ota_data(data: list) -> dict:
    # 建立list数据索引
    list_data = {item["tradeId"]: item for item in data if item["tSource"] == "list"}
    
    # 处理OTA数据
    result = {}
    for item in data:
        if item["tSource"] == "ota":
            list_item = list_data.get(item["listTradeId"])
            result[item["tradeId"]] = {
                "otaPrice": item,
                "listPrice": list_item
            }
    return result

def main(param: dict) -> dict:
    
    data, parseStatus = parse_urlencoded_json(param, 'param')
    
    if parseStatus['status'] != 'success':
        return {"status":404, "errMsg": parseStatus['message']}

    final_report = ""
    # 方法二调用
    unified = is_unified_flight(data)
    # print(f"\n是否统一航班号：{unified}")
    if not unified:
         airline_groups = group_by_airline(data)
         # print("航司分组结果：")
         # print(json.dumps(airline_groups, indent=2, ensure_ascii=False))
        
         # 拆分价格变动组
         airline_pc, airline_nc = split_price_changes(airline_groups, 'airline')
         final_report += generate_no_change_report(airline_nc)
         

    # 方法三调用
    flight_groups = group_by_flightno(data)
    # print("\n航班号分组结果：")
    # print(json.dumps(flight_groups, indent=2, ensure_ascii=False))
    # 拆分价格变动组
    flight_pc, flight_nc = split_price_changes(flight_groups, 'flight')
    final_report += generate_no_change_report(flight_nc)

    # 方法四调用
    ota_relations = process_ota_data(data)
    # print("\nOTA数据关联结果：")
    # print(json.dumps(ota_relations, indent=2, ensure_ascii=False))

    
    # 拆分价格变动组
    ota_pc, ota_nc = split_price_changes(ota_relations, 'ota')
    
    # 生成报告
    # 生成综合报告
    final_report += generate_no_change_report(ota_nc)

    finalData = {
        "status": 200,
        "errMsg": "",
        "airLinePriceGroup": airline_pc,
        "flightPriceGroup": flight_pc,
        "list2otaPriceGroup": ota_pc,
        "otherNoPriceChangeGroupMsg":final_report
    }
    return finalData

# 使用示例
if __name__ == "__main__":
    jsonData = [
  {
    "tradeId":"ops_slugger_250222.101637.10.89.68.52.1945857.6337917805_1",
    "flightNo": "CZ5458",
    "searchDateTime": "2025-02-22 07:23:19",
    "departureCity": "WUH",
    "arrivalCity": "SZX",
    "departureDate": "2025-03-03",
    "price": "235",
    "tSource":"list",
    "tag":"TSA1"
  },
  {
    "tradeId":"ops_slugger_250222.072319.10.89.68.51.1632380.1120770521_1",
    "flightNo": "CZ3912",
    "searchDateTime": "2025-02-22 07:23:19",
    "departureCity": "WUH",
    "arrivalCity": "SZX",
    "departureDate": "2025-03-03",
    "price": "400",
    "tSource":"list",
    "tag":"TSA1"
  },
  {
    "tradeId":"ops_slugger_250222.101637.10.89.68.52.1945857.6337917805_2",
    "flightNo": "CZ5458",
    "searchDateTime": "2025-02-22 07:23:19",
    "departureCity": "WUH",
    "arrivalCity": "SZX",
    "departureDate": "2025-03-03",
    "price": "400",
    "tSource":"ota",
    "tag":"TSA1",
    "listTradeId":"ops_slugger_250222.101637.10.89.68.52.1945857.6337917805_1"
  }
]
    encodeData, error = json_to_urlencoded(jsonData)
    input = {"param": encodeData}

    print(json.dumps(input, indent=2, ensure_ascii=False))
    
    finalData = main(input)
    print('执行结果：' + json.dumps(finalData, indent=2, ensure_ascii=False))
    
