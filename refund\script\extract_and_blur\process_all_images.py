#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import cv2
import numpy as np
from pathlib import Path
import argparse
import shutil

# 导入之前创建的姓名提取函数
from extract_patient_name import extract_patient_name

# 定义敏感信息的正则表达式模式
PATTERNS = {
    '电话': [r'电话[：:]\s*(\d+)', r'联系电话[：:]\s*(\d+)', r'手机[：:]\s*(\d+)', r'联系方式[：:]\s*(\d+)', r'\d{11}'],
    '身份证号码': [r'([0-9]{17}[0-9X])', r'([0-9]{15})', r'身份证[：:]\s*([0-9X]{15,18})']
}

def apply_mosaic(image, box, mosaic_size=15):
    """
    对图像的指定区域应用马赛克效果
    
    参数:
        image: 原始图像
        box: 要应用马赛克的区域 [x1, y1, x2, y2]
        mosaic_size: 马赛克块的大小
    
    返回:
        处理后的图像
    """
    x1, y1, x2, y2 = box
    
    # 确保坐标在图像范围内
    height, width = image.shape[:2]
    x1 = max(0, x1)
    y1 = max(0, y1)
    x2 = min(width, x2)
    y2 = min(height, y2)
    
    # 提取区域
    region = image[y1:y2, x1:x2].copy()
    
    # 应用马赛克效果
    h, w = region.shape[:2]
    
    # 如果区域太小，直接返回
    if h <= 0 or w <= 0:
        return image
    
    # 缩小然后放大以创建马赛克效果
    mosaic_h = max(1, h // mosaic_size)
    mosaic_w = max(1, w // mosaic_size)
    
    # 缩小
    small = cv2.resize(region, (mosaic_w, mosaic_h), interpolation=cv2.INTER_LINEAR)
    # 放大
    mosaic = cv2.resize(small, (w, h), interpolation=cv2.INTER_NEAREST)
    
    # 将马赛克区域放回原图
    result = image.copy()
    result[y1:y2, x1:x2] = mosaic
    
    return result

def find_name_box(txt_path, patient_name):
    """
    在OCR结果中查找患者姓名的位置
    
    参数:
        txt_path: OCR文本文件路径
        patient_name: 患者姓名
    
    返回:
        name_box: 姓名的位置 [x1, y1, x2, y2]，如果未找到则返回None
    """
    if not patient_name:
        return None
    
    try:
        # 读取OCR文本文件
        with open(txt_path, 'r', encoding='utf-8') as f:
            ocr_data = json.load(f)
    except Exception as e:
        print(f"读取文件错误 {txt_path}: {e}")
        return None
    
    # 提取OCR结果
    ocr_results = []
    try:
        if "data" in ocr_data and "res" in ocr_data["data"]:
            for item in ocr_data["data"]["res"]:
                if "ocr_result" in item:
                    ocr_results.extend(item["ocr_result"])
    except Exception:
        return None
    
    # 查找包含患者姓名的文本框
    for item in ocr_results:
        text = item.get("text", "")
        if text == patient_name or patient_name in text:
            box = item.get("box", [])
            if box:
                print(f"找到姓名位置: {box}")
                return box
    
    # 特殊处理藤田和也
    if patient_name == "藤田和也":
        for item in ocr_results:
            text = item.get("text", "")
            if "藤田" in text and "和也" in text:
                box = item.get("box", [])
                if box:
                    print(f"特殊处理找到藤田和也位置: {box}")
                    return box
    
    return None

def process_file(txt_path, output_dir, enhanced=False):
    """
    处理单个OCR文本文件和对应的图片
    
    参数:
        txt_path: OCR文本文件路径
        output_dir: 输出目录
        enhanced: 是否使用增强处理（包括姓名提取和标记）
    """
    # 获取对应的图片路径
    img_path = str(txt_path).replace('.txt', '.png')
    if not os.path.exists(img_path):
        img_path = str(txt_path).replace('.txt', '.PNG')
        if not os.path.exists(img_path):
            print(f"找不到对应的图片文件: {img_path}")
            return
    
    # 读取OCR文本文件
    try:
        with open(txt_path, 'r', encoding='utf-8') as f:
            ocr_data = json.load(f)
    except json.JSONDecodeError:
        print(f"JSON解析错误: {txt_path}")
        return
    except Exception as e:
        print(f"读取文件错误 {txt_path}: {e}")
        return
    
    # 读取图片
    image = cv2.imread(img_path)
    patient_name = None # Initialize patient_name
    if enhanced:
        # 提取患者姓名 - 只获取最佳匹配
        patient_name = extract_patient_name(txt_path, debug=True, return_all=False)
        if patient_name:
            print(f"找到患者姓名: {patient_name}")
        else:
            print(f"未能从 {os.path.basename(txt_path)} 提取到有效姓名")

    # 如果读取图片失败，提前返回
    if image is None:
        print(f"无法读取图像: {img_path}")
        return

    # 提取OCR结果
    ocr_results = []
    try:
        if "data" in ocr_data and "res" in ocr_data["data"]:
            for item in ocr_data["data"]["res"]:
                if "ocr_result" in item:
                    ocr_results.extend(item["ocr_result"])
        else:
            print(f"OCR数据结构不符合预期: {txt_path}")
            return
    except Exception as e:
        print(f"处理OCR数据错误: {e}")
        return
    
    # 找出敏感信息
    sensitive_boxes = []
    
    # 先查找姓名位置并添加到敏感区域列表中
    if enhanced and patient_name:
        # 查找姓名在图像中的位置
        name_box = None
        for item in ocr_results:
            text = item.get("text", "")
            if text == patient_name or patient_name in text:
                name_box = item.get("box", [])
                print(f"找到姓名位置: {name_box}")
                break
        
        # 特殊处理藤田和也
        if patient_name == "藤田和也" and not name_box:
            for item in ocr_results:
                text = item.get("text", "")
                if "藤田" in text and "和也" in text:
                    name_box = item.get("box", [])
                    print(f"特殊处理找到藤田和也位置: {name_box}")
                    break
        
        # 将姓名位置添加到敏感区域列表中
        if name_box and len(name_box) == 4:
            sensitive_boxes.append(name_box)
            print(f"已将姓名 '{patient_name}' 添加到敏感区域列表中")
    
    # 检查其他敏感信息
    for item in ocr_results:
        text = item.get("text", "")
        box = item.get("box", [])
        
        # 检查是否包含敏感信息
        is_sensitive = False
        
        # 检查电话
        for pattern in PATTERNS['电话']:
            if re.search(pattern, text):
                is_sensitive = True
                print(f"找到电话: {text}")
                break
        
        # 检查身份证号码 - 只处理号码部分
        for pattern in PATTERNS['身份证号码']:
            match = re.search(pattern, text)
            if match:
                # 如果文本中包含"身份证"，只打码身份证号码部分
                if "身份证" in text and match.group(1):
                    # 获取匹配的身份证号码的位置
                    start_idx = match.start(1)
                    end_idx = match.end(1)
                    
                    # 计算身份证号码在原始文本中的比例
                    if len(text) > 0:
                        id_ratio = (end_idx - start_idx) / len(text)
                        
                        # 根据比例计算身份证号码在box中的位置
                        if len(box) == 4:
                            x1, y1, x2, y2 = box
                            width = x2 - x1
                            
                            # 调整box只包含身份证号码部分
                            adjusted_x1 = int(x1 + start_idx / len(text) * width)
                            adjusted_x2 = int(x1 + end_idx / len(text) * width)
                            
                            # 添加调整后的box
                            sensitive_boxes.append([adjusted_x1, y1, adjusted_x2, y2])
                            print(f"找到身份证号码: {match.group(1)}，只打码号码部分")
                            continue  # 跳过下面的代码，不添加整个box
                
                # 如果不是上面的特殊情况，标记整个文本为敏感
                is_sensitive = True
                print(f"找到身份证号码: {text}")
                break
        
        # 如果是敏感信息，记录其位置
        if is_sensitive and len(box) == 4:
            # box格式为 [x1, y1, x2, y2]
            sensitive_boxes.append(box)
    
    # 在应用马赛克之前保存原始图像（用于对比）
    if enhanced and patient_name:
        before_path = os.path.join(output_dir, "before_" + os.path.basename(img_path))
        cv2.imwrite(before_path, image)
        print(f"已保存原始图像: {before_path}")
    
    # 对敏感区域进行马赛克处理
    print(f"开始处理 {len(sensitive_boxes)} 个敏感区域")
    for i, box in enumerate(sensitive_boxes):
        print(f"  处理敏感区域 {i+1}: {box}")
        image = apply_mosaic(image, box)
    
    # 保存处理后的图片
    output_path = os.path.join(output_dir, os.path.basename(img_path))
    cv2.imwrite(output_path, image)
    print(f"已处理并保存图片: {output_path}")

def main():
    #parser = argparse.ArgumentParser(description='处理OCR文本文件和图片，对敏感信息进行马赛克处理')
    #parser.add_argument('--source', '-s', default='/Users/<USER>/Documents/code/blurimage/source', 
                        #help='源目录，包含OCR文本文件和图片')
    #parser.add_argument('--output', '-o', default='/Users/<USER>/Documents/code/blurimage/enhanced_output', 
                        #help='输出目录，用于保存处理后的图片')
    #parser.add_argument('--enhanced', '-e', action='store_true', 
                        #help='启用增强处理，包括姓名提取和标记')
    #parser.add_argument('--clean', '-c', action='store_true', 
                       #help='清空输出目录')
    
    #args = parser.parse_args()
    
    # 设置源目录和输出目录

    source_dir = Path('/Users/<USER>/Downloads/Archive/source')
    output_dir = Path("/Users/<USER>/Desktop/qnideawork/urs_script/refund/script/enhanced_output")
    clean_output=False
    #增强处理
    enhanced=False
    
    # 清空输出目录
    if clean_output and output_dir.exists():
        shutil.rmtree(output_dir)
        print(f"已清空输出目录: {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理所有txt文件
    txt_files = list(source_dir.glob('*.txt'))
    print(f"找到 {len(txt_files)} 个OCR文本文件")
    
    for txt_file in txt_files:
        print(f"\n处理文件: {txt_file}")
        process_file(txt_file, output_dir, enhanced)
    
    print(f"\n所有文件处理完成，结果保存在: {output_dir}")

if __name__ == "__main__":
    main()
