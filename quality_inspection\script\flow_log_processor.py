import json
import urllib.parse
from typing import Any, Dict
import requests

def invoke_http_by_proxy(
    appCode: str, appToken: str, proxyData: Dict[str, Any], proxy: str
) -> Dict[str, Any]:
    """
    Make HTTP requests through a proxy by posting the proxyData as JSON.

    Args:
        proxyData (Dict[str, Any]): Configuration for the HTTP request
        proxy (str): Proxy URL in format 'http://host:port'

    Returns:
        Dict[str, Any]: Response from the server

    Raises:
        ValueError: If proxy is not provided
    """
    HEADER_SERVER_TOKEN = "Q-Server-Token"
    HEADER_APP_CODE = "Q-App-Code"
    if not proxy:
        raise ValueError("proxy parameter is required")

    try:
        headers = {"Content-Type": "application/json"}
        if appCode:
            headers[HEADER_APP_CODE] = appCode
        if appToken:
            headers[HEADER_SERVER_TOKEN] = appToken

        # Make POST request with proxyData as JSON
        response = requests.post(proxy, json=proxyData, headers=headers)

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "status_code": (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response")
                else None
            ),
        }

def process_flow_logs(flow_no: str, appCode: str, appToken: str) -> list:
    """
    Process flow logs for a given flow number and return filtered results.
    
    Args:
        flow_no (str): The flow number to query
        appCode (str): Application code for authentication
        appToken (str): Application token for authentication
        
    Returns:
        list: List of filtered flow logs containing only specified fields
    """
    # Prepare proxy data for the API call
    proxyData = {
        "method": "get",
        "url": f"https://fuwu.qunar.com/callcenter/flowLog/list?flowNo={flow_no}&newPage=true",
        "data": "",
        "dataType": "form-data",
        "authType": "qsso",
        "qssAuthParam": {
            "targetLoginUrl": "https://fuwu.corp.qunar.com/domainLogin?loginType=qsso",
            "authCookies": ["QN1", "QSSOFP", "_mdp", "_uf", "currentId"],
        },
    }

    proxy = "http://pangunew.corp.qunar.com/api/maintenance/proxyHttpInvoke"
    
    # Call the API through proxy
    result = invoke_http_by_proxy(appCode, appToken, proxyData, proxy)
    
    if "error" in result:
        print(f"Error: {result.get('error')}")
        return []
    
    # Try to parse the response data
    try:
        response_data = json.loads(result.get("data", "{}"))
        if not response_data or response_data.get("ret") is False:
            error_msg = response_data.get("errmsg", "Response data is empty or ret is false")
            print(f"Error: {error_msg}")
            return []
            
        # Extract flowLogList from the response
        flow_logs = response_data.get("data", {}).get("flowLogList", [])
        
        # Filter and transform the logs to only include required fields
        filtered_logs = []
        for log in flow_logs:
            filtered_log = {
                "logListType": log.get("logListType"),
                "logTypeName": log.get("logTypeName"),
                "problemNames": log.get("problemNames"),
                "content": log.get("content")
            }
            filtered_logs.append(filtered_log)
        
        result = {
            "response": {
                "flowLogList": filtered_logs
            }
        }

        return {"success": True, "error": "", "data": result}
        
    except json.JSONDecodeError as e:
        print(f"Error parsing response: {str(e)}")
        return []

def main(param: Dict[str, str]) -> Dict[str, Any]:
    # Example usage
    flow_no = param.get("flowNo")
    appCode = param.get("invokeAppCode")
    appToken = param.get("invokeToken")
    
    logs = process_flow_logs(flow_no, appCode, appToken)
    return  logs

def test():
    # 测试示例
    param = {
        "flowNo": "NPF20250413155924966944",
        "invokeAppCode": "f_pangu",
        "invokeToken": "Hnu88YsOdF2FekK3qbEBhpPzK8ix8OhdGuwok9RaQsFd54/2hkM8VXaUTyAp/qJR9KtgLQH8J+OoP6KsnxKBEom/ju5QamxJIgzeIyxsSC0mzQ3m7T6ZCW2d5cdSR+rAbsg5cqXlCwqM5KxElKz6wKcm5CM35atOjQDM9Whing4="
    }
    result = main(param)
    print(json.dumps(result, ensure_ascii=False))

if __name__ == "__main__":
    test() 