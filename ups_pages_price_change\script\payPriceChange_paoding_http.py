import requests
import json
import copy
from datetime import datetime
from typing import Dict, Any, List, Optional
from requests.exceptions import RequestException
from collections import defaultdict


# 第一部分：原始数据获取和基础处理
def search_user_case(username: str, startDate: str, endDate: str) -> Dict[str, Any]:
    """获取原始订单数据"""
    url = "http://paoding.corp.qunar.com/open/stagePrice/query"
    params = {
        "userName": username,
        "startDate": startDate,
        "endDate": endDate,
        "page": 1,
        "pageSize": 20,
    }
    headers = {"Paoding-Open-Source": "tradeCore"}

    try:
        response = requests.get(url, params=params, headers=headers, timeout=20)
        response.raise_for_status()
        return response.json().get("data", {})
    except (RequestException, ValueError) as e:
        return {"error": f"订单查询失败: {str(e)}"}


def extract_order_list(raw_data: Dict[str, Any]) -> List[str]:
    """从原始数据中提取orderList"""
    order_list = []

    # 防御性处理数据结构
    list_data = raw_data.get("list", []) if isinstance(raw_data, dict) else []

    for item in list_data:
        if not isinstance(item, dict):
            continue

        # 提取orderRecord中的orderNo
        order_record = item.get("orderRecord", {})
        if isinstance(order_record, dict):
            for sub_order in order_record.values():
                if isinstance(sub_order, dict):
                    order_no = sub_order.get("orderNo")
                    if isinstance(order_no, str) and order_no.strip():
                        order_list.append(order_no.strip())

    return order_list


def process_multi_list(original_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理原始数据并添加multiList字段
    :param original_data: 接口返回的原始数据
    :return: 添加了multiList的新数据结构
    """
    # 创建结果对象的拷贝
    processed_data = copy.deepcopy(original_data)

    # 防御性处理list字段
    if not isinstance(processed_data.get("list"), list):
        processed_data["list"] = []

    # 使用默认字典进行分组
    trace_groups = defaultdict(list)

    # 遍历处理每个订单记录
    for item in processed_data["list"]:
        if isinstance(item, dict):
            trace = item.get("createOrderQtrace")
            # 过滤空值和非字符串类型的trace
            if isinstance(trace, str) and trace:
                trace_groups[trace].append(item)

    # 构建multiList（只保留重复的分组）
    processed_data["multiList"] = [
        group for group in trace_groups.values() if len(group) > 1
    ]

    return processed_data


# 第二部分：分析请求相关
def build_analysis_payload(
    order_list: List[str], start_date: str, end_date: str
) -> Dict[str, Any]:
    """构建分析请求体"""

    def format_date(date_str: str, is_start: bool) -> str:
        """日期格式转换"""
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return (
                date_obj.strftime("%Y-%m-%d 00:00:00")
                if is_start
                else date_obj.strftime("%Y-%m-%d 23:59:59")
            )
        except ValueError:
            raise ValueError(f"无效的日期格式: {date_str}")

    return {
        "dateOffset": None,
        "customDateOffset": None,
        "measures": [
            {
                "measureFilter": None,
                "eventName": "traceTradeResult",
                "eventName_ZH": "跟踪交易阶段结果\n",
                "aggregator_ZH": "总次数",
                "isExpression": False,
                "alias": "",
                "aggregator": "general",
            }
        ],
        "byFields": [
            "event.$Anything.orderNo",
            "event.$Anything.tradeStage",
            "event.$Anything.errCode",
            "event.$Anything.bizErrorCode",
            "event.$Anything.interceptType",
            "event.$Anything.interceptRemark",
        ],
        "filter": {
            "conditions": [
                {
                    "function": "equal",
                    "params": order_list,
                    "field": "event.$Anything.orderNo",
                    "cname": "订单号",
                }
            ]
        },
        "fromDate": format_date(start_date, is_start=True),
        "toDate": format_date(end_date, is_start=False),
        # ... [其他固定参数保持原样] ...
    }


def send_analysis_request(
    order_list: List[str], start_date: str, end_date: str
) -> Dict[str, Any]:
    """发送分析请求"""
    if not order_list:
        return {"warning": "空orderList跳过分析请求"}

    url = "http://qlibra.corp.qunar.com/api/evtAnalyze/query/"
    params = {"projectId": "1472", "source": "search"}
    headers = {
        "Cookie": "sensor_7896=f522d549419540487e022d2cb5b841af390f480154a3927e19ed826931fbfb0888ce60dbbbcff266d9cb1337a6f102b88369ee059ac7eb4fb9b4fa1087654c8734ce2a425139897b36b4a930bd4ef7ac2170c0495a73ea78f4b4d1dc8d12a64d; QN1=00014b00247c6c3c37e0c9fa",
        "Content-Type": "application/json",
    }

    try:
        payload = build_analysis_payload(order_list, start_date, end_date)
        response = requests.post(
            url, params=params, headers=headers, json=payload, timeout=15
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": f"分析请求失败: {str(e)}"}


def get_stage_priority(stage: str) -> int:
    """定义阶段排序优先级"""
    priority_order = {"生单阶段": 1, "异步生单阶段": 2, "支付前校验": 3}
    return priority_order.get(stage.strip(), 99)  # 未知阶段排最后


def process_analysis_data(analysis_response: Dict[str, Any]) -> Dict[str, List[Dict]]:
    """处理分析结果数据并排序"""
    result = {}

    # 防御性处理数据结构
    data_list = analysis_response.get("data", [])
    if not isinstance(data_list, list):
        return {}

    # 第一遍：收集数据
    for data_item in data_list:
        rows = data_item.get("rows", [])
        if not isinstance(rows, list):
            continue

        for row in rows:
            by_values = row.get("byValues", [])
            if not isinstance(by_values, list) or len(by_values) < 6:
                continue

            try:
                # 构建标准化数据结构
                order_info = {
                    "orderNo": str(by_values[0]).strip(),
                    "stage": str(by_values[1]).strip(),
                    "errCode": (
                        int(by_values[2]) if str(by_values[2]).strip().isdigit() else 0
                    ),
                    "bizErrorCode": str(by_values[3]).strip(),
                    "interceptType": str(by_values[4]).strip(),
                    "interceptRemark": str(by_values[5]).strip(),
                }

                # 按orderNo分组
                order_no = order_info["orderNo"]
                if order_no:
                    if order_no not in result:
                        result[order_no] = []
                    result[order_no].append(order_info)
            except Exception as e:
                # 记录错误但继续处理其他数据
                print(f"数据处理异常: {str(e)}")
                continue

    # 第二遍：排序数据
    for order_no in result:
        try:
            result[order_no].sort(
                key=lambda x: (
                    get_stage_priority(x["stage"]),
                    x["stage"],  # 相同优先级按原始顺序
                )
            )
        except KeyError:
            # 处理缺失stage字段的情况
            continue

    return result


# 第三部分：主流程整合
def main(param: Dict[str, Any]) -> Dict[str, Any]:
    """主处理流程"""
    # 获取原始数据
    raw_data = search_user_case(
        username=param.get("username", ""),
        startDate=param.get("startDate", ""),
        endDate=param.get("endDate", ""),
    )

    # 错误处理
    if "error" in raw_data:
        return raw_data

    # 处理multiList（保持原始功能）
    processed_data = process_multi_list(raw_data)

    # 提取orderList（不修改原始数据）
    order_list = extract_order_list(raw_data)

    # 发送分析请求
    analysis_result = send_analysis_request(
        order_list=order_list, start_date=param["startDate"], end_date=param["endDate"]
    )

    # 处理分析结果
    event_analysis = {}
    if "error" not in analysis_result:
        event_analysis = process_analysis_data(analysis_result)

    # 构建最终结果
    return {**processed_data, "orderStageInfo": event_analysis}
