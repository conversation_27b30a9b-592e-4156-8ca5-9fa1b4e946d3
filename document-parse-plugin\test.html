<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>文档解析助手 - 测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #2196F3;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background-color: #45a049;
    }
    .result {
      margin-top: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
      white-space: pre-wrap;
      font-family: monospace;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>文档解析助手 - 功能测试</h1>

    <div class="test-section">
      <h3>库加载测试</h3>
      <button onclick="testLibraries()">检查库加载状态</button>
      <div id="libraryResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>HTML导出测试</h3>
      <button onclick="testHTMLExport()">测试HTML导出</button>
      <div id="htmlResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>Markdown导出测试</h3>
      <button onclick="testMarkdownExport()">测试Markdown导出</button>
      <button onclick="testMarkdownWithSampleData()">测试Markdown导出(含示例数据)</button>
      <div id="markdownResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>JSON导出测试</h3>
      <button onclick="testJSONExport()">测试JSON导出</button>
      <div id="jsonResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>打开主界面</h3>
      <button onclick="openMainInterface()">打开文档解析助手</button>
    </div>
  </div>

  <script src="lib/xlsx.full.min.js"></script>
  <script src="lib/mammoth.browser.min.js"></script>
  <script src="lib/turndown.min.js"></script>
  <script src="lib/table-formatter.js"></script>
  <script src="documentProcessor.js"></script>

  <script>
    function testLibraries() {
      const result = document.getElementById('libraryResult');
      let status = '';

      status += 'XLSX库: ' + (typeof XLSX !== 'undefined' ? '✓ 已加载' : '✗ 未加载') + '\n';
      status += 'Mammoth库: ' + (typeof mammoth !== 'undefined' ? '✓ 已加载' : '✗ 未加载') + '\n';
      status += 'TurndownService库: ' + (typeof TurndownService !== 'undefined' ? '✓ 已加载' : '✗ 未加载') + '\n';
      status += 'TableFormatter库: ' + (typeof TableFormatter !== 'undefined' ? '✓ 已加载' : '✗ 未加载') + '\n';
      status += 'DocumentProcessor类: ' + (typeof DocumentProcessor !== 'undefined' ? '✓ 已加载' : '✗ 未加载') + '\n';

      result.textContent = status;
    }

    function testHTMLExport() {
      const result = document.getElementById('htmlResult');
      try {
        const dp = new DocumentProcessor();
        const html = dp.generateCombinedHTML();
        result.textContent = html.substring(0, 500) + (html.length > 500 ? '...\n\n[HTML内容已截断，总长度: ' + html.length + ' 字符]' : '');
      } catch (error) {
        result.textContent = '错误: ' + error.message;
      }
    }

    function testMarkdownExport() {
      const result = document.getElementById('markdownResult');
      try {
        const dp = new DocumentProcessor();
        const markdown = dp.generateCombinedMarkdown();
        result.textContent = markdown.substring(0, 800) + (markdown.length > 800 ? '...\n\n[Markdown内容已截断，总长度: ' + markdown.length + ' 字符]' : '');
      } catch (error) {
        result.textContent = '错误: ' + error.message;
      }
    }

    function testMarkdownWithSampleData() {
      const result = document.getElementById('markdownResult');
      try {
        const dp = new DocumentProcessor();

        // 添加模拟的Word文档数据
        dp.files.word = [{
          name: '示例文档.docx',
          type: 'word',
          data: {
            html: '<h1>示例标题</h1><p>这是一个示例段落。</p><table><tr><td>列1</td><td>列2</td></tr><tr><td>数据1</td><td>数据2</td></tr></table>',
            text: '示例标题\n这是一个示例段落。\n列1 列2\n数据1 数据2'
          }
        }];

        // 添加模拟的Excel数据
        dp.files.excel = [{
          name: '示例表格.xlsx',
          type: 'excel',
          data: {
            SheetNames: ['Sheet1', 'Sheet2'],
            Sheets: {
              'Sheet1': {
                // 模拟XLSX工作表数据
              }
            }
          }
        }];

        // 模拟XLSX.utils.sheet_to_json方法
        const originalSheetToJson = XLSX.utils.sheet_to_json;
        XLSX.utils.sheet_to_json = function(worksheet, options) {
          return [
            ['姓名', '年龄', '部门'],
            ['张三', 25, '技术部'],
            ['李四', 30, '市场部'],
            ['王五', 28, '人事部']
          ];
        };

        const markdown = dp.generateCombinedMarkdown();

        // 恢复原始方法
        XLSX.utils.sheet_to_json = originalSheetToJson;

        result.textContent = markdown.substring(0, 1200) + (markdown.length > 1200 ? '...\n\n[Markdown内容已截断，总长度: ' + markdown.length + ' 字符]' : '');
      } catch (error) {
        result.textContent = '错误: ' + error.message;
      }
    }

    function testJSONExport() {
      const result = document.getElementById('jsonResult');
      try {
        const dp = new DocumentProcessor();
        const json = dp.generateCombinedJSON();
        result.textContent = json.substring(0, 500) + (json.length > 500 ? '...\n\n[JSON内容已截断，总长度: ' + json.length + ' 字符]' : '');
      } catch (error) {
        result.textContent = '错误: ' + error.message;
      }
    }

    function openMainInterface() {
      window.open('main.html', '_blank');
    }

    // 页面加载时自动检查库状态
    window.addEventListener('load', function() {
      testLibraries();
    });
  </script>
</body>
</html>
