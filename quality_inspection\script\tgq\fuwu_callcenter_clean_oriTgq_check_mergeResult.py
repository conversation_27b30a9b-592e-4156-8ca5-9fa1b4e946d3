import traceback


def validate_parameters(params):
    """
    校验入参参数
    Args:
        params: 入参字典
    Returns:
        tuple: (是否校验通过(bool), 校验失败原因(str))
    """
    # Step 1: 校验外层参数是否为空
    if not params:
        return False, "入参为空"

    required_outer_fields = ["baseInfo", "cabinPriceInfo", "tgEventInfo", "tgqInfo"]
    for field in required_outer_fields:
        if field not in params:
            return False, f"缺少必要参数: {field}"
        if not isinstance(params[field], dict):
            return False, f"参数 {field} 必须是字典类型"

    # Step 2: 校验 tgqInfo.oriSaleTgqInfo 不能为空
    if not params["tgqInfo"].get("oriSaleTgqInfo"):
        return False, "tgqInfo.oriSaleTgqInfo 不能为空"

    # Step 3: 校验成人和儿童信息
    cabin_price_info = params["cabinPriceInfo"]
    has_adult_info = bool(
        cabin_price_info.get("aduCabin") and cabin_price_info.get("aduViewPrice")
    )
    has_child_info = bool(
        cabin_price_info.get("chdCabin") and cabin_price_info.get("chdViewPrice")
    )

    if not (has_adult_info or has_child_info):
        return (
            False,
            "成人信息(成人舱位和票面价)或儿童信息(儿童舱位和票面价)至少要填写一组",
        )

    # Step 4: 校验 firstRefundTime 和 firstChangeTime 不能同时为空
    tg_event_info = params["tgEventInfo"]
    if not (
        tg_event_info.get("firstRefundTime") or tg_event_info.get("firstChangeTime")
    ):
        return False, "firstRefundTime 和 firstChangeTime 不能同时为空"

    return True, ""


def main(param: dict) -> dict:
    try:
        is_valid = param.get("is_valid", False)
        errorMsg = param.get("errorMsg", "")
        aiResponse = param.get("aiResponse", "")

        if not aiResponse:
            return {
                "is_valid": False,
                "errorMsg": "aiResponse不能为空",
                "aiResponse": "",
            }

        return {"is_valid": is_valid, "errorMsg": errorMsg, "aiResponse": aiResponse}
    except Exception as e:
        # 处理其他所有异常
        stack_trace = traceback.format_exc()
        error_msg = f"参数校验异常: {str(e)}\n堆栈跟踪:\n{stack_trace}"
        return {"is_valid": False, "errorMsg": error_msg, "aiResponse": ""}


def test_validate_parameters():
    """
    测试参数校验函数
    """
    # 测试用例1：正常数据
    test_data_1 = {
        "baseInfo": {
            "orderNo": "acj250207095432726",
            "domain": "",
            "orderStatus": "",
            "flowNo": "NIMF20250319154541775336",
            "fromSID": "",
            "fOrderNo": "",
            "passengerInfo": "",
        },
        "cabinPriceInfo": {
            "aduCabin": "Y",
            "aduViewPrice": "1000",
            "chdCabin": "",
            "chdViewPrice": "",
            "aduTicketCabin": "",
            "chdTicketCabin": "",
        },
        "tgEventInfo": {
            "tgType": "",
            "firstRefundTime": "2024-03-19",
            "firstRefundAmount": "",
            "secRefundTime": "",
            "secRefundAmount": "",
            "firstChangeTime": "",
            "firstChangePayAmount": "",
            "firstChangeFeeAmount": "",
            "firstChangeUpCabinAmount": "",
        },
        "tgqInfo": {"basePriceEnum": "", "tgqSourceType": "", "oriSaleTgqInfo": "test"},
    }

    is_valid, message = validate_parameters(test_data_1)
    print("Test case 1 - Valid data:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")

    # 测试用例2：缺少必要参数
    test_data_2 = {"baseInfo": {}, "tgEventInfo": {}}

    is_valid, message = validate_parameters(test_data_2)
    print("\nTest case 2 - Missing required fields:")
    print(f"Is valid: {is_valid}")
    print(f"Message: {message}")


if __name__ == "__main__":
    test_validate_parameters()
