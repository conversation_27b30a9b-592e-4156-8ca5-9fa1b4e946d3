import traceback
import cv2
import numpy as np
import os


def detect_copy_move(
    image_path,
    block_size=16,
    step_size=8,
    mse_threshold=200,
    min_shift_distance=50,
    min_cluster_size=5,
):
    """
    Detects copy-move forgery in an image using a simplified block-based approach.

    Args:
        image_path (str): Path to the input image.
        block_size (int): Size of the square blocks for comparison.
        step_size (int): Step for sliding the blocks (determines overlap).
                         step_size < block_size creates overlapping blocks.
        mse_threshold (float): Maximum Mean Squared Error for two blocks to be
                               considered a match. Lower values mean stricter matches.
        min_shift_distance (int): Minimum Euclidean distance (in pixels) between
                                  the top-left corners of two matched blocks to
                                  be considered a valid (non-trivial) pair.
        min_cluster_size (int): Minimum number of matched block pairs sharing the
                                same shift vector to be considered a significant
                                forged region.

    Returns:
        tuple: (output_image_with_boxes, forgery_mask)
               - output_image_with_boxes: Original image with detected regions
                                          highlighted by rectangles.
               - forgery_mask: A binary mask of the same size as the input
                               image, where detected forged regions are marked
                               with 255.
               Returns (None, None) if an error occurs (e.g., image not found).
    """
    img_bgr = cv2.imread(image_path)
    if img_bgr is None:
        print(f"Error: Image not found or cannot be read from '{image_path}'")
        return None, None

    img_gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    h, w = img_gray.shape
    print(f"Image loaded: {image_path} (Dimensions: {w}x{h})")

    # --- 1. Extract Overlapping Blocks and their Features (Flattened Pixels) ---
    blocks_data = []  # Stores {'vector': flattened_block, 'coords': (y, x)}

    if block_size > min(h, w):
        print(
            f"Error: block_size ({block_size}) is too large for image dimensions ({w}x{h})."
        )
        # Return original image and an empty mask
        return img_bgr, np.zeros_like(img_gray, dtype=np.uint8)

    print(f"Extracting blocks with block_size={block_size}, step_size={step_size}...")
    for y in range(0, h - block_size + 1, step_size):
        for x in range(0, w - block_size + 1, step_size):
            block = img_gray[y : y + block_size, x : x + block_size]
            blocks_data.append({"vector": block.flatten(), "coords": (y, x)})

    if not blocks_data:
        print(
            "Error: No blocks extracted. Check image dimensions and block/step sizes."
        )
        return img_bgr, np.zeros_like(img_gray, dtype=np.uint8)

    num_blocks = len(blocks_data)
    print(f"Extracted {num_blocks} blocks.")

    # --- 2. Match Blocks (Pairwise Comparison) ---
    # This is O(N^2) and can be very slow for many blocks.
    matched_block_pairs_info = (
        []
    )  # Stores {'p1':(y1,x1), 'p2':(y2,x2), 'shift':(dy,dx), 'mse':val}

    print("Matching blocks (this may take a while)...")
    for i in range(num_blocks):
        if i % (num_blocks // 10 if num_blocks > 10 else 1) == 0:  # Progress indicator
            print(f"  Matching progress: {int((i/num_blocks)*100)}%")
        for j in range(i + 1, num_blocks):  # Avoid self-comparison and redundant pairs
            vec1 = blocks_data[i]["vector"]
            vec2 = blocks_data[j]["vector"]

            # Calculate Mean Squared Error (MSE)
            mse = np.mean((vec1.astype(np.float32) - vec2.astype(np.float32)) ** 2)

            if mse < mse_threshold:
                y1, x1 = blocks_data[i]["coords"]
                y2, x2 = blocks_data[j]["coords"]

                # Check minimum shift distance to avoid trivial matches
                pixel_dist_sq = (y1 - y2) ** 2 + (x1 - x2) ** 2
                if pixel_dist_sq > min_shift_distance**2:
                    matched_block_pairs_info.append(
                        {
                            "p1": (y1, x1),
                            "p2": (y2, x2),
                            "shift": (y1 - y2, x1 - x2),  # Shift from p2 to p1
                            "mse": mse,
                        }
                    )

    print(
        f"Found {len(matched_block_pairs_info)} raw matched block pairs (MSE < {mse_threshold}, dist > {min_shift_distance})."
    )
    if not matched_block_pairs_info:
        print("No raw matched pairs found meeting initial criteria.")
        return img_bgr, np.zeros_like(img_gray, dtype=np.uint8)

    # --- 3. Cluster Matched Pairs by Shift Vector ---
    shift_vector_clusters = (
        {}
    )  # Key: (dy, dx) shift vector, Value: list of ((y1,x1), (y2,x2)) block coordinate pairs
    for pair_info in matched_block_pairs_info:
        shift = pair_info["shift"]
        if shift not in shift_vector_clusters:
            shift_vector_clusters[shift] = []
        shift_vector_clusters[shift].append((pair_info["p1"], pair_info["p2"]))

    # --- 4. Filter Clusters and Identify Forged Regions ---
    output_img_with_boxes = img_bgr.copy()
    forgery_mask = np.zeros_like(img_gray, dtype=np.uint8)
    significant_regions_found = 0

    # Sort clusters by size (number of block pairs in them) in descending order
    sorted_clusters = sorted(
        shift_vector_clusters.items(), key=lambda item: len(item[1]), reverse=True
    )

    print(f"Clustering and filtering regions (min_cluster_size={min_cluster_size})...")
    for shift, block_coord_pairs_in_cluster in sorted_clusters:
        if len(block_coord_pairs_in_cluster) >= min_cluster_size:
            significant_regions_found += 1
            print(
                f"  Detected potential forged region with shift vector {shift} "
                f"involving {len(block_coord_pairs_in_cluster)} block pairs."
            )
            for (y1, x1), (y2, x2) in block_coord_pairs_in_cluster:
                # Draw rectangles on the output image for both source and destination
                # To distinguish, p1 can be red (source relative to shift) and p2 green (destination)
                # If shift = p1 - p2, then p1 is "source-like", p2 is "destination-like"
                cv2.rectangle(
                    output_img_with_boxes,
                    (x1, y1),
                    (x1 + block_size, y1 + block_size),
                    (0, 0, 255),
                    1,
                )  # p1 in Red
                cv2.rectangle(
                    output_img_with_boxes,
                    (x2, y2),
                    (x2 + block_size, y2 + block_size),
                    (0, 255, 0),
                    1,
                )  # p2 in Green

                # Update the binary forgery mask
                forgery_mask[y1 : y1 + block_size, x1 : x1 + block_size] = 255
                forgery_mask[y2 : y2 + block_size, x2 : x2 + block_size] = 255
        # else: # Optional: if clusters are sorted, we can break early if desired
        # break

    if significant_regions_found == 0:
        print(
            f"No significant forged regions found after clustering (min_cluster_size={min_cluster_size})."
        )
    else:
        print(
            f"Highlighted {significant_regions_found} potential forged region(s) on the output image."
        )

    return output_img_with_boxes, forgery_mask


def batch_check_images(
    folder_path: str,
    file_name: str,
    param_block_size: int,
    param_step_size: int,
    param_mse_threshold: int,
    param_min_shift_dist: int,
    param_min_cluster: int,
) -> None:
    """
    批量检查文件夹下的图片，并将结果输出到Excel

    Args:
        folder_path: 图片文件夹路径
        output_excel: 输出Excel文件路径
    """
    # 支持的图片格式
    image_extensions = {".jpg", ".jpeg", ".png", ".bmp", ".gif"}

    # 存储所有检测结果
    results = []

    # 遍历文件夹
    for filename in os.listdir(folder_path):
        # 检查文件扩展名
        if not any(filename.lower().endswith(ext) for ext in image_extensions):
            continue

        if file_name and filename.find(file_name) == -1:
            continue

        file_path = os.path.join(folder_path, filename)

        try:
            # 读取图片数据
            with open(file_path, "rb") as f:
                image_data = f.read()
                print(f"Processing image: {file_path}")

            processed_image, forgery_highlight_mask = detect_copy_move(
                file_path,
                block_size=param_block_size,
                step_size=param_step_size,
                mse_threshold=param_mse_threshold,
                min_shift_distance=param_min_shift_dist,
                min_cluster_size=param_min_cluster,
            )

            if processed_image is not None:
                print("-" * 30)
                print("Processing complete.")
                # --- Display or Save Results ---
                # To display (if you have a GUI environment like a desktop):
                try:
                    original_img_for_display = cv2.imread(file_path)
                    if original_img_for_display is not None:
                        cv2.imshow("Original Image", original_img_for_display)
                    cv2.imshow("Detected Copy-Move Forgery", processed_image)
                    if (
                        forgery_highlight_mask is not None
                        and forgery_highlight_mask.any()
                    ):
                        cv2.imshow(
                            "Forgery Mask (Detected Regions)", forgery_highlight_mask
                        )
                    print(
                        "Displaying images. Press any key in an image window to close all."
                    )
                    cv2.waitKey(0)
                    cv2.destroyAllWindows()
                except cv2.error as e:
                    print(
                        f"OpenCV GUI error (often occurs in non-GUI environments): {e}"
                    )
                    print("Attempting to save output images instead.")

                # Always try to save output files:
                output_detected_path = os.path.join(
                    folder_path, "detected_" + file_name
                )
                output_mask_path = os.path.join(folder_path, "mask_" + file_name)
                cv2.imwrite(output_detected_path, processed_image)
                print(f"Image with detections saved to: {output_detected_path}")
                if forgery_highlight_mask is not None and forgery_highlight_mask.any():
                    cv2.imwrite(output_mask_path, forgery_highlight_mask)
                    print(f"Forgery mask saved to: {output_mask_path}")
                elif forgery_highlight_mask is not None:
                    print(f"Forgery mask was empty; not saved.")
        except Exception as e:
            error_info = traceback.format_exc()
            print(f"处理文件 {filename} 时出错: {error_info}")


# --- Example Usage ---
if __name__ == "__main__":
    # Use os.path.join for better path handling with Chinese characters
    folder_path = "D:\\work\\ps_check_data"
    file_name = "yck250430161228642_001.PNG"
    # --- Parameters to Tune ---
    # These values are illustrative and WILL LIKELY NEED ADJUSTMENT for your specific image.
    # The duplicated dates "2025-04-30" are relatively small text.

    # Block size: Should be large enough to capture some unique part of the text,
    # but not so large that it includes too much varying background.
    # If text height is ~15-20 pixels, block_size around 8 to 16 might be a start.
    param_block_size = 16  # Size of the square blocks (pixels)

    # Step size: Determines block overlap. Smaller step = more blocks = more computation.
    # Overlap = block_size - step_size.
    param_step_size = 4  # Step for sliding blocks (e.g., 75% overlap if block_size=16)

    # MSE threshold: Max Mean Squared Error for a match. Range for 8-bit pixels is 0 to 255^2.
    # For nearly identical text copies on a plain background, this should be VERY LOW.
    # Start low (e.g., 50-200) and adjust. If too high, many false positives. If too low, misses.
    param_mse_threshold = 100

    # Min shift distance: Min pixel distance between origins of matched blocks.
    # Helps avoid matching parts of the same continuous object.
    param_min_shift_dist = 30

    # Min cluster size: Min number of matched block pairs with the same shift vector
    # to be considered a significant region (filters out noise/random matches).
    # For a date string, this might be 5-15 depending on block_size and text length.
    param_min_cluster = 8

    print(f"Attempting CMFD for image: {folder_path}")
    print(
        f"Parameters: block_size={param_block_size}, step_size={param_step_size}, "
        f"mse_threshold={param_mse_threshold}, min_shift_dist={param_min_shift_dist}, "
        f"min_cluster_size={param_min_cluster}"
    )
    print("-" * 30)

    processed_image, forgery_highlight_mask = batch_check_images(
        folder_path,
        file_name,
        param_block_size=param_block_size,
        param_step_size=param_step_size,
        param_mse_threshold=param_mse_threshold,
        param_min_shift_dist=param_min_shift_dist,
        param_min_cluster=param_min_cluster,
    )
