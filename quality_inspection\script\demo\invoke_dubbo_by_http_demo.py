import json
import requests
from typing import Dict, Any, Optional


class DubboInvoker:
    def __init__(self, base_url: str):
        """
        Initialize the DubboInvoker with the base URL of the proxy service.

        Args:
            base_url (str): The base URL of the Dubbo proxy service
        """
        self.base_url = base_url.rstrip("/")

    def invoke_dubbo_service(
        self,
        interface_name: str,
        method: str,
        parameter_types: list,
        param_mapper_names: list,
        param_json: str,
        version: str = "1.0.0",
        group: str = "",
        address: str = "",
        provider_app_code: str = "",
        provider_app_env: str = "",
        provider_port: str = "",
        direct_con: bool = True,
        timeout: int = 50000,
        invokeAppCode: str = "",
        invokeToken: str = "",
    ) -> Dict[str, Any]:
        """
        Invoke a Dubbo service through HTTP proxy.

        Args:
            interface_name (str): The interface name of the Dubbo service
            method (str): The method name to invoke
            parameter_types (list): List of parameter types
            param_mapper_names (list): List of parameter mapper names
            param_json (str): JSON string of parameters
            version (str): Service version
            group (str): Service group
            address (str): Zookeeper address
            provider_app_code (str): Provider application code
            provider_app_env (str): Provider application environment
            provider_port (str): Provider port
            direct_con (bool): Whether to use direct connection
            timeout (int): Request timeout in milliseconds

        Returns:
            Dict[str, Any]: The response from the Dubbo service
        """
        request_data = {
            "invokerInfo": {
                "dubboConfigInfo": {
                    "interfaceName": interface_name,
                    "version": version,
                    "group": group,
                    "address": address,
                    "providerAppCode": provider_app_code,
                    "providerAppEnv": provider_app_env,
                    "providerPort": provider_port,
                    "directCon": direct_con,
                    "timeout": timeout,
                },
                "method": method,
                "parameterTypes": parameter_types,
                "paramMapperNames": param_mapper_names,
                "dubboInvokeName": f"{interface_name}#{method}",
            },
            "paramJson": param_json,
        }

        try:
            HEADER_SERVER_TOKEN = "Q-Server-Token"
            HEADER_APP_CODE = "Q-App-Code"
        # Make POST request with proxyData as JSON
            headers = {"Content-Type": "application/json"}
            if invokeAppCode:
               headers[HEADER_APP_CODE] = invokeAppCode
            if invokeToken:
               headers[HEADER_SERVER_TOKEN] = invokeToken

            response = requests.post(
                f"{self.base_url}",
                json=request_data,
                headers=headers,
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to invoke Dubbo service: {str(e)}")


import json
from pathlib import Path
from typing import Any, Callable, Optional


def write_json_to_file(
    obj: Any,
    file_path: str,
    encoding: str = "utf-8",
    ensure_ascii: bool = False,
    indent: int = 2,
    default: Optional[Callable] = None,
    **json_kwargs,
) -> bool:
    """
    将 Python 对象序列化为 JSON 并写入文件

    :param obj: 要序列化的对象
    :param file_path: 目标文件路径
    :param encoding: 文件编码，默认utf-8
    :param ensure_ascii: 是否确保ASCII字符，默认False(允许Unicode)
    :param indent: 缩进空格数，默认2
    :param default: 自定义序列化函数
    :param json_kwargs: 其他json.dump参数
    :return: 是否写入成功
    """
    try:
        # 创建目标目录
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 处理自定义对象的默认序列化方式
        serialize_default = default or (lambda o: o.__dict__)

        # 写入文件
        with open(file_path, "w", encoding=encoding) as f:
            json.dump(
                obj,
                f,
                ensure_ascii=ensure_ascii,
                indent=indent,
                default=serialize_default,
                **json_kwargs,
            )
        return True

    except TypeError as e:
        print(f"类型错误: {str(e)}，建议提供自定义序列化函数")
    except PermissionError:
        print(f"权限错误: 无法写入文件 {file_path}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

    return False


# Example usage:
if __name__ == "__main__":
    # Initialize the invoker with the proxy service URL
    invoker = DubboInvoker("http://pangunew.corp.qunar.com/api/maintenance/proxyRpcInvoke")

    # Example parameters
    result = invoker.invoke_dubbo_service(
        interface_name="com.qunar.flight.tts.trade.order.IUserOrderDetailService",
        method="searchOrderDetail",
        parameter_types=[
            "com.qunar.flight.tts.trade.order.model.params.UserOrderParam"
        ],
        param_mapper_names=["orderParam"],
        version="1.0.0",
        group="tts_order",
        address="cn1tczk.corp.qunar.com:2181",
        provider_app_code="f_tts_trade_order",
        provider_app_env="proddocker",
        provider_port="20785",
        param_json=json.dumps({"orderParam": {"orderNo": "pmo250331134905613"}}),
        invokeAppCode="f_pangu",
        invokeToken="Hnu88YsOdF2FekK3qbEBhpPzK8ix8OhdGuwok9RaQsFd54/2hkM8VXaUTyAp/qJR9KtgLQH8J+OoP6KsnxKBEom/ju5QamxJIgzeIyxsSC0mzQ3m7T6ZCW2d5cdSR+rAbsg5cqXlCwqM5KxElKz6wKcm5CM35atOjQDM9Whing4="
    )

    print(result)
    write_json_to_file(result, "quality_inspection/data/dubbo_invoke_demo_result.json")
