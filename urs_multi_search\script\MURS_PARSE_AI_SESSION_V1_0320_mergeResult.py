import re
import json
from datetime import datetime, date
from typing import List, Dict, Any, Set, Tuple, Union, Optional


def extract_json_from_text(text: str) -> Optional[Union[dict, list]]:
    """
    从AI返回的文本中提取并解析JSON数据。
    该方法可以处理以下情况：
    1. 纯JSON文本
    2. 带有markdown代码块标记的JSON (```json)
    3. 带有其他代码块标记的JSON (```python, ```code等)
    4. 包含其他文本干扰的JSON
    5. 多行JSON

    Args:
        text (str): AI返回的文本内容

    Returns:
        Optional[Union[dict, list]]: 解析后的JSON对象，如果解析失败则返回None

    Examples:
        >>> text = '''```json
        ... {"name": "test", "value": 123}
        ... ```'''
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'name': 'test', 'value': 123}
    """
    try:
        # 1. 首先尝试直接解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 2. 尝试提取代码块中的内容
        code_block_pattern = r"```(?:json|python|code)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, text, re.DOTALL)

        for match in matches:
            content = match.group(1).strip()
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                continue

        # 3. 尝试查找文本中的第一个 { 或 [ 到最后一个 } 或 ]
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        matches = re.finditer(json_pattern, text)

        for match in matches:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                continue

        # 4. 如果上述方法都失败，尝试清理文本后解析
        # 移除可能的markdown标记
        cleaned_text = re.sub(r"^```.*$", "", text, flags=re.MULTILINE)
        cleaned_text = re.sub(r"^`.*$", "", cleaned_text, flags=re.MULTILINE)
        # 移除空行
        cleaned_text = re.sub(r"^\s*$\n", "", cleaned_text, flags=re.MULTILINE)

        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        return None

    except Exception as e:
        print(f"JSON解析错误: {str(e)}")
        return None


def safe_json_parse(text, default: Any = None) -> Any:
    """
    安全地解析JSON文本，如果解析失败则返回默认值。

    Args:
        text (str): 要解析的JSON文本
        default (Any, optional): 解析失败时返回的默认值. Defaults to None.

    Returns:
        Any: 解析后的JSON对象或默认值
    """
    if not text:
        return default
    if isinstance(text, (dict, list, tuple)):
        return text
    if isinstance(text, str):
        result = extract_json_from_text(text)
        return result if result is not None else default
    return default


def is_deep_empty(obj: Any) -> bool:
    if obj is None:
        return True
    if isinstance(obj, (list, dict, set, tuple)):
        if len(obj) == 0:
            return True
        # 如果是字典，检查所有值；如果是列表/集合/元组，检查所有元素
        if isinstance(obj, dict):
            return all(is_deep_empty(v) for v in obj.values())
        return all(is_deep_empty(item) for item in obj)
    if isinstance(obj, str):
        return obj.strip() == ""
    return False  # 非容器类型且非 None 的视为非空


def validate_param(param: Dict[str, Any]) -> Dict[str, str]:
    """
    验证输入参数是否有效

    Args:
        param (Dict[str, Any]): 输入参数字典

    Returns:
        Dict[str, str]: 包含验证结果的字典，格式为 {"isValid": "true/false", "errorMsg": "错误信息"}
    """
    result = {"isValid": "false", "errorMsg": "未知", "parseResult": {}}

    # 检查param是否为空
    if not param:
        result["errorMsg"] = "输入参数为空"
        return result

    # 检查intentSlots
    if "parseResult" not in param:
        result["errorMsg"] = (
            param.get("errorMsg") if param.get("errorMsg") else "解析AI助手参数结果为空"
        )
        return result

    parseResult = safe_json_parse(param["parseResult"])
    if parseResult is None:
        result["errorMsg"] = "解析AI助手参数结果为空"
        return result

    if is_deep_empty(parseResult):
        result["errorMsg"] = "解析AI助手参数结果为空"
        return result

    # 检查conversationList
    result["parseResult"] = parseResult
    result["isValid"] = "true"
    result["errorMsg"] = ""
    return result


def main(param):
    result = validate_param(param)
    return result


if __name__ == "__main__":

    param = {
        "errorMsg": "sjninhg4391",
        "isValid": "多次搜索时",
        "parseResult": '{"isValidSession":"是","isPriceChangeIntent":"是","departureDateList":["2025-03-26 22:00"],"departureCityName":"南宁","carrier":"","arrivalCityName":"泉州","carrierList":"","flightNumber":"","departureCity":"NNG","arrivalCity":"JJN","flightMatchFisrtQA":"是","preSurPriceGroups":[{"prePrecisePriceList":[{"parseType":"精准","price":"510"}],"preDurationPriceList":[],"surPrecisePriceList":[{"parseType":"精准","price":"580"},{"parseType":"精准","price":"590"},{"parseType":"精准","price":"600"}],"surDurationPriceList":[],"changePrecisePriceList":[],"changeDurationPriceList":[{"leftPrice":"70","rightPrice":"90","parseType":"区间波动价格"}]}]}',
    }
    print(
        "请求参数Json:"
        + json.dumps(
            param,
            ensure_ascii=False,
            separators=(",", ":"),  # 移除多余空格
            check_circular=True,
        )
    )
    reuslt = main(param)
    if reuslt.get("error"):
        print("获取记录失败，错误信息:" + reuslt.get("error"))
    else:
        print(
            "检验结果:"
            + json.dumps(
                reuslt,
                ensure_ascii=False,
                separators=(",", ":"),  # 移除多余空格
                check_circular=True,
            )
        )
        # print("获取记录:" + json.dumps(
        #     reuslt.get("results"),
        #     ensure_ascii=False,
        #     separators=(",", ":"),  # 移除多余空格
        #     check_circular=True,
        # ))
