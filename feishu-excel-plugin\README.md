# 飞书Excel助手插件安装指南

## 系统要求
- Chrome浏览器 最新版本
- 飞书账号
- 网络连接（用于访问飞书API）

## 完整安装步骤

### 1. 下载插件文件
确保您已获取完整的插件文件夹`feishu-excel-plugin`

### 2. 安装SheetJS库（必须）
1. 打开插件目录中的`lib`文件夹
2. 按照`lib/README.md`的说明下载`xlsx.full.min.js`文件
3. 确认文件已正确放置在`lib`目录下

### 3. 配置飞书应用
1. 访问[飞书开放平台](https://open.feishu.cn/)
2. 创建新应用，获取App ID和App Secret
3. 修改插件中的`manifest.json`文件：
   - 替换`oauth2.client_id`为您应用的App ID
   - 确保已添加以下权限：
     ```json
     "permissions": [
       "activeTab",
       "storage",
       "identity",
       "scripting"
     ]
     ```

### 4. 加载插件到Chrome
1. 打开Chrome浏览器，访问`chrome://extensions/`
2. 启用右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择整个`feishu-excel-plugin`文件夹

### 5. 首次使用设置
1. 点击浏览器工具栏中的插件图标
2. 点击"登录"按钮
3. 输入您的飞书App ID和App Secret
4. 完成授权流程

## 使用说明

### 基本功能
1. **导入Excel文件**：
   - 点击"选择文件"或拖拽Excel文件到指定区域
   - 支持.xlsx和.xls格式

2. **选择工作表**：
   - 从列表中选择要处理的工作表
   - 预览数据内容

3. **导入到飞书**：
   - 从下拉列表选择目标飞书表格
   - 点击"导入到飞书"按钮

4. **导出为文本**：
   - 将当前工作表数据导出为文本文件(.txt)
   - 保持表格结构，使用制表符分隔
   - 自动处理特殊字符

### 常见问题解决

#### 1. "XLSX is not defined"错误
- 确认`lib/xlsx.full.min.js`文件是否存在
- 检查文件大小是否为约1.5MB
- 重新加载插件

#### 2. 飞书授权失败
- 检查App ID和Secret是否正确
- 确认飞书应用已开通表格读写权限
- 尝试重新登录

#### 3. 数据导入失败
- 检查网络连接
- 确认目标表格有写入权限
- 尝试减少导入数据量

## 注意事项
1. 插件最多处理前1000行数据
2. 大文件处理可能需要较长时间
3. 定期检查飞书API调用限额

## 技术支持
如有任何问题，请联系开发者或参考飞书开放平台文档。
